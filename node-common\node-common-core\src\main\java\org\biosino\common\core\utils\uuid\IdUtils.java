package org.biosino.common.core.utils.uuid;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import org.apache.commons.codec.binary.Base32;

import java.nio.ByteBuffer;

/**
 * ID生成器工具类
 *
 * <AUTHOR>
 */
public class IdUtils {
    private static Snowflake mySnowflake;

    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 随机UUID
     */
    public static String fastUUID() {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID() {
        return UUID.fastUUID().toString(true);
    }

    public static String getShortUUID() {
        UUID uuid = UUID.randomUUID();
        Base32 base32 = new Base32();
        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());
        return base32.encodeToString(bb.array()).replace("=", "");
    }

    private static synchronized Snowflake snowflakeInstance() {
        if (mySnowflake == null) {
            mySnowflake = new Snowflake(DateUtil.parse("2024-11-01", DatePattern.NORM_DATE_FORMAT).toJdkDate(), 0, 0, false);
        }
        return mySnowflake;
    }

    public static long snowflakeId() {
        return snowflakeInstance().nextId();
    }

    public static String snowflakeIdStr() {
        return String.valueOf(snowflakeId());
    }
}
