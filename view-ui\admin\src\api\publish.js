import request from '@/utils/request';

const baseURL = '/system/publish';

// 查询文献列表
export function listPublish(params) {
  return request({
    url: `${baseURL}/list`,
    method: 'get',
    params: params,
  });
}

// 新增
export function addPublish(data) {
  return request({
    url: `${baseURL}/add`,
    method: 'post',
    data: data,
  });
}

// 修改
export function updatePublish(data) {
  return request({
    url: `${baseURL}/edit`,
    method: 'put',
    data: data,
  });
}

export function deletePublish(id) {
  return request({
    url: `${baseURL}/delete/${id}`,
    method: 'delete',
  });
}

export function getPubInfoFromPlosp(doi) {
  return request({
    url: `/app/publish/getPubInfoFromPlosp`,
    method: 'get',
    params: { doi },
  });
}
