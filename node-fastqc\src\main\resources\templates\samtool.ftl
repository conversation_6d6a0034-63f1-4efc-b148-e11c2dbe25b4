#!/bin/sh

set -e
export LC_ALL=en_US.utf-8
echo `date` '${dataNo} started samtool ...'
${initCommand}
# 如果软链接不存在 建立软链接
if [ ! -L ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType} ]; then
ln -s ${nodeDataPath}/${dataFilePath} ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType}
fi

singularity exec -B ${nodeDataPath}:${nodeDataPath} -B ${fastqcPath}:${fastqcPath} -B ${imagePath}:${imagePath} -B ${scriptPath}:${scriptPath} ${imagePath}/samtool_v1.21.sif \
samtools quickcheck -vvvv -u ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType} > ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.samtool.log 2>> ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.samtool.err.txt

echo `date` '${dataNo} finished samtool ...'

sleep 1 && echo 'RUN COMPLETED!' && sync
