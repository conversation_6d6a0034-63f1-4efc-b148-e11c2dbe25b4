package org.biosino.sftp.ftp.download;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.sshd.server.session.ServerSession;
import org.apache.sshd.sftp.server.AbstractSftpEventListenerAdapter;
import org.apache.sshd.sftp.server.FileHandle;
import org.apache.sshd.sftp.server.Handle;
import org.biosino.common.core.domain.IpAddr;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.DownloadType;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.DownloadLog;
import org.biosino.common.redis.service.RedisService;
import org.biosino.sftp.authentication.MemberHolder;
import org.biosino.sftp.db.FtpDbService;
import org.biosino.sftp.ftp.download.path.CommonPath;
import org.biosino.sftp.ftp.download.path.FileAttributes;

import java.io.IOException;
import java.net.SocketAddress;
import java.nio.file.AccessDeniedException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * <p>
 * SFTP 的日志监听类
 */
@Slf4j
public class DownloadEventListener extends AbstractSftpEventListenerAdapter {

    private final FtpDbService ftpDbService;

    private final RedisService redisService;

    private final static String SFTP_DOWNLOAD_INFO = "sftp_download_info:";

    public DownloadEventListener(FtpDbService ftpDbService, RedisService redisService) {
        super();
        this.ftpDbService = ftpDbService;
        this.redisService = redisService;
    }

    @Override
    public void reading(
            ServerSession session, String remoteHandle, FileHandle localHandle,
            long offset, byte[] data, int dataOffset, int dataLen)
            throws IOException {

        super.reading(session, remoteHandle, localHandle, offset, data, dataOffset, dataLen);

        // 第一次读数据才进行数据库更新
        if (offset > 0) {
            return;
        }
        CommonPath commonPath = (CommonPath) localHandle.getFile();
        FileAttributes fileAttributes = (FileAttributes) commonPath.getAttributeView().readAttributes();
        Data mongoData = fileAttributes.getData();

        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);

        if (member == null
                || StrUtil.isBlank(member.getHomeDirectory())
                || StrUtil.isBlank(member.getId())
                || StrUtil.isBlank(member.getUsername())) {
            log.error("用户未找到：{}", member);
            throw new AccessDeniedException("User not found");
        }

        // 缓存当前用户的下载信息
        try {
            redisService.addToSet(SFTP_DOWNLOAD_INFO + session.getUsername(), localHandle.getFile().toString(), 1, TimeUnit.DAYS);
        } catch (Exception e) {
            e.printStackTrace();
        }

        DownloadLog downloadLog = new DownloadLog();
        downloadLog.setType(AuthorizeType.data.name());
        downloadLog.setTypeNo(mongoData.getDatNo());
        downloadLog.setMemberId(member.getId());
        downloadLog.setOwnerId(mongoData.getCreator());
        downloadLog.setDownloadType(DownloadType.ftp.name());
        downloadLog.setCreateTime(new Date());
        SocketAddress remoteAddress = session.getRemoteAddress();
        String sockerAddress = remoteAddress.toString();
        // /127.0.0.1:61086 => 127.0.0.1
        String ip = StrUtil.subBetween(sockerAddress, "/", ":");
        IpAddr ipAddr = IpUtils.getIpCountry(ip);
        downloadLog.setIp(ip);
        if (ipAddr != null) {
            downloadLog.setCountry(ipAddr.getCountry());
        }
        ThreadUtil.execute(() -> {
            // 保存下载记录
            this.ftpDbService.insertDownloadLog(downloadLog);

            // 增加下载次数
            this.ftpDbService.incDownloadNum(mongoData.getDatNo());
        });
    }

    public void closed(ServerSession session, String remoteHandle, Handle localHandle, Throwable thrown) throws IOException {
        super.closed(session, remoteHandle, localHandle, thrown);
        // 删除当前用户的下载信息
        try {
            redisService.removeFromSet(SFTP_DOWNLOAD_INFO + session.getUsername(), localHandle.getFile().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
