package org.biosino.upload;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.Sample;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
@SpringBootTest
public class DataExportTest {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void exportMashData() {
        ExcelReader reader = ExcelUtil.getReader(FileUtil.file("C:\\Users\\<USER>\\Desktop\\node_non_host0822-20241022-xu.xlsx"));
        List<Map<String, Object>> maps = reader.readAll();
        List<String> runIds = maps.stream().map(x -> String.valueOf(x.get("run_id"))).distinct().collect(Collectors.toList());


        ArrayList<Map<String, String>> lines = new ArrayList<>();

        // 每10个一组遍历runIds
        int windowSize = 5000;
        for (int i = 0; i < runIds.size(); i += windowSize) {
            List<String> sub = CollUtil.sub(runIds, i, i + windowSize);

            // 查询runList
            List<Run> runList = mongoTemplate.find(Query.query(Criteria.where("run_no").in(sub)), Run.class);

            List<String> expNos = runList.stream().map(Run::getExpNo).distinct().collect(Collectors.toList());
            List<String> sapNos = runList.stream().map(Run::getSapNo).distinct().collect(Collectors.toList());

            // 查找数据experiment 和 sample
            List<Experiment> experimentList = mongoTemplate.find(Query.query(Criteria.where("exp_no").in(expNos)), Experiment.class);
            Map<String, Experiment> expNoToExpmap = experimentList.stream().collect(Collectors.toMap(Experiment::getExpNo, x -> x, (x, y) -> x));

            List<Sample> sampleList = mongoTemplate.find(Query.query(Criteria.where("sap_no").in(sapNos)), Sample.class);
            Map<String, Sample> sapNoToSapMap = sampleList.stream().collect(Collectors.toMap(Sample::getSapNo, x -> x, (x, y) -> x));

            List<String> projNos = experimentList.stream().map(Experiment::getProjectNo).collect(Collectors.toList());

            List<Project> projList = mongoTemplate.find(Query.query(Criteria.where("proj_no").in(projNos)), Project.class);
            Map<String, Project> projNoToProjMap = projList.stream().collect(Collectors.toMap(Project::getProjectNo, x -> x, (x, y) -> x));

            for (Run run : runList) {
                Experiment experiment = expNoToExpmap.get(run.getExpNo());
                Sample sample = sapNoToSapMap.get(run.getSapNo());
                Project project = projNoToProjMap.get(experiment.getProjectNo());

                Map<String, String> line = new LinkedHashMap<>();
                // project id、experiment_id、experiment_type、sample_id、sample_type、run_id、样本属性里面的depth、water_depth、lat_lon、latitude、longitude、temperature、salinity、pressure、pH、biome
                line.put("project_id", project.getProjectNo());
                line.put("experiment_id", experiment.getExpNo());
                line.put("experiment_type", experiment.getExpType());
                line.put("sample_id", sample.getSapNo());
                line.put("sample_type", sample.getSubjectType());
                line.put("run_id", run.getRunNo());
                line.put("depth", sample.getAttributes().get("depth"));
                line.put("water_depth", sample.getAttributes().get("water_depth"));
                line.put("lat_lon", sample.getAttributes().get("lat_lon"));
                line.put("latitude", sample.getAttributes().get("latitude"));
                line.put("longitude", sample.getAttributes().get("longitude"));
                line.put("temperature", sample.getAttributes().get("temperature"));
                line.put("salinity", sample.getAttributes().get("salinity"));
                line.put("pressure", sample.getAttributes().get("pressure"));
                line.put("ph", sample.getAttributes().get("ph"));
                line.put("biome", sample.getAttributes().get("biome"));
                lines.add(line);
            }


        }

        ExcelWriter writer = ExcelUtil.getWriter(FileUtil.file("C:\\Users\\<USER>\\Desktop\\node_non_host.xlsx"));
        writer.write(lines, true);
        writer.flush();
        writer.close();
    }

}
