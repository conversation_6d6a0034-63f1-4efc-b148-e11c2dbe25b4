<template>
  <div class="page submission">
    <div class="container-fluid">
      <Breadcrumb bread-item="Submission" />
      <div v-loading="loading" class="radius-12 sub-detail card">
        <div class="submit-card">
          <div
            class="align-items-center card-shadow radius-12 w-35 bg-white p-20 pl-10"
          >
            <el-row
              :gutter="20"
              class="submit-info align-items-center pl-10"
              :class="computedBgClass"
            >
              <el-col :span="12">
                <span class="font-600 text-main-color font-24">
                  ID: {{ subNo }}
                </span>
              </el-col>
              <el-col :span="12">
                <span
                  v-if="processing"
                  class="sub-state w-60"
                  :class="linearBg"
                >
                  Data Processing
                </span>
                <span v-else class="sub-state w-60" :class="linearBg">
                  {{ statusName }}
                </span>
              </el-col>
              <el-col :span="12">
                <span>Last modify: {{ submission.updateTime }}</span>
              </el-col>
              <el-col v-if="submission.auditor" :span="12">
                <span>Auditor: {{ submission.auditor }}</span>
              </el-col>
            </el-row>
          </div>
        </div>

        <div v-if="!loading" class="main-page-container">
          <div
            v-if="submission.rejectReason && submission.status !== 'complete'"
            class="mb-1"
          >
            <el-alert type="error">
              <template #default>
                <span class="font-14 font-600">Reason for rejection</span>
                <div
                  v-for="(item, idx) in submission.rejectReason"
                  :key="'rejectReason' + idx"
                >
                  <span class="font-14 font-600">{{ item.type }}:</span>
                  <span class="font-14 ml-05">{{ item.reason }}</span>
                </div>
              </template>
            </el-alert>
          </div>

          <!--Raw Data-->
          <template v-if="project">
            <div
              class="text-main-color font-600 category-title"
              :class="computedBgClass"
            >
              Project
            </div>
            <el-row :gutter="20" class="detail-submitter mt-05 row-gap">
              <el-col :span="8">
                <div class="label font-600 text-secondary-color">
                  Project ID
                </div>
                <div
                  v-if="project.projectNo"
                  class="content bg-gray radius-12 text-primary"
                >
                  <router-link
                    :to="'/project/detail/' + project.projectNo"
                    target="_blank"
                    >{{ project.projectNo }}</router-link
                  >
                </div>
                <div v-else class="content bg-gray radius-12">
                  Will be allocated automatically after check.
                </div>
              </el-col>

              <el-col :span="8">
                <div
                  class="label font-600 text-secondary-color"
                  style="width: 200px"
                >
                  Project Name
                </div>

                <el-popover
                  placement="bottom"
                  :width="500"
                  trigger="hover"
                  :content="project.name"
                >
                  <template #reference>
                    <div
                      class="content bg-gray radius-12"
                      style="
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ $text(project.name) }}
                    </div>
                  </template>
                </el-popover>
              </el-col>

              <el-col :span="8">
                <div
                  class="label font-600 text-secondary-color"
                  style="width: 200px"
                >
                  Project Description
                </div>

                <el-popover
                  placement="bottom"
                  :width="500"
                  trigger="hover"
                  :content="project.description"
                >
                  <template #reference>
                    <div
                      class="content bg-gray radius-12"
                      style="
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ $text(project.description) }}
                    </div>
                  </template>
                </el-popover>
              </el-col>

              <el-col
                v-for="(it, index) in project.relatedLinks"
                :key="'project.relatedLinks' + index"
                :span="8"
              >
                <div class="label font-600 text-secondary-color">
                  Related Links {{ index > 0 ? index + 1 : '' }}
                </div>
                <div class="content bg-gray radius-12 text-primary">
                  <a :href="it" target="_blank">{{ it }}</a>
                </div>
              </el-col>
            </el-row>

            <div
              v-if="
                project?.publish &&
                project?.publish.length !== 0 &&
                project?.publish[0].id
              "
              class="bg-gray p-15 mt-1"
            >
              <PreviewPublish
                :key="'project-publish-' + hotTableKey"
                v-model:publish-data="project.publish"
                :show-title="false"
              ></PreviewPublish>
            </div>
          </template>

          <div
            v-if="experiment"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Experiment
          </div>

          <HotTable
            v-for="(exp, idx) in experiment"
            :id="hotTableKey + 'sub-detail-exp-ht-table-id-' + idx"
            :key="hotTableKey + 'sub-detail-exp-ht-table-key-' + idx"
            :hot-table="exp.rows"
            :hot-columns="exp.title"
            type="Experiment"
            :sub-type="exp.type"
          ></HotTable>

          <div
            v-if="expPublish && expPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'exp-publish-' + hotTableKey"
              v-model:publish-data="expPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="sample"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Sample
          </div>

          <HotTable
            v-for="(exp, idx) in sample"
            :id="hotTableKey + 'sub-detail-sample-ht-table-id-' + idx"
            :key="hotTableKey + 'sub-detail-sample-ht-table-key-' + idx"
            :hot-table="exp.rows"
            :hot-columns="exp.title"
            type="Sample"
            :sub-type="exp.type"
          ></HotTable>

          <div
            v-if="sapPublish && sapPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'sap-publish-' + hotTableKey"
              v-model:publish-data="sapPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="rawDataArchiveData"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Archiving
          </div>

          <HotTable
            v-if="rawDataArchiveData"
            :id="'sub-detail-archiving-id-' + hotTableKey"
            :key="'sub-detail-archiving-key-' + hotTableKey"
            :hot-table="rawDataArchiveData"
            :hot-columns="archivingColumns"
            type="Archiving"
            sub-type="Raw Data"
          ></HotTable>

          <!--Analysis Data-->
          <div
            v-if="analysis"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Analysis
          </div>
          <HotTable
            v-if="analysis"
            :id="'sub-detail-analysis-id-' + hotTableKey"
            :key="'sub-detail-analysis-key-' + hotTableKey"
            :hot-table="analysis"
            :hot-columns="analysisColumns"
            type="Analysis"
          ></HotTable>

          <div
            v-if="analysisPublish && analysisPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'analysis-publish-' + hotTableKey"
              v-model:publish-data="analysisPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="analysisArchiveData"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Archiving
          </div>
          <HotTable
            v-if="analysisArchiveData"
            :id="'sub-detail-analysis-archiving-id-' + hotTableKey"
            :key="'sub-detail-analysis-archiving-key-' + hotTableKey"
            :hot-table="analysisArchiveData"
            :hot-columns="analysisArchivingColumns"
            type="Archiving"
            sub-type="Analysis"
          ></HotTable>

          <template v-if="publishInfo?.doi">
            <div
              class="text-main-color font-600 category-title mt-1"
              :class="computedBgClass"
            >
              Publications
            </div>
            <div class="bg-gray p-15 mt-1">
              <PreviewPublish
                :key="'add-publish-' + hotTableKey"
                v-model:publish-data="publishArray"
                :show-title="false"
              ></PreviewPublish>
            </div>

            <el-table
              class="mt-1"
              :data="publishInfo?.typeInfoList"
              style="width: 100%; margin-bottom: 20px"
              :header-cell-style="{
                backgroundColor: '#f2f2f2',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              max-height="300"
            >
              <el-table-column
                prop="typeNo"
                label="Related ID"
                width="120"
                sortable
              >
                <template #default="scope">
                  <router-link
                    class="text-primary"
                    :to="'/' + scope.row.type + '/detail/' + scope.row.typeNo"
                    target="_blank"
                    >{{ scope.row.typeNo }}</router-link
                  >
                </template>
              </el-table-column>

              <el-table-column
                prop="typeName"
                label="Related Name"
                sortable
                show-overflow-tooltip
              />

              <el-table-column
                prop="articleName"
                label="Publish Title"
                sortable
                show-overflow-tooltip
              />
              <el-table-column
                prop="email"
                label="Creator"
                sortable
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.username }} ({{ scope.row.email }})
                </template>
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="Publish Create Date"
                sortable
                width="180"
              />
            </el-table>
          </template>

          <div
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            Submitter
          </div>
          <el-row :gutter="20" class="detail-submitter mt-05">
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">First Name</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.firstName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Middle Name</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.middleName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Last Name</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.lastName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">
                Organization
              </div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.orgName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Department</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.deptName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">PI Name</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.piName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Email</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.email) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Phone</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.phone) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Fax</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.fax) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Street</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.street) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">City</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.city) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">
                State/Province
              </div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.stateProvince) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">Postal code</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.postalCode) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">
                Country/Region
              </div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.countryRegion) }}
              </div>
            </el-col>
          </el-row>

          <div class="text-align-right mt-2 pr-20">
            <el-button
              v-if="submission.status === 'editing'"
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="submitSubmission"
              >Submit
            </el-button>
            <el-button
              v-if="
                submission.status !== 'complete' &&
                submission.status !== 'deleted' &&
                submission.status !== 'reviewing' &&
                submission.status !== 'waiting'
              "
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="editSub"
            >
              Edit
            </el-button>
            <el-button
              v-if="
                submission.status === 'editing' ||
                submission.status === 'rejected'
              "
              type="danger"
              class="btn btn-s btn-shadow"
              round
              plain
              @click="deletedSubmission()"
              >Deleted
            </el-button>
            <el-button class="btn-primary btn btn-round" round @click="goBack">
              Back
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <EditPublish ref="editPublishRef"></EditPublish>
    <DeleteLog ref="deleteLog"></DeleteLog>
  </div>
</template>

<script setup>
  import router from '@/router';
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { computed, getCurrentInstance, onMounted, ref } from 'vue';
  import HotTable from '@/views/submit/submission/detail/hotTable/table.vue';
  import { useRoute } from 'vue-router';
  import {
    deleted,
    getDetailData,
    rejectToEdit,
    submit,
  } from '@/api/submission';
  import PreviewPublish from '@/views/submit/metadata/rawData/common/PreviewPublish.vue';
  import EditPublish from '@/components/Publish/editPublish.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import useUserStore from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const { proxy } = getCurrentInstance();
  const route = useRoute();

  const subNo = route.params.subNo;

  const submission = ref({});
  const project = ref({});

  const hotTableKey = ref(1);
  const loading = ref(false);

  const experiment = ref([]);
  const expPublish = ref({});

  const sample = ref([]);
  const sapPublish = ref({});

  const rawDataArchiveData = ref([]);

  const analysis = ref([]);
  const analysisPublish = ref([]);
  const analysisArchiveData = ref([]);

  const publishArray = ref([]);
  const publishInfo = ref({});

  const processing = ref(false);

  onMounted(() => {
    // 初始化数据
    initData();
  });

  /** 初始化数据 */
  function initData() {
    loading.value = true;
    getDetailData(subNo)
      .then(response => {
        const result = response.data;
        submission.value = result.submission;
        processing.value = result.processing;
        project.value = result.project;

        if (project.value && project.value.projectNo) {
          const regex = /^[0-9a-z]{32}$/; // 匹配32位的UUID字符串的正则表达式

          if (regex.test(project.value.projectNo)) {
            project.value.projectNo = undefined;
          }
        }

        experiment.value = result.experiment;

        expPublish.value = result.expPublish;

        sample.value = result.sample;
        sapPublish.value = result.sapPublish;

        rawDataArchiveData.value = result.rawDataArchiveData;

        analysis.value = result.analysis;
        analysisPublish.value = result.analysisPublish;

        analysisArchiveData.value = result.analysisArchiveData;

        if (result.publish) {
          publishInfo.value = result.publish;
          publishInfo.value.typeInfoList.sort((a, b) => {
            if (
              a.creator === submission.value.creator &&
              b.creator !== submission.value.creator
            ) {
              return -1; // a排在b前面
            } else if (
              a.creator !== submission.value.creator &&
              b.creator === submission.value.creator
            ) {
              return 1; // b排在a前面
            } else {
              return 0; // 保持原有顺序
            }
          });

          if (
            member?.value?.id &&
            publishInfo.value?.typeInfoList?.length !== 0
          ) {
            publishInfo.value.typeInfoList =
              publishInfo.value.typeInfoList.filter(
                obj => obj.creator === member.value.id,
              );
          }
          publishArray.value.push(result.publish);
        }

        hotTableKey.value++;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function deletedSubmission() {
    proxy.$modal
      .confirm(
        `Confirm deleting the Submission: ${subNo}?<br>Data cannot be retrieved after deletion, please operate with caution`,
      )
      .then(() => {
        proxy.$modal.loading('Deleting, please wait');
        deleted(subNo)
          .then(response => {
            if (response.data) {
              proxy.$refs['deleteLog'].openLog(response.data);
              return;
            }
            proxy.$modal.msgSuccess('Delete successful');
            router.push({
              path: `/submit/submission/list`,
            });
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  }

  function submitSubmission() {
    if (!subNo) {
      proxy.$modal.alertWarning('Submission NO cannot be empty');
      return;
    }
    /*if (
      (submission.value.dataType === 'rawData' ||
        submission.value.dataType === 'analysisData') &&
      !analysisArchiveData.value &&
      !rawDataArchiveData.value
    ) {
      proxy.$modal.alertWarning('Please archive the data before submitting');
      return;
    }*/
    proxy.$modal
      .confirm(`Confirm submitting data for ${subNo}?`)
      .then(() => {
        proxy.$modal.loading('Submitting, please wait');
        return submit(subNo);
      })
      .then(response => {
        if (response.data) {
          proxy.$alert(
            "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
              response.data +
              '</div>',
            'Error',
            { dangerouslyUseHTMLString: true },
          );
        } else {
          router.push({
            path: `/submit/submission/list`,
          });
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function editSub() {
    if (submission.value.dataType === 'publish') {
      proxy.$refs['editPublishRef'].editPublish(publishInfo.value.id);
      return;
    }

    // 如果是失败，则先要转换给编辑中状态
    if (submission.value.status === 'rejected') {
      rejectToEdit(subNo).finally(() => {
        router.push({
          path: `/submit/metadata/${submission.value.dataType}/edit/${subNo}`,
        });
      });
    }

    // 已经是编辑状态，直接跳转到编辑页面
    if (submission.value.status === 'editing') {
      router.push({
        path: `/submit/metadata/${submission.value.dataType}/edit/${subNo}`,
      });
    }
  }

  const archivingColumns = ref([
    'experiment_id',
    'experiment_name',
    'sample_id',
    'sample_name',
    'run_id',
    'run_name',
    'run_description',
    'data_id',
    'file_name',
    'data_remark',
  ]);

  const analysisColumns = ref([
    'analysis_id',
    'analysis_name',
    'description',
    'analysis_type',
    'other_analysis_type',
    'index',
    'program',
    'link',
    'version',
    'note',
    'output_file',
    'target_project',
    'target_experiment',
    'target_sample',
    'target_analysis',
    'target_run',
    'target_data',
    'target_other_name',
    'target_other_link',
  ]);

  const analysisArchivingColumns = ref([
    'analysis_id',
    'analysis_name',
    'data_id',
    'file_name',
    'data_remark',
  ]);

  const computedBgClass = computed(() => {
    if (!submission.value.status) {
      return 'editing-bg';
    }
    return `${submission.value.status.toLowerCase()}-bg`;
  });

  const statusName = computed(() => {
    if (!submission.value.status) {
      return '';
    }
    if (submission.value.status === 'waiting') {
      return `Waiting Review`;
    }
    if (submission.value.status === 'rejected') {
      return `Rejected`;
    }
    // 首字母大写
    return (
      submission.value.status.substring(0, 1).toUpperCase() +
      submission.value.status.substring(1)
    );
  });

  const linearBg = computed(() => {
    if (!submission.value.status) {
      return 'editing-linear-bg';
    }
    return `${submission.value.status.toLowerCase()}-linear-bg`;
  });

  function goBack() {
    window.history.back();
  }
</script>

<style lang="scss" scoped>
  .submission {
    padding: 20px 0 25px 0;
  }

  .main-page-container {
    margin-top: -50px;
  }

  :deep(.el-popper.is-dark) {
    max-width: 30% !important;
  }

  :deep(.el-alert__description) {
    margin-top: 0;
  }
</style>
