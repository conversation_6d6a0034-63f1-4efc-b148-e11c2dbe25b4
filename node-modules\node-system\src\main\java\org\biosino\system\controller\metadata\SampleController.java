package org.biosino.system.controller.metadata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.SampleDTO;
import org.biosino.system.service.meta.SampleService;
import org.biosino.system.vo.metadata.SampleListVO;
import org.biosino.system.vo.metadata.SampleVO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/sample")
public class SampleController {
    private final SampleService sampleService;


    /**
     * 列出所有的Sample
     */
    @PostMapping("/listSample")
    public TableDataInfo list(@RequestBody MetadataQueryDTO queryDTO) {
        Page<SampleListVO> page = sampleService.listAuditedSample(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询sample的详细信息
     */
    @GetMapping("/getByNo/{sapNo}")
    public AjaxResult getByNo(@PathVariable("sapNo") String sapNo) {
        SampleVO result = sampleService.getSapInfoByNo(sapNo);
        return AjaxResult.success(result);
    }

    /**
     * 获取Organism
     */
    @GetMapping("/getAuditedOrganism")
    public AjaxResult getAuditedOrganism() {
        List<String> result = sampleService.getAuditedOrganism();
        return AjaxResult.success(result);
    }

    /**
     * 获取SapType
     */
    @GetMapping("/getAuditedSapType")
    public AjaxResult getAuditedSapType() {
        List<String> result = sampleService.getAuditedSapType();
        return AjaxResult.success(result);
    }

    /**
     * 保存编辑的Sample
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", businessType = BusinessType.UPDATE)
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody SampleDTO sampleDTO) {
        SampleVO result = sampleService.updateSample(sampleDTO);
        return AjaxResult.success(result);
    }


    /**
     * 修改用户、删除 预检查
     */
    @GetMapping("/deleteCheck/{sapNo}")
    public AjaxResult deleteCheck(@PathVariable("sapNo") String sapNo) {
        DeleteCheckResultVO result = sampleService.deleteCheck(sapNo);
        return AjaxResult.success(result);
    }

    /**
     * 批量删除校验
     */
    @GetMapping("/batchDeleteCheck")
    public AjaxResult batchDeleteCheck(String[] sapNos) {
        DeleteCheckResultVO result = sampleService.batchDeleteCheck(CollUtil.newArrayList(sapNos));

        return AjaxResult.success(result);
    }

    /**
     * 删除Sample 及其下面相关的内容
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteAll")
    public AjaxResult deleteAll(String sapNo) {
        // 删除数据
        sampleService.deleteAll(sapNo);
        return AjaxResult.success();
    }

    /**
     * 批量删除sample及以下的内容
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", module3 = "批量", businessType = BusinessType.DELETE)
    @RequestMapping("/batchDeleteAll")
    public AjaxResult batchDeleteAll(String[] sapNos) {
        sampleService.batchDeleteAll(Arrays.asList(sapNos));
        return AjaxResult.success();
    }

    /**
     * 修改creator
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", module3 = "Change Creator", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateCreator")
    public AjaxResult changeCreator(String sapNo, String newCreator) {
        // 删除数据
        sampleService.updateCreator(sapNo, newCreator);
        return AjaxResult.success();
    }

    /**
     * 导出sap_no
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", module3 = "Export ID", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportId")
    public void exportId(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        List<String> list = sampleService.getSampleNos(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File file = FileUtil.writeUtf8Lines(list, FileUtil.file(tempDir, "sample_id.txt"));
        DownloadUtils.download(request, response, file, "sample_id.txt");
    }

    /**
     * 导出sample的的数据
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Sample", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("metadata:sample:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = sampleService.exportSample(queryDTO);
        DownloadUtils.download(request, response, file, "sample.json");
    }
}
