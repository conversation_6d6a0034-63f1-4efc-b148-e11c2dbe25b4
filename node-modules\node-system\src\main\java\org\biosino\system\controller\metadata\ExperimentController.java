package org.biosino.system.controller.metadata;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.ExperimentDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.service.meta.ExperimentService;
import org.biosino.system.vo.metadata.ExperimentListVO;
import org.biosino.system.vo.metadata.ExperimentVO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/4/26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/experiment")
public class ExperimentController {

    private final ExperimentService experimentService;

    /**
     * 列出所有的Experiment
     */
    @PostMapping("/listExperiment")
    public TableDataInfo list(@RequestBody MetadataQueryDTO queryDTO) {
        Page<ExperimentListVO> page = experimentService.listAuditedExperiment(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询experiment的详细信息
     */
    @GetMapping("/getByNo/{expNo}")
    public AjaxResult getExpInfoByNo(@PathVariable String expNo) {
        ExperimentVO result = experimentService.getExpInfoByNo(expNo);
        return AjaxResult.success(result);
    }

    /**
     * 获取所有编辑ExpType
     */
    @GetMapping("/getAuditedExpType")
    public AjaxResult getAuditedExpType() {
        List<String> result = experimentService.getAuditedExpType();
        return AjaxResult.success(result);
    }

    /**
     * 保存编辑的Experiment
     */
    @RequestMapping("/edit")
    @Log(module1 = "Metadata Mgmt", module2 = "Experiment", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody ExperimentDTO experimentDTO) {
        ExperimentVO result = experimentService.updateExperiment(experimentDTO);
        return AjaxResult.success(result);
    }

    /**
     * 修改用户、删除 预检查
     */
    @GetMapping("/deleteCheck/{expNo}")
    public AjaxResult deleteCheck(@PathVariable("expNo") String expNo) {
        DeleteCheckResultVO result = experimentService.deleteCheck(expNo);
        return AjaxResult.success(result);
    }

    /**
     * 删除Experiment 及其下面相关的内容
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Experiment", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteAll")
    public AjaxResult deleteAll(String expNo) {
        // 删除数据
        experimentService.deleteAll(expNo);
        return AjaxResult.success();
    }

    /**
     * 修改creator
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Experiment", module3 = "Change Creator", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateCreator")
    public AjaxResult changeCreator(String expNo, String newCreator) {
        // 删除数据
        experimentService.updateCreator(expNo, newCreator);
        return AjaxResult.success();
    }

    /**
     * 导出sap_no
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Experiment", module3 = "Export ID", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportId")
    public void exportId(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        List<String> list = experimentService.getExperimentNos(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File file = FileUtil.writeUtf8Lines(list, FileUtil.file(tempDir, "experiment_id.txt"));
        DownloadUtils.download(request, response, file, "experiment_id.txt");
    }

    /**
     * 导出experiment的数据
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Experiment", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("metadata:experiment:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = experimentService.exportExperiment(queryDTO);
        DownloadUtils.download(request, response, file, "experiment.json");
    }
}
