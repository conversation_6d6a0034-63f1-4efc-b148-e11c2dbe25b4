import request from '@/utils/request';

// 查询特殊数据集首页统计信息
export function fdHomeStat() {
  return request({
    url: '/system/fdWeb/homeStat',
    method: 'get',
  });
}

// 查询特殊数据集首页统计信息
export function fdResDetail(query) {
  return request({
    url: '/system/fdWeb/fdResDetail',
    method: 'get',
    params: query,
  });
}

// 查询特殊数据集首页统计信息
export function fdOmicsDetail(query) {
  return request({
    url: '/system/fdWeb/fdOmicsDetail',
    method: 'get',
    params: query,
  });
}

// 查询hmdsNfsc Dataset 统计数据
export function hmdsStatDetail() {
  return request({
    url: '/system/fdWeb/hmdsStatDetail',
    method: 'get',
  });
}

export function hmdsSapList(params) {
  return request({
    url: '/system/fdWeb/hmdsSapList',
    method: 'get',
    params: params,
  });
}
