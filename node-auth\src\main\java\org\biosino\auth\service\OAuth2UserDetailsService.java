package org.biosino.auth.service;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.oauth2.model.OAuthUser;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.model.CustomUserDetails;
import org.biosino.system.api.model.Member;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户详情服务
 * 用于根据用户信息加载本地用户详情
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OAuth2UserDetailsService {

    private final RemoteMemberService remoteMemberService;

    /**
     * 根据用户信息加载本地用户详情
     *
     * @param OAuthUser 用户信息
     * @return 本地用户详情
     * @throws UsernameNotFoundException 用户不存在异常
     */
    public UserDetails loadUserByOAuthUser(OAuthUser OAuthUser) throws UsernameNotFoundException {
        log.info("根据OAuth2用户信息加载本地用户: OAuth2 ID={}, Email={}", OAuthUser.getId(), OAuthUser.getEmail());
        // 示例代码，需要替换为实际的用户查询逻辑
        R<MemberDTO> r = remoteMemberService.getOneMemberByBioId(OAuthUser.getId(), "FtpUser", SecurityConstants.INNER);
        if (R.isError(r)) {
            throw new ServiceException("调用node-member服务失败");
        }
        if (R.isSuccess(r) && r.getData() == null) {
            throw new AuthenticationException("用户未绑定") {
            };
        }
        MemberDTO memberDTO = r.getData();
        Member member = BeanUtil.copyProperties(memberDTO, Member.class);
        // 将密码置为空，避免泄露
        member.setPassword(CustomUserDetails.NON_EXISTENT_PASSWORD_VALUE);

        // 用户权限集合
        Set<GrantedAuthority> authorities = new HashSet<>();

        return new CustomUserDetails(
                OAuthUser.getEmail(), // 使用OAuth2用户名作为用户名
                CustomUserDetails.NON_EXISTENT_PASSWORD_VALUE,
                true, // 账户是否启用
                authorities,
                member.getId().toString(),
                member.getName() != null ? member.getName() : OAuthUser.getName(),
                member,
                "enable"
        );
    }
}
