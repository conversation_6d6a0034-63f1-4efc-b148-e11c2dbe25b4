package org.biosino.upload.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import org.biosino.common.core.constant.HttpStatus;
import org.biosino.common.core.enums.FtpFileLogStatus;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.upload.dto.FtpFileLogQueryDTO;
import org.biosino.upload.mapper.FtpFileLogMapper;
import org.biosino.upload.service.FtpFileLogService;
import org.biosino.upload.vo.CheckingExport;
import org.biosino.upload.vo.FtpFileLogVO;
import org.biosino.upload.vo.UncheckExport;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
public class FtpFileLogServiceImpl extends ServiceImpl<FtpFileLogMapper, FtpFileLog> implements FtpFileLogService {
    @Override
    public List<FtpFileLogVO> listUnchecked(FtpFileLogQueryDTO queryDTO) {
        // 12万条记录的测试数据 <EMAIL>  ID: IV2WVL62QZEPJJURHGACCWBZTQ
//        String homeDir = "/bdp-picb/node/ftp_home/20201217/<EMAIL>";
        String homeDir = "D:/test/node/sftp/20210831/<EMAIL>";
        // 查询还未归档的数据
        // 限制1万条
        queryDTO.setLimit(5000);
        queryDTO.setStatus(CollUtil.newArrayList(FtpFileLogStatus.unchecked.getStatus()));
        List<FtpFileLog> list = getFtpFileLogList(queryDTO);
        // 将list中md5结尾的过滤出来
//        List<String> md5FilePaths = list.stream().map(FtpFileLog::getPath).filter(path -> path.endsWith(".md5")).collect(Collectors.toList());
        list = list.stream().filter(path -> !path.getPath().endsWith(".md5")).collect(Collectors.toList());
        list.forEach((it) -> {
                    it.setRelativePath(it.getPath().replace(homeDir, ""));
                }
        );
        List<FtpFileLogVO> vos = buildFileTree(list);

        if (CollUtil.isNotEmpty(vos)) {
            return vos.get(0).getChildren();
        } else {
            return new ArrayList<>();
        }
    }


    /**
     * 构建文件树
     *
     * @param list 文件列表
     * @return 文件树
     */
    public List<FtpFileLogVO> buildFileTree(List<FtpFileLog> list) {
        List<FtpFileLogVO> fileTree = new ArrayList<>();
        for (FtpFileLog ftpFileLog : list) {
            String filepath = ftpFileLog.getRelativePath();
            String[] parts = filepath.split("/");
            List<FtpFileLogVO> currentNodeChildren = fileTree;
            for (int i = 0; i < parts.length; i++) {
                boolean isDir = (i < parts.length - 1);
                String fileName = parts[i];
                FtpFileLogVO fileNode = findNodeByName(currentNodeChildren, fileName);
                if (fileNode == null) {
                    fileNode = new FtpFileLogVO();
                    fileNode.setName(fileName);
                    fileNode.setIsDir(isDir);
                    if (!isDir) {
                        fileNode.setId(ftpFileLog.getId());
                        fileNode.setPath(ftpFileLog.getPath());
                        fileNode.setRelativePath(ftpFileLog.getRelativePath());
                        fileNode.setSize(FileUtil.readableFileSize(ftpFileLog.getSize()));
                        fileNode.setCreateTime(ftpFileLog.getCreateTime());
                        fileNode.setUploadType("SFTP");
                        fileNode.setMd5FileStatus(NodeUtils.getFileMd5Status(ftpFileLog.getPath()));
                    } else {
                        fileNode.setId(IdUtils.simpleUUID());
                    }
                    fileNode.setChildren(new ArrayList<>());
                    currentNodeChildren.add(fileNode);
                }

                currentNodeChildren = fileNode.getChildren();
            }
        }
        return fileTree;
    }

    public static FtpFileLogVO findNodeByName(List<FtpFileLogVO> nodes, String name) {
        for (FtpFileLogVO node : nodes) {
            if (node.getName().equals(name)) {
                return node;
            }
        }
        return null;
    }

    @Override
    public void deleteFtpFileByIds(List<String> ids) {
        for (String id : ids) {
            FtpFileLog ftpFileLog = getById(id);
            // 如果没有找到文件，跳过
            if (ftpFileLog == null) {
                continue;
            }
            // 获取文件路径
            String path = ftpFileLog.getPath();
            String md5Filepath = path + ".md5";
            // 删除文件
            FileUtil.del(path);
            if (FileUtil.exist(md5Filepath)) {
                // 删除md5文件
                FileUtil.del(md5Filepath);
                FtpFileLog md5FileLog = findFirstByCreatorAndPath(SecurityUtils.getMemberId(), md5Filepath);
                if (md5FileLog != null) {
                    md5FileLog.setStatus(FtpFileLogStatus.deleted.getStatus());
                    saveOrUpdate(md5FileLog);
                }
            }
            // 修改状态
            ftpFileLog.setStatus(FtpFileLogStatus.deleted.getStatus());
            saveOrUpdate(ftpFileLog);
        }
    }

    private List<FtpFileLog> getFtpFileLogList(FtpFileLogQueryDTO query) {
        Wrapper<FtpFileLog> qw = Wrappers.<FtpFileLog>lambdaQuery().eq(FtpFileLog::getCreator, SecurityUtils.getMemberId())
                .in(CollUtil.isNotEmpty(query.getIds()), FtpFileLog::getId, query.getIds())
                .in(CollUtil.isNotEmpty(query.getStatus()), FtpFileLog::getStatus, query.getStatus())
                .like(StrUtil.isNotBlank(query.getName()), FtpFileLog::getPath, query.getName())
                .ge(query.getBeginTime() != null, FtpFileLog::getCreateTime, query.getBeginTime() != null ? DateUtil.beginOfDay(query.getBeginTime()) : null)
                .le(query.getBeginTime() != null, FtpFileLog::getCreateTime, query.getEndTime() != null ? DateUtil.endOfDay(query.getEndTime()) : null)
                .notLikeLeft(FtpFileLog::getName, ".md5")
                .orderByDesc(FtpFileLog::getCreateTime)
                .last(query.getLimit() != null, " limit " + query.getLimit());
        return this.list(qw);
    }

    @Override
    public FtpFileLog findFirstByCreatorAndPath(String creator, String path) {
        return this.baseMapper.findFirstByCreatorAndPath(creator, path);
    }

    @Override
    public TableDataInfo listChecking(FtpFileLogQueryDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getStatus())) {
            queryDTO.setStatus(FtpFileLogStatus.includeCheckStatus());
        }
        List<FtpFileLog> list = getFtpFileLogList(queryDTO);
        List<FtpFileLogVO> result = BeanUtil.copyToList(list, FtpFileLogVO.class);
        String homeDir = SecurityUtils.getMemberFtpPath();
        result.forEach(x -> x.setRelativePath(x.getPath().replace(homeDir, "")));

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(result);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    @Override
    public void exportUnchecked(HttpServletResponse response, FtpFileLogQueryDTO queryDTO) {
        queryDTO.setStatus(CollUtil.newArrayList(FtpFileLogStatus.unchecked.getStatus()));
        List<FtpFileLog> list = getFtpFileLogList(queryDTO);
        // 把所有含有md5的文件过滤出来
        List<String> md5FilePaths = list.stream().map(FtpFileLog::getPath).filter(path -> path.endsWith(".md5")).collect(Collectors.toList());
        list = list.stream().filter(path -> !path.getPath().endsWith(".md5")).collect(Collectors.toList());
        List<UncheckExport> result = new ArrayList<>();
        String homeDir = SecurityUtils.getMemberFtpPath();
        for (FtpFileLog item : list) {
            UncheckExport export = new UncheckExport();
            export.setName(item.getName());
            export.setUploadType("SFTP");
            export.setRelativePath(item.getPath().replace(homeDir, ""));
            export.setReadableSize(FileUtil.readableFileSize(item.getSize()));
            export.setUploadDate(item.getCreateTime());
            export.setMd5FileStatus(NodeUtils.getFileMd5Status(item.getPath()));
            export.setMd5(item.getMd5());
            export.setMd5FileContent(item.getMd5FileContent());

            result.add(export);
        }
        ExcelUtil<UncheckExport> util = new ExcelUtil<>(UncheckExport.class);
        util.exportExcel(response, result, "sheet1");
    }

    @Override
    public void exportChecking(HttpServletResponse response, FtpFileLogQueryDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getStatus())) {
            queryDTO.setStatus(FtpFileLogStatus.includeCheckStatus());
        }
        List<FtpFileLog> list = getFtpFileLogList(queryDTO);
        List<CheckingExport> result = new ArrayList<>();
        String homeDir = SecurityUtils.getMemberFtpPath();
        for (FtpFileLog item : list) {
            CheckingExport export = new CheckingExport();
            export.setName(item.getName());
            export.setRelativePath(item.getPath().replace(homeDir, ""));
            export.setReadableSize(FileUtil.readableFileSize(item.getSize()));
            export.setUploadDate(item.getCreateTime());
            export.setStatus(item.getStatus());
            export.setFailCause(item.getFailCause());
            result.add(export);
        }
        ExcelUtil<CheckingExport> util = new ExcelUtil<>(CheckingExport.class);
        util.exportExcel(response, result, "sheet1");
    }

    @Override
    public FtpFileLog getByIdAndCreator(String id, String creator) {
        FtpFileLog ftpFileLog = getById(id);
        if (ftpFileLog == null) {
            return null;
        }
        if (!ftpFileLog.getCreator().equals(creator)) {
            return null;
        }
        return ftpFileLog;
    }

    @Override
    public long countByStatusInAndCreator(List<String> status, String memberId) {
        if (CollUtil.isEmpty(status) || StrUtil.isBlank(memberId)) {
            return 0;
        }
        Wrapper<FtpFileLog> qw = Wrappers.<FtpFileLog>lambdaQuery()
                .eq(FtpFileLog::getCreator, memberId)
                .in(FtpFileLog::getStatus, status)
                .notLikeLeft(FtpFileLog::getPath, ".md5");
        long count = this.count(qw);
        return count;
    }
}
