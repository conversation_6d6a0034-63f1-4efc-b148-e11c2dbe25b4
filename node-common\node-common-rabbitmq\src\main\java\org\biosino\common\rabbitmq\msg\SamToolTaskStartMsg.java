package org.biosino.common.rabbitmq.msg;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Data
public class SamToolTaskStartMsg {

    @JsonProperty("data_no")
    private String dataNo;

    @JsonProperty("data_file_path")
    private String dataFilePath;

    @JsonProperty("priority")
    private String priority;

    @JsonIgnore
    private Integer priorityInt;

    @JsonProperty("data_file_name")
    private String dataFileName;

    @JsonProperty("data_type")
    private String fileType;

    @JsonProperty("result_base_dir")
    private String resultBaseDir;

}
