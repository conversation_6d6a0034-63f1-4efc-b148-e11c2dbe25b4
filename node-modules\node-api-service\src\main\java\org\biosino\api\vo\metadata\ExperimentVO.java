package org.biosino.api.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class ExperimentVO {

    private String expNo;

    private String projectNo;

    private String name;

    private String description;

    private String protocol;

    private String expType;

    private Map<String, Object> attributes = new LinkedHashMap<>();

    private String creator;

    private Date createDate;

    private Date updateDate;

    private Submitter submitter;

    private List<String> usedIds;

    private List<String> sourceProject;

    private String visibleStatus;

    private List<PublishVO> references;
}
