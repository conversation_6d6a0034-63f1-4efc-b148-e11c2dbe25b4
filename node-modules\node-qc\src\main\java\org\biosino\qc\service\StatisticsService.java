package org.biosino.qc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.SubmissionDataTypeEnum;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.other.RejectReason;
import org.biosino.qc.enums.AuditLogEnum;
import org.biosino.qc.repository.AuditLogRepository;
import org.biosino.qc.repository.SubmissionRepository;
import org.biosino.qc.vo.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final AuditLogRepository auditLogRepository;
    private final SubmissionRepository submissionRepository;

    public DataVolumeVO dataVolume(BaseQuery dto) {
        List<Submission> allNum = submissionRepository.findAllNum(dto);

        DataVolumeVO vo = new DataVolumeVO();

        if (CollUtil.isEmpty(allNum)) {
            return vo;
        }

        vo.setSubNum(allNum.size());
        vo.setProjNum(allNum.stream().mapToInt(Submission::getProjNum).sum());
        vo.setExpNum(allNum.stream().mapToInt(Submission::getExpNum).sum());
        vo.setSapNum(allNum.stream().mapToInt(Submission::getSapNum).sum());
        vo.setRunNum(allNum.stream().mapToInt(Submission::getRunNum).sum());
        vo.setAnalNum(allNum.stream().mapToInt(Submission::getAnalNum).sum());
        vo.setPublish(allNum.stream().mapToInt(Submission::getPublishNum).sum());
        vo.setDataNum(allNum.stream().mapToInt(Submission::getDataNum).sum());
        vo.setDataSize(FileUtil.readableFileSize(allNum.stream().mapToLong(Submission::getDataSize).sum()));
        return vo;
    }

    public AuditOverviewVO auditOverview(BaseQuery dto) {

        AuditOverviewVO vo = new AuditOverviewVO();

        vo.setSubReviews(auditLogRepository.countByStatus(dto, null));
        // audit_log的条数 + 待审核的submission记录数
        vo.setSubTotal(vo.getSubReviews() + submissionRepository.countByStatus(dto, SubmissionStatusEnum.waiting.name()));

        vo.setFrequencyItem(auditLogRepository.countTotalByStatus(dto, null));
        // audit_log中各num总数 + 待审核submission记录中各num总数
        vo.setFrequencyTotal(vo.getFrequencyItem() + submissionRepository.countTotalByStatus(dto, SubmissionStatusEnum.waiting.name()));

        vo.setSubSuccess(submissionRepository.countByStatus(dto, SubmissionStatusEnum.complete.name()));
        vo.setSubmissionTotal(submissionRepository.countByNotStatus(dto, SubmissionStatusEnum.deleted.name()));
        return vo;
    }

    public AuditDataVO auditData(BaseQuery dto) {
        List<AuditLog> all = auditLogRepository.findAll(dto);

        AuditDataVO vo = new AuditDataVO();
        if (CollUtil.isEmpty(all)) {
            return vo;
        }

        vo.setSubNum(all.size());

        int rejectReasonTotal = 0;
        Map<String, Integer> errMap = new HashMap<>();

        for (AuditLog auditLog : all) {
            if (AuditLogEnum.pass.name().equals(auditLog.getStatus())) {
                vo.setSuccess(vo.getSuccess() + 1);
            }
            if (AuditLogEnum.reject.name().equals(auditLog.getStatus())) {
                vo.setFail(vo.getFail() + 1);
            }
            vo.setProjNum(vo.getProjNum() + auditLog.getProjNum());
            vo.setExpNum(vo.getExpNum() + auditLog.getExpNum());
            vo.setSapNum(vo.getSapNum() + auditLog.getSapNum());
            vo.setAnalNum(vo.getAnalNum() + auditLog.getAnalNum());
            vo.setPublish(vo.getPublish() + auditLog.getProjNum());

            List<RejectReason> rejectReason = auditLog.getRejectReason();

            if (CollUtil.isNotEmpty(rejectReason)) {
                rejectReasonTotal = rejectReasonTotal + rejectReason.size();

                for (RejectReason reason : rejectReason) {
                    String reasonType = reason.getType();
                    if (errMap.containsKey(reasonType)) {
                        Integer i = errMap.get(reasonType);
                        errMap.put(reasonType, ++i);
                    } else {
                        errMap.put(reasonType, 1);
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(errMap)) {

            // 将 HashMap 的键值对转换为 List
            List<Map.Entry<String, Integer>> entryList = new ArrayList<>(errMap.entrySet());

            // 按照值进行排序
            entryList.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));

            // 取出前3个键值对
            List<Map.Entry<String, Integer>> top3Entries = entryList.subList(0, Math.min(3, entryList.size()));

            List<AuditDataVO.Reason> topReason = new ArrayList<>();

            int top3Value = 0;
            for (Map.Entry<String, Integer> top3Entry : top3Entries) {
                AuditDataVO.Reason reason = new AuditDataVO.Reason();
                reason.setName(top3Entry.getKey());
                reason.setValue(top3Entry.getValue());
                topReason.add(reason);

                top3Value += reason.getValue();
            }

            if (entryList.size() > 3) {
                AuditDataVO.Reason reason = new AuditDataVO.Reason();
                reason.setName("Other");
                reason.setValue(rejectReasonTotal - top3Value);
                topReason.add(reason);
            }

            vo.setTopReason(topReason);
            vo.setRejectReasonTotal(rejectReasonTotal);
        }
        return vo;
    }

    public QualityLogVO qualityLog(BaseQuery dto) {
        List<AuditLog> all = auditLogRepository.findAll(dto);

        QualityLogVO vo = new QualityLogVO();
        if (CollUtil.isEmpty(all)) {
            return vo;
        }

        Map<String, Integer> userMap = new HashMap<>();

        for (AuditLog auditLog : all) {
            // 通过的
            if (AuditLogEnum.pass.name().equals(auditLog.getStatus())) {
                vo.setProjSuccessNum(vo.getProjSuccessNum() + auditLog.getProjNum());
                vo.setExpSuccessNum(vo.getExpSuccessNum() + auditLog.getExpNum());
                vo.setSapSuccessNum(vo.getSapSuccessNum() + auditLog.getSapNum());
                vo.setAnalSuccessNum(vo.getAnalSuccessNum() + auditLog.getAnalNum());
                vo.setRunSuccessNum(vo.getRunSuccessNum() + auditLog.getRunNum());
                vo.setPublishSuccess(vo.getPublishSuccess() + auditLog.getPublishNum());

                if (SubmissionDataTypeEnum.rawData.name().equals(auditLog.getDataType())) {
                    vo.setRawData(vo.getRawData() + auditLog.getDataNum());
                    vo.setRawDataSize(vo.getRawDataSize() + auditLog.getDataSize());
                }

                if (SubmissionDataTypeEnum.analysisData.name().equals(auditLog.getDataType())) {
                    vo.setAnalData(vo.getAnalData() + auditLog.getDataNum());
                    vo.setAnalDataSize(vo.getRawDataSize() + auditLog.getDataSize());
                }

                vo.setPass(vo.getPass() + 1);
            }

            // 总的
            vo.setProjNum(vo.getProjNum() + auditLog.getProjNum());
            vo.setExpNum(vo.getExpNum() + auditLog.getExpNum());
            vo.setSapNum(vo.getSapNum() + auditLog.getSapNum());
            vo.setAnalNum(vo.getAnalNum() + auditLog.getAnalNum());
            vo.setRunNum(vo.getRunNum() + auditLog.getRunNum());
            vo.setPublish(vo.getPublish() + auditLog.getPublishNum());

            String qcUser = auditLog.getAuditor();
            if (userMap.containsKey(qcUser)) {
                Integer i = userMap.get(qcUser);
                userMap.put(qcUser, ++i);
            } else {
                userMap.put(qcUser, 1);
            }

            if (AuditLogEnum.startReview.name().equals(auditLog.getStatus())) {
                vo.setStartReview(vo.getStartReview() + 1);
            }

            if (AuditLogEnum.reject.name().equals(auditLog.getStatus())) {
                vo.setReject(vo.getReject() + 1);
            }
        }

        if (CollUtil.isNotEmpty(userMap)) {

            // 将 HashMap 的键值对转换为 List
            List<Map.Entry<String, Integer>> entryList = new ArrayList<>(userMap.entrySet());

            // 按照值进行排序
            entryList.sort((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()));

            // 取出前3个键值对
            List<Map.Entry<String, Integer>> top3Entries = entryList.subList(0, Math.min(3, entryList.size()));

            List<QualityLogVO.Reason> topReason = new ArrayList<>();

            int top3Value = 0;
            for (Map.Entry<String, Integer> top3Entry : top3Entries) {
                QualityLogVO.Reason reason = new QualityLogVO.Reason();
                reason.setName(top3Entry.getKey());
                reason.setValue(top3Entry.getValue());
                topReason.add(reason);

                top3Value += reason.getValue();
            }

            if (entryList.size() > 3) {
                QualityLogVO.Reason reason = new QualityLogVO.Reason();
                reason.setName("Other");
                reason.setValue(all.size() - top3Value);
                topReason.add(reason);
            }
            vo.setTopQcOperator(topReason);
            vo.setQcOperatorTotal(all.size());
        }

        vo.setRawDataSizeStr(FileUtil.readableFileSize(vo.getRawDataSize()));
        vo.setAnalDataSizeStr(FileUtil.readableFileSize(vo.getAnalDataSize()));
        return vo;
    }

    public List<AuditDetailVO> auditDetailData(BaseQuery dto) {
        dto.setOrderByColumn("createTime");
        dto.setIsAsc("descending");
        List<AuditLog> auditLogs = auditLogRepository.findAll(dto);

        List<AuditDetailVO> result = new ArrayList<>();

        Map<String, AuditDetailVO> monthMap = new HashMap<>();

        for (AuditLog log : auditLogs) {
            Date createDate = log.getCreateTime();
            String month = DateUtils.formatDateToStr("yyyy.MM", createDate);

            AuditDetailVO detailVO = monthMap.get(month);
            if (detailVO == null) {
                detailVO = new AuditDetailVO();
                detailVO.setMonth(month);
                detailVO.setResults(new ArrayList<>());
                monthMap.put(month, detailVO);
                result.add(detailVO);
            }

            AuditDetailVO finalDetailVO = detailVO;
            AuditDetailVO.AuditDetail detail = detailVO.getResults().stream()
                    .filter(d -> d.getName().equals(log.getAuditor()))
                    .findFirst()
                    .orElseGet(() -> {
                        AuditDetailVO.AuditDetail newDetail = new AuditDetailVO.AuditDetail();
                        newDetail.setName(log.getAuditor());
                        finalDetailVO.getResults().add(newDetail);
                        return newDetail;
                    });

            detail.setReviews(detail.getReviews() + 1);

            if (log.getStatus().equals(AuditLogEnum.pass.name())) {
                detail.setSuccess(detail.getSuccess() + 1);
            }

            detail.setItems(detail.getItems() + log.getTotal());
        }

        return result;
    }

    // 导出审核数据
    public void exportAuditData(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<AuditDetailVO> auditDetailDataList = auditDetailData(new BaseQuery());
        // 获取所有的月份
        Set<String> months = new TreeSet<>();
        for (AuditDetailVO auditDetailVO : auditDetailDataList) {
            months.add(auditDetailVO.getMonth());
        }

        // 获取所有的审核员名称
        Set<String> names = new TreeSet<>();
        for (AuditDetailVO auditDetailVO : auditDetailDataList) {
            List<AuditDetailVO.AuditDetail> results = auditDetailVO.getResults();
            for (AuditDetailVO.AuditDetail result : results) {
                names.add(result.getName());
            }
        }

        List<String[]> csvData = new ArrayList<>(auditDetailDataList.size() + 1);
        // 构建表头
        StringBuilder header = new StringBuilder("Months, ");
        for (String name : names) {
            header.append(name).append("(success), ").append(name).append("(reviews), ").append(name).append("(items), ");
        }
        // 删除最后一个逗号和空格
        header.delete(header.length() - 2, header.length());

        csvData.add(header.toString().split(","));

        // 构建数据行
        for (String month : months) {
            StringBuilder row = new StringBuilder(month);
            for (String name : names) {
                long success = 0;
                long reviews = 0;
                long items = 0;

                for (AuditDetailVO auditDetailVO : auditDetailDataList) {
                    if (auditDetailVO.getMonth().equals(month)) {
                        List<AuditDetailVO.AuditDetail> results = auditDetailVO.getResults();
                        for (AuditDetailVO.AuditDetail result : results) {
                            if (result.getName().equals(name)) {
                                success += result.getSuccess();
                                reviews += result.getReviews();
                                items += result.getItems();
                            }
                        }
                    }
                }

                row.append(", ").append(success).append(", ").append(reviews).append(", ").append(items);
            }
            csvData.add(row.toString().split(","));
        }

        // 临时文件夹
        File tempDir = FileUtil.getTmpDir();
        File auditData = FileUtil.file(tempDir, "audit-data.csv");

        // 指定路径和编码
        CsvWriter writer = CsvUtil.getWriter(auditData, CharsetUtil.CHARSET_UTF_8);
        // 按行写出
        writer.write(csvData);

        DownloadUtils.download(request, response, auditData, "audit-data.csv");

        FileUtil.del(auditData);
    }

}
