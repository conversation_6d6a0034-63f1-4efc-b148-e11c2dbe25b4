<template>
  <div class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form ref="queryRef2" :model="queryParams" :inline="true">
            <el-form-item label="Submission ID" prop="subNo">
              <el-input
                v-model="queryParams.subNo"
                style="width: 160px"
                clearable
                placeholder="Submission ID"
                @keyup.enter="getDataList"
              />
            </el-form-item>
            <el-form-item label="Data Type" prop="dataType">
              <el-select
                v-model="queryParams.dataType"
                style="width: 160px"
                clearable
              >
                <el-option label="Raw Data" value="rawData" />
                <el-option label="Analysis Data" value="analysisData" />
                <el-option label="Project" value="project" />
                <el-option label="Experiment" value="experiment" />
                <el-option label="Sample" value="sample" />
                <el-option label="Publish" value="publish" />
              </el-select>
            </el-form-item>

            <el-form-item label="Status" prop="status">
              <el-select
                v-model="queryParams.status"
                style="width: 120px"
                clearable
              >
                <el-option label="Start Review" value="startReview" />
                <el-option label="Pass" value="pass" />
                <el-option label="Rejected" value="reject" />
              </el-select>
            </el-form-item>

            <el-form-item label="Auditor" prop="auditor">
              <el-select
                v-model="queryParams.auditor"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="(item, idx) in roleList"
                  :key="'role-' + idx"
                  :label="item.userName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="Audit Time">
              <el-date-picker
                v-model="dateRange"
                clearable
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                style="width: 240px"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >Search</el-button
              >
              <el-button icon="Refresh" @click="reset">Reset</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        :default-sort="defaultSort"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="subNo"
          label="Submission ID"
          width="140"
          sortable
        >
          <template #default="scope">
            <span
              class="text-primary cursor-pointer"
              @click="toDetail(scope.row)"
            >
              {{ scope.row.subNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="dataType"
          label="Data Type"
          width="150"
          sortable
        />
        <el-table-column prop="status" label="Status" width="150">
          <template #default="scope">
            <div class="d-flex align-items-center">
              <div v-if="scope.row.status === 'pass'">
                <el-icon color="#07BCB4" size="17">
                  <CircleCheckFilled />
                </el-icon>
                <span class="ml-05" style="color: #07bcb4">Pass </span>
              </div>
              <div
                v-else-if="scope.row.status === 'startReview'"
                class="d-flex align-items-center"
              >
                <svg-icon
                  icon-class="reviewing"
                  class-name="edit-waiting"
                ></svg-icon>
                <span class="ml-05" style="color: #fe7f2b">Start Review </span>
              </div>
              <div
                v-else-if="scope.row.status === 'reject'"
                class="d-flex align-items-center"
              >
                <el-icon size="17" color="#FF8181">
                  <CircleCloseFilled />
                </el-icon>
                <span class="ml-05 mr-05" style="color: #ff8989">Rejected</span>
                <el-popover placement="right" width="450" trigger="hover">
                  <template #reference>
                    <el-icon color="#DA0619" class="cursor-pointer">
                      <ChatDotRound />
                    </el-icon>
                  </template>
                  <div v-if="scope.row.rejectReason">
                    <div class="text-main-color font-600 font-16 text-center">
                      Reason for rejection
                    </div>
                    <el-divider class="mb-1 mt-1"></el-divider>
                    <div class="mb-1">
                      <div
                        v-for="(item, idx) in scope.row.rejectReason"
                        :key="'row-rejectReason-' + idx"
                      >
                        <el-divider
                          v-if="idx !== 0"
                          class="mb-1 mt-1"
                        ></el-divider>
                        <div class="mb-05">
                          <span
                            class="text-secondary-color fail-label font-600 mr-05"
                            >Reason Type:</span
                          >
                          <span class="text-secondary-color">{{
                            item.type
                          }}</span>
                        </div>
                        <div class="d-flex">
                          <span
                            class="text-secondary-color fail-label font-600 label mr-05"
                            >Details:</span
                          >
                          <span class="text-secondary-color">{{
                            item.reason
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="projNum" label="Project" sortable />
        <el-table-column
          prop="expNum"
          label="Experiment"
          sortable
          width="120"
        />
        <el-table-column prop="sapNum" label="Sample" sortable />
        <el-table-column prop="analNum" label="Analysis" sortable />
        <el-table-column prop="runNum" label="Run" sortable />
        <el-table-column prop="dataNum" label="Data" sortable />
        <el-table-column prop="publishNum" label="Publish" sortable />
        <el-table-column prop="total" label="Total" sortable />

        <el-table-column prop="auditor" label="Auditor" sortable width="150" />
        <el-table-column
          prop="createTime"
          label="Audit Time"
          width="200"
          sortable
        />
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @click="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { listLog } from '@/api/submission/audit';
  import { findQcUser } from '@/api/system/user';
  import { useRouter } from 'vue-router';

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createTime',
      isAsc: 'descending',
      auditor: '',
      status: '',
      subNo: '',
      creator: '',
    },
    dateRange: [],
    roleList: [],
    loading: false,
    defaultSort: { prop: 'createTime', order: 'descending' },
  });

  const {
    tableData,
    total,
    queryParams,
    dateRange,
    loading,
    defaultSort,
    roleList,
  } = toRefs(data);

  onMounted(() => {
    findQcUserData();
    getDataList();
  });

  /** 查询审核员列表*/
  function findQcUserData() {
    findQcUser().then(response => {
      roleList.value = response.data;
    });
  }

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listLog(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 重置查询*/
  function reset() {
    proxy.resetForm('queryRef2');
    dateRange.value = [];
    getDataList();
  }

  /** 触发排序事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  const toDetail = row => {
    router.push({
      path: `/submission/detail/audit/${row.subNo}`,
    });
  };
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }

  .fail-label {
    display: inline-block;
    width: 85px;
    text-align: right;
  }
</style>
