<template>
  <div class="app-container">
    <div class="card list">
      <el-row>
        <el-col :span="24" justify="space-between">
          <el-form ref="formRef" :model="queryParams" :inline="true">
            <el-form-item label="Creator" prop="creatorEmail">
              <el-input
                v-model="queryParams.creatorEmail"
                style="width: 240px"
                clearable
                @keyup.enter="getDataList"
              ></el-input>
            </el-form-item>
            <el-form-item label="Type" prop="type">
              <el-select
                v-model="queryParams.type"
                clearable
                style="width: 180px"
                @change="getDataList"
              >
                <el-option value="project" label="Project"></el-option>
                <el-option value="experiment" label="Experiment"></el-option>
                <el-option value="sample" label="Sample"></el-option>
                <el-option value="run" label="Run"></el-option>
                <el-option value="data" label="Data"></el-option>
                <el-option value="analysis" label="Analysis"></el-option>
                <el-option value="share" label="Share"></el-option>
                <el-option value="review" label="Review"></el-option>
                <el-option value="publish" label="Publish"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Type Nos" prop="typeNos">
              <el-input
                v-model="queryParams.typeNos"
                type="textarea"
                rows="1"
                style="width: 180px"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="Subject Type" prop="subjectType">
              <el-select
                v-model="queryParams.subjectType"
                clearable
                style="width: 180px"
                :teleported="false"
                placeholder="Select"
                @change="getDataList"
              >
                <el-option
                  v-for="(item, index) in subjectTypes"
                  :key="index"
                  style="width: 180px"
                  :value="item"
                  :label="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Tag" prop="tag">
              <el-select
                v-model="queryParams.tags"
                clearable
                style="width: 180px"
                :teleported="false"
                placeholder="Select"
                @change="getDataList"
              >
                <el-option
                  v-for="dict in tag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-checkbox
                v-model="queryParams.hasFilterField"
                value="true"
                label="Filter Field"
              >
                <template #default>
                  <el-tooltip
                    effect="dark"
                    raw-content
                    content="If you fill in the Filter Field and check the Checkbox, the data will be filtered if the Field is equal to the Filter Field.<br>
                    If you fill in the Filter Field and the Checkbox is not checked, the data will be filtered if the Field is not equal to the Filter Field."
                  >
                    <span class="font-700">Filter Field</span>
                  </el-tooltip>
                  <el-input
                    v-model="queryParams.filterField"
                    class="ml-1"
                    style="width: 180px"
                  />
                </template>
              </el-checkbox>
            </el-form-item>
            <el-form-item label="Create Date">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                style="width: 240px"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >Search
              </el-button>
              <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="mb-1">
        <el-col :span="4">
          <el-button type="warning" plain @click="handleDbCheck"
            >Integrality Check
          </el-button>
          <el-button type="info" plain @click="handleExport">Export</el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="dbId" label="ID" width="250" />
        <el-table-column prop="typeId" label="NO" width="150" />
        <el-table-column prop="type" label="Type" width="150" />
        <el-table-column prop="subjectType" label="Subject Type" width="150" />
        <el-table-column prop="tags" label="Tag" width="200" />
        <el-table-column prop="field" label="Field" width="200" />
        <el-table-column
          prop="createDate"
          label="Create Date"
          min-width="140"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="message" label="Description" min-width="250" />
        <el-table-column prop="creatorEmail" label="Creator" min-width="200" />
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { checkAllDB, checkLogList } from '@/api/dataIntegralityCheck';
  import { getAllNames } from '@/api/standard/expMg';
  import { isStrBlank } from '@/utils';

  let subjectTypes = ref([]);

  onMounted(() => {
    getAllNames().then(res => {
      subjectTypes.value = res.data;
    });
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const { tag } = proxy.useDict('tag');

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      creatorEmail: '',
      type: '',
      subjectType: '',
      tags: '',
      hasFilterField: false,
      filterField: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function resetQuery() {
    dateRange.value = [];
    proxy.$refs['formRef'].resetFields();

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function getDataList() {
    if (
      queryParams.value.hasFilterField &&
      isStrBlank(queryParams.value.filterField)
    ) {
      proxy.$modal.msgError('Please enter the filter field');
      return;
    }

    loading.value = true;
    checkLogList(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function handleDbCheck() {
    proxy.$modal
      .confirm(
        'Are you sure you want to Integrality Check? it may take a long time',
      )
      .then(() => {
        checkAllDB().then(() => {
          proxy.$modal.msgSuccess('Integrality Checking , please wait...');
        });
      });
  }

  function handleExport() {
    proxy.download(
      '/qc/dataIntegralityCheck/export',
      queryParams.value,
      `db_check_log_${new Date().getTime()}.xlsx`,
    );
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-select,
    .el-input {
      width: 330px;
    }
  }

  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
