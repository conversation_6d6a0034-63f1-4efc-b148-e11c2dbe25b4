# Tomcat
server:
  port: 9212

# Spring
spring:
  application:
    # 应用名称
    name: node-task
  profiles:
    # 环境配置
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
      config:
        # 配置中心地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# TOS 配置示例（可选，如果不配置则使用原来的 rsync 逻辑）
# tos:
#   # TOS 开关，true 启用 TOS 上传，false 使用原来的 rsync 逻辑
#   enabled: true
#   # TOS 服务端点
#   endpoint: https://tos-s3-cn-beijing.volces.com
#   # TOS 区域
#   region: cn-beijing
#   # TOS 访问密钥 ID
#   access-key: your-access-key
#   # TOS 访问密钥
#   secret-key: your-secret-key
#   # TOS 存储桶名称
#   bucket-name: your-bucket-name
