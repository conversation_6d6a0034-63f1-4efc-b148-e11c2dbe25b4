package org.biosino.sftp.ftp.download.iterator;

import cn.hutool.core.util.StrUtil;
import org.apache.sshd.common.session.SessionContext;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.common.mongo.entity.Run;
import org.biosino.sftp.authentication.MemberHolder;
import org.biosino.sftp.db.FtpDbService;
import org.biosino.sftp.ftp.download.DownloadFileSystem;
import org.biosino.sftp.ftp.download.path.CommonPath;

import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by yhju on 2018/3/22.
 */
public class RunIterator extends AbsIterator {

    private Iterator<String> iterator;

    public RunIterator(DownloadFileSystem rfs, Path currentPath, FtpDbService ftpDbService, SessionContext session) {
        super(rfs, currentPath, ftpDbService, session);
        this.iterator = listFiles().iterator();
    }

    @Override
    public boolean hasNext() {
        return iterator.hasNext();
    }

    @Override
    public Path next() {
        String next = iterator.next();
        return new CommonPath(this.rfs, this.currentPath.toString(), Arrays.asList(next));
    }

    private Collection<String> listFiles() {

        String security = this.currentPath.getSecurity();
        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);
        String memberId = member.getId();
        if (SecurityEnum._private.getDesc().equalsIgnoreCase(security)) {
            // 查询这个no是否是别人分析给他的私有的
            if (ftpDbService.existShareByRunNo(this.currentPath.getNo(), member.getUsername())) {
                return ftpDbService.getDataByRunNo(this.currentPath.getNo())
                        .stream()
                        .map(d -> d.getDatNo() + "_" + d.getFileName()).collect(Collectors.toList());
            }
            return ftpDbService.getDataByRunNoAndCreator(this.currentPath.getNo(), memberId)
                    .stream()
                    .map(d -> d.getDatNo() + "_" + d.getFileName()).collect(Collectors.toList());
        }
        // if (SecurityEnum._restricted.getDesc().equalsIgnoreCase(security)) {
        //     return restrictedRunFileNames();
        // }
        if (SecurityEnum._public.getDesc().equalsIgnoreCase(security)) {
            return publicRunFileNames();
        }
        return new ArrayList<>(0);
    }

    private Collection<String> restrictedRunFileNames() {

        List<String> files = new ArrayList<>();

        Collection<Data> runDatas = ftpDbService.getDataByRunNo(this.currentPath.getNo());

        Collection<ResourceAuthorize> resourceAuthorizes = ftpDbService.searchAuthorizeToUserDataNo(memberHolder.getId());
        Set<String> authorizeDataNos = resourceAuthorizes.stream().map(ResourceAuthorize::getTypeId).collect(Collectors.toSet());

        Collection<String> shareDataNos = ftpDbService.searchShareToUserDataNo(memberHolder.getUsername(), this.currentPath.getNo());

        runDatas.forEach((data) -> {
            if (shareDataNos.contains(data.getDatNo()) || authorizeDataNos.contains(data.getDatNo())
                    || memberHolder.getId().equals(data.getCreator())) {
                files.add(data.getFileName());
            }
        });

        return files;
    }

    private Collection<String> publicRunFileNames() {
        List<String> files = new ArrayList<>();
        // ID数字部分增多减少升级需要修改的地方，重点是no的length判断
        if ((this.currentPath.getNo().contains("NODER") && this.currentPath.getNo().replaceAll("[A-Z]+", "").length() < 8)
                || (this.currentPath.getNo().contains("OER") && this.currentPath.getNo().replaceAll("[A-Z]+", "").length() < ConfigConstants.ID_LENGTH)) {
            Collection<String> runNos = ftpDbService.searchUserPublicRunNo(this.currentPath.getNo(), 2);
            files.addAll(runNos);
        } else {
            Run run = ftpDbService.getRun(this.currentPath.getNo());
            if (run == null) {
                return files;
            }
            Collection<Data> datas = ftpDbService.getDataByRunNo(this.currentPath.getNo());
            List<Data> notPublicDatas = datas.stream().filter(item -> !SecurityEnum._public.getDesc().equals(item.getSecurity())).collect(Collectors.toList());
            List<String> notPublicDataNos = notPublicDatas.stream().map(Data::getDatNo).collect(Collectors.toList());

            // 查询被授权的数据
            Collection<String> authorizeDataNos = ftpDbService.searchAuthorizeToUserDataNos(memberHolder.getId(), notPublicDataNos);
            // 查询被分享的数据
            Collection<String> sharedDataNos = ftpDbService.searchShareToUserDataNos(memberHolder.getUsername(), notPublicDataNos);
            // 遍历datas，public的直接加入，private 或者 Restricted 的判断是否有权限
            for (Data data : datas) {
                String security = data.getSecurity();
                if (security.equals(SecurityEnum._public.getDesc())) {
                    // 2024/12/17增加，因为文件名前面加了DataNo
                    // files.add(data.getFileName());
                    files.add(data.getDatNo() + "_" + data.getFileName());
                } else if (StrUtil.equals(data.getCreator(), memberHolder.getId())) {
                    // 2024/12/17增加，因为文件名前面加了DataNo
                    // files.add(data.getFileName());
                    files.add(data.getDatNo() + "_" + data.getFileName());
                } else if (authorizeDataNos.contains(data.getDatNo()) || sharedDataNos.contains(data.getDatNo())) {
                    // 2024/12/17增加，因为文件名前面加了DataNo
                    // files.add(data.getFileName());
                    files.add(data.getDatNo() + "_" + data.getFileName());
                }
            }

        }
        return files;
    }

}
