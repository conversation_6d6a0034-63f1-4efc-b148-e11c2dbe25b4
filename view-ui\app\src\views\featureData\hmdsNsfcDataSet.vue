<template>
  <div>
    <h3 class="text-main-color mt-05 mb-0">Hydrosphere</h3>
    <el-divider class="mt-05 mb-1"></el-divider>
    <el-row class="row-gap-15 mt-1" :gutter="20">
      <div
        v-for="(it, index) in statInfo"
        :key="`fd_hr_${index}`"
        class="d-flex"
        :span="4"
        :xs="24"
        :md="4"
        :class="activeCardName === it.name ? 'active' : ''"
        @click="clickCard(it)"
      >
        <div style="margin-left: 15px">
          <div class="item" style="width: 240px">
            <div class="ml-05">
              <div style="width: 185px" class="text-main-color it-name">
                {{ it.name }}
              </div>
              <div>
                <span class="text-warning">{{
                  (it.size / Math.pow(10, 12)).toFixed(2)
                }}</span>
                <span class="tb text-warning">Tbase</span>
                <span class="text-primary number">{{
                  formatNumber(it.sapNumber)
                }}</span>
                <span class="text-primary sample">Samples</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-row>
    <div class="card mt-1">
      <div class="text-secondary-color font-18 font-600 mb-05 mt-1">
        Accessible Sample List
      </div>
      <el-table
        v-loading="loading"
        tooltip-effect="dark"
        :data="tableData"
        :header-cell-style="{
          backgroundColor: '#F8F8F8',
          color: '#333333',
          fontWeight: 700,
        }"
        :max-height="500"
        border
        @sort-change="handleSortChange"
      >
        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="sampID"
          label="Sample ID"
          width="120"
        >
          <template #default="scope">
            <router-link
              :to="`/sample/detail/${scope.row.sampID}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row.sampID }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="sampName"
          label="Sample Name"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="des"
          label="Description"
          min-width="80"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ isStrBlank(scope.row.des) ? 'Not provided' : scope.row.des }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="sampType"
          label="Sample Type"
          min-width="90"
          show-overflow-tooltip
        />
        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="organism"
          label="Organism"
          min-width="100"
          show-overflow-tooltip
        />
        <!--        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="attributes.env_biome"
          label="Env Biome"
          min-width="140"
          show-overflow-tooltip
        />-->
        <el-table-column
          sortable
          :sort-orders="['ascending', 'descending']"
          prop="attributes.biome_curated"
          label="Curated Biome"
          min-width="200"
          show-overflow-tooltip
        />
      </el-table>
      <div style="margin-top: 8px">
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          class="mb-1"
          @pagination="
            () => {
              getDataList();
            }
          "
        />
      </div>
    </div>
  </div>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { hmdsSapList, hmdsStatDetail } from '@/api/system/featureData';
  import { isStrBlank } from '@/utils';
  import { formatNumber } from '@/utils/nodeCommon';

  let { proxy } = getCurrentInstance();
  const data = reactive({
    statInfo: [],
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
      orderByColumn: '',
      isAsc: '',
    },
    loading: false,
  });
  const activeCardName = ref('');

  let { tableData, total, queryParams, loading, defaultSort, statInfo } =
    toRefs(data);

  function clickCard(it) {
    activeCardName.value = it.name;
    queryParams.value.name = it.name;
    getDataList();
  }

  function getDataList() {
    loading.value = true;
    hmdsSapList(queryParams.value)
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function handleSortChange(column) {
    let propMap = {
      sampID: 'sap_no',
      sampName: 'name',
      des: 'description',
      sampType: 'subject_type',
      organism: 'organism',
      tissue: 'tissue',
    };
    if (column.order) {
      queryParams.value.orderByColumn = propMap[column.prop] || column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  onMounted(() => {
    hmdsStatDetail().then(response => {
      statInfo.value = response.data;
      if (isStrBlank(activeCardName.value)) {
        activeCardName.value = response.data[0].name;
      }
      getDataList();
    });
  });
</script>

<style scoped lang="scss">
  .active .item {
    border-color: #ebf2fd;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }

  .item {
    display: flex;
    //min-width: 350px;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s linear;

    &.active {
      border-color: #3a78e8;
    }

    .it-name {
      color: #505050;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 0.2rem;
    }

    &:hover {
      cursor: pointer;
      transform: translateY(-4px);
    }
  }

  .tb {
    font-size: 14px;
    margin-left: 0.3rem;
  }

  .number {
    margin-left: 1rem;
  }

  .sample {
    font-size: 14px;
    margin-left: 0.5rem;
  }

  .font-13 {
    font-size: 13px;
  }
</style>
