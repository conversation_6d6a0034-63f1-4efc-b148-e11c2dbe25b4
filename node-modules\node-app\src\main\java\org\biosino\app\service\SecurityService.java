package org.biosino.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.biosino.app.api.dto.UpdateSecurityDTO;
import org.biosino.app.mq.index.IndexUpdateEvent;
import org.biosino.app.repository.*;
import org.biosino.app.vo.DataVO;
import org.biosino.app.vo.SecurityVO;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.ThreadPoolUtil;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.security.enums.ExperimentTypeEnum;
import org.biosino.common.security.enums.SampleTypeEnum;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.DataSearchDTO;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.dto.SecurityDTO;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityService extends BaseService {

    private final RunRepository runRepository;
    private final DataRepository dataRepository;
    private final SampleRepository sampleRepository;
    private final ProjectRepository projectRepository;
    private final AnalysisRepository analysisRepository;
    private final ApplicationContext applicationContext;
    private final ExperimentRepository experimentRepository;
    private final SecurityUpdateLogRepository securityUpdateLogRepository;
    private final RemoteDataService remoteDataService;

    private final HashSet<String> expTypeSet = CollUtil.newHashSet(ExperimentTypeEnum.Genomic.getDesc(), ExperimentTypeEnum.Transcriptomic.getDesc(), ExperimentTypeEnum.GenomicSingleCell.getDesc(), ExperimentTypeEnum.TranscriptomicSingleCell.getDesc());
    private final HashSet<String> sapTypeSet = CollUtil.newHashSet(SampleTypeEnum.Human.getDesc(), SampleTypeEnum.CellLine.getDesc(), SampleTypeEnum.EnvironmentHost.getDesc());


    /**
     * 字段自动补全检索Data
     */
    public Set<String> searchSelectData(DataShareSearchVO queryVO) {
        R<Set<String>> selectData = remoteDataService.findSelectData(queryVO, SecurityConstants.INNER);
        return selectData.getData();
    }

    public SecurityVO getDataList(DataShareSearchVO search) {
        final String memberId = SecurityUtils.getMemberId();
        if (memberId == null && SecurityUtils.getUserId() == null) {
            throw new NotPermissionException("Not permission");
        }

        String typeNo = search.getTypeNo();

        search.setCreator(memberId);

        R<EsPageInfo<RelatedDataDTO>> dataShareData = remoteDataService.findDataShareData(search, SecurityConstants.INNER);
        EsPageInfo<RelatedDataDTO> esDataList = dataShareData.getData();
        List<RelatedDataDTO> dataList = esDataList.getList();

        SecurityVO result = new SecurityVO();
        result.setDataVos(dataList);
        result.setTotal(esDataList.getTotal());

        // 处理Related Analysis数据
        List<Analysis> analysisByTarget = analysisRepository.getByTargetAndCreator(typeNo, memberId);

        if (CollUtil.isNotEmpty(analysisByTarget)) {

            Map<String, String> analysisMap = analysisByTarget.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Analysis::getName, (existingValue, newValue) -> existingValue));

            List<String> analysisIds = analysisByTarget.stream().map(Analysis::getAnalysisNo).collect(Collectors.toList());

            List<Data> analysisData = dataRepository.findByAnalNos(analysisIds).stream()
                    .filter(data -> data.getSecurity().equals(search.getSecurity()))
                    .collect(Collectors.toList());

            List<DataVO> relatedAnalysisData = new ArrayList<>();

            if (CollUtil.isNotEmpty(analysisData)) {
                for (Data data : analysisData) {
                    DataVO dataVO = new DataVO();
                    dataVO.setName(data.getName());
                    dataVO.setAnalNo(data.getAnalNo());
                    dataVO.setAnalName(analysisMap.get(data.getAnalNo()));
                    dataVO.setDatNo(data.getDatNo());
                    dataVO.setDataType(FileTypeUtils.getDataTypeByName(data.getName()));
                    dataVO.setUploadTime(data.getCreateDate());
                    relatedAnalysisData.add(dataVO);
                }
            }

            result.setRelatedAnalysisDataVos(relatedAnalysisData);
        }

        return result;
    }

    public Set<String> verifyDataHumanType(UpdateSecurityDTO dto) {

        List<String> dataNos = dto.getDataNos();

        if (CollUtil.isEmpty(dataNos)) {
            throw new ServiceException("No data selected");
        }

        String targetSecurity = dto.getSecurityType();

        if (!targetSecurity.equals(SecurityEnum._public.getDesc())) {
            return null;
        }

        DataSearchDTO searchDTO = new DataSearchDTO();
        searchDTO.setDataNos(dataNos);

        R<List<RelatedDataDTO>> relatedDataR = remoteDataService.findAllByDataNos(searchDTO, SecurityConstants.INNER);

        List<RelatedDataDTO> data = relatedDataR.getData();

        Set<String> hasSapType = new HashSet<>();
        for (RelatedDataDTO datum : data) {
            if (expTypeSet.contains(datum.getExpType()) && sapTypeSet.contains(datum.getSapType())) {
                hasSapType.add(datum.getSapType());
            }
        }

        return hasSapType;
    }

    public List<String> updateDataSecurity(UpdateSecurityDTO dto) {
        // 前台调用接口需要身份认证
        if (dto == null || (!dto.getAdmin() && SecurityUtils.getMemberId() == null)) {
            throw new NotPermissionException("Not permission");
        }

        List<String> errMsg = new ArrayList<>();

        List<String> dataNos = dto.getDataNos();

        if (CollUtil.isEmpty(dataNos)) {
            throw new ServiceException("No data selected");
        }

        String targetSecurity = dto.getSecurityType();
        String pubDateStr = dto.getPubDate();

        Date pubDate;

        if (targetSecurity.equals(SecurityEnum._restricted.getDesc()) && StrUtil.isNotBlank(pubDateStr)) {
            pubDate = DateUtils.parseDate(pubDateStr);
            if (pubDate == null) {
                errMsg.add("Until Date format error");
                return errMsg;
            }
        } else {
            pubDate = null;
        }

        List<String> analysisNos = new ArrayList<>();
        List<String> runNos = new ArrayList<>();

        Set<String> runNosSet = new HashSet<>();
        Set<String> sapNosSet = new HashSet<>();
        Set<String> expNosSet = new HashSet<>();
        Set<String> projNosSet = new HashSet<>();

        String targetVisible;
        if (SecurityEnum._private.getDesc().equals(targetSecurity)) {
            targetVisible = VisibleStatusEnum.Unaccessible.name();
        } else {
            targetVisible = VisibleStatusEnum.Accessible.name();
        }

        List<Data> updateData = dataRepository.findByDataNos(dataNos);
        if (CollUtil.isEmpty(updateData)) {
            throw new ServiceException("No data found");
        }

        for (Data data : updateData) {
            String datNo = data.getDatNo();
            if (data.getComplete() != null && !data.getComplete()) {
                errMsg.add("Modifying " + datNo + " failed! Data integrity verification failed, please <NAME_EMAIL>");
                continue;
            }

            Data tempData = data.getTempData();
            if (tempData != null) {
                String subNo = tempData.getSubNo();
                if (StrUtil.isNotBlank(subNo)) {
                    errMsg.add("Modifying " + datNo + " failed! Because the data is occupied in Submission: " + subNo);
                }
            }

            if (StrUtil.isNotBlank(data.getAnalNo())) {
                analysisNos.add(data.getAnalNo());
            }
            if (StrUtil.isNotBlank(data.getRunNo())) {
                runNos.add(data.getRunNo());
            }
        }
        if (CollUtil.isNotEmpty(runNos)) {
            List<Run> runs = runRepository.findAllByNos(runNos.stream().distinct().collect(Collectors.toList()));
            sapNosSet = runs.stream().map(Run::getSapNo).collect(Collectors.toSet());

            expNosSet = runs.stream().map(Run::getExpNo).collect(Collectors.toSet());

            List<Experiment> experimentByExpNos = experimentRepository.findAllByExpNoIn(expNosSet);
            projNosSet = experimentByExpNos.stream().map(Experiment::getProjectNo).collect(Collectors.toSet());

            if (CollUtil.isNotEmpty(projNosSet)) {
                List<Project> projectList = projectRepository.findAllByProjNoIn(projNosSet);
                for (Project project : projectList) {
                    if (project.getTempData() != null) {
                        errMsg.add("Modifying " + project.getProjectNo() + " failed! Because the Project is occupied in Submission: " + project.getTempData().getSubNo());
                    }
                }
            }

            if (CollUtil.isNotEmpty(experimentByExpNos)) {
                for (Experiment experiment : experimentByExpNos) {
                    if (experiment.getTempData() != null) {
                        errMsg.add("Modifying " + experiment.getExpNo() + " failed! Because the Experiment is occupied in Submission: " + experiment.getTempData().getSubNo());
                    }
                }
            }

            if (CollUtil.isNotEmpty(sapNosSet)) {
                List<Sample> sampleList = sampleRepository.findAllBySapNoIn(sapNosSet);
                for (Sample sample : sampleList) {
                    if (sample.getTempData() != null) {
                        errMsg.add("Modifying " + sample.getSapNo() + " failed! Because the Sample is occupied in Submission: " + sample.getTempData().getSubNo());
                    }
                }
            }
        }
        List<Analysis> analysisList = new ArrayList<>();
        if (CollUtil.isNotEmpty(analysisNos)) {
            analysisList = analysisRepository.findAllByNos(analysisNos);
        }

        // 先把analysis数据提取出来校验，校验target相关的数据通过后才能进行数据库操作
        if (CollUtil.isNotEmpty(analysisList)) {
            for (Analysis anal : analysisList) {
                String analysisNo = anal.getAnalysisNo();

                if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible)) {
                    continue;
                }
                List<AnalysisTarget> target = anal.getTarget();
                if (CollUtil.isEmpty(target)) {
                    continue;
                }

                if (anal.getTempData() != null) {
                    errMsg.add("Modifying " + analysisNo + " failed! Because the Analysis is occupied in Submission: " + anal.getTempData().getSubNo());
                }

                for (AnalysisTarget analysisTarget : target) {
                    String type = analysisTarget.getType();
                    List<String> nos = analysisTarget.getNos();
                    for (String no : nos) {

                        // 如果当前analysis target关联的数据未公开，则不允许修改
                        if (AuthorizeType.project.name().equals(type)) {
                            Project proj = projectRepository.findByNo(no);
                            if (VisibleStatusEnum.Unaccessible.name().equals(proj.getVisibleStatus())) {
                                projNosSet.addAll(analysisTarget.getNos());
                                errMsg.add("Modifying " + analysisNo + " failed! Because the target of the analysis: " + proj.getProjectNo() + " is not publicly available");
                            }
                        }

                        if (AuthorizeType.experiment.name().equals(type)) {
                            Experiment experiment = experimentRepository.findByNo(no);
                            if (VisibleStatusEnum.Unaccessible.name().equals(experiment.getVisibleStatus())) {
                                projNosSet.addAll(analysisTarget.getNos());
                                errMsg.add("Modifying " + analysisNo + " failed! Because the target of the analysis: " + experiment.getExpNo() + " is not publicly available");
                            }
                        }

                        if (AuthorizeType.sample.name().equals(type)) {
                            Sample sample = sampleRepository.findSampleBySampleNo(no);
                            if (VisibleStatusEnum.Unaccessible.name().equals(sample.getVisibleStatus())) {
                                projNosSet.addAll(analysisTarget.getNos());
                                errMsg.add("Modifying " + analysisNo + " failed! Because the target of the analysis: " + sample.getSapNo() + " is not publicly available");
                            }
                        }

                        if (AuthorizeType.run.name().equals(type)) {
                            Run run = runRepository.findByNo(no);
                            if (VisibleStatusEnum.Unaccessible.name().equals(run.getVisibleStatus())) {
                                projNosSet.addAll(analysisTarget.getNos());
                                errMsg.add("Modifying " + analysisNo + " failed! Because the target of the analysis: " + run.getRunNo() + " is not publicly available");
                            }
                        }

                        if (AuthorizeType.analysis.name().equals(type)) {
                            Analysis analysis = analysisRepository.findByNo(no);
                            if (VisibleStatusEnum.Unaccessible.name().equals(analysis.getVisibleStatus())) {
                                projNosSet.addAll(analysisTarget.getNos());
                                errMsg.add("Modifying " + analysisNo + " failed! Because the target of the analysis: " + analysis.getAnalysisNo() + " is not publicly available");
                            }
                        }
                    }
                }
            }
        }

        // 有错误，返回前端
        if (CollUtil.isNotEmpty(errMsg)) {
            return errMsg;
        }

        // 填充人类遗传备案号信息
        final HashSet<String> humanDataSet = new HashSet<>();
        String recordRadio = dto.getRecordRadio();
        String recordValue = dto.getRecordValue();

        if (SecurityEnum._public.getDesc().equals(targetSecurity) && recordRadio != null) {
            DataSearchDTO searchDTO = new DataSearchDTO();
            searchDTO.setDataNos(dataNos);
            R<List<RelatedDataDTO>> relatedDataR = remoteDataService.findAllByDataNos(searchDTO, SecurityConstants.INNER);

            List<RelatedDataDTO> relatedDataList = relatedDataR.getData();

            for (RelatedDataDTO datum : relatedDataList) {
                if (expTypeSet.contains(datum.getExpType()) && sapTypeSet.contains(datum.getSapType())) {
                    humanDataSet.add(datum.getDatNo());
                }
            }
        }
        try {
            ExecutorService executor = ThreadPoolUtil.executor();

            final CountDownLatch dataCdl = new CountDownLatch(updateData.size());
            for (Data data : updateData) {
                executor.submit(() -> {
                    saveSecurityUpdateLog(AuthorizeType.data.name(), data.getDatNo(), data.getSecurity(), data.getPublicDate(), targetSecurity, pubDate);
                    securityUpdateLogRepository.updateSecurity("dat_no", Data.class, data.getDatNo(), targetSecurity, pubDate);

                    // 更新人类遗传资源备案编号
                    if (CollUtil.isNotEmpty(humanDataSet) && humanDataSet.contains(data.getDatNo())) {
                        dataRepository.updateDataHumanRecordNo(data.getId(), recordRadio, recordValue);
                    }
                    dataCdl.countDown();
                });
            }

            if (CollUtil.isNotEmpty(runNos)) {
                runNosSet = new HashSet<>(runNos);

                List<Run> runs = runRepository.findAllByNos(runNosSet);
                sapNosSet = runs.stream().map(Run::getSapNo).collect(Collectors.toSet());

                expNosSet = runs.stream().map(Run::getExpNo).collect(Collectors.toSet());

                List<Experiment> experimentByExpNos = experimentRepository.findAllByExpNoIn(expNosSet);
                projNosSet = experimentByExpNos.stream().map(Experiment::getProjectNo).collect(Collectors.toSet());
            }

            final CountDownLatch runCdl = new CountDownLatch(runNosSet.size());
            if (CollUtil.isNotEmpty(runNosSet)) {
                for (String runNo : runNosSet) {
                    if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible) && existPublicData("runNo", runNo, dataNos)) {
                        runCdl.countDown();
                        continue;
                    }
                    executor.submit(() -> {
                        Run run = runRepository.findByNo(runNo);
                        if (run != null) {
                            saveSecurityUpdateLog(AuthorizeType.run.name(), run.getRunNo(), run.getVisibleStatus(), run.getPublicDate(), targetVisible, pubDate);
                            securityUpdateLogRepository.updateSecurity("run_no", Run.class, runNo, targetVisible, pubDate);
                        }
                        runCdl.countDown();
                    });
                }
            }

            final CountDownLatch sapCdl = new CountDownLatch(sapNosSet.size());
            if (CollectionUtils.isNotEmpty(sapNosSet)) {
                for (String sapNo : sapNosSet) {
                    if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible) && existPublicData("sapNo", sapNo, dataNos)) {
                        sapCdl.countDown();
                        continue;
                    }
                    executor.submit(() -> {
                        Sample sample = sampleRepository.findSampleBySampleNo(sapNo);
                        if (sample != null) {
                            saveSecurityUpdateLog(AuthorizeType.sample.name(), sample.getSapNo(), sample.getVisibleStatus(), sample.getPublicDate(), targetVisible, pubDate);
                            securityUpdateLogRepository.updateSecurity("sap_no", Sample.class, sapNo, targetVisible, pubDate);
                        }
                        sapCdl.countDown();
                    });
                }
            }

            final CountDownLatch expCdl = new CountDownLatch(expNosSet.size());
            if (CollectionUtils.isNotEmpty(expNosSet)) {
                for (String expNo : expNosSet) {
                    if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible) && existPublicData("expNo", expNo, dataNos)) {
                        expCdl.countDown();
                        continue;
                    }
                    executor.submit(() -> {
                        Experiment exp = experimentRepository.findByNo(expNo);
                        if (exp != null) {
                            saveSecurityUpdateLog(AuthorizeType.experiment.name(), exp.getExpNo(), exp.getVisibleStatus(), exp.getPublicDate(), targetVisible, pubDate);
                            securityUpdateLogRepository.updateSecurity("exp_no", Experiment.class, expNo, targetVisible, pubDate);
                        }
                        expCdl.countDown();
                    });
                }
            }

            // Analysis通过校验后才能更新
            final CountDownLatch analCdl = new CountDownLatch(analysisList.size());
            if (CollUtil.isNotEmpty(analysisList)) {
                for (Analysis anal : analysisList) {
                    if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible) && existPublicData("analNo", anal.getAnalysisNo(), dataNos)) {
                        analCdl.countDown();
                        continue;
                    }
                    executor.submit(() -> {
                        // 记录状态转换日志
                        saveSecurityUpdateLog(AuthorizeType.analysis.name(), anal.getAnalysisNo(), anal.getVisibleStatus(), anal.getPublicDate(), targetVisible, pubDate);
                        // 修改数据库状态数据
                        securityUpdateLogRepository.updateSecurity("anal_no", Analysis.class, anal.getAnalysisNo(), targetVisible, pubDate);
                        // 更新analysis索引
                        applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.analysis, anal.getAnalysisNo()));
                        analCdl.countDown();
                    });
                }
            }

            dataCdl.await(30, TimeUnit.MINUTES);
            runCdl.await(30, TimeUnit.MINUTES);
            sapCdl.await(30, TimeUnit.MINUTES);
            expCdl.await(30, TimeUnit.MINUTES);
            analCdl.await(30, TimeUnit.MINUTES);

            if (CollectionUtils.isNotEmpty(projNosSet)) {
                for (String projNo : projNosSet) {
                    if (VisibleStatusEnum.Unaccessible.name().equals(targetVisible) && existPublicData("projNo", projNo, dataNos)) {
                        continue;
                    }
                    Project proj = projectRepository.findByNo(projNo);

                    saveSecurityUpdateLog(AuthorizeType.project.name(), proj.getProjectNo(), proj.getVisibleStatus(), proj.getPublicDate(), targetVisible, pubDate);
                    securityUpdateLogRepository.updateSecurity("proj_no", Project.class, projNo, targetVisible, pubDate);
                }
                // 更新项目索引
                applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.project, projNosSet));
            }

            // 更新Data索引
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, dataNos));
        } catch (InterruptedException e) {
            log.error("修改数据:{}安全等级报错：{}", CollUtil.join(dataNos, ";"), e.getMessage());
        }
        return null;
    }

    private void saveSecurityUpdateLog(String resourceType, String resourceId, String sourceSecurity, Date sourceValidDate, String targetSecurity, Date targetValidDate) {
        final String memberId = SecurityUtils.getMemberId();
        final String ip = IpUtils.getIpAddr();

        SecurityUpdateLog securityUpdateLog = new SecurityUpdateLog();
        securityUpdateLog.setResouceId(resourceId);
        securityUpdateLog.setResourceType(resourceType);
        securityUpdateLog.setOperator(memberId);
        securityUpdateLog.setIp(ip);
        securityUpdateLog.setOperateTime(new Date());
        securityUpdateLog.setSourceSecurity(sourceSecurity);
        securityUpdateLog.setSourceValidDate(sourceValidDate);

        securityUpdateLog.setTargetSecurity(targetSecurity);
        securityUpdateLog.setTargetValidDate(targetValidDate);
        securityUpdateLogRepository.save(securityUpdateLog);
    }

    private Boolean existPublicData(String filed, String typeNo, Collection<String> dataNos) {
        R<Boolean> existPublicByDataNos = remoteDataService.existPublicByDataNos(new SecurityDTO(filed, typeNo, dataNos), SecurityConstants.INNER);
        if (R.isSuccess(existPublicByDataNos)) {
            return existPublicByDataNos.getData();
        }
        return false;
    }

    public List<String> verifyMetadataEdit(DataShareSearchVO searchDTO) {
        searchDTO.noLimitPageSize(5000);
        R<EsPageInfo<RelatedDataDTO>> dataShareData = remoteDataService.findDataShareData(searchDTO, SecurityConstants.INNER);
        EsPageInfo<RelatedDataDTO> esDataList = dataShareData.getData();
        List<RelatedDataDTO> dataList = esDataList.getList();
        List<String> projNos = dataList.stream().map(RelatedDataDTO::getProjNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> expNos = dataList.stream().map(RelatedDataDTO::getExpNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> sapNos = dataList.stream().map(RelatedDataDTO::getSapNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> analNos = dataList.stream().map(RelatedDataDTO::getAnalNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<String> errMsgs = new ArrayList<>();

        List<Project> projectList = projectRepository.findHasTempDataByProjNoIn(projNos);
        if (CollUtil.isNotEmpty(projectList)) {
            for (Project project : projectList) {
                if (project.getTempData() != null) {
                    errMsgs.add(project.getProjectNo() + " is in use by " + project.getTempData().getSubNo());
                }
            }
        }

        List<Experiment> experimentList = experimentRepository.findHasTempDataByExpNoIn(expNos);
        if (CollUtil.isNotEmpty(experimentList)) {
            for (Experiment experiment : experimentList) {
                if (experiment.getTempData() != null) {
                    errMsgs.add(experiment.getExpNo() + " is in use by " + experiment.getTempData().getSubNo());
                }
            }
        }

        List<Sample> sampleList = sampleRepository.findHasTempDataBySapNoIn(sapNos);
        if (CollUtil.isNotEmpty(sampleList)) {
            for (Sample sample : sampleList) {
                if (sample.getTempData() != null) {
                    errMsgs.add(sample.getSapNo() + " is in use by " + sample.getTempData().getSubNo());
                }
            }
        }

        List<Analysis> analysisList = analysisRepository.findHasTempDataByAnalNoIn(analNos);
        if (CollUtil.isNotEmpty(analysisList)) {
            for (Analysis analysis : analysisList) {
                if (analysis.getTempData() != null) {
                    errMsgs.add(analysis.getAnalysisNo() + " is in use by " + analysis.getTempData().getSubNo());
                }
            }
        }


        return errMsgs;
    }
}
