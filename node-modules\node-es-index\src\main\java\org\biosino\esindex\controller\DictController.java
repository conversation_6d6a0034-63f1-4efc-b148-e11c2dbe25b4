package org.biosino.esindex.controller;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.Select;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.biosino.esindex.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ES字典接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class DictController {

    @Autowired
    private DictService dictService;

    /**
     * 根据用户输入的关键字 模糊匹配 Platform
     *
     * @param keyword 检索关键字
     */
    @GetMapping("/findPlatformLike")
    public R<List<Select>> findPlatformLike(@RequestParam(required = false) String keyword) {
        try {
            List<Select> select = dictService.findPlatform(keyword);
            return R.ok(select);
        } catch (Exception e) {
            log.error("查询Platform失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询Platform是否存在
     */
    @GetMapping("/existPlatform")
    public R<Boolean> existPlatform(@RequestParam(required = false) String name) {
        try {
            return R.ok(dictService.existPlatform(name));
        } catch (Exception e) {
            log.error("查询Platform失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据用户输入的关键字 模糊匹配 Taxonomy
     *
     * @param keyword 检索关键字
     */
    @GetMapping("/findTaxonomyLike")
    public R<List<Select>> findTaxonomyLike(@RequestParam(required = false) String keyword) {
        try {
            List<Select> select = dictService.findTaxonomy(keyword);
            return R.ok(select);
        } catch (Exception e) {
            log.error("查询Taxonomy失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询Taxonomy是否存在
     *
     * @param name organism
     */
    /*@GetMapping("/findTaxIdByExistName")
    public R<String> findTaxIdByExistName(@RequestParam(required = false) String name) {
        try {
            return R.ok(dictService.findTaxIdByExistName(name));
        } catch (Exception e) {
            log.error("查询Taxonomy失败", e);
            return R.fail(e.getMessage());
        }
    }*/

    /**
     * 根据Taxonomy name查询tax Id
     *
     * @param name 检索关键字
     */
    @GetMapping("/getTaxIdByName")
    public R<List<String>> getTaxIdByName(@RequestParam(required = false) String name) {
        try {
            return R.ok(dictService.getTaxIdByName(name));
        } catch (Exception e) {
            log.error("查询Taxonomy失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据用户输入的关键字 模糊匹配 Disease
     *
     * @param keyword 检索关键字
     */
    @GetMapping("/findDiseaseLike")
    public R<List<Select>> findDiseaseLike(@RequestParam(required = false) String keyword) {
        try {
            List<Select> select = dictService.findDisease(keyword);
            return R.ok(select);
        } catch (Exception e) {
            log.error("查询Disease失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据用户输入的关键字 模糊匹配 Biome
     *
     * @param type    biome的3个类型之一
     * @param keyword 检索关键字
     */
    @GetMapping("/findBiomeLike")
    public R<List<Select>> findBiomeLike(@RequestParam String type, @RequestParam(required = false) String keyword) {
        try {
            List<Select> select = dictService.findBiome(type, keyword);
            return R.ok(select);
        } catch (Exception e) {
            log.error("查询Biome失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询Disease是否存在
     */
    @GetMapping("/existDisease")
    public R<Boolean> existDisease(@RequestParam(required = false) String name) {
        try {
            return R.ok(dictService.existDisease(name));
        } catch (Exception e) {
            log.error("查询Disease失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询Taxonomy
     */
    @GetMapping("/getTaxByTaxId")
    public R<List<TaxonomyNodeDTO>> getTaxByTaxId(@RequestParam String taxId) {
        try {
            return R.ok(dictService.getTaxByTaxId(taxId));
        } catch (Exception e) {
            log.error("查询Taxonomy失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询biome是否存在
     */
    @GetMapping("/existBiome")
    public R<Boolean> existBiomeByTypeAndValue(@RequestParam String type, @RequestParam String value) {
        try {
            return R.ok(dictService.existBiomeByTypeAndValue(type, value));
        } catch (Exception e) {
            log.error("查询Biome失败", e);
            return R.fail(e.getMessage());
        }
    }
}
