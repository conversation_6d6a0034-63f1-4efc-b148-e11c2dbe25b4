package org.biosino.esindex.repository.node.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.BiomeCurated;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.esindex.repository.node.SampleCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/4
 */
@RequiredArgsConstructor
public class SampleCustomRepositoryImpl implements SampleCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Map<String, List<String>> getBiomeTypeToSapNosMap() {
        // 先查询分组信息
        List<BiomeCurated> biomeCurateds = mongoTemplate.find(new Query(), BiomeCurated.class);

        Map<String, List<String>> typeToBiomeCurated = biomeCurateds.stream()
                .sorted(Comparator.comparingInt(BiomeCurated::getOrderNum))
                .collect(Collectors.groupingBy(
                        BiomeCurated::getDataTypeCurated,// 分组依据
                        LinkedHashMap::new,
                        Collectors.mapping(BiomeCurated::getBiomeCurated, Collectors.toList()) // 将 biomeCurated 字段收集到列表中
                ));
        LinkedHashMap<String, List<String>> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : typeToBiomeCurated.entrySet()) {
            String type = entry.getKey();
            List<String> biomeCurated = entry.getValue();
            if (StrUtil.equals(type, "other")) {
                ArrayList<Criteria> conditionList = new ArrayList<>();
                conditionList.add(new Criteria().orOperator(
                        Criteria.where("attributes.biome_curated").in(biomeCurated),
                        Criteria.where("attributes.biome_curated").exists(false),
                        Criteria.where("attributes.biome_curated").is(null)
                ));
                conditionList.add(Criteria.where("source_project").in("HMDS"));
                Query query = new Query(new Criteria().andOperator(conditionList));
                List<String> sapNos = mongoTemplate.findDistinct(query, "sap_no", Sample.class, String.class);
                result.put(type, sapNos);
            } else {
                ArrayList<Criteria> conditionList = new ArrayList<>();
                conditionList.add(Criteria.where("attributes.biome_curated").in(biomeCurated));
                conditionList.add(Criteria.where("source_project").in("HMDS"));
                Query query = new Query(new Criteria().andOperator(conditionList));
                List<String> sapNos = mongoTemplate.findDistinct(query, "sap_no", Sample.class, String.class);
                result.put(type, sapNos);
            }
        }
        return result;
    }
}
