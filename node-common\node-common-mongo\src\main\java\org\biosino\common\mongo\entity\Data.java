package org.biosino.common.mongo.entity;

import org.biosino.common.core.enums.ArchiveEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.authorize.IJudgeAuthorize;
import org.biosino.common.mongo.authorize.Judge;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@lombok.Data
@Document
public class Data implements IJudgeAuthorize {
    @Id
    private String id;

    @NotBlank(message = "dat_no不能为空")
    @Field("dat_no")
    @GenerateValue(prefix = SequenceType.DATA)
    private String datNo;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    private String subNo;

    @Field("run_no")
    private String runNo;

    @Field("anal_no")
    private String analNo;

    @NotBlank(message = "name不能为空")
    private String name;

    @Field("data_type")
    private String dataType;

    @Field("upload_type")
    private String uploadType;

    private String remark;

    @ValidEnum(enumClass = ArchiveEnum.class, allowNull = false, message = "archived不在合法范围内")
    private String archived;

    private String text;

    @NotBlank(message = "file_name不能为空")
    @Field("file_name")
    private String fileName;

    @Field("file_path")
    private String filePath;

    @Field("ftp_path")
    private String ftpPath;

    @Field("source_path")
    private String sourcePath;

    @NotNull(message = "file_size不能为空")
    @Field("file_size")
    private Long fileSize;

    private Long bases;
    private String ip;

    private String express;
    @Field("tracking_num")
    private String trackingNum;

    @NotBlank(message = "creator不能为空")
    private String creator;

    @NotNull(message = "submission_date不能为空")
    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    private String security;

    @Field("public_date")
    private Date publicDate;

    private String operator;

    @Field("operation_date")
    private Date operationDate;

    /**
     * 用户选择的人类遗传备案号选项
     */
    @Field("human_record_option")
    private String humanRecordOption;

    /**
     * 人类遗传资源备案编号
     */
    @Field("human_record_no")
    private String humanRecordNo;

    private String status;

    private Date uploadTime;

    private Date startTime;

    @Field("used_ids")
    private List<String> usedIds;

    private String ownership;

    private String md5;

    @Field("cal_date")
    private Date calcDate;

    @Field("temp_data")
    private Data tempData;

    @Field("complete")
    private Boolean complete = true;

    public String getObjType() {
        return "data";
    }

    public Judge getJudge() {
        Judge judge = new Judge();
        judge.setSecurity(getSecurity());
        judge.setObjNo(getDatNo());
        judge.setCreator(getCreator());
        judge.setPubDate(getPublicDate());
        return judge;
    }
}
