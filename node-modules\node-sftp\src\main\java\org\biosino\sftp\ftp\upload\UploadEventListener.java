package org.biosino.sftp.ftp.upload;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.sshd.server.session.ServerSession;
import org.apache.sshd.sftp.server.AbstractSftpEventListenerAdapter;
import org.apache.sshd.sftp.server.FileHandle;
import org.apache.sshd.sftp.server.Handle;
import org.biosino.common.core.enums.FtpFileLogStatus;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.sftp.authentication.MemberHolder;
import org.biosino.sftp.db.FtpDbService;
import org.biosino.system.api.domain.sftp.FtpFileLog;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.PosixFilePermissions;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collection;
import java.util.Date;

/**
 * <AUTHOR>
 * <p>
 * SFTP 的日志监听类
 * <p>
 * 注意：
 * 无论 window 还是 linux，都采用 / 作为分隔符存入数据库
 */
@Slf4j
public class UploadEventListener extends AbstractSftpEventListenerAdapter {

    private final FtpDbService ftpDbService;

    public UploadEventListener(FtpDbService ftpDbService) throws NoSuchAlgorithmException {
        super();
        this.ftpDbService = ftpDbService;
    }

    @Override
    public void removed(ServerSession session, Path path, boolean isDirectory, Throwable thrown)
            throws IOException {

        super.removed(session, path, isDirectory, thrown);

        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);

        if (member == null
                || StrUtil.isBlank(member.getHomeDirectory())
                || StrUtil.isBlank(member.getId())
                || StrUtil.isBlank(member.getUsername())) {
            log.error("用户未找到：{}", member);
            throw new AccessDeniedException("User not found");
        }

        Path fullAbsolutePath = Paths.get(member.getHomeDirectory(), path.toString()).toAbsolutePath();
        if (Files.exists(fullAbsolutePath)) {
            FileUtils.forceDelete(fullAbsolutePath.toFile());
        }

        String fullPath = FilenameUtils.normalizeNoEndSeparator(fullAbsolutePath.toString(), true);
        // 删除文件或文件夹
        if (isDirectory) {
            // 如果是文件夹，找到当前用户下的以此目录开头的文件夹，然后状态更新为删除
            ftpDbService.updateFtpFileStatusByCreatorAndPathLike(member.getId(), fullPath + "/", FtpFileLogStatus.deleted.getStatus());
        } else {
            // 如果是文件，找到当前用户下的此文件，然后状态更新为删除
            ftpDbService.updateFtpFileStatusByCreatorAndPath(member.getId(), fullPath, FtpFileLogStatus.deleted.getStatus());
            // 如果删除的是md5文件
            if (StrUtil.endWith(path.toString(), ".md5")) {
                String dataFilePath = fullPath.substring(0, fullPath.length() - 4);
                FtpFileLog dataFile = ftpDbService.getFtpFileByCreatorAndPath(member.getId(), dataFilePath);
                if (dataFile != null) {
                    ftpDbService.updateMd5Info(null, dataFile.getId());
                }
            }
        }

    }

    @Override
    public void writing(
            ServerSession session, String remoteHandle, FileHandle localHandle,
            long offset, byte[] data, int dataOffset, int dataLen)
            throws IOException {

        super.writing(session, remoteHandle, localHandle, offset, data, dataOffset, dataLen);

        // 第一次写入数据才进行数据库更新，如果offset大于0
        if (offset > 0) {
            return;
        }

        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);

        if (member == null
                || StrUtil.isBlank(member.getHomeDirectory())
                || StrUtil.isBlank(member.getId())
                || StrUtil.isBlank(member.getUsername())) {
            log.error("用户未找到：{}", member);
            throw new AccessDeniedException("User not found");
        }

        Path fullAbsolutePath = Paths.get(member.getHomeDirectory(), localHandle.getFile().toString()).toAbsolutePath();

        saveFtpFileLog(member, fullAbsolutePath.toFile(), FtpFileLogStatus.uploading);
    }

    private final ThreadLocal<MessageDigest> md5ThreadLocal = ThreadLocal.withInitial(() -> {
        try {
            return MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            // 处理异常情况
            e.printStackTrace();
            return null;
        }
    });

    @Override
    public void written(
            ServerSession session, String remoteHandle, FileHandle localHandle,
            long offset, byte[] data, int dataOffset, int dataLen, Throwable thrown)
            throws IOException {

        super.written(session, remoteHandle, localHandle, offset, data, dataOffset, dataLen, thrown);

        MessageDigest md = md5ThreadLocal.get();
        // 每一次写入，都更新md5
        if (md != null) {
            md.update(data, dataOffset, dataLen);
        }
    }


    @Override
    public void closing(ServerSession session, String remoteHandle, Handle localHandle) throws IOException {

        super.closing(session, remoteHandle, localHandle);

        if (!Files.exists(localHandle.getFile())) {
            return;
        }
        if (Files.isDirectory(localHandle.getFile())) {
            return;
        }

        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);

        if (member == null
                || StrUtil.isBlank(member.getHomeDirectory())
                || StrUtil.isBlank(member.getId())
                || StrUtil.isBlank(member.getUsername())) {
            log.error("用户未找到：{}", member);
            throw new AccessDeniedException("User not found");
        }

        Path fullAbsolutePath = Paths.get(member.getHomeDirectory(), localHandle.getFile().toString()).toAbsolutePath();
        if (!Files.exists(fullAbsolutePath)) {
            log.warn("未找到用户上传的文件：{}", fullAbsolutePath);
            return;
        }

        updateFtpFileLog(member, fullAbsolutePath.toFile(), FtpFileLogStatus.unchecked);
    }

    private void updateFtpFileLog(MemberHolder member, File file, FtpFileLogStatus status) {
        String fileAbsolutePath = FilenameUtils.normalizeNoEndSeparator(file.getAbsolutePath(), true);
        FtpFileLog ftpFile = ftpDbService.getFtpFileByCreatorAndPath(member.getId(), fileAbsolutePath);
        if (ftpFile == null) {
            return;
        }
        // 更新状态
        ftpFile.setStatus(status.getStatus());
        // 更新文件大小
        ftpFile.setSize(file.length());
        ftpFile.setFailCause(null);

        if (status.equals(FtpFileLogStatus.unchecked)) {
            MessageDigest md = md5ThreadLocal.get();
            if (md != null) {
                byte[] digest = md.digest();
                StringBuilder sb = new StringBuilder();
                for (byte b : digest) {
                    sb.append(String.format("%02x", b));
                }
                String md5 = sb.toString();
                log.warn("Upload file MD5: {}", md5);
                // 设置MD5值
                ftpFile.setMd5(md5);
                // 清除线程局部变量中的 MessageDigest 对象
                md5ThreadLocal.remove();
                try {
                    Files.setPosixFilePermissions(file.toPath(), PosixFilePermissions.fromString("rw-r--r--"));
                } catch (Exception e) {
                    log.error("更新文件权限失败：{}", e.getMessage());
                }
            }
        }
        ftpFile.setUpdateTime(new Date());
        ftpDbService.saveOrUpdateFtpFile(ftpFile);
    }

    private void saveFtpFileLog(MemberHolder member, File file, FtpFileLogStatus status) {

        String fileAbsolutePath = FilenameUtils.normalizeNoEndSeparator(file.getAbsolutePath(), true);

        // 查询是否有此文件记录
        FtpFileLog ftpFile = ftpDbService.getFtpFileByCreatorAndPath(member.getId(), fileAbsolutePath);
        if (ftpFile == null) {
            ftpFile = new FtpFileLog();
            ftpFile.setId(IdUtils.getShortUUID());
        }

        ftpFile.setName(file.getName());
        ftpFile.setPath(fileAbsolutePath);
        ftpFile.setSize(file.length());
        ftpFile.setStatus(status.getStatus());
        ftpFile.setMd5FileContent(null);
        ftpFile.setMd5(null);
        ftpFile.setCreateTime(new Date(file.lastModified()));
        ftpFile.setCreator(member.getId());
        ftpFile.setUpdateTime(new Date());

        ftpDbService.saveOrUpdateFtpFile(ftpFile);
    }

    @Override
    public void moving(ServerSession session, Path srcPath, Path dstPath, Collection<CopyOption> opts)
            throws IOException {

        super.moving(session, srcPath, dstPath, opts);
    }

    @Override
    public void moved(ServerSession session, Path srcPath, Path dstPath, Collection<CopyOption> opts, Throwable thrown)
            throws IOException {

        super.moved(session, srcPath, dstPath, opts, thrown);

        if (thrown != null) {
            log.error("移动文件出错, src: {} to: {} error: {}", srcPath, dstPath, thrown);
        }

        MemberHolder member = session.getAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY);

        if (member == null
                || StrUtil.isBlank(member.getHomeDirectory())
                || StrUtil.isBlank(member.getId())
                || StrUtil.isBlank(member.getUsername())) {
            log.error("用户未找到：{}", member);
            throw new AccessDeniedException("User not found");
        }

        Path srcAbsolutePath = Paths.get(member.getHomeDirectory(), srcPath.toString()).toAbsolutePath();
        Path dstAbsolutePath = Paths.get(member.getHomeDirectory(), dstPath.toString()).toAbsolutePath();

        if (!Files.exists(dstAbsolutePath)) {
            log.warn("移动后的文件未找到, From: {} To: {}", srcAbsolutePath, dstAbsolutePath);
            return;
        }

        String srcAbsolutePathStr = FilenameUtils.normalizeNoEndSeparator(srcAbsolutePath.toString(), true);
        String dstAbsolutePathStr = FilenameUtils.normalizeNoEndSeparator(dstAbsolutePath.toString(), true);

        // 如果是目录，就更新目录
        if (Files.isDirectory(dstAbsolutePath)) {
            ftpDbService.updateFtpFilePathByCreatorAndPathLike(member.getId(), srcAbsolutePathStr + "/", dstAbsolutePathStr + "/");
            return;
        }

        FtpFileLog ftpFile = ftpDbService.getFtpFileByCreatorAndPath(member.getId(), srcAbsolutePathStr);
        if (ftpFile == null) {
            ftpFile = new FtpFileLog();
            ftpFile.setId(IdUtils.getShortUUID());
            ftpFile.setCreateTime(new Date(Files.getLastModifiedTime(dstAbsolutePath).toMillis()));
            ftpFile.setCreator(member.getId());
            ftpFile.setMd5FileContent(null);
            ftpFile.setMd5(null);
        }

        ftpFile.setSize(Files.size(dstAbsolutePath));
        ftpFile.setName(FilenameUtils.getName(dstAbsolutePathStr));
        ftpFile.setPath(dstAbsolutePathStr);
        ftpFile.setStatus(FtpFileLogStatus.unchecked.getStatus());
        ftpFile.setUpdateTime(new Date());
        ftpDbService.saveOrUpdateFtpFile(ftpFile);
    }
}
