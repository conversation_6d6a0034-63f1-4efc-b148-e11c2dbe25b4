package org.biosino.upload.repository;

import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.SelectQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public interface AnalysisCustomRepository {
    /**
     * 分析名称查重范围：同一个用户下，未正式分配ID的数据
     *
     * @param creator    用户ID
     * @param analysisNo 项目ID（可选）
     * @param name       （分析名称）
     * @return true 没有重复， false，校验未通过，有重复名称
     */
    Analysis validateAnalysisName(String creator, String analysisNo, String name);

    Page findAccessableSelectPage(SelectQueryDTO queryDTO, Class clazz);

    Analysis findByAnalNo(String analysisNo);

    List<Analysis> findAllByCreator(String creator);

    boolean existAccessableNo(String no, TypeInformation typeInformation, String creator);

    Set<String> getAccessableNos(Collection<String> nos, TypeInformation typeInformation, String creator);

    Map<String, String> findNameByNamesInAndAnalNoNotIn(Collection<String> names, String memberId, List<String> analNos);

    Page<Analysis> findAllByPage(ArchivedSelectQueryDTO queryDTO);

    boolean existsByCreatorAndNo(String analNo, String creator);

    Map<String, Boolean> existsByCreatorAndNos(Collection<String> analNos, String memberId);

    Map<String, Boolean> existsByCreatorAndNos(List<String> analNos, String memberId);

    boolean existAuditInitAnalByAnalName(String analName, String creator);

    Map<String, Boolean> existAuditInitAnalByAnalNames(List<String> analNames, String creator);

    Analysis findAuditInitAnalByAnalName(String analName, String creator);

    List<Analysis> findAllAuditInitAnalByAnalNames(List<String> analNames, String creator);

    void deleteTempByNosAndCreator(Collection<String> analNos, String creator);

    Map<String, String> findTempNameByNamesInAndNoNotIn(Collection<String> names, String creator, Collection<String> nos);

    Map<String, Analysis> findTempNameByNamesInAndNoIn(Collection<String> names, String creator, Collection<String> nos);

    void updateToDeleteAllByAnalysisNoIn(List<String> analNos);

    Optional<Analysis> findFirstByAnalysisNo(String analysisNo);

    List<Analysis> findAllByAnalysisNoIn(Collection<String> nos);
}
