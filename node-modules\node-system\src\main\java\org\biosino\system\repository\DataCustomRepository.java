package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.DataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataCustomRepository {

    List<Data> findDetailByRunNoIn(Collection<String> runNos);

    List<Data> findAllByRunNoIn(List<String> runNos);

    List<Data> findAllByAnalNoIn(Collection<String> analNos);

    List<Data> findTempByRunNoIn(Collection<String> runNos);

    void updateToDeleteAllByDatNoIn(Collection<String> dataNos);

    void updateCreatorByDatNoIn(Collection<String> dataNos, String creator);

    List<Data> findDetailByAnalNoIn(Collection<String> analNos);

    Page<Data> findDataPage(DataQueryDTO queryDTO);

    Optional<Data> findFirstByFilePath(String relativepath);

    List<Data> findAllByDataNoIn(List<String> dataNos);

    List<Data> findAllByDataNoIn(Collection<String> dataNos, String creator);

    List<String> findDataCheckNotPass();

    void updateCompleteByDatNoIn(List<String> dataNos, Boolean complete);

    MongoPagingIterator<Data> getPagingIterator(DataQueryDTO queryDTO);
}
