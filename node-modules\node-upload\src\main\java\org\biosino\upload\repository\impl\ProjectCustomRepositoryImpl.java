package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.repository.ProjectCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ProjectCustomRepositoryImpl implements ProjectCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Project> findAllByCreator(String creator) {
        Query query = new Query();
        query.fields().include("name").include("proj_no");
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(creator)) {
            criteria.andOperator(Criteria.where("creator").is(creator));
        }
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public Project validateProjectName(String creator, String projNo, String projectName) {
        Query query = new Query();
        Criteria criteria = Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("name").is(projectName)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(projNo)) {
            // 如果有projNo，说明是编辑数据时查重，需要排除自身
            criteria.andOperator(Criteria.where("proj_no").ne(projNo));
        }
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Project.class);
    }


    @Override
    public Project findByNo(String projNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("proj_no").is(projNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Project.class);
    }

    @Override
    public List<String> findAllProjectNosByCreator(String creator) {
        if (creator == null) {
            return new ArrayList<>();
        }
        // 查询正式项目编号
        Criteria criteria = prjBase(creator).and("audited").ne(AuditEnum.init.name());
        final Query query = new Query(criteria);
        query.fields().include("proj_no").include("used_ids");
        final List<Project> projects = mongoTemplate.find(query, Project.class);
        return initNoList(projects);
    }

    @Override
    public Map<String, String> findInitNameAndNoByCreator(final String creator) {
        if (creator == null) {
            return new HashMap<>();
        }
        Criteria criteria = prjBase(creator).and("audited").is(AuditEnum.init.name());
        final Query query = new Query(criteria);
        query.fields().include("name").include("proj_no");
        return initNameNoMap(mongoTemplate.find(query, Project.class));
    }

    private Map<String, String> initNameNoMap(final List<Project> projects) {
        final Map<String, String> map = new HashMap<>();
        for (Project project : projects) {
            if (project.getName() != null) {
                map.put(project.getName(), project.getProjectNo());
            }
        }
        return map;
    }

    private Criteria prjBase(final String creator) {
        return Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
    }


    private Criteria noCriteria(final Collection<String> nos, String creator) {
        if (CollUtil.isNotEmpty(nos)) {
            final int size = nos.size();
            if (size > 1) {
                return new Criteria().andOperator(
                        prjBase(creator),
                        new Criteria().orOperator(
                                Criteria.where("proj_no").in(nos), Criteria.where("used_ids").in(nos)
                        )
                );
            } else {
                return new Criteria().andOperator(
                        prjBase(creator),
                        new Criteria().orOperator(
                                Criteria.where("proj_no").is(nos.iterator().next()),
                                Criteria.where("used_ids").in(nos.iterator().next())
                        )
                );
            }
        } else {
            return new Criteria();
        }
    }

    private List<String> initNoList(final List<Project> projects) {
        final Set<String> data = new HashSet<>();
        for (Project project : projects) {
            data.add(project.getProjectNo());
            List<String> usedIds = project.getUsedIds();
            if (CollUtil.isNotEmpty(usedIds)) {
                for (String usedId : usedIds) {
                    if (usedId != null) {
                        data.add(usedId);
                    }
                }
            }
        }
        return new ArrayList<>(data);
    }

    /**
     * key为audited+下划线+项目编号,值为项目名称
     */
    private Map<String, String> initNoNameMapWithAudited(final List<Project> projects) {
        final Map<String, String> map = new HashMap<>();
        for (Project project : projects) {
            final String keyPrefix = StrUtil.trimToEmpty(project.getAudited()) + PROJECT_KEY_CONNECTOR;
            final String name = project.getName();
            map.put(keyPrefix + project.getProjectNo(), name);
            List<String> usedIds = project.getUsedIds();
            if (CollUtil.isNotEmpty(usedIds)) {
                for (String usedId : usedIds) {
                    if (usedId != null) {
                        map.put(keyPrefix + usedId, name);
                    }
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, String> findNoAndNameByProjectNosAndCreator(Collection<String> prjNos, String creator) {
        if (CollUtil.isEmpty(prjNos) || creator == null) {
            return new HashMap<>();
        }
        Criteria criteria = noCriteria(prjNos, creator);
        Query query = new Query(criteria);
        query.fields().include("proj_no").include("name").include("used_ids").include("audited");
        final List<Project> projects = mongoTemplate.find(query, Project.class);
        return initNoNameMapWithAudited(projects);
    }

    @Override
    public Page<Project> findAllByPage(ArchivedSelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("creator").is(queryDTO.getCreator());
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("proj_no").regex(pattern),
                    Criteria.where("name").regex(pattern));
        }

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("name").include("proj_no");
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));

        // 统计
        long count = mongoTemplate.count(query, Project.class);
        // 分页
        query.with(queryDTO.getPageable());
        // 查询
        List<Project> content = mongoTemplate.find(query, Project.class);
        // 精准查询并放到首位
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Criteria subCri = new Criteria();
            subCri.orOperator(Criteria.where("proj_no").is(queryDTO.getName()), Criteria.where("name").is(queryDTO.getName()));
            Project proj = mongoTemplate.findOne(Query.query(subCri), Project.class);
            if (proj != null) {
                // 将list的首位替换成exp
                content.set(0, proj);
            }
        }
        return new PageImpl<>(content, queryDTO.getPageable(), count);
    }

    @Override
    public void updateToDeleteAllByProjectNo(Collection<String> projNos) {
        if (CollUtil.isEmpty(projNos)) {
            return;
        }
        // 将对应projNo的数据状态改为删除
        Query query = new Query(Criteria.where("proj_no").in(projNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name());
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Project.class);
    }

    @Override
    public Optional<Project> findTopByProjectNo(String projectNo) {
        if (StrUtil.isBlank(projectNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("proj_no").is(projectNo),
                Criteria.where("used_ids").in(projectNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Project project = mongoTemplate.findOne(query, Project.class);
        return Optional.ofNullable(project);
    }

    @Override
    public List<Project> findAllByProjectNoIn(Collection<String> projectNos) {
        if (CollUtil.isEmpty(projectNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("proj_no").in(projectNos),
                Criteria.where("used_ids").in(projectNos)
        ));
        List<Project> list = mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Project.class);
        return list;
    }
}
