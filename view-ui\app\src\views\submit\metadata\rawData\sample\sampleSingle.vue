<template>
  <div v-loading="loadingFlag" class="w-100">
    <div class="card card-container general-info mt-1 pt-0">
      <FillTip :recommend="true"></FillTip>
      <div class="category-title font-600 text-main-color">Sample Type</div>
      <div class="mt-1">
        <div
          v-for="(item, index) in sapTypeList"
          :key="index + item + 'a'"
          class="d-flex type-radio mt-1"
        >
          <div
            class="plr-20 mr-1"
            :class="form.subjectType === item.name ? 'bg-warning' : 'bg-gray'"
          >
            <el-radio
              v-model="form.subjectType"
              :label="item.name"
              size="large"
              @change="changeSampleType(index, item.children)"
              >{{ item.name }}
            </el-radio>
          </div>
          <div
            class="text plr-20 bg-gray w-100 d-flex align-items-center"
            :class="form.subjectType === item.name ? 'bg-warning' : 'bg-gray'"
          >
            <div
              :ref="el => setRef(el, index)"
              class="radio-content"
              :class="{ ellipsis: !isExpanded[index] }"
            >
              <span v-html="item.description"></span>
              <div
                v-if="
                  (item.children && isExpanded[index]) ||
                  form.subjectType === item.name
                "
                class="sub-type-content"
              >
                <div
                  v-for="(item2, index2) in item.children"
                  :key="index2 + item2 + 'sap'"
                  class="d-flex type-radio mt-1"
                >
                  <div
                    class="plr-20 mr-1"
                    :class="
                      form.subjectType === item2.name ? 'bg-warning' : 'bg-gray'
                    "
                  >
                    <el-radio
                      v-model="form.subjectType"
                      :label="item2.value"
                      size="large"
                      @change="resetForm"
                      >{{ item2.name }}
                    </el-radio>
                  </div>
                  <div
                    class="text plr-20 bg-gray w-100 d-flex align-items-center"
                    :class="
                      form.subjectType === item2.name ? 'bg-warning' : 'bg-gray'
                    "
                  >
                    <div
                      :ref="el => setRef(el, index2 + 99)"
                      class="radio-content"
                      :class="{ ellipsis: !isExpanded[index2 + 99] }"
                      v-html="item2.description"
                    ></div>
                    <span
                      v-show="showToggleButton(index2 + 99)"
                      class="text-primary pointer more ml-1"
                      @click="toggleText(index2 + 99)"
                    >
                      {{ isExpanded[index2 + 99] ? 'Collapse' : 'More>>' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <span
              v-show="showToggleButton(index)"
              class="text-primary pointer more ml-1"
              @click="toggleText(index)"
            >
              {{ isExpanded[index] ? 'Collapse' : 'More>>' }}
            </span>
          </div>
        </div>
      </div>

      <div class="category-title font-600 text-main-color mt-1">
        General Information
      </div>
      <div
        :key="'sap-general-info-' + componentKey"
        class="plr-20 bg-gray mt-1"
      >
        <el-form
          ref="sapForm"
          label-position="top"
          :model="form"
          :inline="true"
          :rules="rules"
          :scroll-to-error="true"
          class="pt8"
        >
          <el-form-item label="Sample Name" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item
            v-if="hasOrganism !== 'none'"
            label="Organism"
            :required="hasOrganism === 'required'"
            prop="organism"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon
                v-if="hasOrganism === 'recommend'"
              ></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Organism</span>
            </template>

            <el-select
              v-model="form.organism"
              clearable
              filterable
              remote
              :fit-input-width="false"
              :teleported="false"
              reserve-keyword
              placeholder="Please input search taxonomy"
              :remote-method="querySearchAsync"
              :loading="taxonomyLoading"
            >
              <el-option
                v-for="taxonomyItem in taxonomyOptions"
                :key="`taxonomyOption-${taxonomyItem.label} [${taxonomyItem.value}]`"
                :label="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
                :value="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
                :title="`Lineage: ${trimStr(taxonomyItem.title)}`"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="hasTissue !== 'none'"
            :required="hasTissue === 'required'"
            label="Tissue"
            prop="tissue"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon v-if="hasTissue === 'recommend'"></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Tissue</span>
            </template>
            <el-input v-model="form.tissue" clearable />
          </el-form-item>

          <el-form-item
            v-if="hasDesc !== 'none'"
            class="w-100"
            :required="hasDesc === 'required'"
            label="Sample Description"
            prop="description"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon v-if="hasDesc === 'recommend'"></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Sample Description</span>
            </template>
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>

          <el-form-item
            v-if="hasProtocol !== 'none'"
            class="w-100"
            :required="hasProtocol === 'required'"
            prop="protocol"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon
                v-if="hasProtocol === 'recommend'"
              ></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Sample Processing Protocol</span>
            </template>
            <el-input v-model="form.protocol" type="textarea" rows="5" />
          </el-form-item>

          <RelatedLinks
            :key="'sap-RelatedLinks' + componentKey"
            v-model:related-links="form.relatedLinks"
          ></RelatedLinks>
        </el-form>
      </div>

      <div class="category-title font-600 text-main-color">
        Sample Information
      </div>
      <div class="plr-20">
        <SapAttr
          :key="'attrSap' + componentKey"
          ref="sapAttrRef"
          v-model:attributes="form.attributes"
          v-model:recommend-filled-count="recommendFilledCount"
          :sample-type="form.subjectType"
        ></SapAttr>

        <CustomAttr
          :key="'sapCustomAttr' + componentKey"
          ref="sapCustomAttrRef"
          v-model:custom-attributes="form.customAttributes"
        ></CustomAttr>
      </div>

      <Publication
        :key="'sap-Publication' + componentKey"
        v-model:publish-data="form.publish"
      ></Publication>

      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >Continue
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="previewData"
          >Preview & Save
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >Reset
        </el-button>
        <el-button
          :disabled="!form.sapNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >Delete
        </el-button>
      </div>
    </div>

    <el-dialog
      v-model="previewDialog"
      title="Preview"
      width="70%"
      class="preview-dialog radius-14"
    >
      <el-divider content-position="left"
        ><h3 class="preview-title">Sample</h3></el-divider
      >
      <div class="d-flex preview">
        <div>
          <span class="title">Sample ID</span>
          <span class="content"
            >Will be allocated automatically after check.</span
          >
        </div>
        <div>
          <div>
            <span class="title">Sample Type</span>
            <span class="content">{{ form.subjectType }}</span>
          </div>
        </div>
        <div>
          <span class="title">Sample Name</span>
          <span class="content">{{ $text(form.name) }}</span>
        </div>
        <div>
          <span class="title">Description</span>
          <span class="content">
            <el-tooltip
              effect="light"
              :content="form.description"
              placement="top-start"
            >
              {{ $text(form.description) }}
            </el-tooltip>
          </span>
        </div>
        <div>
          <span class="title">Processing Protocol</span>
          <span class="content">{{ $text(form.protocol) }}</span>
        </div>
        <div>
          <span class="title">Related Links</span>
          <span
            v-if="!form.relatedLinks || form.relatedLinks.length === 0"
            class="content"
            >-</span
          >
          <div class="d-flex flex-column">
            <p
              v-for="(val, idx) in form.relatedLinks"
              :key="'review-relatedLinks-' + idx"
              class="content"
            >
              {{ $text(val) }}
            </p>
          </div>
        </div>
      </div>

      <PreviewSapAttr
        v-if="previewDialog"
        :key="'PreviewSapAttr-' + componentKey"
        v-model:attr-data="form.attributes"
        v-model:custom-attr-data="form.customAttributes"
        :sap-type="form.subjectType"
      ></PreviewSapAttr>

      <PreviewPublish :publish-data="form.publish"></PreviewPublish>

      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="saveData"
              >Save</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >Back Edit</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>
    <DeleteLog ref="deleteLog" curr-type="Sample"></DeleteLog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    nextTick,
    onActivated,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';

  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import { findTaxonomyLike } from '@/api/search';
  import { getSampleType } from '@/api/metadata/dict';
  import {
    deleteSample,
    getSampleInfo,
    saveSample,
    validateSampleName,
  } from '@/api/metadata/sample';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';

  import Publication from '../common/Publications.vue';
  import SapAttr from '@/views/submit/metadata/rawData/sample/SapAttr.vue';
  import CustomAttr from '@/views/submit/metadata/rawData/sample/CustomAttr.vue';
  import RelatedLinks from '@/views/submit/metadata/rawData/common/RelatedLinks.vue';
  import PreviewPublish from '@/views/submit/metadata/rawData/common/PreviewPublish.vue';
  import PreviewSapAttr from '@/views/submit/metadata/rawData/sample/PreviewSapAttr.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import FillTip from '@/views/submit/components/FillTip.vue';
  import RecommendIcon from '@/views/submit/metadata/rawData/common/RecommendIcon.vue';
  import { sleep, trimStr } from '@/utils';

  const route = useRoute();
  const { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();
  const emit = defineEmits(['continueMessage']);
  const { subNo, submission } = storeToRefs(submissionStore);

  const oldFormStr = ref('');
  const sapTypeList = ref([]); // 系统拥有的样本类型列表
  const componentKey = ref(1);
  const previewDialog = ref(false);

  const loadingFlag = ref(false);

  /** 样本名查重 */
  const checkSampleName = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('Please input Sample Name'));
    }
    validateSampleName({
      sapNo: form.value.sapNo,
      name: form.value.name,
    })
      .then(response => {
        if (response && response.msg) {
          callback(new Error(response.msg));
        }
        callback();
      })
      .catch(error => callback(new Error(error)));
  };

  const data = reactive({
    form: {
      sapNo: undefined,
      name: '',
      description: undefined,
      protocol: undefined,
      organism: undefined,
      tissue: undefined,
      subjectType: 'Human',
      attributes: {},
      customAttributes: [],
      relatedLinks: undefined,
      publish: {
        publication: undefined,
        doi: undefined,
        pmid: undefined,
        reference: undefined,
        articleName: undefined,
      },
    },
    rules: {
      name: [
        {
          required: true,
          validator: checkSampleName,
          trigger: 'blur',
        },
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      description: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      protocol: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      organism: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      tissue: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
      relatedLinks: [
        {
          validator: proxy.$validateChinese,
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  let resultArr = reactive([]); // 存放子组件的数组
  let errListMsg = ref(''); // 用来存储错误提示

  // 创建Promise 实例，为多个组件校验使用
  const checkForm = formChild => {
    let result = new Promise((resolve, reject) => {
      formChild.validate((valid, fields) => {
        if (valid) {
          resolve();
        } else {
          Object.keys(fields).forEach((v, index) => {
            if (index === 0) {
              // 定位到错误的位置
              // const PropName = fields[v][0].field;
              // formChild.scrollToField(PropName);
              errListMsg.value = fields[v][0].message;
            }
          });
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  let sapAttrRef = ref();
  // 必须填写的推荐字段数量
  let mustRecommendNum = ref(0);
  // 自定义表单中填写了推荐字段数量
  let recommendFilledCount = ref(0);

  /** 预览数据 */
  function previewData() {
    // 基本数据中的推荐字段
    let allRecommendFilledCount = recommendFilledCount.value;
    if (hasOrganism.value === 'recommend' && form.value.organism) {
      allRecommendFilledCount++;
    }
    if (hasTissue.value === 'recommend' && form.value.tissue) {
      allRecommendFilledCount++;
    }
    if (hasDesc.value === 'recommend' && form.value.description) {
      allRecommendFilledCount++;
    }
    if (hasProtocol.value === 'recommend' && form.value.protocol) {
      allRecommendFilledCount++;
    }
    if (allRecommendFilledCount < mustRecommendNum.value) {
      // 校验推荐填写的数量是否达标
      proxy.$modal.alertWarning(
        'Insufficient data for recommended attributes, please continue to fill in',
      );
      return;
    }

    // 获取该子组件暴露出来的form的ref
    const approvalSapAttrRef = sapAttrRef.value.sapAttrForm;
    // 调用上面创建好的方法
    checkForm(approvalSapAttrRef);
    checkForm(proxy.$refs['sapForm']);

    Promise.all(resultArr)
      .then(() => {
        // 校验通过
        previewDialog.value = true;
      })
      .catch(() => {
        // 校验不通过提示
        proxy.$modal.msgError(errListMsg.value);
      });
    resultArr = []; // 每次请求完要清空数组
    errListMsg.value = ''; // 提示也需要清空
  }

  /** 提交数据 */
  const saveData = () => {
    proxy.$modal.loading('Saving data, please wait...');
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  /** 保存表单 */
  const saveForm = () => {
    form.value.subNo = subNo.value;
    saveSample(form.value)
      .then(response => {
        form.value = response.data;

        previewDialog.value = false;
        proxy.$modal.alertSuccess(
          'Sample has been saved successfully, you can continue next step now!',
        );

        stringifyFormData();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  };

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm('Are you sure to delete this Sample?')
      .then(() => {
        const params = {
          subNo: subNo.value,
          sampleType: form.value.subjectType,
          single: true,
        };
        deleteSample(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess('Delete successful');
          resetForm();
          stringifyFormData();
        });
      })
      .catch(() => {});
  };

  /** 初始化数据 */
  function initData() {
    const subNo = route.params.subNo;
    // 如果存在SUB NO则说明是编辑数据，从数据库回显
    if (subNo && submission.value.sapSingleNo) {
      getSampleInfo(submission.value.sapSingleNo)
        .then(response => {
          form.value = response.data;
          componentKey.value++;

          stringifyFormData();
        })
        .finally(() => {
          sapTypeList.value.forEach((value, index) => {
            if (value.name === form.value.subjectType) {
              toggleText(index);
            }
            if (value.children) {
              value.children.forEach(child => {
                if (child.name === form.value.subjectType) {
                  toggleText(index);
                }
              });
            }
          });
        });
    } else {
      stringifyFormData();
    }
  }

  /** 等所有的子组件全部渲染完成后序列化json */
  function stringifyFormData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldFormStr.value = JSON.stringify(form.value);
    });
  }

  /** 重置表单 */
  const resetForm = () => {
    loadingFlag.value = true;
    proxy.resetForm('sapForm');
    form.value.publish = undefined;
    form.value.relatedLinks = undefined;
    form.value.attributes = {};
    form.value.customAttributes = [];
    componentKey.value++;
    hideLoading();
  };

  async function hideLoading() {
    await sleep(750);
    loadingFlag.value = false;
  }

  /** 继续 */
  function continueNext() {
    if (validateSaved()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  /** 校验表单数据是否被更改 */
  function validateSaved() {
    const currentFormStr = JSON.stringify(form.value);
    return currentFormStr === oldFormStr.value;
  }

  const sendContinueMessage = () => {
    emit('continueMessage', 'ArchivingSingle');
  };

  /** 加载系统所拥有的样本类型 */
  function loadSampleType() {
    return getSampleType().then(response => {
      sapTypeList.value = transformSampleType(response.data.types);
      form.value.subjectType = sapTypeList.value[0].name;

      updateOrganismStatus(form.value.subjectType);
    });
  }

  /** 按照样本类型父子关系，构建树结构 */
  function transformSampleType(data) {
    const transformedData = [];

    const map = new Map();

    // 第一次遍历，将每个元素的 name 和 value 添加到 map 中
    for (const item of data) {
      const {
        name,
        organism,
        tissue,
        recommendNum,
        desc,
        protocol,
        description,
      } = item;
      const value = name;
      map.set(name, {
        name,
        value,
        recommendNum,
        organism,
        tissue,
        desc,
        protocol,
        description,
        children: [],
      });
    }

    // 第二次遍历，构建转换后的数据结构
    for (const item of data) {
      const { name, parentName } = item;
      const currentItem = map.get(name);

      if (parentName) {
        const parentItem = map.get(parentName);
        if (parentItem) {
          if (
            !parentItem.children.find(child => child.name === currentItem.name)
          ) {
            parentItem.children.push(currentItem);
          }
        }
      } else {
        transformedData.push(currentItem);
      }
    }

    // 添加 "Default" 对象到相应的父节点的 children 数组中
    for (const item of transformedData) {
      if (item.children.length >= 1) {
        const defaultItem = {
          name: 'Default',
          value: item.name,
          organism: item.organism,
          tissue: item.tissue,
          recommendNum: item.recommendNum,
          desc: item.desc,
          protocol: item.protocol,
          description: item.description,
        };
        item.children.unshift(defaultItem);
      }
    }

    return transformedData;
  }

  const taxonomyOptions = ref([]);
  const taxonomyLoading = ref(false);

  /** 查询ES中的Taxonomy */
  const querySearchAsync = query => {
    taxonomyLoading.value = true;
    findTaxonomyLike({ keyword: query })
      .then(response => {
        taxonomyOptions.value = response.data;
      })
      .finally(() => {
        taxonomyLoading.value = false;
      });
  };

  onMounted(() => {
    loadSampleType().then(() => {
      initData();
    });

    querySearchAsync(form.value.organism);
  });

  const hasOrganism = ref('none');
  const hasTissue = ref('none');
  const hasDesc = ref('optional');
  const hasProtocol = ref('optional');

  /** 将基本信息中可以自定义的字段提取出来 */
  function updateOrganismStatus(sampleType) {
    sapTypeList.value.forEach(value => {
      if (value.name === sampleType) {
        hasOrganism.value = value.organism;
        hasTissue.value = value.tissue;
        hasDesc.value = value.desc;
        hasProtocol.value = value.protocol;
        mustRecommendNum.value = value.recommendNum;
      }
      if (value.children) {
        value.children.forEach(child => {
          if (child.name === sampleType) {
            hasOrganism.value = child.organism;
            hasTissue.value = child.tissue;
            hasDesc.value = child.desc;
            hasProtocol.value = child.protocol;
            mustRecommendNum.value = child.recommendNum;
          }
        });
      }
    });
    componentKey.value++;
  }

  // 监听样本类型的变化，动态显示Organism和Tissue
  watch(
    () => form.value.subjectType,
    newType => {
      updateOrganismStatus(newType);
    },
  );

  // 切换组件时，自动定位到顶部
  onActivated(() => {
    window.scrollTo(0, 0);
  });

  // 展开 收起
  const isExpanded = ref(sapTypeList.value.map(() => false));

  const refs = ref([]);

  const setRef = (el, index) => {
    refs.value[index] = el;
    return el;
  };

  const changeSampleType = (index, children) => {
    loadingFlag.value = true;
    if (children != null) {
      isExpanded.value[index] = true;
    }
    resetForm();
  };

  const showToggleButton = index => {
    const textRef = refs.value[index];
    if (textRef) {
      const textLength = textRef.innerText.length;
      const containerWidth = textRef.parentNode.offsetWidth;
      const maxCharsPerLine = Math.floor(containerWidth / (14 * 0.45));
      const numLines = Math.ceil(textLength / maxCharsPerLine);
      return numLines > 1;
    }
  };

  const toggleText = index => {
    isExpanded.value[index] = !isExpanded.value[index];
  };
</script>

<style lang="scss" scoped>
  .exist-form {
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 30px;

      .el-select {
        width: 100%;
      }
    }
  }

  .radio-content {
    text-align: justify;
    // 文本溢出自动隐藏
    &.ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 40px;
      transition: all 1s;
    }
  }

  .type-radio:nth-child(-n + 5) {
    :deep(.el-radio .el-radio--large) {
      width: 90px;
    }
  }

  .type-radio:nth-child(n + 6) {
    :deep(.el-radio) {
      width: 230px !important;
    }
  }

  .general-info .el-form {
    .el-form-item {
      width: 30%;

      .el-select {
        width: 100%;
      }

      .el-radio {
        width: 100%;
        margin-right: 0;

        :deep(.el-radio__label) {
          width: 100%;
        }
      }
    }
  }

  .links {
    .el-button {
      padding: 2px 8px;
      border-radius: 50%;
    }

    :deep(.el-form-item__content) {
      flex-direction: column;
      align-items: flex-start;

      & + .el-form-item__label {
        font-weight: 700;
      }
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 700;
  }

  :deep(.el-radio__label) {
    font-size: 16px;
  }
</style>
