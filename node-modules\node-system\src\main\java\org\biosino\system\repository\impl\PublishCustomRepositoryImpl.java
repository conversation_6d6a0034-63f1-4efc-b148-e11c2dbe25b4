package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.Publish;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.PublishSearchDTO;
import org.biosino.system.repository.PublishCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class PublishCustomRepositoryImpl implements PublishCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Publish> findByTypeId(String type, String typeId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("type").is(type).and("type_id").is(typeId).and("deleted").is(false);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public void updateCreatorByTypeAndTypeId(String type, Collection<String> typeIds, String newCreator) {
        if (CollUtil.isEmpty(typeIds)) {
            return;
        }
        Query query = new Query(Criteria.where("type").is(type).and("type_id").in(typeIds));
        Update update = new Update()
                .set("creator", newCreator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Publish.class);
    }

    @Override
    public PageImpl<Publish> findAllPage(PublishSearchDTO dto) {

        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(dto.getArticleName())) {
            criteriaList.add(Criteria.where("article_name").regex(".*?" + dto.getArticleName().trim() + ".*?", "i"));
        }

        if (StrUtil.isNotBlank(dto.getDoi())) {
            criteriaList.add(Criteria.where("DOI").regex(".*?" + dto.getDoi().trim() + ".*?", "i"));
        }

        if (StrUtil.isNotBlank(dto.getTypeId())) {
            criteriaList.add(Criteria.where("type_id").is(dto.getTypeId()));
        }

        if (StrUtil.isNotBlank(dto.getStatus())) {
            criteriaList.add(Criteria.where("status").is(dto.getStatus()));
        }

        if (StrUtil.isNotBlank(dto.getPublication())) {
            criteriaList.add(Criteria.where("publication").is(dto.getPublication()));
        }

        queryDate(dto, criteriaList, "create_date");

        if (StrUtil.isNotBlank(dto.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(dto.getCreator()));
        }

        if (dto.getDeleted() != null) {
            criteriaList.add(Criteria.where("deleted").is(dto.getDeleted() ));
        }

        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = dto.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, Publish.class);
        query.with(pageable);

        List<Publish> content = mongoTemplate.find(query, Publish.class);
        return new PageImpl<>(content, dto.getPageable(), total);
    }


    private static void queryDate(BaseQuery dto, List<Criteria> criteriaList, String createTime) {
        if (ObjectUtil.isNotEmpty(dto.getBeginTime()) && ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())).lte(DateUtil.endOfDay(dto.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getBeginTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).lte(DateUtil.endOfDay(dto.getEndTime())));
        }
    }

}
