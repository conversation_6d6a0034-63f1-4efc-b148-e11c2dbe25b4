package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.system.dto.dto.AnalysisDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
public class AnalysisExportDTO {
    @JSONField(name = "id", ordinal = 1)
    private String id;

    @JSONField(name = "anal_no", ordinal = 2)
    private String analysisNo;

    @JSONField(name = "sub_no", ordinal = 3)
    private String subNo;

    @JSONField(name = "temp_data", ordinal = 4)
    private AnalysisDTO tempData;

    @JSONField(name = "name", ordinal = 5)
    private String name;

    @JSONField(name = "description", ordinal = 6)
    private String description;

    @JSONField(name = "analysis_type", ordinal = 7)
    private String analysisType;

    @JSONField(name = "custom_analysis_type", ordinal = 8)
    private String customAnalysisType;

    @JSONField(name = "pipeline", ordinal = 9)
    private List<Pipeline> pipeline;

    @JSONField(name = "target", ordinal = 10)
    private List<AnalysisTarget> target;

    @JSONField(name = "custom_target", ordinal = 11)
    private List<CustomTarget> customTarget;

    @JSONField(name = "submitter", ordinal = 12)
    private Submitter submitter;

    @JSONField(name = "creator", ordinal = 13)
    private String creator;

    @JSONField(name = "submission_date", ordinal = 14)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 15)
    private Date updateDate;

    @JSONField(name = "hit_num", ordinal = 16)
    private Long hitNum;

    @JSONField(name = "export_num", ordinal = 17)
    private Long exportNum;

    @JSONField(name = "public_date", ordinal = 18)
    private Date publicDate;

    @JSONField(name = "operator", ordinal = 19)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 20)
    private Date operationDate;

    @JSONField(name = "used_ids", ordinal = 21)
    private List<String> usedIds;

    @JSONField(name = "audited", ordinal = 22)
    private String audited;

    @JSONField(name = "ownership", ordinal = 23)
    private String ownership;

    @JSONField(name = "source_project", ordinal = 24)
    private List<String> sourceProject;

    @JSONField(name = "visible_status", ordinal = 25)
    private String visibleStatus;
}
