package org.biosino.system.vo.fd;

import lombok.Data;
import org.biosino.common.core.domain.dto.es.StatDTO;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * 特殊数据集 明细界面
 *
 * <AUTHOR>
 */
@Data
public class FdDetailVO implements Serializable {

    private FdHomeStatVO.FdStatItem currCat1Item;
    private List<FdHomeStatVO.FdStatItem> statInfo;
    private StatDTO cat1Stat;
    private List<CatItem> cat2Data;

    private List<PrjInfo> projectInfo;
    private List<SapInfo> sampleInfo;

    private long sampleTotal = 0;
//    private int pageNum = 1;
//    private int pageSize = 10;

    @Data
    public static class CatItem {
        private String cateName;
        private LinkedHashSet<String> prjNos;
        private StatDTO statDTO;

        private List<CatItem> cateThree;
    }

    @Data
    public static class PrjInfo {
        private String projID;
        private String projName;
        private String des;
        private String expType;
    }

    @Data
    public static class SapInfo {
        private String sampID;
        private String sampName;
        private String des;
        private String sampType;
        private String organism;
        private String expType;
        private String tissue;

        private Map<String, String> attributes;
    }

}
