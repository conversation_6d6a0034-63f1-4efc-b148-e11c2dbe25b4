<template>
  <div v-loading="loading" class="card mt-1">
    <el-row>
      <el-col :span="24" align="center" class="d-flex justify-center">
        <span
          v-for="(item, index) in Object.keys(statusStatInfo)"
          :key="`status_stat_${index}`"
          class="ml-05 d-flex align-items-center"
        >
          <el-icon :class="statusMap[item].textCls" class="ml-1 mr-03">
            <component :is="statusMap[item].icon" />
          </el-icon>
          {{ item }}：{{ statusStatInfo[item] }}</span
        >
      </el-col>
    </el-row>
    <el-divider class="mb-1 mt-1"></el-divider>
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-form ref="searchFormRef" :model="queryParams" :inline="true">
          <el-form-item label="Data" prop="dataNo">
            <el-input
              v-model="queryParams.dataNo"
              style="width: 300px"
              clearable
              placeholder="Search for Data ID or Name"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="getDataList"
              >Search
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="d-flex align-items-center mb-05">
      <span class="font-600 text-secondary-color mr-1">Status: </span>
      <el-radio-group v-model="queryParams.status" @change="getDataList">
        <el-radio value="" label="All">All</el-radio>
        <el-radio value="ready" label="Ready">Ready</el-radio>
        <el-radio value="queuing" label="Queuing">Queuing</el-radio>
        <el-radio value="running" label="Running">Running</el-radio>
        <el-radio value="success" label="Success">Success</el-radio>
        <el-radio value="failed" label="Failed">Failed</el-radio>
      </el-radio-group>
    </div>

    <el-table
      tooltip-effect="light"
      :data="tableData"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-style="{
        position: 'relative',
      }"
      max-height="550"
      class="data-list"
      border
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column label="Data ID" prop="dataNo" min-width="115" sortable />
      <el-table-column
        label="Data Name"
        prop="dataFileName"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        label="File Size"
        prop="dataFileSize"
        min-width="100"
        sortable
      >
        <template #default="scope">
          {{ filesize(scope.row.dataFileSize) }}
        </template>
      </el-table-column>
      <el-table-column
        label="Status : Cause"
        prop="status"
        min-width="90"
        width="200"
        sortable
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.status
          }}{{
            scope.row.status === 'failed' ? ` : ${scope.row.failCause}` : ''
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="Create Date"
        prop="createDate"
        min-width="160"
        sortable
      />
      <el-table-column
        label="Status Update Date"
        prop="updateDate"
        min-width="170"
        sortable
      />
      <el-table-column
        label="format"
        prop="seqkitResult.format"
        min-width="110"
      />
      <el-table-column label="type" prop="seqkitResult.type" min-width="110" />
      <el-table-column
        label="num_seqs"
        prop="seqkitResult.numSeqs"
        min-width="110"
        sortable
      />
      <el-table-column
        label="bases"
        prop="seqkitResult.sumLen"
        min-width="110"
        sortable
      />
      <el-table-column
        label="min_len"
        prop="seqkitResult.minLen"
        min-width="110"
      />
      <el-table-column
        label="avg_len"
        prop="seqkitResult.avgLen"
        min-width="110"
      />
      <el-table-column
        label="max_len"
        prop="seqkitResult.maxLen"
        min-width="110"
      />
      <el-table-column
        label="Q20(%)"
        prop="seqkitResult.q20"
        min-width="110"
        sortable
      />
      <el-table-column
        label="Q30(%)"
        prop="seqkitResult.q30"
        min-width="110"
        sortable
      />
      <el-table-column
        label="AvgQual"
        prop="seqkitResult.avgQual"
        min-width="110"
      />
      <el-table-column label="GC(%)" prop="seqkitResult.gc" min-width="110" />
      <el-table-column label="Operate" fixed="right" align="center" width="110">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.status === 'success'"
            content="View FastQC Report"
          >
            <svg-icon
              icon-class="review"
              class-name="download download-svg"
              @click="toReportPage(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip
            v-if="scope.row.status === 'failed'"
            content="View Error Log"
          >
            <svg-icon
              icon-class="review"
              class-name="download download-svg"
              @click="toErrorLogPage(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip v-if="scope.row.status === 'failed'" content="Retry">
            <svg-icon
              icon-class="retry"
              class-name="ml-05 download download-svg"
              @click="retryTask(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <!--          <el-tooltip content="Download FastQC Report">-->
          <!--            <svg-icon-->
          <!--              icon-class="download"-->
          <!--              class-name="download download-svg"-->
          <!--              @click="downloadReport(scope.row.dataNo)"-->
          <!--            ></svg-icon>-->
          <!--          </el-tooltip>-->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      :auto-scroll="false"
      @pagination="getDataList"
    />
  </div>
</template>
<script setup>
  import {
    defineEmits,
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    getDataQcDetail,
    getQcStatusStat,
    retryFastQCTask,
  } from '@/api/submission/audit';
  import {
    Failed,
    InfoFilled,
    Loading,
    Stopwatch,
    SuccessFilled,
  } from '@element-plus/icons-vue';
  import { filesize } from 'filesize';

  const { proxy } = getCurrentInstance();
  const emit = defineEmits();

  let props = defineProps({
    subNo: {
      type: String,
      required: true,
    },
    qcFinishFlag: {
      type: Boolean,
      required: true,
    },
  });

  onMounted(() => {
    getDataList();
  });

  const { subNo } = props;

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      subNo: '',
      dataNo: '',
      status: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'dataNo',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'dataNo', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  let statusStatInfo = ref({});

  function getDataList() {
    queryParams.value.subNo = subNo;
    getDataQcDetail(queryParams.value)
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
        getQcStatusStat(subNo).then(res => {
          statusStatInfo.value = res.data;
        });
      });
  }

  watch(
    statusStatInfo,
    newVal => {
      let successTask =
        newVal['ready'] === 0 &&
        newVal['queuing'] === 0 &&
        newVal['running'] === 0 &&
        newVal['failed'] === 0;
      emit('update:qcFinishFlag', successTask);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  let statusMap = {
    ready: {
      textCls: 'text-primary',
      icon: InfoFilled,
    },
    queuing: {
      textCls: 'text-warning',
      icon: Stopwatch,
    },
    running: {
      textCls: 'text-warning',
      icon: Loading,
    },
    success: {
      textCls: 'text-success',
      icon: SuccessFilled,
    },
    failed: {
      textCls: 'text-danger',
      icon: Failed,
    },
  };

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');
    getDataList();
  }

  function toReportPage(dataNo) {
    window.open(`${import.meta.env.VITE_APP_BASE_API}/app/fastqc/${dataNo}`);
  }

  function toErrorLogPage(dataNo) {
    window.open(
      `${import.meta.env.VITE_APP_BASE_API}/app/fastqc/errorLog/${dataNo}`,
    );
  }

  function retryTask(no) {
    proxy.$modal.confirm(`Are you sure to retry task ${no}?`).then(() => {
      retryFastQCTask({
        dataNos: no,
      })
        .then(response => {
          proxy.$modal.alertSuccess('Task has been resubmitted');
          getDataList();
        })
        .finally(() => {});
    });
  }

  function downloadReport(dataNo) {
    proxy.download(
      `/app/fastqc/downloadReport/${dataNo}`,
      null,
      `${dataNo}_FastQC_Report.zip`,
    );
  }
</script>

<style lang="scss" scoped>
  .downloadTable {
    :deep(.el-table td.el-table__cell div) {
      display: flex;
      align-items: center;
    }
  }

  .download-svg {
    width: 20px;
    height: 20px;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }

  :deep(.el-table td.el-table__cell:last-child div) {
    justify-content: center !important;
  }

  .download {
    width: 20px;
    cursor: pointer;
  }
</style>
