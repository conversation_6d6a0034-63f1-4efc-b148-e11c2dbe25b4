{"name": "node", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@handsontable/vue3": "^14.3.0", "animate.css": "^4.1.1", "axios": "^1.6.8", "crypto-js": "^4.2.0", "echarts": "^5.5.0", "el-table-infinite-scroll": "^3.0.3", "element-plus": "^2.7.3", "file-saver": "^2.0.5", "handsontable": "^14.3.0", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.27", "vue-i18n": "^9.13.1", "vue-router": "^4.3.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "4.6.2", "@vue/compiler-sfc": "3.4.27", "@vue/eslint-config-standard": "^8.0.1", "eslint": "8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-html": "^8.1.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "sass": "^1.77.1", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "4.5.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "^2.0.1"}}