package org.biosino.upload.repository;

import org.biosino.common.mongo.entity.Submission;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubmissionRepository extends MongoRepository<Submission, String>, SubmissionCustomRepository {

    Optional<Submission> findTopBySubNo(String subNo);

    List<Submission> findAllBySubNoIn(Collection<String> subNos);
}
