package org.biosino.esindex.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.biosino.esindex.service.IndexScheduledService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * node 索引控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/nodeES")
public class NodeEsController extends BaseController {

    private final IndexScheduledService indexScheduledService;

    @Value("${app.api-token}")
    private String apiToken;

    public void checkApi(String token) {
        String configToken = this.apiToken;
        if (StrUtil.isBlank(configToken)) {
            configToken = "79cf30ac381fca234820fc673d3cc0b6qOVaINfjXWiTg8Ow4GX6w28wA";
        }
        if (!configToken.equals(token)) {
            throw new ServiceException("非法的token");
        }
    }

    /**
     * 创建所有browse索引
     *
     * @return
     */
    @GetMapping("/createIndex")
    public R<Boolean> createIndex(Boolean isIncremental, String token) {
        try {
            checkApi(token);
            indexScheduledService.createIndex();
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node索引出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新指定项目所有es数据
     *
     * @param projectNo
     * @return
     */
    @GetMapping("/createProjectIndex/{projectNo}")
    public R<Boolean> createProjectIndex(@PathVariable(value = "projectNo") String projectNo) {
        try {
            indexScheduledService.createProjectIndex(projectNo);
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node索引出错，项目编号：" + projectNo, e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新指定项目所有es数据
     *
     * @param analysisNo
     * @return
     */
    @GetMapping("/createAnalysisIndex/{analysisNo}")
    public R<Boolean> createAnalysisIndex(@PathVariable(value = "analysisNo") String analysisNo) {
        try {
            indexScheduledService.createAnalysisIndex(analysisNo);
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node索引出错，Analysis编号：" + analysisNo, e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 创建关联数据ES索引数据
     */
    @GetMapping("/createRelatedNodeEs")
    public R<Boolean> createRelatedNodeEs(final String token) {
        try {
            checkApi(token);
            indexScheduledService.createRelatedNodeEs();
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node关联数据es索引出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更加data编号同步es关联数据
     */
    @GetMapping("/createRelatedDataNodeEs/{dataNo}")
    public R<Boolean> createRelatedDataNodeEs(@PathVariable(value = "dataNo") String dataNo) {
        try {
            indexScheduledService.createRelatedDataNodeEs(dataNo);
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node关联数据es索引出错, dataNo: " + dataNo, e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更加data编号集合同步es关联数据
     */
    @PostMapping("/createRelatedDataNodeEsByNos")
    public R<Boolean> createRelatedDataNodeEsByNos(@RequestBody Set<String> dataNos) {
        try {
            indexScheduledService.createRelatedDataNodeEsByNos(dataNos);
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node关联数据es索引出错, dataNo个数: " + CollUtil.size(dataNos), e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 保存从dms获取的Taxonomy数据
     */
    @PostMapping("/saveNewTaxonomy")
    public R<Boolean> saveNewTaxonomy(@RequestBody final TaxonomyNodeDTO dto) {
        return R.fail("Api已废弃");
        /*try {
            indexScheduledService.saveNewTaxonomy(dto);
            return R.ok(true);
        } catch (Exception e) {
            log.error("保存从dms获取的Taxonomy数据出错: ", e);
            return R.fail(e.getMessage());
        }*/
    }

    /**
     * 创建Taxonomy等ES索引
     */
    @GetMapping("/createOtherNodeEs")
    public R<Boolean> createOtherNodeEs(final String token) {
        try {
            checkApi(token);
            indexScheduledService.createOtherNodeEs();
            return R.ok(true);
        } catch (Exception e) {
            log.error("生成node关联数据es索引出错", e);
            return R.fail(e.getMessage());
        }
    }
}
