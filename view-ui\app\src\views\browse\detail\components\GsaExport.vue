<template>
  <div v-loading="loading">
    <el-dialog
      v-model="showDialog"
      title="Export GSA data"
      width="800"
      class="radius-14 security"
    >
      <el-form ref="formRef" :rules="rules" :model="form" label-width="auto">
        <el-form-item label="Security" prop="security">
          <el-checkbox-group v-model="form.security">
            <el-checkbox
              v-for="item in security"
              :key="item"
              :value="item"
              :label="item"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="Experiment type" prop="expTypes">
          <el-checkbox-group
            v-if="expTypes.length !== 0"
            v-model="form.expTypes"
            @change="loadExpInfo"
          >
            <el-checkbox
              v-for="item in expTypes"
              :key="item"
              :value="item"
              :label="item"
            ></el-checkbox>
          </el-checkbox-group>
          <el-text v-else type="danger"
            >GSA currently does not support non sequencing data submission.
          </el-text>
        </el-form-item>
        <el-form-item label="Experiment ID" prop="expNos">
          <template #label><label>Experiment ID</label></template>
          <el-input
            v-if="form.expTypes.length !== 0"
            v-model="searchKeyword"
            placeholder="Search for experiment ID or name"
            prefix-icon="Search"
            clearable
            style="margin-bottom: 10px"
          />
          <el-table
            v-if="form.expTypes.length !== 0"
            ref="expInfoTableRef"
            v-loading="tableLoading"
            :data="filteredExpInfoTable"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            border
            max-height="400"
            tooltip-effect="dark"
            :row-key="row => row.expNo"
            :default-sort="{ prop: 'expNo', order: 'ascending' }"
            @selection-change="selectionDataChange"
          >
            <el-table-column
              :reserve-selection="true"
              type="selection"
            ></el-table-column>
            <el-table-column
              label="Experiment ID"
              prop="expNo"
              sortable
            ></el-table-column>
            <el-table-column
              label="Experiment Name"
              prop="name"
              sortable
            ></el-table-column>
          </el-table>
          <el-text v-else type="danger"
            >Please select at least one experiment type.
          </el-text>
        </el-form-item>
        <el-form-item label="Subject type" prop="subjectType">
          <el-radio-group
            v-if="subjectTypes.length !== 0"
            v-model="form.subjectType"
            @change="
              () => {
                form.templateName = typeRelaRules[form.subjectType][0];
              }
            "
          >
            <!--            <el-radio
                          v-for="item in subjectTypes"
                          :key="item"
                          :value="item"
                          :label="item"
                        ></el-radio>-->
            <el-radio
              v-for="(item, idx) in subjectTypes"
              :key="'subject_type_' + idx"
              :value="item"
              :label="item"
            ></el-radio>
          </el-radio-group>
          <el-text v-else type="danger"
            >There is no exportable Subject Type under the current project.
          </el-text>
        </el-form-item>
        <el-form-item label="GSA Template" prop="gsaTemplate">
          <el-radio-group v-model="form.templateName">
            <el-radio
              v-for="(item, idx) in typeRelaRules[form.subjectType]"
              :key="'template_name_' + idx"
              :value="item"
              :label="item"
            ></el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            :disabled="
              expTypes.length === 0 &&
              security.length === 0 &&
              subjectTypes.length === 0
            "
            class="radius-8"
            type="primary"
            @click="exportGsaData"
          >
            Export
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="showDialog = false"
            >Cancel
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance, ref, watch } from 'vue';
  import {
    getProjectDataSecurity,
    getProjectExpInfo,
    getProjectExpTypes,
    getProjectSubjectTypes,
  } from '@/api/app/project';

  const { proxy } = getCurrentInstance();

  const type = ref('');
  const typeNo = ref('');
  let loading = ref(false);
  let tableLoading = ref(false);
  let expTypes = ref([]);
  let security = ref([]);
  let subjectTypes = ref([]);
  let showDialog = ref(false);
  let expInfoTable = ref([]);
  let searchKeyword = ref('');

  const form = ref({
    projNo: '',
    security: [],
    expNos: [],
    expTypes: [],
    subjectType: 'Human',
    templateName: 'Human_General',
  });
  const rules = ref({
    security: [
      {
        required: true,
        message: 'Please select security',
      },
    ],
    expTypes: [
      {
        required: true,
        message: 'Please select experiment type',
      },
    ],
    expNos: [
      {
        required: true,
        message: 'Please select experiment',
      },
    ],
    subjectType: [
      {
        required: true,
        message: 'Please select subject type',
      },
    ],
  });

  const typeRelaRules = ref({
    'Human': [
      'Solid_Tumor',
      'Hematologic_Tumor',
      'Autoimmune',
      'Human_General',
    ],
    'Animalia': ['Model_animal'],
    'Cell line': ['Human_Cellline', 'Model_animal', 'Plant'],
    'Pathogen': [
      'Pathogen_Clinicla or host-associated',
      'Pathogen_Environmental, food orther',
      'Human_Clinical_pathogen',
    ],
    'Environment host': [
      'Human_associated_metagenome',
      'Metagenome or environmental',
    ],
    'Environment non-host': [
      'MetagenomeMISMS.me_soil',
      'MetagenomeMISMS.me_water',
      'Metagenome or environmental',
    ],
    'Microbe': ['Microbe', 'Virus'],
  });

  // 根据搜索关键词过滤表格数据
  const filteredExpInfoTable = computed(() => {
    if (!searchKeyword.value) return expInfoTable.value;
    return expInfoTable.value.filter(
      item =>
        item.expNo.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    );
  });

  function init(optionType, optionNo) {
    type.value = optionType;
    typeNo.value = optionNo;
    form.value.projNo = optionNo;
    showDialog.value = true;
    loading.value = true;

    let promise = getProjectExpTypes(typeNo.value).then(response => {
      expTypes.value = response.data;
    });
    let promise1 = getProjectSubjectTypes(typeNo.value).then(response => {
      subjectTypes.value = response.data;
    });
    let promise2 = getProjectDataSecurity(typeNo.value).then(response => {
      security.value = response.data;
    });

    Promise.all([promise, promise1, promise2]).then(() => {
      loading.value = false;
      form.value.subjectType = subjectTypes.value[0];
      form.value.templateName = typeRelaRules.value[form.value.subjectType][0];
    });
  }

  function loadExpInfo() {
    tableLoading.value = true;
    getProjectExpInfo(form.value)
      .then(response => {
        expInfoTable.value = response.data;
      })
      .finally(() => {
        tableLoading.value = false;
      });
  }

  const selectedDataRows = ref([]);

  function selectionDataChange(selection) {
    selectedDataRows.value = selection;
  }

  // 监听selectedDataRows的变化
  watch(
    selectedDataRows,
    newVal => {
      form.value.expNos = [];
      newVal.forEach(item => {
        form.value.expNos.push(item.expNo);
      });
    },
    {
      deep: true,
    },
  );

  function exportGsaData() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.download(
          '/app/project/exportGsaData',
          { ...form.value },
          `${typeNo.value}_GSA_${form.value.subjectType}_${form.value.templateName}.zip`,
        );
      }
    });
  }

  defineExpose({
    init,
  });
</script>

<style scoped lang="scss"></style>
