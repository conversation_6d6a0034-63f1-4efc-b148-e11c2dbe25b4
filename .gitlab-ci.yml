variables:
  GIT_DEPTH: "3"

stages:
  - build
  - deploy
# 打包jar
mvn_build_job:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/adoptopenjdk.maven-openjdk8:latest
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - mvn clean package -Dmaven.test.skip=true -P dev-test
    - mkdir -p /node-cloud/node-auth
    - mkdir -p /node-cloud/node-fastqc
    - mkdir -p /node-cloud/node-gateway
    - mkdir -p /node-cloud/node-es
    - mkdir -p /node-cloud/node-member
    - mkdir -p /node-cloud/node-notification
    - mkdir -p /node-cloud/node-sftp
    - mkdir -p /node-cloud/node-task
    - mkdir -p /node-cloud/node-system
    - mkdir -p /node-cloud/node-upload
    - mkdir -p /node-cloud/node-app
    - mkdir -p /node-cloud/node-qc
    - mkdir -p /node-cloud/node-download
    - mkdir -p /node-cloud/node-job
    - mkdir -p /node-cloud/node-api-service
    - mv ./node-auth/target/node-auth.jar /node-cloud/node-auth/
    - mv ./node-gateway/target/node-gateway.jar /node-cloud/node-gateway/
    - mv ./node-fastqc/target/node-fastqc.jar /node-cloud/node-fastqc/
    # es 有一点不同，目录和jar包名称不一致
    - mv ./node-modules/node-es-index/target/node-es-index.jar /node-cloud/node-es/
    - mv ./node-modules/node-member/target/node-member.jar /node-cloud/node-member/
    - mv ./node-modules/node-notification/target/node-notification.jar /node-cloud/node-notification/
    - mv ./node-modules/node-sftp/target/node-sftp.jar /node-cloud/node-sftp/
    - mv ./node-modules/node-task/target/node-task.jar /node-cloud/node-task/
    - mv ./node-modules/node-system/target/node-system.jar /node-cloud/node-system/
    - mv ./node-modules/node-upload/target/node-upload.jar /node-cloud/node-upload/
    - mv ./node-modules/node-app/target/node-app.jar /node-cloud/node-app/
    - mv ./node-modules/node-qc/target/node-qc.jar /node-cloud/node-qc/
    - mv ./node-modules/node-download/target/node-download.jar /node-cloud/node-download/
    - mv ./node-modules/node-job/target/node-job.jar /node-cloud/node-job/
    - mv ./node-modules/node-api-service/target/node-api-service.jar /node-cloud/node-api-service/

# 打包前端 APP
nodejs_build_job_app:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/node:16.20.2-alpine3.18
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - rm -rf /node-cloud/node-ui/html/keep-node2
    - mkdir -p /node-cloud/node-ui/html/keep-node2
    - cd view-ui/app
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - npm run build:stage
    - cp -r ./keep-node2/* /node-cloud/node-ui/html/keep-node2

# 打包前端 Admin
nodejs_build_job_admin:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/node:16.20.2-alpine3.18
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - rm -rf /node-cloud/node-ui/html/keep-node-admin
    - mkdir -p /node-cloud/node-ui/html/keep-node-admin
    - cd view-ui/admin
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - npm run build:stage
    - cp -r ./keep-node-admin/* /node-cloud/node-ui/html/keep-node-admin

# 打包前端 QC
nodejs_build_job_qc:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/node:16.20.2-alpine3.18
  stage: build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    - rm -rf /node-cloud/node-ui/html/keep-node-qc
    - mkdir -p /node-cloud/node-ui/html/keep-node-qc
    - cd view-ui/qc
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - npm run build:stage
    - cp -r ./keep-node-qc/* /node-cloud/node-ui/html/keep-node-qc

# 部署
deploy_k8s_job:
  image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/bitnami.kubectl:1.20.15
  stage: deploy
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
  script:
    # 重启
    - kubectl -n xuqw rollout restart deployment node-auth node-gateway node-es node-notification node-sftp node-system node-task node-upload node-app node-qc node-download node-nginx node-job node-fastqc2 node-member node-api-service
