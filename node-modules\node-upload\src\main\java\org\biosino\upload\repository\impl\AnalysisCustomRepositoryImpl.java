package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.*;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.SelectQueryDTO;
import org.biosino.upload.repository.AnalysisCustomRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR> Li
 * @date 2024/1/13
 */
@RequiredArgsConstructor
public class AnalysisCustomRepositoryImpl implements AnalysisCustomRepository {

    private final MongoTemplate mongoTemplate;

    private Criteria auditInitBaseQuery(String creator) {
        return Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
    }


    @Override
    public Analysis validateAnalysisName(String creator, String analysisNo, String name) {
        Query query = new Query();
        Criteria criteria = Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("name").is(name)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(analysisNo)) {
            // 如果有projNo，说明是编辑数据时查重，需要排除自身
            criteria.andOperator(Criteria.where("anal_no").ne(analysisNo));
        }
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Analysis.class);
    }

    @Override
    public Page findAccessableSelectPage(SelectQueryDTO queryDTO, Class clazz) {
        Query query = getAccessableQuery(queryDTO);
        long total = mongoTemplate.count(query, clazz);
        query.with(queryDTO.getPageable());
        List content = mongoTemplate.find(query, clazz);
        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    public Query getAccessableQuery(SelectQueryDTO queryVO) {

        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));

        // Data数据查询规则：自己得到授权的+自己的全部正式数据
        if (AuthorizeType.data.name().equals(queryVO.getType())) {
            if (CollUtil.isNotEmpty(queryVO.getShareNos())) {
                criteriaList.add(new Criteria().orOperator(Criteria.where("creator").is(queryVO.getCreator())
                                .and("security").in(SecurityEnum.includeAllSecurity()),
                        Criteria.where(queryVO.getIdField()).in(queryVO.getShareNos())));
            } else {
                criteriaList.add(Criteria.where("creator").is(queryVO.getCreator())
                        .and("security").in(SecurityEnum.includeAllSecurity()));
            }
        } else {
            criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
            if (CollUtil.isNotEmpty(queryVO.getShareNos())) {
                criteriaList.add(new Criteria().orOperator(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()),
                        Criteria.where("creator").is(queryVO.getCreator()).and("audited").is(AuditEnum.audited.name()),
                        Criteria.where(queryVO.getIdField()).in(queryVO.getShareNos())));
            } else {
                // Project、Experiment、Sample、Run、Analysis等数据查询：可见的数据 或 用户自己全部正式数据
                criteriaList.add(new Criteria().orOperator(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()),
                        Criteria.where("creator").is(queryVO.getCreator()).and("audited").is(AuditEnum.audited.name())));
            }
        }

        if (StrUtil.isNotBlank(queryVO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryVO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where(queryVO.getIdField()).regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }
        return new Query(new Criteria().andOperator(criteriaList)).with(Sort.by(Sort.Direction.DESC, "submission_date"));
    }


    @Override
    public Analysis findByAnalNo(String analysisNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analysisNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Analysis.class);
    }

    @Override
    public List<Analysis> findAllByCreator(String creator) {
        Criteria criteria = Criteria.where("creator").is(creator);
        criteria.andOperator(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()),
                Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));

        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));

        return mongoTemplate.find(query, Analysis.class);
    }

    @Override
    public boolean existAccessableNo(String no, TypeInformation typeInformation, String creator) {
        Class clazz = typeInformation.getClazz();

        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where(typeInformation.getMongoField()).is(no));

        if (clazz == Data.class) {
            criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
            criteriaList.add(new Criteria().orOperator(Criteria.where("security").is(SecurityEnum._public.name()),
                    Criteria.where("creator").is(creator)));

        } else {
            criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

            criteriaList.add(new Criteria().orOperator(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()),
                    Criteria.where("creator").is(creator)));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        return mongoTemplate.exists(query, clazz);
    }

    @Override
    public Set<String> getAccessableNos(Collection<String> nos, TypeInformation typeInformation, String creator) {
        Class clazz = typeInformation.getClazz();

        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where(typeInformation.getMongoField()).in(nos));

        if (clazz == Data.class) {
            criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
            criteriaList.add(new Criteria().orOperator(Criteria.where("security").is(SecurityEnum._public.name()),
                    Criteria.where("creator").is(creator)));

        } else {
            criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
            criteriaList.add(new Criteria().orOperator(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()),
                    Criteria.where("creator").is(creator)));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        List<Object> list = mongoTemplate.find(query, clazz);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newHashSet();
        }
        List<String> matchNos = list.stream().map(x -> (String) ReflectUtil.getFieldValue(x, typeInformation.getField()))
                .collect(Collectors.toList());
        return CollUtil.intersectionDistinct(nos, matchNos);
    }

    @Override
    public Map<String, String> findNameByNamesInAndAnalNoNotIn(Collection<String> names, String memberId, List<String> analNos) {
        Criteria criteria = auditInitBaseQuery(memberId);
        criteria.and("name").in(names);
        if (CollUtil.isNotEmpty(analNos)) {
            criteria.and("anal_no").nin(analNos);
        }
        Query query = new Query(criteria);
        query.fields().include("name").include("sub_no");
        List<Analysis> analysisList = mongoTemplate.find(query, Analysis.class);
        return analysisList.stream().collect(Collectors.toMap(Analysis::getName, Analysis::getSubNo, (existingValue, newValue) -> existingValue));
    }

    @Override
    public Page<Analysis> findAllByPage(ArchivedSelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("creator").is(queryDTO.getCreator());
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("anal_no").regex(pattern),
                    Criteria.where("name").regex(pattern));
        }

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("name").include("anal_no");
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));

        // 统计
        long count = mongoTemplate.count(query, Project.class);
        // 分页
        query.with(queryDTO.getPageable());
        // 查询
        List<Analysis> content = mongoTemplate.find(query, Analysis.class);
        return new PageImpl<>(content, queryDTO.getPageable(), count);
    }

    @Override
    public boolean existsByCreatorAndNo(String analNo, String memberId) {
        Criteria criteria = Criteria.where("creator").is(memberId)
                .and("anal_no").is(analNo)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.exists(new Query(criteria), Analysis.class);
    }

    @Override
    public Map<String, Boolean> existsByCreatorAndNos(Collection<String> analNos, String memberId) {
        Criteria criteria = Criteria.where("creator").is(memberId)
                .and("anal_no").in(analNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        List<String> existAnalNos = mongoTemplate.findDistinct(query, "anal_no", Analysis.class, String.class);
        Map<String, Boolean> result = new LinkedHashMap<>();
        for (String no : analNos) {
            result.put(no, existAnalNos.contains(no));
        }
        return result;
    }

    @Override
    public Map<String, Boolean> existsByCreatorAndNos(List<String> analNos, String memberId) {
        Criteria criteria = Criteria.where("creator").is(memberId)
                .and("anal_no").in(analNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        List<String> existAnalNos = mongoTemplate.findDistinct(query, "anal_no", Analysis.class, String.class);
        Map<String, Boolean> result = new LinkedHashMap<>();
        for (String analNo : analNos) {
            result.put(analNo, existAnalNos.contains(analNo));
        }
        return result;
    }

    @Override
    public boolean existAuditInitAnalByAnalName(String analName, String creator) {
        return mongoTemplate.exists(new Query(Criteria.where("name").is(analName)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Analysis.class);
    }

    @Override
    public Map<String, Boolean> existAuditInitAnalByAnalNames(List<String> analNames, String creator) {
        Query query = new Query(Criteria.where("name").in(analNames)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("ownership").is(OwnershipEnum.self_support.getDesc()));
        List<String> existAnalNos = mongoTemplate.findDistinct(query, "name", Analysis.class, String.class);
        Map<String, Boolean> result = new LinkedHashMap<>();
        for (String analName : analNames) {
            result.put(analName, existAnalNos.contains(analName));
        }
        return result;
    }

    @Override
    public Analysis findAuditInitAnalByAnalName(String analName, String creator) {
        return mongoTemplate.findOne(new Query(Criteria.where("name").is(analName)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Analysis.class);
    }

    @Override
    public List<Analysis> findAllAuditInitAnalByAnalNames(List<String> analNames, String creator) {
        return mongoTemplate.find(new Query(Criteria.where("name").in(analNames)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Analysis.class);
    }

    @Override
    public void deleteTempByNosAndCreator(Collection<String> analNos, String creator) {
        if (CollUtil.isEmpty(analNos) || creator == null) {
            return;
        }
        List<Analysis> analysisList = mongoTemplate.find(Query.query(
                        Criteria.where(tempKey("anal_no")).in(analNos).and(tempKey("creator")).is(creator)
                                .and(tempKey("visible_status")).in(VisibleStatusEnum.includeExistsVisibleStatus()))
                , Analysis.class);

        List<ObjectId> deleteIds = new ArrayList<>();
        List<ObjectId> deleteTempDataIds = new ArrayList<>();

        for (Analysis analysis : analysisList) {
            if (AuditEnum.init.name().equals(analysis.getAudited())) {
                deleteIds.add(new ObjectId(analysis.getId()));
            } else {
                Analysis tempData = analysis.getTempData();
                if (tempData != null) {
                    deleteTempDataIds.add(new ObjectId(analysis.getId()));
                }
            }
        }
        // 删除第一次提交的暂存数据
        mongoTemplate.remove(new Query(Criteria.where("_id").in(deleteIds)), Analysis.class);
        // 删除老数据编辑时提交的暂存数据
        mongoTemplate.updateMulti(new Query(Criteria.where("_id").in(deleteTempDataIds)),
                new Update().unset("tempData"), Analysis.class);
    }

    @Override
    public Map<String, String> findTempNameByNamesInAndNoNotIn(Collection<String> names, String creator, Collection<String> nos) {
        if (CollUtil.isEmpty(names) || creator == null) {
            return new HashMap<>();
        }
        Criteria criteria = Criteria.where(tempKey("creator")).is(creator)
                .and(tempKey("name")).in(names)
                .and(tempKey("ownership")).is(OwnershipEnum.self_support.getDesc())
                .and(tempKey("audited")).is(AuditEnum.init.name())
                .and(tempKey("visible_status")).in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (CollUtil.isNotEmpty(nos)) {
            criteria.and(tempKey("anal_no")).nin(nos).and(tempKey("used_ids")).nin(nos);
        }
        Query query = new Query(criteria);
        List<Analysis> analysisList = mongoTemplate.find(query, Analysis.class);
        final Map<String, String> tempNames = new HashMap<>();
        for (Analysis analysis : analysisList) {
            Analysis tempData = analysis.getTempData();
            if (tempData != null && !tempData.getAnalysisNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                tempNames.put(tempData.getName(), tempData.getSubNo());
            }
        }
        return tempNames;
    }

    @Override
    public Map<String, Analysis> findTempNameByNamesInAndNoIn(Collection<String> names, String creator, Collection<String> nos) {
        if (CollUtil.isEmpty(names) || creator == null) {
            return new HashMap<>();
        }
        Criteria criteria = Criteria.where(tempKey("creator")).is(creator)
                .and(tempKey("name")).in(names)
                .and(tempKey("ownership")).is(OwnershipEnum.self_support.getDesc())
                .and(tempKey("audited")).is(AuditEnum.init.name())
                .and(tempKey("visible_status")).in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query;
        if (CollUtil.isNotEmpty(nos)) {
            query = new Query(new Criteria().andOperator(criteria, new Criteria().orOperator(
                    Criteria.where(tempKey("anal_no")).in(nos),
                    Criteria.where(tempKey("used_ids")).in(nos))));
        } else {
            query = new Query(criteria);
        }
        List<Analysis> analysisList = mongoTemplate.find(query, Analysis.class);
        Map<String, Analysis> tempNames = new HashMap<>();
        for (Analysis analysis : analysisList) {
            Analysis tempData = analysis.getTempData();
            if (tempData != null && !tempData.getAnalysisNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                tempNames.put(tempData.getName(), analysis);
            }
        }
        return tempNames;
    }

    @Override
    public void updateToDeleteAllByAnalysisNoIn(List<String> analNos) {
        if (CollUtil.isEmpty(analNos)) {
            return;
        }
        Query query = new Query(Criteria.where("anal_no").in(analNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name());
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Analysis.class);
    }

    @Override
    public Optional<Analysis> findFirstByAnalysisNo(String analysisNo) {
        if (StrUtil.isBlank(analysisNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("anal_no").is(analysisNo),
                Criteria.where("used_ids").in(analysisNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Analysis analysis = mongoTemplate.findOne(query, Analysis.class);
        return Optional.ofNullable(analysis);
    }

    @Override
    public List<Analysis> findAllByAnalysisNoIn(Collection<String> nos) {
        if (CollUtil.isEmpty(nos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("anal_no").in(nos),
                Criteria.where("used_ids").in(nos)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Analysis.class);
    }
}
