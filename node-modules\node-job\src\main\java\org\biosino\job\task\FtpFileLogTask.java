package org.biosino.job.task;

import lombok.extern.slf4j.Slf4j;
import org.biosino.job.service.FtpFileLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Slf4j
@Component("ftpFileLogTask")
public class FtpFileLogTask {
    @Autowired
    private FtpFileLogService ftpFileLogService;


    /**
     * 清理状态是uploading的记录且updateTime是20天及以前的记录，将状态修改为error
     */
    public void cleanUploadingRecords() {
        log.info("清理长期是uploading的ftp_file_log记录");
        Long count = ftpFileLogService.cleanUploadingRecords();
        log.info("清理完成，共清理{}条ftp_file_log记录", count);
    }
}
