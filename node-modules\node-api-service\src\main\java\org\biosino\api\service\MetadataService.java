package org.biosino.api.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.api.dto.ApiDataSearchDTO;
import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.api.repository.*;
import org.biosino.api.vo.metadata.*;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.mongo.entity.*;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/10/17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MetadataService {

    private final ProjectRepository projectRepository;

    private final ExperimentRepository experimentRepository;

    private final SampleRepository sampleRepository;

    private final RunRepository runRepository;

    private final AnalysisRepository analysisRepository;

    private final DataRepository dataRepository;

    private final RemoteDataService remoteDataService;

    private final PublishRepository publishRepository;

    public List<ProjectVO> listProject(MetadataSearchDTO dto) {

        Page<Project> page = projectRepository.findPage(dto);

        List<Project> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> projNos = list.stream().map(Project::getProjectNo).collect(Collectors.toList());

        List<Publish> publishList = publishRepository.findByTypeAndTypeIdInAndAuditedAndDeleted(AuthorizeType.project.name(), projNos, AuditEnum.audited.name(), false);

        Map<String, List<Publish>> typeIdToPublishes = publishList.stream().collect(Collectors.groupingBy(Publish::getTypeId));

        List<ProjectVO> result = BeanUtil.copyToList(list, ProjectVO.class);

        for (ProjectVO item : result) {
            List<Publish> publishes = typeIdToPublishes.get(item.getProjectNo());
            if (CollUtil.isNotEmpty(publishes)) {
                item.setReferences(BeanUtil.copyToList(publishes, PublishVO.class));
            }
        }

        return result;
    }

    public List<ExperimentVO> listExperiment(MetadataSearchDTO dto) {

        Page<Experiment> page = experimentRepository.findPage(dto);

        List<Experiment> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<String> expNos = list.stream().map(Experiment::getExpNo).collect(Collectors.toList());

        List<Publish> publishList = publishRepository.findByTypeAndTypeIdInAndAuditedAndDeleted(AuthorizeType.experiment.name(), expNos, AuditEnum.audited.name(), false);

        Map<String, List<Publish>> typeIdToPublishes = publishList.stream().collect(Collectors.groupingBy(Publish::getTypeId));

        List<ExperimentVO> result = BeanUtil.copyToList(list, ExperimentVO.class);

        for (ExperimentVO item : result) {
            List<Publish> publishes = typeIdToPublishes.get(item.getExpNo());
            if (CollUtil.isNotEmpty(publishes)) {
                item.setReferences(BeanUtil.copyToList(publishes, PublishVO.class));
            }
        }
        return result;
    }

    public List<SampleVO> listSample(MetadataSearchDTO dto) {
        Page<Sample> page = sampleRepository.findPage(dto);

        List<Sample> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> sapNos = list.stream().map(Sample::getSapNo).collect(Collectors.toList());

        List<Publish> publishList = publishRepository.findByTypeAndTypeIdInAndAuditedAndDeleted(AuthorizeType.sample.name(), sapNos, AuditEnum.audited.name(), false);

        Map<String, List<Publish>> typeIdToPublishes = publishList.stream().collect(Collectors.groupingBy(Publish::getTypeId));

        List<SampleVO> result = BeanUtil.copyToList(list, SampleVO.class);

        for (SampleVO item : result) {
            List<Publish> publishes = typeIdToPublishes.get(item.getSapNo());
            if (CollUtil.isNotEmpty(publishes)) {
                item.setReferences(BeanUtil.copyToList(publishes, PublishVO.class));
            }
        }
        return result;
    }

    public List<RunVO> listRun(MetadataSearchDTO dto) {
        Page<Run> page = runRepository.findPage(dto);

        List<Run> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, RunVO.class);
    }

    public List<DataVO> listData(MetadataSearchDTO dto) {
        Page<Data> page = dataRepository.findPage(dto);

        List<Data> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, DataVO.class);
    }

    public List<DataVO> listDeleteData(MetadataSearchDTO dto) {
        Page<Data> page = dataRepository.findDeletePage(dto);

        List<Data> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, DataVO.class);
    }

    public List<RelatedDataDTO> listRelatedData(ApiDataSearchDTO dto) {
        RelatedEsSearchVO searchVO = new RelatedEsSearchVO();
        BeanUtil.copyProperties(dto, searchVO);
        if (StrUtil.isNotBlank(dto.getProjNo())) {
            searchVO.setProjNos(CollUtil.newArrayList(dto.getProjNo()));
        }

        if (StrUtil.isNotBlank(dto.getExpNo())) {
            searchVO.setExpNos(CollUtil.newArrayList(dto.getExpNo()));
        }

        if (StrUtil.isNotBlank(dto.getSapNo())) {
            searchVO.setSapNos(CollUtil.newArrayList(dto.getSapNo()));
        }

        if (StrUtil.isNotBlank(dto.getRunNo())) {
            searchVO.setRunNos(CollUtil.newArrayList(dto.getRunNo()));
        }

        if (StrUtil.isNotBlank(dto.getAnalNo())) {
            searchVO.setAnalNos(CollUtil.newArrayList(dto.getAnalNo()));
        }

        if (StrUtil.isNotBlank(dto.getDataNo())) {
            searchVO.setDataNos(CollUtil.newArrayList(dto.getDataNo()));
        }

        R<EsPageInfo<RelatedDataDTO>> r = remoteDataService.findRelatedDataPage(searchVO, SecurityConstants.INNER);

        EsPageInfo<RelatedDataDTO> data = r.getData();
        List<RelatedDataDTO> list = data.getList();
        return list;
    }

    public List<AnalysisVO> listAnalysis(MetadataSearchDTO dto) {

        Page<Analysis> page = analysisRepository.findPage(dto);

        List<Analysis> list = page.getContent();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<String> analNos = list.stream().map(Analysis::getAnalysisNo).collect(Collectors.toList());

        List<Publish> publishList = publishRepository.findByTypeAndTypeIdInAndAuditedAndDeleted(AuthorizeType.analysis.name(), analNos, AuditEnum.audited.name(), false);

        Map<String, List<Publish>> typeIdToPublishes = publishList.stream().collect(Collectors.groupingBy(Publish::getTypeId));

        List<AnalysisVO> result = BeanUtil.copyToList(list, AnalysisVO.class);

        for (AnalysisVO item : result) {
            List<Publish> publishes = typeIdToPublishes.get(item.getAnalysisNo());
            if (CollUtil.isNotEmpty(publishes)) {
                item.setReferences(BeanUtil.copyToList(publishes, PublishVO.class));
            }
        }
        return result;
    }
}
