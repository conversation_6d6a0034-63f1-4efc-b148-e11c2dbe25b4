package org.biosino.sftp.authentication;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.model.Member;
import cn.hutool.crypto.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.sshd.server.auth.AsyncAuthException;
import org.apache.sshd.server.auth.password.PasswordAuthenticator;
import org.apache.sshd.server.auth.password.PasswordChangeRequiredException;
import org.apache.sshd.server.session.ServerSession;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * <p>
 * 用户认证
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SFtpPasswordAuthenticator implements PasswordAuthenticator {

    private final RemoteMemberService remoteMemberService;

    @SneakyThrows
    @Override
    public boolean authenticate(String username, String password, ServerSession session)
            throws PasswordChangeRequiredException, AsyncAuthException {

        if (StringUtils.isAnyBlank(username, password)) {
            log.debug("username or password is empty");
            return false;
        }

        // 请求bmdc获取用户信息
        R<MemberDTO> ftpUser = remoteMemberService.getMemberInfoByEmail(username, "FtpUser", SecurityConstants.INNER);
        MemberDTO member = ftpUser.getData();

        if (member == null) {
            log.warn("User[{}] login error, username not found", username);
            return false;
        }

        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        // 比对password前需要对password进行md5加密
        String pwd = SecureUtil.md5(password);
        if (!bCryptPasswordEncoder.matches(pwd, member.getPassword())) {
            log.warn("User[{}] login error, username or password error", username);
            return false;
        }

        MemberHolder memberHolder = MemberHolder.generate(member);
        if (!memberHolder.isEnable()) {
            log.warn("User[{}] login error, account status is not enable", username);
            return false;
        }

        Path rootPath = Paths.get(memberHolder.getHomeDirectory());
        // 登录成功后创建用户目录
        if (!Files.exists(rootPath)) {
            Files.createDirectories(rootPath);
        }

        session.setAttribute(MemberHolder.MEMBER_ATTRIBUTE_KEY, memberHolder);

        log.debug("User {} login success.", username);

        return true;
    }

}
