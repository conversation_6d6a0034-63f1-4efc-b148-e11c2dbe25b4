import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import useUserStore from '@/store/modules/user';
import useRouteStore from '@/store/modules/route';
import { isStrBlank } from '@/utils';
import { getUrlQueryString } from '@/utils/nodeCommon';

NProgress.configure({ showSpinner: false });

// 白名单
//   '/submit',
const whiteList = [
  '/',
  '/browse',
  '/getTopPublish',
  '/browse\\?(\\w| )+=.*',
  '/download',
  '/home',
  '/index',
  '/help',
  '/login',
  '/register',
  '/project/**',
  '/experiment/**',
  '/analysis/**',
  '/sample/**',
  '/run/**',
  '/unauthorized',
  '/featureData/**',
  '/review/detail/**',
  '/statistic',
  '/download/node/data/public/**',
  '/download/node/review/**',
];

const metadataRules = [
  '/project/detail/**',
  '/experiment/detail/**',
  '/analysis/detail/**',
  '/sample/detail/**',
  '/run/detail/**',
];

router.beforeEach((to, from, next) => {
  const accessToken = useRouteStore().getQuery['access-token'];

  // 如果 pinia 中存 access-token 存在且不在 to.query 中，且是访问的metadata，则将其添加到 to.query 中
  if (
    !isStrBlank(accessToken) &&
    !to.query['access-token'] &&
    metadataMatches(to.fullPath)
  ) {
    const updatedQuery = { ...to.query, 'access-token': accessToken };
    next({ path: to.path, query: updatedQuery });
  } else {
    NProgress.start();
    // 获取cookie
    if (getToken()) {
      /* has token*/
      if (to.path === '/login') {
        next({ path: '/' });
        NProgress.done();
      } else {
        next();
        NProgress.done();
      }
    } else {
      // 没有token
      let pass = false;
      // whiteList.forEach(it => {
      //   if (to.path.indexOf(it) !== -1) {
      //     // 在免登录白名单，直接进入
      //     pass = true;
      //   }
      // });
      pass = matches(to.path);
      if (pass) {
        next();
        NProgress.done();
      } else {
        useUserStore()
          .casLogin()
          .then(res => {
            // console.log(res);
            next();
            NProgress.done();
          })
          .catch(() => {
            console.log('------loginerr-----');
          });
      }
    }
  }
});

router.afterEach((to, from) => {
  useRouteStore().update(to.fullPath);
  useRouteStore().updateQuery(to.query);
  NProgress.done();
});

export function matches(path) {
  for (let i = 0; i < whiteList.length; i++) {
    const pattern = whiteList[i].replace(/\*\*/g, '.*');
    const regex = new RegExp(`^${pattern}$`);
    if (regex.test(path)) {
      return true;
    }
  }
  if (!isStrBlank(getUrlQueryString('access-token'))) {
    return true;
  }
  return false;
}

export function metadataMatches(path) {
  for (let i = 0; i < metadataRules.length; i++) {
    const pattern = metadataRules[i].replace(/\*\*/g, '.*');
    const regex = new RegExp(`^${pattern}$`);
    if (regex.test(path)) {
      return true;
    }
  }
  return false;
}
