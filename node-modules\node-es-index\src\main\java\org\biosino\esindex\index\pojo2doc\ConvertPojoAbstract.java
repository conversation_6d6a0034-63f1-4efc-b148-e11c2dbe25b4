package org.biosino.esindex.index.pojo2doc;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.common.core.enums.MicrobeSourceEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.enums.es.PublicOrControlled;
import org.biosino.common.core.enums.es.YesOrNo;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.es.entity.NodeEs;
import org.biosino.common.es.entity.base.BaseEsInfo;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.entity.Publish;
import org.biosino.common.mongo.entity.other.Submitter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 */
public abstract class ConvertPojoAbstract<T, E extends BaseEsInfo> {
    static Logger logger = LoggerFactory.getLogger(ConvertPojoAbstract.class);

    /**
     * 设置当前类支持哪些es core
     * <p>
     * 当有数据需要更新时，将自动循环这些core，每个core调用 toDocument(SolrCoreEnum core, T data) 返回
     *
     * @return
     */
//    public abstract SolrCoreEnum[] getSupportedCores();

    /**
     * data to es document
     *
     */
//    abstract E toDocument(T data);


    /**
     * 获取集合中的第一个元素
     */
    public static <O> O getFirst(Collection<O> coll) {
        return CollUtil.isEmpty(coll) ? null : CollUtil.getFirst(coll);
    }

    /**
     * 导入索引到es
     */
    /*public final Map<SolrCoreEnum, E> toDocument(T data) {

        final Map<SolrCoreEnum, E> result = new LinkedHashMap<>();

        if (data == null) {
            return result;
        }

        for (SolrCoreEnum core : getSupportedCores()) {
            E value = toDocument(core, data);
            if (value == null) {
                continue;
            }
            result.put(core, value);
        }

        return result;
    }*/
    protected void wrapSubmitter(E doc, Submitter submitter) {
        if (submitter == null) {
            return;
        }
        // 提交者姓名规则firstName lastName
        final String subName = StringUtils.initSubName(submitter.getFirstName(), submitter.getLastName());
        doc.setSubFirstName(StrUtil.trimToNull(submitter.getFirstName()));
        doc.setSubLastName(StrUtil.trimToNull(submitter.getLastName()));
        doc.setSubName(subName);
        doc.setRelaSubName(addValIntoSet(doc.getRelaSubName(), subName));

        doc.setSubOrg(submitter.getOrgName());
        doc.setRelaSubOrg(addValIntoSet(doc.getRelaSubOrg(), submitter.getOrgName()));

        doc.setSubCountry(submitter.getCountryRegion());
        doc.setRelaSubCountry(addValIntoSet(doc.getRelaSubCountry(), submitter.getCountryRegion()));
    }

    protected void wrapPublishInfo(E doc, List<Publish> publishInfos) {
        boolean published = false;
        if (CollUtil.isNotEmpty(publishInfos)) {
            LinkedHashSet<String> doiSet = new LinkedHashSet<>();
            LinkedHashSet<String> pmidSet = new LinkedHashSet<>();
            LinkedHashSet<String> articleNameSet = new LinkedHashSet<>();
            for (Publish publishInfo : publishInfos) {
                String doi = publishInfo.getDoi();
                if (StrUtil.isNotBlank(doi)) {
                    published = true;
                    addValIntoSet(doiSet, doi);
                    doc.setRelaDoi(addValIntoSet(doc.getRelaDoi(), doi));
                }

                String pmid = publishInfo.getPmid();
                if (StrUtil.isNotBlank(pmid)) {
                    addValIntoSet(pmidSet, pmid);
                    doc.setRelaPmid(addValIntoSet(doc.getRelaPmid(), pmid));
                }

                String articleName = publishInfo.getArticleName();
                if (StrUtil.isNotBlank(articleName)) {
                    addValIntoSet(articleNameSet, articleName);
                    doc.setRelaArticleName(addValIntoSet(doc.getRelaArticleName(), articleName));
                }
            }

            doc.setDoi(CollUtil.isEmpty(doiSet) ? null : doiSet);
            doc.setPmid(CollUtil.isEmpty(pmidSet) ? null : pmidSet);
            doc.setArticleName(CollUtil.isEmpty(articleNameSet) ? null : articleNameSet);
        }
        doc.setPublished(YesOrNo.findNameByBool(published));
    }

    public static <T> LinkedHashSet<T> addCollIntoSet(LinkedHashSet<T> set, Collection<T> vals) {
        if (CollUtil.isNotEmpty(vals)) {
            if (set == null) {
                set = new LinkedHashSet<>();
            }
            set.addAll(vals);
        }
        return set;
    }

    public static <T> LinkedHashSet<T> addValIntoSet(LinkedHashSet<T> set, T val) {
        if (val != null && StrUtil.isNotBlank(val.toString())) {
            if (set == null) {
                set = new LinkedHashSet<>();
            }
            set.add(val);
        }
        return set;
    }

    public static LinkedHashSet<String> addCollToSet(Collection<String> vals) {
        LinkedHashSet<String> set = null;
        if (CollUtil.isNotEmpty(vals)) {
            set = new LinkedHashSet<>(vals);
        }
        return set;
    }

    public static LinkedHashSet<String> addObjToSet(Object val) {
        LinkedHashSet<String> set = null;
        if (val != null && StrUtil.isNotBlank(val.toString())) {
            set = new LinkedHashSet<>();
            set.add(val.toString());
        }
        return set;
    }

    public static void mergePrjInfo(NodeEs project, NodeEs sampleOrExp) {
        if (project == null || sampleOrExp == null) {
            return;
        }
        if (isDeletedVisible(project.getVisibleStatus()) || project.getId() == null) {
            return;
        }
        mergeCommonNodeEs(project, sampleOrExp);
    }

    /**
     * 合并实验信息
     */
    public static void mergeExpInfo(NodeEs exp, NodeEs sampleOrPrj) {
        mergeExpInfo(exp, sampleOrPrj, true);
    }

    public static void mergeExpInfo(NodeEs exp, NodeEs sampleOrPrj, final boolean checkVisible) {
        if (exp == null || sampleOrPrj == null) {
            return;
        }
        if (exp.getId() == null) {
            return;
        }
        if (checkVisible && isDeletedVisible(exp.getVisibleStatus())) {
            return;
        }

        LinkedHashSet<String> set;
        set = addCollIntoSet(sampleOrPrj.getRelaProtocol(), exp.getRelaProtocol());
        sampleOrPrj.setRelaProtocol(set);

        set = addCollIntoSet(sampleOrPrj.getRelaExpType(), exp.getRelaExpType());
        sampleOrPrj.setRelaExpType(set);

        set = addCollIntoSet(sampleOrPrj.getRelaLibraryStrategy(), exp.getRelaLibraryStrategy());
        sampleOrPrj.setRelaLibraryStrategy(set);

        set = addCollIntoSet(sampleOrPrj.getRelaLibraryLayout(), exp.getRelaLibraryLayout());
        sampleOrPrj.setRelaLibraryLayout(set);

        set = addCollIntoSet(sampleOrPrj.getRelaLibrarySelection(), exp.getRelaLibrarySelection());
        sampleOrPrj.setRelaLibrarySelection(set);

        set = addCollIntoSet(sampleOrPrj.getRelaLibraryName(), exp.getRelaLibraryName());
        sampleOrPrj.setRelaLibraryName(set);

        set = addCollIntoSet(sampleOrPrj.getRelaPlatform(), exp.getRelaPlatform());
        sampleOrPrj.setRelaPlatform(set);

        set = addCollIntoSet(sampleOrPrj.getRelaMatePair(), exp.getRelaMatePair());
        sampleOrPrj.setRelaMatePair(set);

        mergeCommonNodeEs(exp, sampleOrPrj);
    }

    /**
     * 合并样本信息
     *
     * @param sap
     * @param expOrPrj
     */
    public static void mergeSapInfo(NodeEs sap, NodeEs expOrPrj) {
        if (sap == null || expOrPrj == null) {
            return;
        }
        if (isDeletedVisible(sap.getVisibleStatus()) || sap.getId() == null) {
            return;
        }
        LinkedHashSet<String> set;

        set = addCollIntoSet(expOrPrj.getRelaSampleType(), sap.getRelaSampleType());
        expOrPrj.setRelaSampleType(set);

        set = addCollIntoSet(expOrPrj.getRelaOrganism(), sap.getRelaOrganism());
        expOrPrj.setRelaOrganism(set);

        set = addCollIntoSet(expOrPrj.getRelaTissue(), sap.getRelaTissue());
        expOrPrj.setRelaTissue(set);

        set = addCollIntoSet(expOrPrj.getRelaSubjectId(), sap.getRelaSubjectId());
        expOrPrj.setRelaSubjectId(set);

        set = addCollIntoSet(expOrPrj.getRelaBiomaterialProvider(), sap.getRelaBiomaterialProvider());
        expOrPrj.setRelaBiomaterialProvider(set);

        set = addCollIntoSet(expOrPrj.getRelaDisease(), sap.getRelaDisease());
        expOrPrj.setRelaDisease(set);

        set = addCollIntoSet(expOrPrj.getRelaDisPhenotype(), sap.getRelaDisPhenotype());
        expOrPrj.setRelaDisPhenotype(set);

        set = addCollIntoSet(expOrPrj.getRelaMutationType(), sap.getRelaMutationType());
        expOrPrj.setRelaMutationType(set);

        set = addCollIntoSet(expOrPrj.getRelaSampleLoc(), sap.getRelaSampleLoc());
        expOrPrj.setRelaSampleLoc(set);

        set = addCollIntoSet(expOrPrj.getRelaGender(), sap.getRelaGender());
        expOrPrj.setRelaGender(set);

        set = addCollIntoSet(expOrPrj.getRelaExtractedMolType(), sap.getRelaExtractedMolType());
        expOrPrj.setRelaExtractedMolType(set);

        set = addCollIntoSet(expOrPrj.getRelaDevStage(), sap.getRelaDevStage());
        expOrPrj.setRelaDevStage(set);

        set = addCollIntoSet(expOrPrj.getRelaBiome(), sap.getRelaBiome());
        expOrPrj.setRelaBiome(set);

        // env_biome
        set = addCollIntoSet(expOrPrj.getRelaEnvBiome(), sap.getRelaEnvBiome());
        expOrPrj.setRelaEnvBiome(set);

        // env_material
        set = addCollIntoSet(expOrPrj.getRelaEnvMaterial(), sap.getRelaEnvMaterial());
        expOrPrj.setRelaEnvMaterial(set);

        // env_feature
        set = addCollIntoSet(expOrPrj.getRelaEnvFeature(), sap.getRelaEnvFeature());
        expOrPrj.setRelaEnvFeature(set);

        mergeCommonNodeEs(sap, expOrPrj);
    }

    public static void mergeCommonNodeEs(NodeEs source, NodeEs target) {
        if (source == null || target == null) {
            return;
        }
        LinkedHashSet<String> set = addCollIntoSet(target.getRelaTypeIds(), source.getRelaTypeIds());
        target.setRelaTypeIds(set);

        set = addCollIntoSet(target.getRelaUsedIds(), source.getRelaUsedIds());
        target.setRelaUsedIds(set);

        set = addCollIntoSet(target.getRelaNames(), source.getRelaNames());
        target.setRelaNames(set);

        set = addCollIntoSet(target.getRelaDoi(), source.getRelaDoi());
        target.setRelaDoi(set);

        set = addCollIntoSet(target.getRelaPmid(), source.getRelaPmid());
        target.setRelaPmid(set);

        set = addCollIntoSet(target.getRelaArticleName(), source.getRelaArticleName());
        target.setRelaArticleName(set);

        initAccess(source);
        initAccess(target);
    }

    public static boolean isDeletedVisible(String visibleStatus) {
        if (
                StringUtils.isBlank(visibleStatus)
                        || VisibleStatusEnum.Deleted.name().equalsIgnoreCase(visibleStatus.trim())
                        || VisibleStatusEnum.Unaccessible.name().equalsIgnoreCase(visibleStatus.trim())
        ) {
            return true;
        }
        return false;
    }

    /*public static boolean isAccess(final String visibleStatus) {
        return VisibleStatusEnum.Accessible.name().equalsIgnoreCase(StrUtil.trim(visibleStatus));
    }*/

    protected String getSourceType(String sourceId) {
        if (sourceId.length() < 3) {
            return null;
        }
        String sourceNo = sourceId.substring(0, 2);
        if (sourceNo.equalsIgnoreCase(MicrobeSourceEnum.SR.name())) {
            return MicrobeSourceEnum.SRA.name();
        }
        if (sourceNo.equalsIgnoreCase(MicrobeSourceEnum.ER.name())) {
            return MicrobeSourceEnum.EBI.name();
        }
        if (sourceNo.equalsIgnoreCase(MicrobeSourceEnum.MG.name())) {
            return MicrobeSourceEnum.MG_RAST.name();
        }
        return MicrobeSourceEnum.JGI.name();
    }

    public static void initAccess(final NodeEs nodeEs) {
        LinkedHashSet<String> access = nodeEs.getRelaAccess();
        if (CollUtil.size(access) == 2) {
            return;
        }
        if (access == null) {
            access = new LinkedHashSet<>();
        }
        final LinkedHashSet<String> security = nodeEs.getSecurity();
        if (CollUtil.isNotEmpty(security)) {
            for (String s : security) {
                if (SecurityEnum._public.getDesc().equals(s)) {
                    access.add(PublicOrControlled.findNameByBool(true));
                } else if (SecurityEnum._restricted.getDesc().equals(s)) {
                    access.add(PublicOrControlled.findNameByBool(false));
                }
            }
        }
        /*else {
            access.add(PublicOrControlled.findNameByBool(false));
        }*/
        nodeEs.setRelaAccess(CollUtil.isEmpty(access) ? null : access);
    }

    public static NodeEs initNodeEs(String id, NodeEsTypeEnum typeEnum, Date createDate, String creator, String name,
                                    String description, Date updateDate, String visibleStatus, String[] usedIds) {
        return initNodeEs(id, typeEnum, createDate, creator, name, description, updateDate, visibleStatus, CollUtil.toList(usedIds));
    }

    public static NodeEs initNodeEs(String id, NodeEsTypeEnum typeEnum, Date createDate, String creator, String name,
                                    String description, Date updateDate, String visibleStatus, List<String> usedIds) {
        final NodeEs doc = new NodeEs();
        doc.setId(id);
        doc.setType(typeEnum.name());

        doc.setTypeId(id);
        doc.setRelaTypeIds(addObjToSet(id));

        doc.setName(name);
        doc.setRelaNames(addObjToSet(name));

        doc.setDescription(description);
        if (updateDate != null) {
            doc.setModifiedDate(updateDate);
        } else {
            doc.setModifiedDate(createDate);
        }
        doc.setCreateDate(createDate);
        doc.setCreator(creator);
        doc.setVisibleStatus(visibleStatus);

        final LinkedHashSet<String> uids = addCollToSet(usedIds);
        doc.setUsedIds(uids);
        doc.setRelaUsedIds(uids);
        return doc;
    }

    public static void addFastQc(final NodeEs doc, final Set<FastQCTask.SeqkitResult> set) {
        if (doc != null && CollUtil.isNotEmpty(set)) {
            for (FastQCTask.SeqkitResult seqkitResult : set) {
                Double num = seqkitResult.getNumSeqs();
                if (num != null) {
//                    doc.setNumSeqs(addValIntoSet(doc.getNumSeqs(), num));
                    doc.setRelaNumSeqs(addValIntoSet(doc.getRelaNumSeqs(), num));
                }
                num = seqkitResult.getSumLen();
                if (num != null) {
//                    doc.setSumLen(addValIntoSet(doc.getSumLen(), num));
                    doc.setRelaSumLen(addValIntoSet(doc.getRelaSumLen(), num));
                }
                num = seqkitResult.getQ20();
                if (num != null) {
//                    doc.setQ20(addValIntoSet(doc.getQ20(), num));
                    doc.setRelaQ20(addValIntoSet(doc.getRelaQ20(), num));
                }
                num = seqkitResult.getQ30();
                if (num != null) {
//                    doc.setQ30(addValIntoSet(doc.getQ30(), num));
                    doc.setRelaQ30(addValIntoSet(doc.getRelaQ30(), num));
                }
            }
        }
    }

    public static void addFastQcToPrj(final NodeEs exp, final NodeEs proj) {
        if (proj != null && exp != null) {
            proj.setRelaSumLen(addCollIntoSet(proj.getRelaSumLen(), exp.getRelaSumLen()));
            proj.setRelaNumSeqs(addCollIntoSet(proj.getRelaNumSeqs(), exp.getRelaNumSeqs()));
            proj.setRelaSumLen(addCollIntoSet(proj.getRelaSumLen(), exp.getRelaSumLen()));
            proj.setRelaQ20(addCollIntoSet(proj.getRelaQ20(), exp.getRelaQ20()));
            proj.setRelaQ30(addCollIntoSet(proj.getRelaQ30(), exp.getRelaQ30()));
        }
    }

}
