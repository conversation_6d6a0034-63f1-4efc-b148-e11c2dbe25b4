package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.repository.ExperimentCustomRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ExperimentCustomRepositoryImpl implements ExperimentCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Experiment validateExpName(String creator, String expNo, String projNo, String expName) {
        Query query = new Query();
        Criteria criteria = expNameBaseQuery(creator)
                .and("proj_no").is(projNo)
                .and("name").is(expName);
        if (StrUtil.isNotBlank(expNo)) {
            // 如果有expNo，说明是编辑数据时查重，需要排除自身
            criteria.andOperator(Criteria.where("exp_no").ne(expNo), Criteria.where("used_ids").nin(expNo));
        }
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Experiment.class);
    }

    private Criteria expNameBaseQuery(String creator) {
        return Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
    }

    private Criteria tempExpNameBaseQuery(String creator) {
        return Criteria.where(tempKey("creator")).is(creator)
                .and(tempKey("ownership")).is(OwnershipEnum.self_support.getDesc())
                .and(tempKey("audited")).is(AuditEnum.init.name())
                .and(tempKey("visible_status")).in(VisibleStatusEnum.includeExistsVisibleStatus());
    }

    @Override
    public Experiment findByNo(String expNo) {
        Query query = new Query();
        Criteria criteria = expNoCriteria(CollUtil.newArrayList(expNo)).and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Experiment.class);
    }

    @Override
    public List<Experiment> findAllByCreator(String creator) {
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(creator)) {
            criteria.andOperator(Criteria.where("creator").is(creator));
        }
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public Page<Experiment> findAllByPage(ArchivedSelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("creator").is(queryDTO.getCreator());
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("exp_no").regex(pattern), Criteria.where("name").regex(pattern));
        }
        if (StrUtil.isNotBlank(queryDTO.getProjectNo())) {
            criteria.and("proj_no").is(queryDTO.getProjectNo());
        }

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("name").include("exp_no").include("proj_no");
        // 排序
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));

        // 统计
        long count = mongoTemplate.count(query, Experiment.class);
        // 分页
        query.with(queryDTO.getPageable());
        // 查询
        List<Experiment> content = mongoTemplate.find(query, Experiment.class);
        // 精准查询并放到首位
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Criteria subCri = new Criteria();
            subCri.orOperator(Criteria.where("exp_no").is(queryDTO.getName()), Criteria.where("name").is(queryDTO.getName()));
            Experiment exp = mongoTemplate.findOne(Query.query(subCri), Experiment.class);
            if (exp != null) {
                // 将list的首位替换成exp
                content.set(0, exp);
            }
        }
        // 返回数据
        return new PageImpl<>(content, queryDTO.getPageable(), count);
    }

    @Override
    public void updateTempId(Collection<String> nos) {
        if (CollUtil.isEmpty(nos)) {
            return;
        }
        Criteria criteria = expNoCriteria(nos);
        final String idKey = tempKey("_id");
        criteria = new Criteria().andOperator(criteria, new Criteria().orOperator(
                        Criteria.where(idKey).isNull(),
                        Criteria.where(idKey).exists(false)
                )
        );
        // 使用数组，才能使$_id读取到外层id值
        String updateString = "[{\"$set\": {\"" + idKey + "\": \"$_id\"}}]";
        final String collName = mongoTemplate.getCollectionName(Experiment.class);
        // q:条件 u:更新
        mongoTemplate.executeCommand("{ update: '" + collName + "', updates: [ { q: " + criteria.getCriteriaObject().toJson() + ", u: " + updateString + ", multi: true } ] }");
    }

    @Override
    public List<Experiment> findTempByProjNo(String projNo) {
        Query query = Query.query(Criteria.where(tempKey("proj_no")).is(projNo)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public Map<String, String> findNameByIds(Collection<String> ids) {
        Query query = Query.query(Criteria.where("exp_no").in(ids)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query.fields().include("exp_no").include("name");
        List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        if (CollUtil.isEmpty(experiments)) {
            return null;
        }
        return experiments.stream().collect(Collectors.toMap(Experiment::getName, Experiment::getExpNo, (existingValue, newValue) -> existingValue));
    }

    @Override
    public Map<String, String> findTempNameByNamesInAndExpNoNotIn(final Collection<String> names, final String creator, final Collection<String> expNos) {
        if (CollUtil.isEmpty(names) || creator == null) {
            return new HashMap<>();
        }
        final Criteria criteria = tempExpNameBaseQuery(creator).and(tempKey("name")).in(names);
        if (CollUtil.isNotEmpty(expNos)) {
            criteria.and(tempKey("exp_no")).nin(expNos).and(tempKey("used_ids")).nin(expNos);
        }
        final Query query = new Query(criteria);
        query.fields().include(tempKey("name")).include(tempKey("sub_no"));
        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        final Map<String, String> tempNames = new HashMap<>();
        for (Experiment experiment : experiments) {
            Experiment tempData = experiment.getTempData();
            if (tempData != null) {
                tempNames.put(tempData.getName(), tempData.getSubNo());
            }
        }
        return tempNames;
    }

    @Override
    public Map<String, Experiment> findTempNameByNamesInAndExpNoIn(Collection<String> names, String creator, Collection<String> expNos) {
        if (CollUtil.isEmpty(names) || CollUtil.isEmpty(expNos) || creator == null) {
            return new HashMap<>();
        }

        final Criteria criteria = tempExpNameBaseQuery(creator).and(tempKey("name")).in(names);
        final Query query = new Query(new Criteria().andOperator(criteria, expNoCriteria(expNos)));

        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        final Map<String, Experiment> map = new HashMap<>();
        for (Experiment experiment : experiments) {
            Experiment tempData = experiment.getTempData();
            if (tempData != null) {
                map.put(tempData.getName(), tempData);
            }
        }
        return map;
    }

    public static Criteria expNoCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return new Criteria().orOperator(
                    Criteria.where("exp_no").in(nos),
                    Criteria.where("used_ids").in(nos)
            );
        } else {
            return new Criteria();
        }
    }

    private Criteria tempExpNoCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return new Criteria().orOperator(
                    Criteria.where(tempKey("exp_no")).in(nos),
                    Criteria.where(tempKey("used_ids")).in(nos)
            );
        } else {
            return new Criteria();
        }
    }

    private Criteria tempExpNoNotCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return new Criteria().andOperator(
                    Criteria.where(tempKey("exp_no")).nin(nos),
                    Criteria.where(tempKey("used_ids")).nin(nos)
            );
        } else {
            return new Criteria();
        }
    }

    /**
     * 查询所有实验信息，如存在临时数据，则使用临时数据，用于判断实验是否正在被审核
     */
    @Override
    public List<Experiment> findAllByNosAndCreator(Collection<String> nos, String creator) {
        if (CollUtil.isEmpty(nos)) {
            return new ArrayList<>();
        }
        final Criteria criteria = expNoCriteria(nos)
                .and("creator").is(creator);
        final Query query = new Query(criteria);
        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        final List<Experiment> tempList = new ArrayList<>();
        for (Experiment experiment : experiments) {
            final Experiment tempData = experiment.getTempData();
            if (tempData != null) {
                tempList.add(tempData);
            } else {
                tempList.add(experiment);
            }
        }
        return tempList;
    }

    @Override
    public List<Experiment> findAllTempByNosAndCreatorAndExpType(Collection<String> nos, String creator, String expType) {
        if (CollUtil.isEmpty(nos) || creator == null) {
            return new ArrayList<>();
        }
        Criteria criteria = tempExpNoCriteria(nos).and(tempKey("creator"))
                .is(creator).and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(expType)) {
            criteria = criteria.and(tempKey("exp_type")).is(expType);
        }
        final List<Experiment> experiments = mongoTemplate.find(new Query(criteria), Experiment.class);
        final List<Experiment> tempList = new ArrayList<>();
        for (Experiment experiment : experiments) {
            final Experiment tempData = experiment.getTempData();
            if (tempData != null) {
                tempList.add(tempData);
            }
        }
        return tempList;
    }

    @Override
    public Set<String> findAllTempNoByNos(Collection<String> nos, String creator) {
        if (CollUtil.isEmpty(nos) || creator == null) {
            return new HashSet<>();
        }
        Criteria criteria = tempExpNoCriteria(nos).and(tempKey("creator")).is(creator);
        Query query = new Query(criteria);
        query.fields().include(tempKey("exp_no")).include(tempKey("used_ids"));
        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        final Set<String> data = new HashSet<>();
        for (Experiment experiment : experiments) {
            final Experiment tempData = experiment.getTempData();
            data.add(tempData.getExpNo());
            List<String> usedIds = tempData.getUsedIds();
            if (CollUtil.isNotEmpty(usedIds)) {
                for (String usedId : usedIds) {
                    if (usedId != null) {
                        data.add(usedId);
                    }
                }
            }
        }
        return data;
    }

    /**
     * 删除临时实验数据
     */
    @Override
    public void deleteTempByNosAndCreator(Collection<String> oldNos, String creator, List<String> newNos) {
        if (CollUtil.isEmpty(oldNos) || creator == null) {
            return;
        }
        Criteria criteria = tempExpNoCriteria(oldNos)
                .and(tempKey("creator")).is(creator);
        criteria = new Criteria().andOperator(criteria, tempExpNoNotCriteria(newNos));

        final Query query = new Query(criteria);
        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        final List<ObjectId> deleteIds = new ArrayList<>();
        final List<ObjectId> deleteTempDataIds = new ArrayList<>();
        for (Experiment experiment : experiments) {
            if (AuditEnum.init.name().equals(experiment.getAudited())) {
                deleteIds.add(new ObjectId(experiment.getId()));
            } else {
                final Experiment tempData = experiment.getTempData();
                if (tempData != null) {
                    deleteTempDataIds.add(new ObjectId(experiment.getId()));
                }
            }
        }
        // 删除第一次提交的暂存数据
        mongoTemplate.remove(new Query(Criteria.where("_id").in(deleteIds)), Experiment.class);
        // 删除老数据编辑时提交的暂存数据
        mongoTemplate.updateMulti(new Query(Criteria.where("_id").in(deleteTempDataIds)), new Update().unset("tempData"), Experiment.class);
    }

    @Override
    public boolean existsUserExperimentByExpNo(String expNo, String creator) {
        return mongoTemplate.exists(Query.query(Criteria.where("exp_no").is(expNo)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Experiment.class);
    }

    @Override
    public Map<String, Boolean> existsUserExperimentByExpNos(List<String> expNos, String creator) {
        Query query = Query.query(Criteria.where("exp_no").in(expNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        List<String> existExpNos = mongoTemplate.findDistinct(query, "exp_no", Experiment.class, String.class);
        Map<String, Boolean> result = new HashMap<>();
        for (String expNo : expNos) {
            result.put(expNo, existExpNos.contains(expNo));
        }
        return result;
    }

    @Override
    public boolean existAuditInitExperimentByExpName(String expName, String memberId) {
        return mongoTemplate.exists(Query.query(Criteria.where("name").is(expName)
                .and("creator").is(memberId)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Experiment.class);
    }

    @Override
    public Map<String, Boolean> existAuditInitExperimentByExpNames(List<String> expNames, String memberId) {
        Query query = Query.query(Criteria.where("name").in(expNames)
                .and("creator").is(memberId)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        List<String> existExpNames = mongoTemplate.findDistinct(query, "name", Experiment.class, String.class);
        Map<String, Boolean> result = new HashMap<>();
        for (String name : expNames) {
            result.put(name, existExpNames.contains(name));
        }
        return result;
    }

    @Override
    public Experiment findAuditInitExpByName(String expName, String memberId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("name").is(expName)
                .and("creator").is(memberId)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Experiment.class);
    }

    @Override
    public List<Experiment> findAllAuditInitExpByNames(List<String> expNames, String memberId) {
        return mongoTemplate.find(Query.query(Criteria.where("name").in(expNames)
                .and("creator").is(memberId)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Experiment.class);
    }

    @Override
    public void updateToDeleteAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return;
        }
        Query query = new Query(Criteria.where("exp_no").in(expNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name());
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Experiment.class);
    }

    @Override
    public Optional<Experiment> findTopByExpNo(String expNo) {
        if (StrUtil.isBlank(expNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("exp_no").is(expNo),
                Criteria.where("used_ids").in(expNo)
        ));
        Experiment exp = mongoTemplate.findOne(new Query(new Criteria().andOperator(condition)), Experiment.class);
        return Optional.ofNullable(exp);
    }

    @Override
    public List<Experiment> findAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("exp_no").in(expNos),
                Criteria.where("used_ids").in(expNos)
        ));
        List<Experiment> list = mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Experiment.class);
        return list;
    }

    @Override
    public List<Experiment> findAllByProjectNo(String projNo) {
        if (StrUtil.isBlank(projNo)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(Criteria.where("proj_no").is(projNo));
        List<Experiment> list = mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Experiment.class);
        return list;
    }
}
