package org.biosino.system.controller.metadata;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.service.meta.RunService;
import org.biosino.system.vo.metadata.RunListVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/run")
public class RunController {
    private final RunService runService;

    /**
     * 列出用户的Run列表
     */
    @RequestMapping("/listRun")
    public TableDataInfo listRun(@RequestBody MetadataQueryDTO queryDTO) {
        Page<RunListVO> page = runService.listAuditedRun(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 导出run数据
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Run", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("metadata:run:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = runService.exportRun(queryDTO);
        DownloadUtils.download(request, response, file, "run.json");
    }

}
