package org.biosino.job.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.job.mapper.FtpFileLogMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @date 2025/3/24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FtpFileLogService {


    private final FtpFileLogMapper ftpFileLogMapper;

    /**
     * 清理状态是uploading的记录且updateTime是20天及以前的记录，将状态修改为error
     */
    public Long cleanUploadingRecords() {
        return ftpFileLogMapper.updateUploadingToError();
    }
}
