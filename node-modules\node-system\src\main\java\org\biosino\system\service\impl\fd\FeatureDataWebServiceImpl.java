package org.biosino.system.service.impl.fd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.CacheConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.BaseSearch;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.entity.admin.HumanResource;
import org.biosino.common.mongo.entity.admin.MultipleOmicsResource;
import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.biosino.common.redis.service.RedisService;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.FdQueryDTO;
import org.biosino.es.api.dto.FdSampleDTO;
import org.biosino.system.api.domain.SysDictData;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.repository.*;
import org.biosino.system.service.ISysDictTypeService;
import org.biosino.system.service.fd.IFeatureDataWebService;
import org.biosino.system.vo.fd.FdDetailVO;
import org.biosino.system.vo.fd.FdHomeStatVO;
import org.biosino.system.vo.fd.FdOmicsListVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 特殊数据集 node前端查询与统计接口
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FeatureDataWebServiceImpl implements IFeatureDataWebService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;
    private final HumanResourceRepository humanResourceRepository;
    private final RedisService redisService;

    private final ISysDictTypeService sysDictTypeService;
    private final ProjectRepository projectRepository;
    private final SampleRepository sampleRepository;

    private final MultipleOmicsResourceRepository multipleOmicsResourceRepository;
    private final MultipleSampleResourceRepository multipleSampleResourceRepository;
    private final SingleSampleResourceRepository singleSampleResourceRepository;

    /**
     * 特殊数据集缓存时间，单位分钟
     */
    private static final long FEATURE_DATA_CACHE_TIME = 60 * 24;

    private static final String HMDS_NFSC_DATASET_CACHE = "hmds-nfsc-dataset";

    /**
     * 首页统计数据
     */
    @Override
    public FdHomeStatVO homeStat() {
        final FdHomeStatVO vo = new FdHomeStatVO();

        vo.setHumanResourceStat(initStat(false));
        vo.setMicrobeResourceStat(initStat(true));

        final Map<String, StatDTO> omicsStatData = cacheOmicsStatData();

        if (CollUtil.isNotEmpty(omicsStatData)) {
            final List<FdHomeStatVO.FdStatItem> omicsResourceStat = new ArrayList<>();
            for (Map.Entry<String, StatDTO> entry : omicsStatData.entrySet()) {
                final StatDTO value = entry.getValue();
                final int expCount = value.getExpCount();
                if (expCount > 0) {
                    final FdHomeStatVO.FdStatItem item = new FdHomeStatVO.FdStatItem();
                    final String name = entry.getKey();
                    item.setName(name);
                    item.setSvgName(FdHomeStatVO.initIco(name));
                    item.setNumber(expCount);
                    item.setSapNumber(value.getSapCount());
                    item.setSize(value.getDataSize());
                    omicsResourceStat.add(item);
                }
            }
            vo.setOmicsResourceStat(omicsResourceStat.stream().sorted((x1, x2) -> x2.getNumber() - x1.getNumber()).collect(Collectors.toList()));
        }
        return vo;
    }

    @Override
    public List<FdHomeStatVO.FdStatItem> getBiomeCuratedStatData() {
        Map<String, StatDTO> biomeCuratedData = cacheBiomeCuratedData();
        if (CollUtil.isEmpty(biomeCuratedData)) {
            return new ArrayList<>();
        }
        List<FdHomeStatVO.FdStatItem> biomeCuratedStat = new ArrayList<>();
        for (Map.Entry<String, StatDTO> entry : biomeCuratedData.entrySet()) {
            StatDTO value = entry.getValue();
            int sapCount = value.getSapCount();
            if (sapCount > 0) {
                FdHomeStatVO.FdStatItem item = new FdHomeStatVO.FdStatItem();
                String name = entry.getKey();
                item.setName(name);
                item.setNumber((int) value.getDataCount());
                item.setSapNumber(value.getSapCount());
                item.setSize(value.getBasesSize());
                biomeCuratedStat.add(item);
            }
        }
        return biomeCuratedStat;
    }

    private Map<String, StatDTO> cacheOmicsStatData() {
        final String omicsCacheKey = getOmicsCacheKey();
        Map<String, StatDTO> omicsStatData = redisService.getCacheObject(omicsCacheKey);
        if (CollUtil.isEmpty(omicsStatData)) {
            final R<Map<String, StatDTO>> resultResult = remoteNodeESSearchService.expStatInfo(SecurityConstants.INNER);
            if (resultResult != null && R.isSuccess(resultResult)) {
                omicsStatData = resultResult.getData();
                if (MapUtil.isNotEmpty(omicsStatData)) {
                    redisService.setCacheObject(omicsCacheKey, omicsStatData, FEATURE_DATA_CACHE_TIME, TimeUnit.MINUTES);
                }
            }
        }
        return omicsStatData;
    }

    private List<FdHomeStatVO.FdStatItem> initStat(final boolean microbeFlag) {
        final HumanResource hrSearch = new HumanResource();
        hrSearch.setMicrobeFlag(microbeFlag);
        final List<HumanResource> content = findEnable(hrSearch);

        final List<FdHomeStatVO.FdStatItem> humanResourceStat = new ArrayList<>();
        if (CollUtil.isNotEmpty(content)) {
            final Map<String, Set<String>> prjIdMap = new HashMap<>();
            final Set<String> allPrjSet = new HashSet<>();
            for (HumanResource item : content) {
                final String category1 = item.getCategory1();
                Set<String> set = prjIdMap.get(category1);
                if (set == null) {
                    set = new HashSet<>();
                }

                final String projectNo = item.getProjectNo();
                set.add(projectNo);
                allPrjSet.add(projectNo);
                prjIdMap.put(category1, set);
            }

            // 根据项目编号统计项目数据
            final Map<String, StatDTO> statData = prjStat(allPrjSet, microbeFlag);

            final Map<String, String> cat1ValLabelMap = new HashMap<>();
            if (!microbeFlag) {
                // Human Resource
                final List<SysDictData> sysDictData = FeatureDataServiceImpl.humanResCat1List(sysDictTypeService);
                if (CollUtil.isNotEmpty(sysDictData)) {
                    for (SysDictData item : sysDictData) {
                        cat1ValLabelMap.put(item.getDictValue(), item.getDictLabel());
                    }
                }
            }

            for (Map.Entry<String, Set<String>> entry : prjIdMap.entrySet()) {
                final FdHomeStatVO.FdStatItem statItem = new FdHomeStatVO.FdStatItem();
                final String cat1 = entry.getKey();
                statItem.setCat1Val(cat1);
                if (!microbeFlag) {
                    // Human Resource
                    statItem.setName(cat1ValLabelMap.get(cat1));
                } else {
                    // Microbe Resource
                    statItem.setName(cat1);
                }
                statItem.setSvgName(FdHomeStatVO.initIco(cat1));

                // 添加统计信息
                if (statData != null) {
                    final Set<String> prjIds = entry.getValue();
                    final StatDTO data = getStatByPrjNos(prjIds, statData);
                    // 总数据大小
                    statItem.setSize(data.getDataSize());
                    // 总项目数量
                    statItem.setNumber(prjIds.size());
                    statItem.setSapNumber(data.getSapCount());
                }
                humanResourceStat.add(statItem);
            }
        }
        return humanResourceStat.stream().sorted((x1, x2) -> x2.getNumber() - x1.getNumber()).collect(Collectors.toList());
    }


    /**
     * 根据biome_curated类别进行统计
     */
    public synchronized Map<String, StatDTO> cacheBiomeCuratedData() {
        final String key = getCacheKey(HMDS_NFSC_DATASET_CACHE);
        Map<String, StatDTO> statData = redisService.getCacheObject(key);
        if (statData == null) {
            final R<Map<String, StatDTO>> statDTOResult = remoteNodeESSearchService.statByBiomeCurated(SecurityConstants.INNER);
            if (statDTOResult != null && R.isSuccess(statDTOResult)) {
                statData = statDTOResult.getData();
                if (MapUtil.isNotEmpty(statData)) {
                    redisService.setCacheObject(key, statData, FEATURE_DATA_CACHE_TIME, TimeUnit.MINUTES);
                }
            }
        }
        return statData;
    }

    /**
     * 项目统计信息
     */
    private Map<String, StatDTO> prjStat(Set<String> prjIds, final boolean microbeFlag) {
        final String humanResourceKey = getFdCacheKey(microbeFlag);
        Map<String, StatDTO> statData = redisService.getCacheObject(humanResourceKey);
        if (statData == null) {
            final R<Map<String, StatDTO>> statDTOResult = remoteNodeESSearchService.statByPrj(prjIds, SecurityConstants.INNER);
            if (statDTOResult != null && R.isSuccess(statDTOResult)) {
                statData = statDTOResult.getData();
                if (MapUtil.isNotEmpty(statData)) {
                    redisService.setCacheObject(humanResourceKey, statData, FEATURE_DATA_CACHE_TIME, TimeUnit.MINUTES);
                }
            }
        }
        return statData;
    }

    private List<HumanResource> findEnable(final HumanResource hrSearch) {
        hrSearch.setStatus(DataStatusEnum.enable.name());
        return humanResourceRepository.search(hrSearch, PageRequest.of(0, BaseSearch.MAX_PAGE_SIZE)).getContent();
    }

    /**
     * 根据项目编号集合，累加统计信息
     *
     * @param prjIds   项目编号集合
     * @param statData 每个编号的统计信息map
     * @return 累加结果
     */
    private StatDTO getStatByPrjNos(final Set<String> prjIds, final Map<String, StatDTO> statData) {
        final StatDTO dto = initStat();
        for (String prjId : prjIds) {
            final StatDTO data = statData.get(prjId);
            addStatData(dto, data);
        }
        dto.setExpCount(CollUtil.size(dto.getExpNos()));
        dto.setSapCount(CollUtil.size(dto.getSapNos()));
        return dto;
    }

    private StatDTO initStat() {
        final StatDTO dto = new StatDTO();
        dto.setPrjCount(0);
        dto.setExpNos(new HashSet<>());
//        dto.setExpCount(0);
        dto.setExpTypes(new HashSet<>());
        dto.setSapNos(new HashSet<>());
//        dto.setSapCount(0);
        dto.setSapTypes(new HashSet<>());
        dto.setDataCount(0);
        dto.setDataSize(0);
        return dto;
    }

    private void addStatData(final StatDTO dto, final StatDTO data) {
        if (data != null) {
            dto.setPrjCount(dto.getPrjCount() + 1);

//            dto.setExpCount(dto.getExpCount() + data.getExpCount());
            if (data.getExpNos() != null) {
                dto.getExpNos().addAll(data.getExpNos());
            }

            if (data.getExpTypes() != null) {
                dto.getExpTypes().addAll(data.getExpTypes());
            }

//            dto.setSapCount(dto.getSapCount() + data.getSapCount());
            if (data.getSapNos() != null) {
                dto.getSapNos().addAll(data.getSapNos());
            }

            if (data.getSapTypes() != null) {
                dto.getSapTypes().addAll(data.getSapTypes());
            }

            dto.setDataCount(dto.getDataCount() + data.getDataCount());
            dto.setDataSize(dto.getDataSize() + data.getDataSize());
        }
    }

    public static String getFdCacheKey(final boolean microbeFlag) {
        return microbeFlag ? getCacheKey("microbeRes") : getCacheKey("humanRes");
    }

    public static String getOmicsCacheKey() {
        return getCacheKey("omics");
    }

    private static String getCacheKey(String configKey) {
        return CacheConstants.SYS_FD_KEY + configKey;
    }

    /**
     * 特殊数据集明细数据
     */
    @Override
    public FdDetailVO fdResDetail(FdQueryDTO search) {
        final boolean microbeFlag = search.isMicrobeFlag();

        // 获取分类1信息
        final List<FdHomeStatVO.FdStatItem> fdStatItems = initStat(microbeFlag);
        if (CollUtil.isEmpty(fdStatItems)) {
            return null;
        }
        final FdDetailVO vo = new FdDetailVO();
//        vo.setStatInfo(fdStatItems.stream().sorted((x1, x2) -> x2.getSapNumber() - x1.getSapNumber()).collect(Collectors.toList()));
        vo.setStatInfo(fdStatItems);

        String category1 = search.getCategory1();
        final String category2 = search.getCategory2();
        final String category3 = search.getCategory3();
        if (StrUtil.isAllBlank(category1, category2, category3)) {
            // 默认选中第一个分类1
            category1 = fdStatItems.get(0).getCat1Val();
        }

        // 设置当前选中的分类1回显信息
        for (FdHomeStatVO.FdStatItem fdStatItem : fdStatItems) {
            if (fdStatItem.getCat1Val().equals(category1)) {
                vo.setCurrCat1Item(fdStatItem);
                break;
            }
        }

        if (StrUtil.isBlank(category1)) {
            return vo;
        }

        // 获取当前分类1下所有项目
        final HumanResource hrSearch = new HumanResource();
        hrSearch.setMicrobeFlag(microbeFlag);
        hrSearch.setCategory1(category1);
        final List<HumanResource> content = findEnable(hrSearch);

        // 是否存在分类2、分类3筛选条件
        final boolean queryCat3 = StrUtil.isNotBlank(category3);
        final boolean queryCat2 = StrUtil.isNotBlank(category2);

        // 当前分类1所有项目编号
        final Set<String> allPrjNos = new HashSet<>();
        // 当前筛选分类下所有项目编号，用于生成下来项目、样本列表
        final Set<String> prjListNos = new LinkedHashSet<>();
        // 分类2、分类3信息
        final Map<String, FdDetailVO.CatItem> cat2Map = new LinkedHashMap<>();
        for (final HumanResource item : content) {
            final String projectNo = item.getProjectNo();

            // 获取分类2、分类3信息
            final String cat2 = item.getCategory2();
            final String cat3 = item.getCategory3();
            if (StrUtil.isNotBlank(cat2)) {
                FdDetailVO.CatItem catItem = cat2Map.get(cat2);
                if (catItem == null) {
                    catItem = new FdDetailVO.CatItem();
                    catItem.setCateName(cat2);
                }
                LinkedHashSet<String> prjNos = catItem.getPrjNos();
                if (prjNos == null) {
                    prjNos = new LinkedHashSet<>();
                }
                prjNos.add(projectNo);
                catItem.setPrjNos(prjNos);

                if (cat3 != null) {
                    List<FdDetailVO.CatItem> cateThree = catItem.getCateThree();
                    if (cateThree == null) {
                        cateThree = new ArrayList<>();
                    }

                    boolean exist = false;
                    for (FdDetailVO.CatItem cat3Item : cateThree) {
                        if (cat3Item.getCateName().equals(cat3)) {
                            cat3Item.getPrjNos().add(projectNo);
                            exist = true;
                        }
                    }

                    if (!exist) {
                        final FdDetailVO.CatItem cat3Item = new FdDetailVO.CatItem();
                        cat3Item.setCateName(cat3);
                        cat3Item.setPrjNos(CollUtil.newLinkedHashSet(projectNo));
                        cateThree.add(cat3Item);
                    }

                    catItem.setCateThree(cateThree);
                }
                cat2Map.put(cat2, catItem);
            }

            // 筛选分类
            if (queryCat3) {
                if (category3.equals(item.getCategory3()) && category2.equals(cat2)) {
                    prjListNos.add(projectNo);
                }
            } else if (queryCat2) {
                if (category2.equals(cat2)) {
                    prjListNos.add(projectNo);
                }
            } else {
                prjListNos.add(projectNo);
            }
            // 所有编号
            allPrjNos.add(projectNo);
        }

        // 获取项目统计信息，key为项目编号，value为该项目统计信息
        final Map<String, StatDTO> prjStatMap = prjStat(allPrjNos, microbeFlag);
        if (CollUtil.isEmpty(cat2Map)) {
            vo.setCat1Stat(getStatByPrjNos(allPrjNos, prjStatMap));
        } else {
            for (Map.Entry<String, FdDetailVO.CatItem> entry : cat2Map.entrySet()) {
                FdDetailVO.CatItem value = entry.getValue();
                value.setStatDTO(getStatByPrjNos(value.getPrjNos(), prjStatMap));
            }
            vo.setCat2Data(new ArrayList<>(cat2Map.values()));
        }

        /*if (prjListNos.size() > 0) {
            return vo;
        }*/

        // 获取项目、样本列表数据
        if (CollUtil.isNotEmpty(prjListNos)) {
            final List<Project> projectList = projectRepository.findPublicProjectByNos(prjListNos);
            if (CollUtil.isNotEmpty(projectList)) {
                // 项目列表
                final List<FdDetailVO.PrjInfo> projectInfo = new ArrayList<>();
                for (Project project : projectList) {
                    FdDetailVO.PrjInfo info = new FdDetailVO.PrjInfo();
                    final String projectNo = project.getProjectNo();
                    info.setProjID(projectNo);
                    info.setProjName(project.getName());
                    info.setDes(project.getDescription());
                    info.setExpType(CollUtil.join(prjStatMap.get(projectNo).getExpTypes(), ", "));
                    projectInfo.add(info);
                }
                vo.setProjectInfo(projectInfo);

                // 样本列表
                search.setPrjNos(prjListNos);
//                vo.setPageNum(search.getPageNum());
//                vo.setPageSize(search.getPageSize());
                final R<FdSampleDTO> r = remoteNodeESSearchService.findSapNoAndExpTypesByPrjNos(search, SecurityConstants.INNER);
                if (r != null && R.isSuccess(r)) {
                    final FdSampleDTO fdSampleDTO = r.getData();
                    final LinkedHashMap<String, Set<String>> data = fdSampleDTO.getSapExpTypes();
                    if (CollUtil.isNotEmpty(data)) {
                        final List<Sample> sampleList = sampleRepository.findAllBySapNoIn(new ArrayList<>(data.keySet()));
                        if (CollUtil.isNotEmpty(sampleList)) {
                            final List<FdDetailVO.SapInfo> sampleInfo = new ArrayList<>();
                            for (Sample sample : sampleList) {
                                FdDetailVO.SapInfo info = new FdDetailVO.SapInfo();
                                final String id = sample.getSapNo();
                                info.setSampID(id);
                                info.setSampName(sample.getName());
                                info.setDes(sample.getDescription());
                                info.setSampType(sample.getSubjectType());
                                info.setOrganism(sample.getOrganism());
                                info.setExpType(CollUtil.join(data.get(id), ", "));
                                info.setTissue(sample.getTissue());
                                sampleInfo.add(info);
                            }
                            vo.setSampleInfo(sampleInfo);
                            vo.setSampleTotal(fdSampleDTO.getTotal());
                        }
                    }
                }
            }
        }
        return vo;
    }

    @Override
    public FdOmicsListVO fdOmicsDetail(FdQueryDTO search) {
        final String omicsType = search.getOmicsType();
        final FdOmicsListVO vo = new FdOmicsListVO();
        if (StrUtil.isBlank(omicsType)) {
            return vo;
        }
        final String searchNo = search.getSearchNo();
        final Set<String> allCol = new LinkedHashSet<>();
        switch (omicsType) {
            case "omicsRes":
                final List<MultipleOmicsResource> list1 = multipleOmicsResourceRepository.findByNoLike(searchNo);
                if (CollUtil.isNotEmpty(list1)) {
                    final List<FdOmicsListVO.FdOmicsItem> data = new ArrayList<>();
                    for (MultipleOmicsResource omicsRes : list1) {
                        FdOmicsListVO.FdOmicsItem item = new FdOmicsListVO.FdOmicsItem();
                        item.setId(omicsRes.getProjID());
                        item.setTypes(new ArrayList<>(omicsRes.getExpTypes()));
                        data.add(item);

                        allCol.addAll(omicsRes.getExpTypes());
                    }
                    vo.setData(data);
                }
                break;
            case "sampleRes":
                final List<MultipleSampleResource> list2 = multipleSampleResourceRepository.findByNoLike(searchNo);
                if (CollUtil.isNotEmpty(list2)) {
                    final List<FdOmicsListVO.FdOmicsItem> data = new ArrayList<>();
                    for (MultipleSampleResource res : list2) {
                        FdOmicsListVO.FdOmicsItem item = new FdOmicsListVO.FdOmicsItem();
                        item.setId(res.getProjID());
                        item.setTypes(new ArrayList<>(res.getSapTypes()));
                        data.add(item);

                        allCol.addAll(res.getSapTypes());
                    }
                    vo.setData(data);
                }
                break;
            case "sampleOmics":
                final List<SingleSampleResource> list3 = singleSampleResourceRepository.findByNoLike(searchNo);
                if (CollUtil.isNotEmpty(list3)) {
                    final List<FdOmicsListVO.FdOmicsItem> data = new ArrayList<>();
                    for (SingleSampleResource res : list3) {
                        FdOmicsListVO.FdOmicsItem item = new FdOmicsListVO.FdOmicsItem();
                        item.setId(res.getSapID());
                        item.setTypes(new ArrayList<>(res.getExpTypes()));
                        data.add(item);

                        allCol.addAll(res.getExpTypes());
                    }
                    vo.setData(data);
                }
                break;
            default:
                throw new ServiceException("Invalid parameter");
        }
        vo.setAllCol(new ArrayList<>(allCol));
        return vo;
    }

    /**
     * 特殊数据集刷新缓存
     */
    @Override
    public void refreshFdCache() {
        final String key1 = getFdCacheKey(true);
        final String key2 = getFdCacheKey(false);
        final String key3 = getOmicsCacheKey();
        redisService.deleteObject(key1);
        redisService.deleteObject(key2);
        redisService.deleteObject(key3);
        // 重新加载缓存
        homeStat();
        // 水圈重新加载缓存
        String key4 = getCacheKey(HMDS_NFSC_DATASET_CACHE);
        redisService.deleteObject(key4);
        cacheBiomeCuratedData();
    }

    @Override
    public Page<FdDetailVO.SapInfo> hmdsSapList(MetadataQueryDTO queryDTO) {
        Map<String, StatDTO> typeToStatMap = cacheBiomeCuratedData();
        String category1 = queryDTO.getName();
        if (StrUtil.isBlank(category1)) {
            // 返回第一个
            Set<String> keySet = typeToStatMap.keySet();
            category1 = keySet.iterator().next();
        }
        StatDTO statDTO = typeToStatMap.get(category1);
        Set<String> sapNos = statDTO.getSapNos();
        Page<Sample> pageable = sampleRepository.findPublicPageBySapNoIn(sapNos, queryDTO.getPageable());
        Page<FdDetailVO.SapInfo> result = pageable.map(sample -> {
            FdDetailVO.SapInfo info = new FdDetailVO.SapInfo();
            info.setSampID(sample.getSapNo());
            info.setSampName(sample.getName());
            info.setDes(sample.getDescription());
            info.setAttributes(sample.getAttributes());
            info.setSampType(sample.getSubjectType());
            info.setOrganism(sample.getOrganism());
            info.setTissue(sample.getTissue());
            return info;
        });
        return result;
    }
}
