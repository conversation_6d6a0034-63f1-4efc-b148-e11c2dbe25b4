package org.biosino.task.repository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.task.repository.FastQCTaskCustomRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
public class FastQCTaskCustomRepositoryImpl implements FastQCTaskCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<FastQCTask> getReadyTasksAndPriorityGte(int priority, int pageSize) {
        Query query = new Query(Criteria.where("status").is("ready")
                .and("priority").gte(priority));
        query.limit(pageSize);
        query.with(Sort.by(Sort.Direction.DESC, "priority")).with(Sort.by(Sort.Direction.ASC, "create_date"));
        return mongoTemplate.find(query, FastQCTask.class);
    }

    @Override
    public List<FastQCTask> getReadyTasksAndPriorityLte(int priority, int pageSize) {
        Query query = new Query(Criteria.where("status").is("ready")
                .and("priority").lte(priority));
        query.limit(pageSize);
        query.with(Sort.by(Sort.Direction.DESC, "priority")).with(Sort.by(Sort.Direction.ASC, "create_date"));
        return mongoTemplate.find(query, FastQCTask.class);
    }

    @Override
    public List<String> getExistDataNos(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return Collections.emptyList();
        }
        Query query = new Query(Criteria.where("data_no").in(dataNos));
        query.fields().include("data_no");

        return mongoTemplate.findDistinct(query, "data_no", FastQCTask.class, String.class);
    }
}
