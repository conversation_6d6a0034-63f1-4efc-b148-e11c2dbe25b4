package org.biosino.task.mq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.SamToolTaskStatusMsg;
import org.biosino.task.service.task.SamToolTaskService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SamToolTaskListener {

    private final SamToolTaskService samToolTaskService;

    private final MessageSender messageSender;

    @RabbitListener(queues = "samtool_task_create_queue")
    @RabbitHandler
    public void handlerTaskCreate(@Payload List<String> msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("samtool_task_create_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            samToolTaskService.handlerTaskCreate(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    @RabbitListener(queues = "samtool_task_status_queue")
    @RabbitHandler
    public void handlerTaskStatus(@Payload SamToolTaskStatusMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("samtool_task_status_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            samToolTaskService.handlerStatusChange(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    // /**
    //  * 模拟一个status，用于调试
    //  */
    // @RabbitListener(queues = {"samtool_task_start_queue", "samtool_hp_task_start_queue"})
    // @RabbitHandler
    // public void handlerTaskStart(@Payload SamToolTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
    //     log.info("samtool_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
    //     try {
    //         SamToolTaskStatusMsg runningMsg = new SamToolTaskStatusMsg();
    //         runningMsg.setDataNo(msg.getDataNo());
    //         runningMsg.setStatus(FastQCTaskStatusEnum.running.name());
    //         messageSender.sendDelayMsg("samtool_task_status_routing_key", runningMsg);
    //
    //         ThreadUtil.safeSleep(10 * 1000);
    //
    //         SamToolTaskStatusMsg successMsg = new SamToolTaskStatusMsg();
    //         successMsg.setDataNo(msg.getDataNo());
    //         successMsg.setStatus(FastQCTaskStatusEnum.success.name());
    //         successMsg.setFailCause("");
    //         successMsg.setExitCode(0);
    //         messageSender.sendMsg("samtool_task_status_routing_key", successMsg);
    //     } catch (Exception e) {
    //         log.error(e.getMessage(), e);
    //     } finally {
    //         // 确认消息
    //         channel.basicAck(tag, false);
    //     }
    // }
}
