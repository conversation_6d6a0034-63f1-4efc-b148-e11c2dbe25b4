package org.biosino.auth.oauth2;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * OAuth2配置参数
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "oauth2")
public class OAuth2Properties {

    /**
     * OAuth2 Client ID
     */
    private String clientId;

    /**
     * OAuth2 Client Secret
     */
    private String clientSecret;

    /**
     * OAuth2回调地址
     */
    private String redirectUri;

    /**
     * 授权地址
     */
    private String authorizationUri;

    /**
     * Token获取地址
     */
    private String tokenUri;

    /**
     * 用户信息获取地址
     */
    private String userInfoUri;

    /**
     * OAuth2授权范围
     */
    private String scope;
}
