package org.biosino.upload.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.ConfigUtils;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.AnalysisImportDTO;
import org.biosino.upload.dto.ArchiveAnalysisImportDTO;
import org.biosino.upload.dto.SubmissionDTO;
import org.biosino.upload.dto.mapper.*;
import org.biosino.upload.mq.IndexUpdateEvent;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ProjectVO;
import org.biosino.upload.vo.PublishVO;
import org.biosino.upload.vo.SubDetailVO;
import org.biosino.upload.vo.SubmissionVO;
import org.biosino.upload.vo.exp.SubExpSampleVO;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionService extends BaseService {

    private final MongoTemplate mongoTemplate;
    private final RunRepository runRepository;
    private final DataRepository dataRepository;
    private final SampleRepository sampleRepository;
    private final ProjectRepository projectRepository;
    private final PublishRepository publishRepository;
    private final AnalysisRepository analysisRepository;
    private final ExperimentRepository experimentRepository;
    private final SubmissionRepository submissionRepository;
    private final FastQCTaskRepository fastQCTaskRepository;
    private final CompleteDataLogRepository completeDataLogRepository;
    private final AuditLogRepository auditLogRepository;

    private final RedisService redisService;
    private final MessageSender messageSender;
    private final SampleService sampleService;
    private final PublishService publishService;
    private final ArchiveService archiveService;
    private final ProjectService projectService;
    private final AnalysisService analysisService;
    private final ExperimentService experimentService;
    private final ApplicationContext applicationContext;

    private final RemoteMemberService remoteMemberService;
    private final RemoteNotificationService remoteNotificationService;

    private final static String UNASSIGNED_ID = "Unassigned formal ID";
    private final static String PASS_SUBMISSION_TASK_KEY = "pass_submission_task_key_";
    private final static int THREAD = 16;

    public Page<SubmissionVO> list(SubmissionDTO dto) {
        PageImpl<Submission> page = submissionRepository.findAllPage(dto);

        return page.map(submission -> {

            SubmissionVO submissionVO = new SubmissionVO();
            BeanUtil.copyProperties(submission, submissionVO);

            Submitter submitter = submission.getSubmitter();
            String submitterName = StrUtil.join(" ", submitter.getFirstName(), submitter.getMiddleName(), submitter.getLastName());
            submissionVO.setSubmitter(submitterName);

            submissionVO.setProjNum(submission.getProjNo() != null ? 1 : 0);
            submissionVO.setExpNum(getExpNum(submission));
            submissionVO.setSapNum(getSapNum(submission));
            submissionVO.setRunNum(getRunNum(submission));
            submissionVO.setAnalNum(getAnalNum(submission));
            submissionVO.setDataNum(getDataSet(submission).size());

            String lockKey = PASS_SUBMISSION_TASK_KEY + submission.getSubNo();
            Boolean inStorage = redisService.hasKey(lockKey);
            submissionVO.setProcessing(inStorage);
            return submissionVO;

        });
    }

    private Integer getRunNum(Submission submission) {
        Set<String> dataSet = getDataSet(submission);

        List<Data> dataList = dataRepository.findAllByDataNoIn(dataSet, SecurityUtils.getMemberId());
        long count = dataList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getRunNo())
                .filter(StrUtil::isNotBlank).distinct().count();

        return Math.toIntExact(count);
    }

    public boolean qcStatusEnable() {
        String qcStatus = ConfigUtils.getConfig(ConfigConstants.QC_STATUS);
        if (qcStatus == null) {
            throw new ServiceException("Error in manually reviewing system status configuration. Please contact the administrator");
        }
        return ConfigConstants.enable.equalsIgnoreCase(qcStatus);
    }

    private Set<String> getDataSet(Submission submission) {
        Set<String> dataSet = new HashSet<>();
        if (SubmissionDataTypeEnum.rawData.name().equals(submission.getDataType())) {
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                dataSet.addAll(submission.getRawDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                dataSet.addAll(submission.getRawDataMultipleNos());
            }
        }
        if (SubmissionDataTypeEnum.analysisData.name().equals(submission.getDataType())) {
            if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                dataSet.addAll(submission.getAnalysisDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                dataSet.addAll(submission.getAnalDataMultipleNos());
            }
        }
        return dataSet;
    }

    private Integer getExpNum(Submission submission) {
        int dataNum = submission.getExpSingleNo() != null ? 1 : 0;
        if (CollUtil.isNotEmpty(submission.getExpMultipleNos())) {
            dataNum += submission.getExpMultipleNos().size();
        }
        return dataNum;
    }

    private Integer getSapNum(Submission submission) {
        int sapNum = submission.getSapSingleNo() != null ? 1 : 0;
        List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
        if (CollUtil.isEmpty(sapMultipleData)) {
            return sapNum;
        }
        for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
            List<String> nos = sapMultipleDatum.getNos();
            if (CollUtil.isNotEmpty(nos)) {
                sapNum += nos.size();
            }
        }
        return sapNum;
    }

    private Integer getAnalNum(Submission submission) {
        int dataNum = submission.getAnalSingleNo() != null ? 1 : 0;
        if (CollUtil.isNotEmpty(submission.getAnalMultipleNos())) {
            dataNum += submission.getAnalMultipleNos().size();
        }
        return dataNum;
    }

    /**
     * 撤回提交
     */
    public void revoke(String subNo) {
        if (SecurityUtils.getMember() == null) {
            throw new NotPermissionException("Not permission");
        }
        String lockKey = PASS_SUBMISSION_TASK_KEY + subNo;
        if (redisService.hasKey(lockKey)) {
            throw new ServiceException("The submission has been approved and the data is currently being stored in the database, Please be patient and wait !");
        }

        Submission submission = getSubmissionByNo(subNo);
        if (submission == null) {
            throw new ServiceException("The submission record was not found");
        }

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.waiting.name().equals(status)) {
            throw new ServiceException("The current submission status is not in waiting and cannot be revoke");
        }

        String creator = submission.getCreator();
        if (creator == null || !creator.equals(SecurityUtils.getMemberId())) {
            throw new ServiceException("No permission");
        }

        submission.setStatus(SubmissionStatusEnum.editing.name());
        saveSubmission(submission);
    }

    /**
     * 提交数据
     */
    public String submit(String subNo) {
        if (SecurityUtils.getMember() == null) {
            throw new NotPermissionException("Not permission");
        }
        Submission submission = getSubmissionByNo(subNo);
        if (submission == null) {
            return "The submission record was not found";
        }

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.editing.name().equals(status)) {
            return "The current submission status is not in editing and cannot be submit";
        }

        String creator = submission.getCreator();
        if (creator == null || !creator.equals(SecurityUtils.getMemberId())) {
            return "No permission";
        }
        String dataType = submission.getDataType();

//        if ((dataType.equals(SubmissionDataTypeEnum.rawData.name())
//                || dataType.equals(SubmissionDataTypeEnum.analysisData.name()))) {
//            // 必须要归档数据
//            if (CollUtil.isEmpty(getDataSet(submission))) {
//                return "The current submission archived data cannot be empty";
//            }
//        }

        // 统计提交的数据
        submission.setProjNum(submission.getProjNo() != null ? 1 : 0);
        submission.setExpNum(getExpNum(submission));
        submission.setSapNum(getSapNum(submission));
        submission.setAnalNum(getAnalNum(submission));

        if (dataType.equals(SubmissionDataTypeEnum.rawData.name())) {
            Set<String> dataSet = getDataSet(submission);

            Set<String> runSet = new HashSet<>();
            Long dataSize = 0L;
            List<Data> dataList = dataRepository.findAllByDataNoIn(dataSet, SecurityUtils.getMemberId());
            Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));
            for (String rawDataNo : dataSet) {
                Data data = dataNoToDataMap.get(rawDataNo);
                if (data == null) {
                    return "Data: " + rawDataNo + ", not found, please contact the administrator";
                }
                data = data.getTempData();
                dataSize += data.getFileSize();
                runSet.add(data.getRunNo());
            }
            submission.setRunNum(runSet.size());
            submission.setDataNum(dataSet.size());
            submission.setDataSize(dataSize);

            // 新增的数据必须要在归档中使用到，不能游离
            Submission submissionCheck = SubmissionDTOMapper.INSTANCE.copy(submission);
            String errMsg = checkRawData(runSet, submissionCheck);
            if (errMsg != null) {
                return errMsg;
            }
        }

        if (dataType.equals(SubmissionDataTypeEnum.analysisData.name())) {
            Set<String> dataSet = getDataSet(submission);

            Long dataSize = 0L;
            Set<String> analysisSet = new HashSet<>();
            List<Data> dataList = dataRepository.findAllByDataNoIn(dataSet, SecurityUtils.getMemberId());
            Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));
            for (String analDataNo : dataSet) {
                Data data = dataNoToDataMap.get(analDataNo);
                if (data == null) {
                    return "Data: " + analDataNo + ", not found, please contact the administrator";
                }
                data = data.getTempData();
                dataSize += data.getFileSize();
                analysisSet.add(data.getAnalNo());
            }
            submission.setDataNum(dataSet.size());
            submission.setDataSize(dataSize);

            // 新增的数据必须要在归档中使用到，不能游离
            Submission submissionCheck = SubmissionDTOMapper.INSTANCE.copy(submission);
            String errMsg = checkAnalysisData(analysisSet, submissionCheck);
            if (errMsg != null) {
                return errMsg;
            }
        }

        if (dataType.equals(SubmissionDataTypeEnum.publish.name())) {
            submission.setPublishNum(1);
        }

        submission.setTotal(submission.getProjNum() + submission.getExpNum() + submission.getSapNum() + submission.getAnalNum() + submission.getRunNum() + submission.getDataNum() + submission.getPublishNum());

        if (submission.getTotal() == 0) {
            return "You have not filled in any valid data, Please continue editing the submission";
        }

        submission.setSubmitTime(new Date());

        // 如果是第一次提交，则设置首次提交时间
        if (submission.getAuditTime() == null) {
            submission.setFirstSubmitTime(submission.getSubmitTime());
        }

        // 如果启用fastqc功能，那么通知fastqc开始分析任务
        Set<String> dataSet = getDataSet(submission);
        if (CollUtil.isNotEmpty(dataSet)) {
            if (SubmissionDataTypeEnum.rawData.name().equals(submission.getDataType())) {
                messageSender.sendDelayMsg("fastqc_task_create_routing_key", CollUtil.newArrayList(dataSet));
            }
            messageSender.sendDelayMsg("samtool_task_create_routing_key", CollUtil.newArrayList(dataSet));
        }

        // 如果人工审核系统开启，则修改待审核状态
        if (qcStatusEnable()) {
            submission.setStatus(SubmissionStatusEnum.waiting.name());
            saveSubmission(submission);

            ThreadUtil.execAsync(() -> {
                // 邮件告知NODE运维人员审核数据
                R<MemberDTO> nodeUser = remoteMemberService.getOneMemberByMemberId(submission.getCreator(), "FtpUser", SecurityConstants.INNER);
                if (R.isError(nodeUser)) {
                    throw new ServiceException("用户服务异常，请联系管理员");
                }

                Map<String, Object> params = new HashMap<>();
                params.put("user", nodeUser.getData().getEmail());
                params.put("submissionId", submission.getSubNo());

                try {
                    final SendEmailDTO sendEmailDTO = new SendEmailDTO();
                    sendEmailDTO.setToEmail(WebConstants.SUPPORT_EMAIL);
                    sendEmailDTO.setParams(params);
                    sendEmailDTO.setMailTemplate(MailTemplate.UploadSubmission_Check);
                    remoteNotificationService.sendEmail(sendEmailDTO, SecurityConstants.INNER);
                } catch (Exception e) {
                    log.error("邮件发送失败 email: {} mailTemplate:{} params: {}", WebConstants.SUPPORT_EMAIL, MailTemplate.UploadSubmission_Check, JSONObject.toJSONString(params), e);
                }
            });
            return null;
        }

        // 未开启审核系统，直接审核通过
        return passSubmit(submission, null, null);
    }

    private String checkRawData(Set<String> runSet, Submission submission) {
        Set<String> projSet = new HashSet<>();
        Set<String> expSet = new HashSet<>();
        Set<String> sapSet = new HashSet<>();

        List<Run> runList = runRepository.findAllByRunNoIn(runSet);
        Map<String, Run> runNoToRunMap = runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> expNos = runList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getExpNo())
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<Experiment> expList = experimentRepository.findAllByExpNoIn(expNos);
        Map<String, Experiment> expNoToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (String runNo : runSet) {
            Run run = runNoToRunMap.get(runNo);
            if (run == null) {
                continue;
            }
            if (run.getTempData() != null) {
                run = run.getTempData();
            }
            String expNo = run.getExpNo();
            if (!expSet.contains(expNo) && !expNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                expSet.add(expNo);
                Experiment experiment = expNoToExpMap.get(expNo);
                String projectNo = experiment.getProjectNo();
                if (!projSet.contains(projectNo) && !projectNo.startsWith(SequenceType.PROJECT.getPrefix())) {
                    projSet.add(projectNo);
                }
            }
            if (!sapSet.contains(run.getSapNo()) && !run.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                sapSet.add(run.getSapNo());
            }
        }
        // 校验实验
        List<String> expMultipleNos = submission.getExpMultipleNos();
        if (CollUtil.isEmpty(expMultipleNos)) {
            expMultipleNos = new ArrayList<>();
        }
        String expSingleNo = submission.getExpSingleNo();
        if (expSingleNo != null) {
            expMultipleNos.add(expSingleNo);
        }
        if (CollUtil.isNotEmpty(expMultipleNos)) {
            for (String expNo : expMultipleNos) {
                if (!expSet.contains(expNo) && !expNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                    Experiment experiment = experimentRepository.findByNo(expNo);
                    return "Experiment: <strong>" + experiment.getName() + "</strong>, not used, please archive the data!";
                }
            }
        }

        String projNo = submission.getProjNo();
        if (projNo != null) {
            if (!projSet.contains(projNo) && !projNo.startsWith(SequenceType.PROJECT.getPrefix())) {
                Project project = projectRepository.findByNo(projNo);
                return "Project: <strong>" + project.getName() + "</strong>, not used, please archive the data!";
            }
        }

        // 校验样本
        Set<String> sapNos = new HashSet<>();
        List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
        if (CollUtil.isNotEmpty(sapMultipleData)) {
            for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                sapNos.addAll(sapMultipleDatum.getNos());
            }
        }
        String sapSingleNo = submission.getSapSingleNo();
        if (sapSingleNo != null) {
            sapNos.add(sapSingleNo);
        }
        if (CollUtil.isNotEmpty(sapNos)) {
            for (String sapNo : sapNos) {
                if (!sapSet.contains(sapNo) && !sapNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
                    Sample sample = sampleRepository.findByNo(sapNo);
                    return "Sample: <strong>" + sample.getName() + "</strong>, not used, please archive the data!";
                }
            }
        }
        return null;
    }

    private String checkAnalysisData(Set<String> analysisSet, Submission submission) {

        List<String> analMultipleNos = submission.getAnalMultipleNos();
        if (analMultipleNos == null) {
            analMultipleNos = new ArrayList<>();
        }
        String analSingleNo = submission.getAnalSingleNo();
        if (analSingleNo != null) {
            analMultipleNos.add(analSingleNo);
        }

        if (CollUtil.isNotEmpty(analMultipleNos)) {
            for (String analNo : analMultipleNos) {
                if (!analysisSet.contains(analNo) && !analNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    Analysis analysis = analysisRepository.findByAnalNo(analNo);
                    if (!dataRepository.existsByAnalNo(analysis.getAnalysisNo())) {
                        return "Analysis: <strong>" + analysis.getName() + "</strong>, not used, please archive the data!";
                    }
                }
            }
        }
        return null;
    }

    /**
     * 审核通过，数据入库
     */
    public String passSubmit(Submission submission, Long auditorId, String auditorName) {
        if (qcStatusEnable()) {
            if (!SubmissionStatusEnum.reviewing.name().equals(submission.getStatus())) {
                return "The current submission status is not is reviewing";
            }
        } else {
            if (!SubmissionStatusEnum.editing.name().equals(submission.getStatus())) {
                return "The current submission status is not is editing";
            }
        }
        String subNo = submission.getSubNo();
        String lockKey = PASS_SUBMISSION_TASK_KEY + subNo;
        if (redisService.hasKey(lockKey)) {
            throw new ServiceException("The submission is processing ! Please be patient !");
        }
        //  临时ID   正式ID
        Map<String, String> idMap = new ConcurrentHashMap<>();
        try {
            // 校验Submission数据一致性
            Submission submissionCheck = SubmissionDTOMapper.INSTANCE.copy(submission);
            String errMsg = verifySubmission(submissionCheck);
            if (errMsg != null) {
                return errMsg;
            }

            log.warn("开始审核Submission: {}", subNo);

            long timeout = 12L;
            redisService.setCacheObject(lockKey, true, timeout, TimeUnit.HOURS);

            String creator = submission.getCreator();
            Submitter submitter = submission.getSubmitter();

            // 数据正式入库前做一个快照，防止审核过程中出现未知异常
            SubDetailVO detail = detail(subNo, true);

            SubDataLog backSubDataLog = new SubDataLog();
            backSubDataLog.setSubNo(subNo);
            backSubDataLog.setStatus(SubmissionStatusEnum.editing.name());
            backSubDataLog.setCreator(submission.getCreator());
            backSubDataLog.setCreateTime(new Date());

            detail.setSubmission(submission);
            String editDataJson = JSON.toJSONString(detail);
            backSubDataLog.setData(editDataJson);

            completeDataLogRepository.insert(backSubDataLog);

            // 记录有正式编号被编辑的数据
            List<String> updateEsProjectNos = Collections.synchronizedList(new ArrayList<>());
            List<String> updateEsExpNos = Collections.synchronizedList(new ArrayList<>());
            List<String> updateEsSapNos = Collections.synchronizedList(new ArrayList<>());
            List<String> updateEsAnalNos = Collections.synchronizedList(new ArrayList<>());

            // 更新项目
            String projNo = submission.getProjNo();
            String projectNo = updateProject(projNo, creator, submitter, updateEsProjectNos);
            if (StrUtil.isNotBlank(projectNo)) {
                submission.setProjNo(projectNo);
                saveSubmission(submission);

                idMap.put(projNo, projectNo);
            }

            // 更新single实验
            String expSingleNo = submission.getExpSingleNo();
            String expNo = updateExperiment(expSingleNo, creator, submitter, updateEsExpNos);
            if (StrUtil.isNotBlank(expNo)) {
                submission.setExpSingleNo(expNo);
                saveSubmission(submission);

                idMap.put(expSingleNo, expNo);
            }

            // 更新Multiple实验
            List<String> expMultipleNos = submission.getExpMultipleNos();
            if (CollUtil.isNotEmpty(expMultipleNos)) {
                expMultipleNos = CollUtil.distinct(expMultipleNos);

                ExecutorService executorService = Executors.newFixedThreadPool(THREAD);

                // 使用多线程优化审核速度
                List<Future<String>> futures = new ArrayList<>();
                for (String expMultipleNo : expMultipleNos) {
                    Callable<String> task = () -> {
                        String newExpNo = updateExperiment(expMultipleNo, creator, submitter, updateEsExpNos);
                        if (StrUtil.isNotBlank(newExpNo)) {
                            idMap.put(expMultipleNo, newExpNo);
                            return newExpNo;
                        } else {
                            return expMultipleNo;
                        }
                    };
                    futures.add(executorService.submit(task));
                }

                List<String> newExpNos = new ArrayList<>();
                try {
                    for (Future<String> future : futures) {
                        newExpNos.add(future.get());
                    }
                    executorService.shutdown();
                    executorService.awaitTermination(timeout, TimeUnit.HOURS);
                } catch (InterruptedException e) {
                    log.error("批量审核通过组学出错: {}", e.getMessage());
                }
                submission.setExpMultipleNos(newExpNos);
                saveSubmission(submission);
            }

            // 更新single样本
            String sapSingleNo = submission.getSapSingleNo();
            String sapNo = updateSample(sapSingleNo, creator, submitter, updateEsSapNos);
            if (StrUtil.isNotBlank(sapNo)) {
                submission.setSapSingleNo(sapNo);
                saveSubmission(submission);

                idMap.put(sapSingleNo, sapNo);
            }

            // 更新Multiple样本
            List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
            if (CollUtil.isNotEmpty(sapMultipleData)) {

                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                    ExecutorService executorService = Executors.newFixedThreadPool(THREAD);

                    List<String> sapMultipleNos = sapMultipleDatum.getNos();
                    if (CollUtil.isNotEmpty(sapMultipleNos)) {
                        sapMultipleNos = CollUtil.distinct(sapMultipleNos);
                    }

                    // 样本的批量提交数据可能会很多，使用多线程优化审核速度
                    List<Future<String>> futures = new ArrayList<>();
                    for (String sapMultipleNo : sapMultipleNos) {
                        Callable<String> task = () -> {
                            String newSapNo = updateSample(sapMultipleNo, creator, submitter, updateEsSapNos);
                            if (StrUtil.isNotBlank(newSapNo)) {
                                idMap.put(sapMultipleNo, newSapNo);
                                return newSapNo;
                            } else {
                                return sapMultipleNo;
                            }
                        };
                        futures.add(executorService.submit(task));
                    }

                    List<String> newSapNos = new ArrayList<>();
                    try {
                        for (Future<String> future : futures) {
                            newSapNos.add(future.get());
                        }
                        executorService.shutdown();
                        executorService.awaitTermination(timeout, TimeUnit.HOURS);
                    } catch (InterruptedException e) {
                        log.error("批量审核通过样本出错: {}", e.getMessage());
                    }
                    sapMultipleDatum.setNos(newSapNos);

                }
                saveSubmission(submission);
            }

            // 更新single分析
            String analSingleNo = submission.getAnalSingleNo();
            String analNo = updateAnalysis(analSingleNo, creator, submitter, updateEsAnalNos);
            if (StrUtil.isNotBlank(analNo)) {
                submission.setAnalSingleNo(analNo);
                saveSubmission(submission);

                idMap.put(analSingleNo, analNo);
            }

            // 更新Multiple分析
            List<String> analMultipleNos = submission.getAnalMultipleNos();
            if (CollUtil.isNotEmpty(analMultipleNos)) {

                ExecutorService executorService = Executors.newFixedThreadPool(THREAD);

                List<Future<String>> futures = new ArrayList<>();
                for (String analMultipleNo : analMultipleNos) {
                    Callable<String> task = () -> {
                        String newAnalNo = updateAnalysis(analMultipleNo, creator, submitter, updateEsAnalNos);
                        if (StrUtil.isNotBlank(newAnalNo)) {
                            idMap.put(analMultipleNo, newAnalNo);
                            return newAnalNo;
                        } else {
                            return analMultipleNo;
                        }
                    };
                    futures.add(executorService.submit(task));
                }

                List<String> newAnalNos = new ArrayList<>();
                try {
                    for (Future<String> future : futures) {
                        newAnalNos.add(future.get());
                    }
                    executorService.shutdown();
                    executorService.awaitTermination(timeout, TimeUnit.HOURS);
                } catch (InterruptedException e) {
                    log.error("批量审核通过分析出错: {}", e.getMessage());
                }

                submission.setAnalMultipleNos(newAnalNos);
                saveSubmission(submission);
            }

            // 更新Raw Data归档
            Set<String> rawDataNos = new HashSet<>();
            List<String> archiveSingleRawNos = submission.getRawDataNos();
            if (CollUtil.isNotEmpty(archiveSingleRawNos)) {
                rawDataNos.addAll(archiveSingleRawNos);
            }
            List<String> rawMultipList = submission.getRawDataMultipleNos();
            if (CollUtil.isNotEmpty(rawMultipList)) {
                rawDataNos.addAll(rawMultipList);
            }

            if (CollUtil.isNotEmpty(rawDataNos)) {
                try {
                    List<Data> allData = dataRepository.findAllByDataNoIn(rawDataNos, creator);
                    Map<String, List<Data>> allDataMap = new HashMap<>();
                    for (Data data : allData) {
                        if (data.getTempData() == null || data.getTempData().getRunNo() == null) {
                            log.error("{}审核过程中错误，数据:{}缺失tempo_data或runNo", subNo, data.getDatNo());
                            continue;
                        }
                        String runNo = data.getTempData().getRunNo();
                        if (allDataMap.containsKey(runNo)) {
                            List<Data> dataList = allDataMap.get(runNo);
                            dataList.add(data);
                            allDataMap.put(runNo, dataList);
                        } else {
                            allDataMap.put(runNo, CollUtil.newArrayList(data));
                        }
                    }

                    ExecutorService executorService = Executors.newFixedThreadPool(THREAD);

                    allDataMap.forEach((runNo, dataList) -> {
                        executorService.submit(() -> {
                            updateRawData(dataList, runNo, creator, idMap);
                        });
                    });
                    executorService.shutdown();
                    executorService.awaitTermination(timeout, TimeUnit.HOURS);
                } catch (InterruptedException e) {
                    log.error("批量审核通过Raw Data归档出错: {}", e.getMessage());
                }
                applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, rawDataNos));
            }

            // 更新Analysis Data归档
            Set<String> analDataNos = new HashSet<>();
            List<String> analysisDataNos = submission.getAnalysisDataNos();
            if (CollUtil.isNotEmpty(analysisDataNos)) {
                analDataNos.addAll(analysisDataNos);
            }
            List<String> analMultipList = submission.getAnalDataMultipleNos();
            if (CollUtil.isNotEmpty(analMultipList)) {
                analDataNos.addAll(analMultipList);
            }
            if (CollUtil.isNotEmpty(analDataNos)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD);
                for (String analysisData : analDataNos) {
                    executorService.submit(() -> updateAnalysisData(analysisData));
                }
                try {
                    executorService.shutdown();
                    executorService.awaitTermination(timeout, TimeUnit.HOURS);
                } catch (InterruptedException e) {
                    log.error("批量审核通过Analysis Multiple 归档出错: {}", e.getMessage());
                }
                applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, analDataNos));
            }

            // 审核参考文献
            publishService.submitPublishBySubNo(subNo);

            submission.setAuditTime(new Date());

            if (auditorId != null) {
                submission.setAuditorId(auditorId);

                submission.setAuditor(auditorName);
            }

            submission.setStatus(SubmissionStatusEnum.complete.name());
            saveSubmission(submission, false);

            // 开始更新需要更新的es数据
            if (CollUtil.isNotEmpty(updateEsProjectNos)) {
                messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(),
                        new IndexUpdateMsg(AuthorizeType.project.name(),
                                updateEsProjectNos));
            }
            if (CollUtil.isNotEmpty(updateEsExpNos)) {
                messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(),
                        new IndexUpdateMsg(AuthorizeType.experiment.name(),
                                updateEsExpNos));
            }
            if (CollUtil.isNotEmpty(updateEsSapNos)) {
                messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(),
                        new IndexUpdateMsg(AuthorizeType.sample.name(),
                                updateEsSapNos));
            }
            if (CollUtil.isNotEmpty(updateEsAnalNos)) {
                messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(),
                        new IndexUpdateMsg(AuthorizeType.analysis.name(),
                                updateEsAnalNos));
            }

            // 审核通过的数据记录一个快照
            SubDataLog subDataLog = new SubDataLog();
            subDataLog.setSubNo(subNo);
            subDataLog.setStatus(SubmissionStatusEnum.complete.name());
            subDataLog.setCreator(submission.getCreator());
            subDataLog.setCreateTime(new Date());

            detail.setSubmission(submission);
            detail.setProcessing(false);
            String dataJson = JSON.toJSONString(detail);

            // 正式分配了ID的数据，将旧的UUID替换掉
            for (Map.Entry<String, String> entry : idMap.entrySet()) {
                String oldId = entry.getKey();
                String newId = entry.getValue();
                dataJson = dataJson.replace(oldId, newId);
            }
            subDataLog.setData(dataJson);

            completeDataLogRepository.insert(subDataLog);

            saveAuditLog(submission, auditorId, auditorName);

            log.warn("{} 审核通过", subNo);
        } catch (Exception e) {
            log.error("审核通过Submission:{}时发生异常：{}", subNo, e.getMessage());
            log.error("新旧ID映射的Map：{}", JSON.toJSONString(idMap));
        } finally {
            redisService.deleteObject(lockKey);
        }
        return null;
    }

    public void saveAuditLog(Submission submission, Long auditorId, String auditorName) {
        if (!qcStatusEnable()) {
            return;
        }
        // 记录审核日志
        AuditLog auditLog = new AuditLog();

        BeanUtil.copyProperties(submission, auditLog);

        auditLog.setId(null);
        auditLog.setStatus("pass");
        auditLog.setAuditorId(auditorId);
        auditLog.setAuditor(auditorName);
        auditLog.setCreateTime(submission.getAuditTime());
        auditLogRepository.insert(auditLog);

        // 邮件告知用户审核通过
        ThreadUtil.execAsync(() -> {
            R<MemberDTO> nodeUser = null;
            Map<String, Object> params = new HashMap<>();
            try {
                nodeUser = remoteMemberService.getOneMemberByMemberId(submission.getCreator(), "FtpUser", SecurityConstants.INNER);
                if (R.isError(nodeUser)) {
                    throw new ServiceException("用户服务异常，请联系管理员");
                }

                params.put("lastName", nodeUser.getData().getLastName());
                params.put("submissionId", submission.getSubNo());

                final SendEmailDTO sendEmailDTO = new SendEmailDTO();
                sendEmailDTO.setToEmail(nodeUser.getData().getEmail());
                sendEmailDTO.setParams(params);
                sendEmailDTO.setMailTemplate(MailTemplate.Audit_Pass);
                remoteNotificationService.sendEmail(sendEmailDTO, SecurityConstants.INNER);
            } catch (Exception e) {
                String email = null;
                if (nodeUser != null) {
                    email = nodeUser.getData().getEmail();
                }
                log.error("邮件发送失败 email: {} mailTemplate:{} params: {}", email, MailTemplate.Audit_Pass, JSONObject.toJSONString(params), e);
            }
        });
    }

    /**
     * 审核通过前的数据校验
     */
    private String verifySubmission(Submission submission) {
        if (submission == null) {
            return "Submission Cannot be empty";
        }
        if (submission.getSubmitter() == null) {
            return "Please fill in the submitter information";
        }
        String subNo = submission.getSubNo();
        String dataType = submission.getDataType();

        String commTip = "</strong>. Please review the Submission and try again. <br>If the Submission ID is not found in the ’Submission Audit‘, please notify the submitter to submit the Submission";

        Set<String> passIds = new HashSet<>();
        if (SubmissionDataTypeEnum.rawData.name().equals(dataType)) {
            Set<String> dataSet = getDataSet(submission);

            Set<String> runSet = new HashSet<>();
            List<Data> dataList = dataRepository.findAllTempByDataNoIn(dataSet);
            Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));
            for (String rawDataNo : dataSet) {
                Data data = dataNoToDataMap.get(rawDataNo);
                if (data == null) {
                    return "Data: <strong>" + rawDataNo + "</strong>, not found, please contact the administrator";
                }
                data = data.getTempData();
                runSet.add(data.getRunNo());
            }

            List<Run> runList = runRepository.findAllByRunNoIn(runSet);
            Map<String, Run> runNoToRunMap = runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

            List<String> expNos = runList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getExpNo())
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<String> sapNos = runList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getSapNo())
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());

            List<Experiment> expList = experimentRepository.findAllByExpNoIn(expNos);
            List<Sample> sapList = sampleRepository.findAllBySapNoIn(sapNos);

            List<String> projNos = expList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getProjectNo())
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());

            List<Project> projectList = projectRepository.findAllByProjectNoIn(projNos);

            Map<String, Project> projNoToProjMap = projectList.stream().collect(Collectors.toMap(Project::getProjectNo, Function.identity(), (existingValue, newValue) -> existingValue));
            Map<String, Experiment> expNoToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
            Map<String, Sample> sapNoToSapMap = sapList.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

            for (String runNo : runSet) {
                Run run = runNoToRunMap.get(runNo);
                if (run == null) {
                    continue;
                }
                if (run.getTempData() != null) {
                    run = run.getTempData();
                }
                String expNo = run.getExpNo();
                if (!passIds.contains(expNo) && !expNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                    // Experiment experiment = experimentRepository.findByNo(expNo);
                    Experiment experiment = expNoToExpMap.get(expNo);
                    if (!subNo.equals(experiment.getTempData().getSubNo())) {
                        return "Experiment: <strong>" + experiment.getName() + "</strong>, not reviewed in Submission ID: <strong>" + experiment.getTempData().getSubNo() + commTip;
                    }
                    String projectNo = experiment.getProjectNo();
                    if (!passIds.contains(projectNo) && !projectNo.startsWith(SequenceType.PROJECT.getPrefix())) {
                        // Project project = projectRepository.findByNo(projectNo);
                        Project project = projNoToProjMap.get(projectNo);
                        if (!subNo.equals(project.getTempData().getSubNo())) {
                            return "Project: <strong>" + project.getName() + "</strong>, not reviewed in Submission ID: <strong>" + project.getTempData().getSubNo() + commTip;
                        }
                        passIds.add(projectNo);
                    }
                    passIds.add(expNo);
                }
                String sapNo = run.getSapNo();
                if (!passIds.contains(sapNo) && !sapNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
                    // Sample sample = sampleRepository.findByNo(sapNo);
                    Sample sample = sapNoToSapMap.get(sapNo);
                    if (!subNo.equals(sample.getTempData().getSubNo())) {
                        return "Sample: <strong>" + sample.getName() + "</strong>, not reviewed in Submission ID: <strong>" + sample.getTempData().getSubNo() + commTip;
                    }
                    passIds.add(sapNo);
                }
            }
        }

        if (SubmissionDataTypeEnum.analysisData.name().equals(dataType)) {
            Set<String> dataSet = getDataSet(submission);

            Set<String> analysisSet = new HashSet<>();
            List<Data> dataList = dataRepository.findAllTempByDataNoIn(dataSet);
            Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));

            List<String> analNos = dataList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getAnalNo())
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());

            List<Analysis> analList = analysisRepository.findAllByAnalysisNoIn(analNos);
            Map<String, Analysis> analNoToAnalMap = analList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

            for (String dataNo : dataSet) {
                // Data data = dataRepository.findByNo(dataNo);
                Data data = dataNoToDataMap.get(dataNo);
                if (data == null) {
                    return "Data: <strong>" + dataNo + "</strong>, not found, please contact the administrator";
                }
                if (data.getTempData() != null) {
                    data = data.getTempData();
                }
                analysisSet.add(data.getAnalNo());
            }

            for (String analNo : analysisSet) {
                if (!passIds.contains(analNo) && !analNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    // Analysis analysis = analysisRepository.findByAnalNo(analNo);
                    Analysis analysis = analNoToAnalMap.get(analNo);
                    if (!subNo.equals(analysis.getTempData().getSubNo())) {
                        return "Analysis: <strong>" + analysis.getName() + "</strong>, not reviewed in Submission ID: <strong>" + analysis.getTempData().getSubNo() + commTip;
                    }
                    passIds.add(analNo);
                }
            }
        }
        return null;
    }

    private String updateProject(String projNo, String creator, Submitter submitter, List<String> updateEsProjectNos) {
        if (StrUtil.isBlank(projNo)) {
            return null;
        }

        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("project not found"));
        Project tempData = project.getTempData();
        Boolean isInited = StrUtil.equals(project.getAudited(), AuditEnum.init.name());

        ProjectDTOMapper.INSTANCE.tempToDb(tempData, project);
        project.setTempData(null);
        project.setSubmitter(submitter);
        project.setAudited(AuditEnum.audited.name());
        project.setUpdateDate(new Date());

        // 正式ID
        if (projNo.startsWith(SequenceType.PROJECT.getPrefix())) {
            projectRepository.save(project);
            if (!isInited) {
                updateEsProjectNos.add(projNo);
            }
            submitPublish(AuthorizeType.project, project.getProjectNo());
            return projNo;
        }

        // 非正式ID
        project.setProjectNo(null);

        Project saveProj = projectRepository.save(project);
        final String newProjNo = saveProj.getProjectNo();

        // 更新Experiment中temp_data的引用
        Query query = new Query(Criteria.where("creator").is(creator).and("temp_data").exists(true).and("temp_data.proj_no").is(projNo));
        Update update = new Update();
        update.set("temp_data.proj_no", newProjNo);
        mongoTemplate.updateMulti(query, update, Experiment.class);

        Query query2 = new Query(Criteria.where("creator").is(creator).and("proj_no").is(projNo));
        Update update2 = new Update();
        update2.set("proj_no", newProjNo);
        mongoTemplate.updateMulti(query2, update2, Experiment.class);

        // 更新文献
        updatePublish(AuthorizeType.project, projNo, newProjNo);
        // 提交文献
        submitPublish(AuthorizeType.project, newProjNo);
        return newProjNo;
    }

    private String updateExperiment(String expNo, String creator, Submitter submitter, List<String> updateEsExpNos) {
        if (StrUtil.isBlank(expNo)) {
            return null;
        }

        Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("experiment not found"));
        Experiment tempData = experiment.getTempData();
        Boolean isInited = StrUtil.equals(experiment.getAudited(), AuditEnum.init.name());

        ExperimentDTOMapper.INSTANCE.tempToDb(tempData, experiment);
        experiment.setTempData(null);
        experiment.setSubmitter(submitter);
        experiment.setAudited(AuditEnum.audited.name());
        experiment.setUpdateDate(new Date());

        // 正式ID
        if (expNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
            experimentRepository.save(experiment);
            if (!isInited) {
                updateEsExpNos.add(expNo);
            }
            submitPublish(AuthorizeType.experiment, experiment.getExpNo());
            return expNo;
        }
        // 非正式ID
        experiment.setExpNo(null);

        Experiment saveExp = experimentRepository.save(experiment);
        final String newExpNo = saveExp.getExpNo();

        // 更新Experiment中temp_data的引用
        Query query = new Query(Criteria.where("creator").is(creator).and("temp_data").exists(true).and("temp_data.exp_no").is(expNo));
        Update update = new Update();
        update.set("temp_data.exp_no", newExpNo);
        mongoTemplate.updateMulti(query, update, Run.class);

        Query query2 = new Query(Criteria.where("creator").is(creator).and("exp_no").is(expNo));
        Update update2 = new Update();
        update2.set("exp_no", newExpNo);
        mongoTemplate.updateMulti(query2, update2, Run.class);

        // 更新文献
        updatePublish(AuthorizeType.experiment, expNo, newExpNo);
        submitPublish(AuthorizeType.experiment, newExpNo);
        return newExpNo;
    }

    private String updateSample(String sapNo, String creator, Submitter submitter, List<String> updateEsSapNos) {
        if (StrUtil.isBlank(sapNo)) {
            return null;
        }

        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("Sample not found"));
        Sample tempData = sample.getTempData();
        Boolean isInited = StrUtil.equals(sample.getAudited(), AuditEnum.init.name());

        SampleDTOMapper.INSTANCE.tempToDb(tempData, sample);
        sample.setTempData(null);
        sample.setSubmitter(submitter);
        sample.setAudited(AuditEnum.audited.name());
        sample.setUpdateDate(new Date());

        // 正式ID
        if (sapNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
            sampleRepository.save(sample);
            if (!isInited) {
                updateEsSapNos.add(sapNo);
            }
            submitPublish(AuthorizeType.sample, sample.getSapNo());
            return sapNo;
        }
        // 非正式ID
        sample.setSapNo(null);

        Sample saveSap = sampleRepository.save(sample);
        final String newSapNo = saveSap.getSapNo();

        // 更新Run中temp_data的引用
        Query query = new Query(Criteria.where("creator").is(creator).and("temp_data").exists(true).and("temp_data.sap_no").is(sapNo));
        Update update = new Update();
        update.set("temp_data.sap_no", newSapNo);
        mongoTemplate.updateMulti(query, update, Run.class);

        Query query2 = new Query(Criteria.where("creator").is(creator).and("sap_no").is(sapNo));
        Update update2 = new Update();
        update2.set("sap_no", newSapNo);
        mongoTemplate.updateMulti(query2, update2, Run.class);

        updatePublish(AuthorizeType.sample, sapNo, newSapNo);
        submitPublish(AuthorizeType.sample, newSapNo);
        return newSapNo;
    }

    private String updateAnalysis(String analNo, String creator, Submitter submitter, List<String> updateEsAnalNos) {
        if (StrUtil.isBlank(analNo)) {
            return null;
        }

        Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("Analysis not found"));
        Analysis tempData = analysis.getTempData();
        Boolean isInited = StrUtil.equals(analysis.getAudited(), AuditEnum.init.name());

        AnalysisDTOMapper.INSTANCE.tempToDb(tempData, analysis);
        analysis.setTempData(null);
        analysis.setSubmitter(submitter);
        analysis.setAudited(AuditEnum.audited.name());
        analysis.setUpdateDate(new Date());

        // 正式ID
        if (analNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
            analysisRepository.save(analysis);
            if (!isInited) {
                updateEsAnalNos.add(analNo);
            }
            submitPublish(AuthorizeType.analysis, analysis.getAnalysisNo());
            return analNo;
        }
        // 非正式ID
        analysis.setAnalysisNo(null);

        Analysis saveAnal = analysisRepository.save(analysis);
        final String newAnal = saveAnal.getAnalysisNo();

        // 更新Data中temp_data的引用
        Query query = new Query(Criteria.where("creator").is(creator).and("temp_data").exists(true).and("temp_data.anal_no").is(analNo));
        Update update = new Update();
        update.set("temp_data.anal_no", newAnal);
        mongoTemplate.updateMulti(query, update, Data.class);

        Query query2 = new Query(Criteria.where("creator").is(creator).and("anal_no").is(analNo));
        Update update2 = new Update();
        update2.set("anal_no", newAnal);
        mongoTemplate.updateMulti(query2, update2, Data.class);

        updatePublish(AuthorizeType.analysis, analNo, newAnal);
        submitPublish(AuthorizeType.analysis, newAnal);
        return newAnal;
    }

    private void updateRawData(List<Data> dataList, final String oldRunNo, String creator, Map<String, String> idMap) {
        if (CollUtil.isEmpty(dataList) || StrUtil.isBlank(oldRunNo)) {
            return;
        }

        String newRunNo = oldRunNo;
        Run run = runRepository.findTempByNo(oldRunNo); // 查询run的temp_data找到run
        if (run != null) {
            Run tempRun = run.getTempData(); // 把run的内层的temp_data的覆盖外层数据
            RunDTOMapper.INSTANCE.tempToDb(tempRun, run);
            run.setTempData(null);
            run.setAudited(AuditEnum.audited.name()); // 审核通过状态
            run.setUpdateDate(new Date());
            // 如果是OER开头则是正式Run ID，不需要做后续处理
            if (oldRunNo.startsWith(SequenceType.RUN.getPrefix())) {
                runRepository.save(run);
            } else {
                run.setRunNo(null);
                run = runRepository.save(run);
                newRunNo = run.getRunNo();

                idMap.put(oldRunNo, newRunNo);
                // 更新Data中内层的引用
                Query query = new Query(Criteria.where("creator").is(creator).and("temp_data").exists(true).and("temp_data.run_no").is(oldRunNo));
                Update update = new Update();
                update.set("temp_data.run_no", newRunNo);
                mongoTemplate.updateMulti(query, update, Data.class);
                // 更新Data中外层的引用
                Query query2 = new Query(Criteria.where("creator").is(creator).and("run_no").is(oldRunNo));
                Update update2 = new Update();
                update2.set("run_no", newRunNo);
                mongoTemplate.updateMulti(query2, update2, Data.class);
            }
        }

        for (Data data : dataList) {
            Data tempData = data.getTempData(); // 取出temp_data，temp_data是用户在submission中编辑的临时数据

            DataDTOMapper.INSTANCE.tempToDb(tempData, data); // 把data内层用户编辑的数据覆盖data的外层
            data.setTempData(null); // 审核通过把外层data的temp_data清空
            data.setUpdateDate(new Date());
            data.setRunNo(newRunNo);
            dataRepository.save(data);

            // 如果系统启用了FastQc，就得同步exp_no和sap_no的信息到FastQc_task
            updateFastQcTask(data, run);
        }
    }

    private void updateAnalysisData(String analDataNo) {
        if (StrUtil.isBlank(analDataNo)) {
            return;
        }
        Data data = dataRepository.findByDatNo(analDataNo).orElseThrow(() -> new ServiceException("Data not found"));
        Data tempData = data.getTempData();

        DataDTOMapper.INSTANCE.tempToDb(tempData, data);
        data.setTempData(null);
        data.setUpdateDate(new Date());
        dataRepository.save(data);
    }

    /**
     * 将data对应的FastQC的expNo和sapNo的信息补充到FastQC中
     */
    private void updateFastQcTask(Data data, Run run) {
        String datNo = data.getDatNo();
        Optional<FastQCTask> optional = fastQCTaskRepository.findFirstByDataNo(datNo);
        if (optional.isPresent() && run != null) {
            Update update = new Update();
            update.set("exp_no", run.getExpNo());
            update.set("sap_no", run.getSapNo());
            mongoTemplate.updateFirst(Query.query(Criteria.where("data_no").is(datNo)), update, FastQCTask.class);
        }
    }

    public SubDetailVO detail(String subNo, boolean init) {
        SubDetailVO vo = new SubDetailVO();
        Submission submission = getSubmissionByNo(subNo);

        String lockKey = PASS_SUBMISSION_TASK_KEY + subNo;
        Boolean processing = redisService.hasKey(lockKey);

        vo.setProcessing(processing);

        if (processing && !init || !init && (SubmissionStatusEnum.complete.name().equals(submission.getStatus()) || SubmissionStatusEnum.deleted.name().equals(submission.getStatus()))) {
            SubDataLog subDataLog = completeDataLogRepository.findTopBySubNoOrderByCreateTimeDesc(subNo).orElseThrow(() -> new ServiceException("No '" + subNo + "' records found in completed Submissions"));
            return JSON.parseObject(subDataLog.getData(), SubDetailVO.class);
        }

        String creator = submission.getCreator();

        vo.setSubmission(submission);
        if (submission.getProjNo() != null) {
            ProjectVO project = projectService.getProjectByNo(submission.getProjNo());
            vo.setProject(project);
        }

        // Raw Data
        // Experiment
        List<String> expMultipleNos = submission.getExpMultipleNos();
        if (CollUtil.isEmpty(expMultipleNos)) {
            expMultipleNos = new ArrayList<>();
        }
        String expSingleNo = submission.getExpSingleNo();
        if (expSingleNo != null) {
            expMultipleNos.add(expSingleNo);

            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.experiment, expSingleNo);
            vo.setExpPublish(publishVo);
        }

        List<SubExpSampleVO> expDataList = experimentService.getExpTypeDataList(expMultipleNos, creator);
        vo.setExperiment(expDataList);

        // Sample
        List<String> sapNos = new ArrayList<>();
        List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
        if (CollUtil.isNotEmpty(sapMultipleData)) {
            for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                sapNos.addAll(sapMultipleDatum.getNos());
            }
        }

        String sapSingleNo = submission.getSapSingleNo();
        if (sapSingleNo != null) {
            sapNos.add(sapSingleNo);

            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.sample, sapSingleNo);
            vo.setSapPublish(publishVo);
        }

        List<SubExpSampleVO> sampleDataList = sampleService.getSampleDataList(sapNos, creator);
        vo.setSample(sampleDataList);

        // Raw Data Archive Data
        List<Map<String, Object>> rawDataArchiveData = archiveService.getMultiRawDataArchiveBySubNo(subNo, false, true);
        vo.setRawDataArchiveData(rawDataArchiveData);

        // Analysis Data
        List<AnalysisImportDTO> analysis = analysisService.getMultiAnalysisBySubNo(subNo, false);
        vo.setAnalysis(analysis);

        String analSingleNo = submission.getAnalSingleNo();
        if (analSingleNo != null) {
            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.analysis, analSingleNo);
            vo.setAnalysisPublish(publishVo);
        }

        // Analysis Data Archive Data
        List<ArchiveAnalysisImportDTO> analArchive = archiveService.getMultiAnalArchiveBySubNo(subNo, false, true);
        vo.setAnalysisArchiveData(analArchive);

        PublishVO publishBaseInfo = publishService.getAuditPublishInfo(subNo);
        if (publishBaseInfo != null) {
            vo.setPublish(publishBaseInfo);
        }
        return vo;
    }

    /**
     * 删除submission
     */
    public List<DeleteErrorMsgVO> deleted(String subNo) {
        if (SecurityUtils.getMember() == null) {
            throw new NotPermissionException("Not permission");
        }
        Submission submission = getSubmissionByNo(subNo);
        if (submission == null) {
            throw new ServiceException("The submission record was not found");
        }
        String status = submission.getStatus();
        if (SubmissionStatusEnum.deleted.name().equals(status) || SubmissionStatusEnum.complete.name().equals(status) || SubmissionStatusEnum.waiting.name().equals(status) || SubmissionStatusEnum.reviewing.name().equals(status)) {
            throw new ServiceException("The current submission status is " + submission.getStatus() + ". Do not allow deletion");
        }

        String memberId = SecurityUtils.getMemberId();
        String creator = submission.getCreator();

        if (!creator.equals(memberId)) {
            throw new ServiceException("Not Permission");
        }

        String lockKey = "delete_submission_key:" + memberId + ":" + subNo;
        Collection<String> keys = redisService.keys("delete_submission_key:" + memberId + ":*");
        if (CollUtil.isNotEmpty(keys)) {
            String key = keys.iterator().next();
            String deleteSubNo = key.replace("delete_submission_key:" + memberId + ":", "");
            throw new ServiceException("You have a Submission [" + deleteSubNo + "] being deleted, please try again later. A user can only delete one Submission at the same time");
        }
        try {
            redisService.setCacheObject(lockKey, true, 3L, TimeUnit.HOURS);

            SubDetailVO detail = detail(subNo, true);

            // 删除数据记录一个快照
            SubDataLog subDataLog = new SubDataLog();
            subDataLog.setSubNo(subNo);
            subDataLog.setStatus(SubmissionStatusEnum.deleted.name());
            subDataLog.setCreator(submission.getCreator());
            subDataLog.setCreateTime(new Date());

            submission.setStatus(SubmissionStatusEnum.deleted.name());
            detail.setSubmission(submission);
            String dataJson = JSON.toJSONString(detail);

            subDataLog.setData(dataJson);

            List<DeleteErrorMsgVO> vos = new ArrayList<>();

            // 校验通过，需要删除的数据
            Project deletedProj = null;

            // 校验项目
            String projNo = submission.getProjNo();

            if (projNo != null) {
                deletedProj = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("Not found data"));

                // 查询在experiment中是否被占用
                List<Experiment> experiments = experimentRepository.findTempByProjNo(projNo);

                if (CollUtil.isNotEmpty(experiments)) {

                    for (Experiment experiment : experiments) {

                        Experiment tempData = experiment.getTempData();
                        String tempSubNo = tempData.getSubNo();

                        if (tempSubNo.equals(subNo)) {
                            continue;
                        }

                        DeleteErrorMsgVO msgVO = DeleteErrorMsgVO.builder()
                                .target(deletedProj.getName()).type(AuthorizeType.experiment.name()).name(tempData.getName()).subNo(tempSubNo)
                                .no(AuditEnum.init.name().equals(experiment.getAudited()) ? UNASSIGNED_ID : tempData.getExpNo()).build();

                        vos.add(msgVO);
                    }
                }
            }

            // 校验组学
            Set<String> deletedExp = new HashSet<>();

            List<String> expNos = submission.getExpMultipleNos();
            if (CollUtil.isEmpty(expNos)) {
                expNos = new ArrayList<>();
            }
            if (submission.getExpSingleNo() != null) {
                expNos.add(submission.getExpSingleNo());
            }

            if (CollUtil.isNotEmpty(expNos)) {

                List<Run> allRuns = runRepository.findAllNameByFieldAndNoIn("exp_no", expNos);

                LinkedHashMap<String, List<Run>> expNoToRunsMap = new LinkedHashMap<>();
                for (Run run : allRuns) {
                    expNoToRunsMap.computeIfAbsent(run.getTempData().getExpNo(), k -> new ArrayList<>()).add(run);
                }

                for (String expNo : expNos) {
                    deletedExp.add(expNo);

                    // 校验当前组学在哪些Run中已被使用
                    // List<Run> runs = runRepository.findAllNameByFieldAndNo("exp_no", expNo);
                    List<Run> runs = expNoToRunsMap.get(expNo);

                    if (CollUtil.isNotEmpty(runs)) {
                        // 获取 run tempData中的expNos
                        List<String> tempExpNos = runs.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getExpNo())
                                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                        List<Experiment> expList = experimentRepository.findAllByExpNoIn(tempExpNos);
                        Map<String, Experiment> expNoToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
                        for (Run run : runs) {
                            Run tempData = run.getTempData();
                            if (tempData == null) {
                                continue;
                            }
                            String tempSubNo = tempData.getSubNo();
                            if (tempSubNo.equals(subNo)) {
                                continue;
                            }
                            Experiment experiment = expNoToExpMap.get(expNo);
                            // Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("not found experiment: " + expNo));

                            DeleteErrorMsgVO msgVO = DeleteErrorMsgVO.builder()
                                    .target(experiment.getName()).type(AuthorizeType.run.name()).name(tempData.getName()).subNo(tempSubNo)
                                    .no(AuditEnum.init.name().equals(tempData.getAudited()) ? UNASSIGNED_ID : tempData.getRunNo()).build();

                            vos.add(msgVO);
                        }
                    }
                }
            }

            // 校验样本
            Set<String> deletedSap = new HashSet<>();

            Set<String> sapNos = new HashSet<>();
            if (submission.getSapSingleNo() != null) {
                sapNos.add(submission.getSapSingleNo());
            }

            List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();

            if (CollUtil.isNotEmpty(sapMultipleData)) {
                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                    sapNos.addAll(sapMultipleDatum.getNos());
                }
            }

            if (CollUtil.isNotEmpty(sapNos)) {

                List<Run> allRuns = runRepository.findAllNameByFieldAndNoIn("sap_no", sapNos);
                LinkedHashMap<String, List<Run>> sapNoToRunsMap = new LinkedHashMap<>();
                for (Run run : allRuns) {
                    sapNoToRunsMap.computeIfAbsent(run.getTempData().getSapNo(), k -> new ArrayList<>()).add(run);
                }
                for (String sapNo : sapNos) {
                    deletedSap.add(sapNo);
                    // 校验当前组学在哪些Run中已被使用
                    // List<Run> runs = runRepository.findAllNameByFieldAndNo("sap_no", sapNo);
                    List<Run> runs = sapNoToRunsMap.get(sapNo);

                    if (CollUtil.isNotEmpty(runs)) {
                        List<String> tempSapNos = runs.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getSapNo())
                                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                        List<Sample> expList = sampleRepository.findAllBySapNoIn(tempSapNos);
                        Map<String, Sample> sapNoToSapMap = expList.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));
                        for (Run run : runs) {
                            Run tempData = run.getTempData();
                            if (tempData == null) {
                                continue;
                            }
                            String tempSubNo = tempData.getSubNo();
                            if (tempSubNo.equals(subNo)) {
                                continue;
                            }
                            // Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
                            Sample sample = sapNoToSapMap.get(sapNo);

                            DeleteErrorMsgVO msgVO = DeleteErrorMsgVO.builder()
                                    .target(sample.getName()).type(AuthorizeType.run.name()).name(tempData.getName()).subNo(tempSubNo)
                                    .no(AuditEnum.init.name().equals(tempData.getAudited()) ? UNASSIGNED_ID : tempData.getRunNo()).build();

                            vos.add(msgVO);
                        }
                    }
                }
            }

            // 校验分析
            Set<String> deletedAnal = new HashSet<>();

            List<String> analNos = submission.getAnalMultipleNos();
            if (CollUtil.isEmpty(analNos)) {
                analNos = new ArrayList<>();
            }
            if (submission.getAnalSingleNo() != null) {
                analNos.add(submission.getAnalSingleNo());
            }

            if (CollUtil.isNotEmpty(analNos)) {

                for (String analNo : analNos) {
                    deletedAnal.add(analNo);

                    // 查询是否有Data绑定了此AnalNo
                    List<Data> dataList = dataRepository.findTempDetailByAnalNo(analNo);
                    List<String> tempAnalNos = dataList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getAnalNo())
                            .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

                    List<Analysis> analList = analysisRepository.findAllByAnalysisNoIn(tempAnalNos);
                    Map<String, Analysis> analNoToAnalMap = analList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));
                    if (CollUtil.isNotEmpty(dataList)) {
                        for (Data data : dataList) {
                            Data tempData = data.getTempData();

                            String tempSubNo = tempData.getSubNo();
                            if (tempSubNo.equals(subNo)) {
                                continue;
                            }
                            // Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("not found analysis: " + analNo));
                            Analysis analysis = analNoToAnalMap.get(analNo);
                            DeleteErrorMsgVO msgVO = DeleteErrorMsgVO.builder()
                                    .target(analysis.getName()).type(AuthorizeType.data.name()).name(tempData.getName()).subNo(tempSubNo)
                                    .no(tempData.getDatNo()).build();

                            vos.add(msgVO);
                        }
                    }
                }
            }

            // 如果存在错误信息，则返回前台提示
            if (CollUtil.isNotEmpty(vos)) {
                return vos;
            }

            // 回滚归档的数据
            Set<String> deletedData = new HashSet<>();
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                deletedData.addAll(submission.getRawDataMultipleNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                deletedData.addAll(submission.getAnalDataMultipleNos());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                deletedData.addAll(submission.getRawDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                deletedData.addAll(submission.getAnalysisDataNos());
            }

            if (CollUtil.isNotEmpty(deletedData)) {
                List<Data> revokeDataList = dataRepository.findAllByNos(deletedData, memberId);
                for (Data data : revokeDataList) {
                    String archived = data.getArchived();
                    if (ArchiveEnum.yes.name().equals(archived)) {
                        data.setTempData(null);
                    } else {
                        Data copyData = DataDTOMapper.INSTANCE.copy(data);
                        copyData.setRunNo(data.getRunNo());
                        copyData.setSubNo(data.getSubNo());
                        copyData.setAnalNo(data.getAnalNo());
                        data.setTempData(copyData);
                    }
                }
                dataRepository.saveAll(revokeDataList);
            }
            runRepository.deletedUselessRun(memberId);

            // 未分配正式ID的Project直接删除
            if (deletedProj != null) {
                if (AuditEnum.init.name().equals(deletedProj.getAudited())) {
                    projectRepository.delete(deletedProj);
                } else {
                    // 有正式ID的，回滚temp
                    deletedProj.setTempData(null);
                    projectRepository.save(deletedProj);
                }
            }

            // 删除或回滚数据
            experimentRepository.deleteTempByNosAndCreator(deletedExp, memberId, null);
            sampleRepository.deleteTempByNosAndCreator(deletedSap, memberId, null);
            analysisRepository.deleteTempByNosAndCreator(deletedAnal, memberId);

            // 删除或者回滚publish
            List<Publish> publishes = publishRepository.findTempBySubNo(subNo);
            if (CollUtil.isNotEmpty(publishes)) {
                for (Publish publish : publishes) {
                    if (AuditEnum.unaudited.name().equals(publish.getAudited())) {
                        publish.setDeleted(true);
                    }
                    publish.setTempData(null);
                    publishRepository.save(publish);
                }
            }

            saveSubmission(submission, false);

            // 记录被删除的数据
            completeDataLogRepository.save(subDataLog);

            return null;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            redisService.deleteObject(lockKey);
        }
    }

    public void rejectToEdit(String subNo) {
        if (SecurityUtils.getMember() == null) {
            throw new NotPermissionException("Not permission");
        }
        Submission submission = getSubmissionByNo(subNo);
        if (submission == null) {
            throw new ServiceException("The submission record was not found");
        }

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.rejected.name().equals(status)) {
            throw new ServiceException("The current submission status is " + submission.getStatus() + ". Do not allow to editing");
        }

        submission.setStatus(SubmissionStatusEnum.editing.name());
        saveSubmission(submission);
    }
}
