package org.biosino.esindex.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.Select;
import org.biosino.common.es.entity.BiomeEs;
import org.biosino.common.es.entity.DiseaseEs;
import org.biosino.common.es.entity.MicroarrayPlatformEs;
import org.biosino.common.es.entity.TaxonomyEs;
import org.biosino.common.es.mapper.BiomeMapper;
import org.biosino.common.es.mapper.DiseaseMapper;
import org.biosino.common.es.mapper.PlatformMapper;
import org.biosino.common.es.mapper.TaxonomyMapper;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.dromara.easyes.core.biz.SAPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class DictService {
    @Autowired
    private PlatformMapper platformMapper;
    @Autowired
    private DiseaseMapper diseaseMapper;
    @Autowired
    private TaxonomyMapper taxonomyMapper;
    @Autowired
    private BiomeMapper biomeMapper;

    private static final int LIMIT_SIZE = 200;

    public List<Select> findPlatform(String keyword) {
        List<Select> result = new ArrayList<>();
        if (keyword == null) {
            keyword = "";
        }

        LambdaEsQueryWrapper<MicroarrayPlatformEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.like(MicroarrayPlatformEs::getTitle, keyword).limit(LIMIT_SIZE);
        List<MicroarrayPlatformEs> platforms = platformMapper.selectList(wrapper);

        for (MicroarrayPlatformEs platform : platforms) {
            // Label暂时就是Value
            Select select = Select.builder()
                    .label(platform.getTitle() + " (" + platform.getAccession() + ")")
                    .value(platform.getTitle()).build();
            result.add(select);
        }
        return result;
    }

    public boolean existPlatform(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return false;
        }
        LambdaEsQueryWrapper<MicroarrayPlatformEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(MicroarrayPlatformEs::getTitle, keyword).limit(1);
        MicroarrayPlatformEs platforms = platformMapper.selectOne(wrapper);
        return platforms != null;
    }

    public List<Select> findTaxonomy(String keyword) {
        keyword = StrUtil.trimToNull(keyword);

        final LambdaEsQueryWrapper<TaxonomyEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.select(TaxonomyEs::getTaxId, TaxonomyEs::getScientificName, TaxonomyEs::getLineage);
        if (keyword != null) {
            wrapper.like(TaxonomyEs::getScientificName, keyword, 0.5F)
                    .or().eq(TaxonomyEs::getTaxId, keyword, 5F);
        }
        // 数据量过大不能使用limit
        // wrapper.limit(LIMIT_SIZE);
        // final List<TaxonomyEs> taxonomyEsList = taxonomyMapper.selectList(wrapper);
        wrapper.sortByScore(true);
        final SAPageInfo<TaxonomyEs> saPageInfo = taxonomyMapper.searchAfterPage(wrapper, null, LIMIT_SIZE);
        final List<TaxonomyEs> taxonomyEsList = saPageInfo.getList();

        final List<Select> result = new ArrayList<>();
        for (TaxonomyEs taxonomyEs : taxonomyEsList) {
            final Select select = Select.builder()
                    .label(taxonomyEs.getScientificName())
                    .value(taxonomyEs.getTaxId())
                    .title(taxonomyEs.getLineage())
                    .build();
            result.add(select);
        }
        return result;
    }

    /**
     * 判断name是否存在，同时返回其taxId
     */
   /* public String findTaxIdByExistName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        LambdaEsQueryWrapper<TaxonomyEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(TaxonomyEs::getScientificName, name).limit(1);
        TaxonomyEs data = taxonomyMapper.selectOne(wrapper);
        if (data == null) {
            // 返回null,则表示name不存在
            return null;
        } else {
            // 不返回null,则表示name存在，若taxId为null，则需转为空字符串
            return StrUtil.trimToEmpty(data.getTaxId());
        }
    }*/
    public List<Select> findDisease(String keyword) {
        List<Select> result = new ArrayList<>();
        if (keyword == null) {
            keyword = "";
        }

        LambdaEsQueryWrapper<DiseaseEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.like(DiseaseEs::getLbl, keyword).limit(LIMIT_SIZE);
        List<DiseaseEs> diseaseEsList = diseaseMapper.selectList(wrapper);

        for (DiseaseEs diseaseEs : diseaseEsList) {
            Select select = Select.builder()
                    .label(diseaseEs.getLbl())
                    .value(diseaseEs.getLbl()).build();
            result.add(select);
        }
        return result;
    }

    public List<Select> findBiome(String type, String keyword) {
        List<Select> result = new ArrayList<>();
        if (keyword == null) {
            keyword = "";
        }

        LambdaEsQueryWrapper<BiomeEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(BiomeEs::getType, type);
        wrapper.like(BiomeEs::getValue, keyword).limit(LIMIT_SIZE);
        List<BiomeEs> biomeList = biomeMapper.selectList(wrapper);

        if (CollUtil.isEmpty(biomeList)) {
            return result;
        }

        for (BiomeEs biome : biomeList) {
            Select select = Select.builder()
                    .label(biome.getValue())
                    .value(biome.getValue()).build();
            result.add(select);
        }
        return result;
    }

    public boolean existDisease(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return false;
        }
        LambdaEsQueryWrapper<DiseaseEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(DiseaseEs::getLbl, keyword).limit(1);
        DiseaseEs diseaseEs = diseaseMapper.selectOne(wrapper);
        return diseaseEs != null;
    }

    public List<String> getTaxIdByName(String name) {
        if (StrUtil.isBlank(name)) {
            return null;
        }

        final LambdaEsQueryWrapper<TaxonomyEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(TaxonomyEs::getScientificName, name);
        wrapper.select(TaxonomyEs::getTaxId);
        wrapper.orderByAsc(TaxonomyEs::getSid);
        final List<TaxonomyEs> taxonomyEsList = taxonomyMapper.searchAfterPage(wrapper, null, 1000).getList();
        if (CollUtil.isNotEmpty(taxonomyEsList)) {
            final Set<String> set = new LinkedHashSet<>();
            for (TaxonomyEs taxonomyEs : taxonomyEsList) {
                set.add(taxonomyEs.getTaxId());
            }
            return new ArrayList<>(set);
        }
        return null;
    }

    public List<TaxonomyNodeDTO> getTaxByTaxId(String taxId) {
        if (StrUtil.isBlank(taxId)) {
            return null;
        }
        LambdaEsQueryWrapper<TaxonomyEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(TaxonomyEs::getTaxId, taxId);
        wrapper.select(TaxonomyEs::getTaxId, TaxonomyEs::getScientificName);

        wrapper.orderByAsc(TaxonomyEs::getSid);
        List<TaxonomyEs> taxonomyEs = taxonomyMapper.searchAfterPage(wrapper, null, 1000).getList();
        if (CollUtil.isNotEmpty(taxonomyEs)) {
            final List<TaxonomyNodeDTO> list = new ArrayList<>();
            for (TaxonomyEs es : taxonomyEs) {
                final TaxonomyNodeDTO dto = new TaxonomyNodeDTO();
                dto.setTaxId(es.getTaxId());
                dto.setScientificName(es.getScientificName());
                list.add(dto);
            }
            return list;
        }
        return null;
    }

    public Boolean existBiomeByTypeAndValue(String type, String value) {
        if (StrUtil.isBlank(type) || StrUtil.isBlank(value)) {
            return false;
        }
        LambdaEsQueryWrapper<BiomeEs> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.eq(BiomeEs::getType, type).eq(BiomeEs::getValue, value).limit(1);
        BiomeEs biomeEs = biomeMapper.selectOne(wrapper);
        return biomeEs != null;
    }
}

