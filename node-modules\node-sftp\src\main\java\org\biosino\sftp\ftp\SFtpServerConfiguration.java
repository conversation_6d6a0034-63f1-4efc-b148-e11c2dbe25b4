package org.biosino.sftp.ftp;

import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.apache.sshd.server.session.ServerSession;
import org.apache.sshd.sftp.common.SftpConstants;
import org.apache.sshd.sftp.common.SftpException;
import org.apache.sshd.sftp.server.FileHandle;
import org.apache.sshd.sftp.server.SftpFileSystemAccessor;
import org.apache.sshd.sftp.server.SftpSubsystemFactory;
import org.apache.sshd.sftp.server.SftpSubsystemProxy;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.ConfigUtils;
import org.biosino.sftp.authentication.SFtpPasswordAuthenticator;
import org.biosino.sftp.config.SFtpProperties;
import org.biosino.sftp.db.FtpDbService;
import org.biosino.sftp.ftp.download.DownloadEventListener;
import org.biosino.sftp.ftp.download.DownloadFileSystemFactory;
import org.biosino.sftp.ftp.upload.UploadEventListener;
import org.biosino.sftp.ftp.upload.UploadFileSystemFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * <p>
 * SFTP Server 相关 Bean 的初始化
 */
@Configuration
@Slf4j
public class SFtpServerConfiguration {

    @Autowired
    private SFtpPasswordAuthenticator passwordAuthenticator;

    @Autowired
    private RedisService redisService;

    /**
     * 上传 sftp 服务
     *
     * @param sftpProperties
     * @return
     */
    @Bean(name = "uploadServer")
    @SneakyThrows
    SshServer uploadServer(SFtpProperties sftpProperties, FtpDbService ftpDbService) {

        SFtpProperties.SFtpArg uploadArg = sftpProperties.getUpload();

        SshServer server = SshServer.setUpDefaultServer();

        server.setPort(uploadArg.getPort());

        // 设置登录方式
        server.setPasswordAuthenticator(passwordAuthenticator);

        server.setKeyPairProvider(new SimpleGeneratorHostKeyProvider(Paths.get(uploadArg.getHostKey())));

        // 设置 SFTP 子系统
        SftpSubsystemFactory.Builder builder = new SftpSubsystemFactory.Builder();
        builder.addSftpEventListener(new UploadEventListener(ftpDbService));
        builder.withFileSystemAccessor(new SftpFileSystemAccessor() {
            @Override
            public SeekableByteChannel openFile(ServerSession session, SftpSubsystemProxy subsystem, FileHandle fileHandle, Path file, String handle, Set<? extends OpenOption> options, FileAttribute<?>... attrs) throws IOException {
                String blackList = ConfigUtils.getConfig("node.file.suffixBlackList");
                if (StrUtil.isNotBlank(blackList)) {
                    String[] split = blackList.split("\\|");
                    if (StrUtil.endWithAnyIgnoreCase(fileHandle.getFile().toString(), split)) {
                        throw new SftpException(SftpConstants.SSH_FX_OP_UNSUPPORTED, StrUtil.format("File upload of {} type is not allowed.", blackList));
                    }
                }
                return SftpFileSystemAccessor.super.openFile(session, subsystem, fileHandle, file, handle, options, attrs);
            }
        });
        server.setSubsystemFactories(Collections.singletonList(builder.build()));

        // 设置文件系统
        server.setFileSystemFactory(new UploadFileSystemFactory());

        return server;
    }

    /**
     * 下载 sftp 服务
     *
     * @param sftpProperties
     * @return
     */
    @Bean(name = "downloadServer")
    @SneakyThrows
    SshServer downloadServer(SFtpProperties sftpProperties, FtpDbService ftpDbService) {

        SFtpProperties.SFtpArg downloadArgs = sftpProperties.getDownload();

        SshServer server = SshServer.setUpDefaultServer();

        server.setPort(downloadArgs.getPort());

        server.setKeyPairProvider(new SimpleGeneratorHostKeyProvider(Paths.get(downloadArgs.getHostKey())));

        // 设置 SFTP 子系统
        SftpSubsystemFactory.Builder builder = new SftpSubsystemFactory.Builder();
        builder.addSftpEventListener(new DownloadEventListener(ftpDbService, redisService));
        server.setSubsystemFactories(Collections.singletonList(builder.build()));

        server.setFileSystemFactory(new DownloadFileSystemFactory(ftpDbService));

        server.setPasswordAuthenticator(passwordAuthenticator);

        return server;
    }

}
