package org.biosino.system.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteTaskService;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Component
@Slf4j
public class RemoteTaskServiceFallbackFactory implements FallbackFactory<RemoteTaskService> {
    @Override
    public RemoteTaskService create(Throwable throwable) {
        log.error("Task服务调用失败:{}", throwable.getMessage());
        return new RemoteTaskService() {
            @Override
            public R pushFastQcTaskStartMsg(String source) {
                return R.fail("pushFastQcTaskStartMsg调用失败:" + throwable.getMessage());
            }

            @Override
            public R pushFastQcHpTaskStartMsg(String source) {
                return R.fail("pushFastQcHpTaskStartMsg调用失败:" + throwable.getMessage());
            }

            @Override
            public R pushSamToolTaskStartMsg(String source) {
                return R.fail("pushSamToolTaskStartMsg调用失败:" + throwable.getMessage());
            }

            @Override
            public R pushSamToolHpTaskStartMsg(String source) {
                return R.fail("pushSamToolHpTaskStartMsg调用失败:" + throwable.getMessage());
            }
        };
    }
}
