package org.biosino.sftp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Mapper
public interface FtpFileLogMapper extends BaseMapper<FtpFileLog> {


    @Select("select * from ftp_file_log where creator=#{creator} and path=#{path} limit 1")
    FtpFileLog findFirstByCreatorAndPath(@Param("creator") String creator, @Param("path") String path);

    @Select("select * from ftp_file_log where path=#{path} limit 1")
    FtpFileLog findFirstByPath(@Param("path") String path);

    @Select("select * from ftp_file_log where creator=#{creator} and path like #{path}+'%'")
    List<FtpFileLog> findByCreatorAndPathStartingWith(@Param("creator") String creator, @Param("path") String path);

    @Select("select * from ftp_file_log where creator=#{creator} and path=#{path} and status=#{status} limit 1")
    FtpFileLog findFirstByCreatorAndPathAndStatus(@Param("creator") String creator, @Param("path") String path, @Param("status") String status);

    @Select("select * from ftp_file_log where creator=#{creator} and path=#{path} and status in #{status}")
    List<FtpFileLog> findAllByCreatorAndPathAndStatusIn(@Param("creator") String creator, @Param("path") String path, @Param("status") List<String> status);

    @Select("select * from ftp_file_log where creator=#{creator}")
    List<FtpFileLog> findAllByCreator(@Param("creator") String creator);

    @Select("select * from ftp_file_log where creator=#{creator} and path=#{path}")
    List<FtpFileLog> findAllByCreatorAndPath(@Param("creator") String creator, @Param("path") String path);

    @Select("select * from ftp_file_log where creator=#{creator} and status in #{status}")
    List<FtpFileLog> findAllByCreatorAndStatusIn(@Param("creator") String creator, @Param("status") List<String> status);

    @Select("select * from ftp_file_log where creator=#{creator} and status in #{status} and name like #{name}")
    List<FtpFileLog> findAllByCreatorAndStatusInAndNameContains(@Param("creator") String creator, @Param("status") List<String> status, @Param("name") String name);

    @Select("select * from ftp_file_log where creator=#{creator} and status in #{status} and name=#{name} order by name asc")
    List<FtpFileLog> findAllByCreatorAndStatusInAndNameContainsOrderByNameAsc(@Param("creator") String creator, @Param("status") List<String> status, @Param("name") String name);

    @Transactional
    @Update("update ftp_file_log set status=#{status} where creator=#{creator} and path like #{path}")
    void updateFtpFileLogStatusByCreatorAndPathLike(@Param("status") String status, @Param("creator") String creator, @Param("path") String path);

    @Transactional
    @Update("update ftp_file_log set status=#{status} where creator=#{creator} and path=#{path}")
    void updateFtpFileLogStatusByCreatorAndPath(@Param("status") String status, @Param("creator") String creator, @Param("path") String path);

    @Transactional
    @Update("update ftp_file_log set path=replace(path, #{sourcePath},#{replaceTo}),update_time=now() where creator=#{creator} and path like #{path}")
    void updateFtpFileLogPathByCreatorAndPathLike(@Param("sourcePath") String sourcePath, @Param("replaceTo") String replaceTo, @Param("creator") String creator, @Param("path") String path);

    @Transactional
    @Update("update ftp_file_log set md5_file_content=#{md5FileContent} where id=#{id} ")
    void updateMd5Info(@Param("md5FileContent") String md5FileContent, @Param("id") String id);

    @Transactional
    @Update("update ftp_file_log set status=#{status} where id=#{id}")
    void updateFtpFileStatusById(@Param("status") String status, @Param("id") String id);
}
