package org.biosino.api.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.api.repository.DataCustomRepository;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    private static List<Criteria> baseConditions() {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        return condition;
    }

    @Override
    public List<Data> findAllFilePathByDataNos(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return new ArrayList<>();
        }
        List<Criteria> conditions = baseConditions();
        conditions.add(new Criteria().orOperator(
                Criteria.where("dat_no").in(dataNos),
                Criteria.where("used_ids").in(dataNos)
        ));

        Query query = new Query(new Criteria().andOperator(conditions));
        query.fields().include("dat_no").include("file_path").include("md5");
        return mongoTemplate.find(query, Data.class);

    }

    @Override
    public Page<Data> findPage(MetadataSearchDTO queryDTO) {

        List<Criteria> conditions = baseConditions();

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            conditions.add(new Criteria().orOperator(
                    Criteria.where("dat_no").in(queryDTO.getNos()),
                    Criteria.where("used_ids").in(queryDTO.getNos())
            ));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            conditions.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            conditions.add(Criteria.where("update_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            conditions.add(Criteria.where("update_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        Query query = new Query(new Criteria().andOperator(conditions));

        // 查询数据量
        long total = mongoTemplate.count(query, Data.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Data> content = mongoTemplate.find(query, Data.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public Page<Data> findDeletePage(MetadataSearchDTO queryDTO) {

        List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("security").in(SecurityEnum.deleteSecurity()));
        conditions.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            conditions.add(new Criteria().orOperator(
                    Criteria.where("dat_no").in(queryDTO.getNos()),
                    Criteria.where("used_ids").in(queryDTO.getNos())
            ));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            conditions.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            conditions.add(Criteria.where("update_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            conditions.add(Criteria.where("update_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        Query query = new Query(new Criteria().andOperator(conditions));

        // 查询数据量
        long total = mongoTemplate.count(query, Data.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Data> content = mongoTemplate.find(query, Data.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public Optional<Data> findByDatNo(String datNo) {
        if (StrUtil.isBlank(datNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(new Criteria().orOperator(
                Criteria.where("dat_no").is(datNo),
                Criteria.where("used_ids").in(datNo)
        ));

        Query query = new Query(new Criteria().andOperator(condition));

        Data data = mongoTemplate.findOne(query, Data.class);

        return Optional.ofNullable(data);
    }
}
