package org.biosino.job.task;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@Component("samToolTask")
@Slf4j
public class SamToolsTask {
    @Autowired
    private RemoteTaskService remoteTaskService;

    public void lowPriority() {
        log.info("当前时间 {}, 开始执行SamTool任务", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMAT));
        R r = remoteTaskService.pushSamToolTaskStartMsg(SecurityConstants.INNER);
        if (R.isError(r)) {
            log.error("pushSamToolTaskStartMsg Task Start error: {}", r.getMsg());
        }
    }

    public void highPriority() {
        log.info("当前时间 {}, 开始执行SamTool任务", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMAT));
        R r = remoteTaskService.pushSamToolHpTaskStartMsg(SecurityConstants.INNER);
        if (R.isError(r)) {
            log.error("pushSamToolHpTaskStartMsg Task Start error: {}", r.getMsg());
        }
    }

}
