package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Data
public class DataVO {
    private String id;

    @Excel(name = "Data ID")
    private String datNo;

    @Excel(name = "File Name")
    private String name;

    private String path;

    @Excel(name = "Source Path")
    private String sourcePath;

    @Excel(name = "Data Type")
    private String dataType;

    @Excel(name = "Upload Type")
    private String uploadType;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    private Long fileSize;

    @Excel(name = "md5")
    private String md5;

    @Excel(name = "File Size")
    private String readableFileSize;

    /* 归档相关字段 */
    private String analNo;

    private String analName;

    private String projectNo;

    private String projectName;

    private String expNo;

    private String expName;

    private String sapNo;

    private String sapName;

    private String runNo;

    private String runName;
}
