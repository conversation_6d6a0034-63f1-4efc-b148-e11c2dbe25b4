<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="searchFormRef" :model="queryParams" :inline="true">
        <el-form-item label="Data ID" prop="dataNo">
          <el-input
            v-model="queryParams.dataNoStr"
            style="width: 220px"
            type="textarea"
            :rows="1"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="File Name" prop="name">
          <el-input
            v-model="queryParams.name"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Analysis ID" prop="analNo">
          <el-input
            v-model="queryParams.analNoStr"
            style="width: 220px"
            clearable
            type="textarea"
            :rows="1"
          ></el-input>
        </el-form-item>
        <el-form-item label="Creator" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Create Date" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
          <el-button
            v-hasPermi="['metadata:analysisdata:export']"
            type="info"
            icon="download"
            @click="exportData"
            >Export
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :row-key="row => row.id"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="50"
          align="center"
        />
        <el-table-column
          prop="datNo"
          label="Data ID"
          min-width="120"
          sortable
        />
        <el-table-column
          prop="name"
          label="File Name"
          min-width="130"
          show-overflow-tooltip
          sortable
        />
        <el-table-column
          prop="dataType"
          label="Data Type"
          width="110"
          sortable
        />
        <el-table-column prop="filesize" label="File Size" width="100" sortable>
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="security"
          label="Data Security"
          min-width="140"
        />

        <el-table-column prop="analNo" label="Analysis ID" min-width="120">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail(scope.row.analNo, scope.row.creator, 'analysis')
              "
            >
              {{ scope.row.analNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="analName"
          label="Analysis Name"
          min-width="160"
        />
        <el-table-column prop="analType" label="Analysis Type" min-width="140">
          <template #default="scope">
            {{
              scope.row.analType === 'Other' && scope.row.customAnalType
                ? `Other(${scope.row.customAnalType})`
                : scope.row.analType
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="creatorEmail"
          label="Creator"
          min-width="160"
          sortable
        />

        <el-table-column
          prop="createDate"
          label="Create Date"
          width="160"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column label="Operate" width="90">
          <template #default="scope">
            <el-tooltip content="Delete">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-show="total > 0"
        style="float: left"
        type="danger"
        icon="Delete"
        :disabled="dataNos.length === 0"
        @click="handleDelete"
        >Delete
      </el-button>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { deleteByDataNos, listAnalysisData } from '@/api/metadata/data';
  import { createAccessToken } from '@/api/login';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNoStr: '',
      dataNos: [],
      name: '',
      analNoStr: '',
      analNos: [],
      creatorEmail: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  watch(
    () => data.queryParams.dataNoStr,
    newVal => {
      data.queryParams.dataNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.analNoStr,
    newVal => {
      data.queryParams.analNos = newVal ? newVal.split('\n') : [];
    },
  );

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.dataNoStr = '';
    queryParams.value.analNoStr = '';
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function showDetail(no, creator, type) {
    proxy.$modal.loading('opening, please wait');
    // 预先生成access_token
    createAccessToken({ memberId: creator })
      .then(response => {
        const token = response.data;
        let href = `${
          import.meta.env.VITE_APP_WEB_URL
        }/${type}/detail/${no}?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function getDataList() {
    loading.value = true;
    listAnalysisData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** data删除 */
  let dataNos = ref([]);

  function handleSelectionChange(selection) {
    dataNos.value = selection.map(item => item.datNo);
  }

  function handleDelete(row) {
    let nos = row.datNo || dataNos.value;
    proxy.$modal
      .confirm('Are you sure to delete Data ID as "' + nos + '" data items?')
      .then(function () {
        return deleteByDataNos(nos);
      })
      .then(() => {
        getDataList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/data/exportAnalysisData',
      {
        query,
      },
      `AnalysisData_${new Date().getTime()}.json`,
    );
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
