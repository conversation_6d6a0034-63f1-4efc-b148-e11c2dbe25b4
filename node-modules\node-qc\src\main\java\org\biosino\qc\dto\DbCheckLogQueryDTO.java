package org.biosino.qc.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.mongo.dto.BaseQuery;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DbCheckLogQueryDTO extends BaseQuery {
    private String creatorEmail;
    private String creator;
    private String type;
    private String typeNos;
    private String subjectType;
    private String tags;
    private Boolean hasFilterField = Boolean.FALSE;
    private String filterField;
    private Integer limit;
}
