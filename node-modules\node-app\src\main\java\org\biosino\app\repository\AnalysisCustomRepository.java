package org.biosino.app.repository;

import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.common.mongo.entity.Analysis;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface AnalysisCustomRepository {

    Analysis findByNo(String analNo);

    List<Analysis> findAllByNos(Collection<String> analNos);

    boolean existVisibleByNo(String analNo);

    List<Analysis> findDetailByTargetIn(String projNo, String targetType);

    BrowseStatDTO getBrowseStatInfo(String analysisNo);

    Analysis findByAnalNoWithPermission(String analNo);

    Map<String, Object> getStatInfo(String analNo, boolean owner);

    List<Analysis> getByTargetAndCreator(String typeNo, String creator);

    Page<Analysis> findAnalysisPage(UserCenterListSearchDTO queryDTO);

    void incHitNum(String analNo);

    Optional<Analysis> findTopByAnalysisNo(String analNo);

    List<Analysis> findHasTempDataByAnalNoIn(Collection<String> analNos);
}
