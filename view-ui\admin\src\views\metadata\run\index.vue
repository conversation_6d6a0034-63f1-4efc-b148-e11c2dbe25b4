<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="ID" prop="nos">
          <el-input
            v-model="queryParams.noStr"
            placeholder="Search for ID"
            style="width: 240px"
            type="textarea"
            :rows="1"
            clearable
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input
            v-model="queryParams.name"
            :rows="1"
            type="textarea"
            clearable
            placeholder="Search for Name"
            style="width: 300px"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Creator" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            clearable
            placeholder="Search for creator"
            style="width: 250px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item prop="sourceProject" label="Tag">
          <el-select
            v-model="queryParams.tags"
            clearable
            style="width: 220px"
            :teleported="false"
            multiple
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Submission Date" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
          <el-button
            v-hasPermi="['metadata:run:export']"
            type="info"
            icon="download"
            @click="exportData"
            >Export
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="runNo" label="ID" sortable width="115">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row.runNo, scope.row.creator, 'run')"
            >
              {{ scope.row.runNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="Name"
          min-width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="dataNum"
          label="Data Number"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag type="success">{{ scope.row.dataNum }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="Description"
          min-width="160"
          show-overflow-tooltip
        />
        <el-table-column
          prop="expNo"
          label="Experiment No"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="
                showDetail(scope.row.expNo, scope.row.creator, 'experiment')
              "
            >
              {{ scope.row.expNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="sapNo"
          label="Sample No"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row.sapNo, scope.row.creator, 'sample')"
            >
              {{ scope.row.sapNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="creatorEmail"
          label="Creator"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column
          prop="visibleStatus"
          label="Status"
          min-width="130"
          sortable
        >
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.visibleStatus === 'Accessible'"
                color="#67C23A"
              >
                <View />
              </el-icon>
              <el-icon v-else color="#F56C6C">
                <Hide />
              </el-icon>
              <div
                class="ml-05"
                :style="{
                  color:
                    scope.row.visibleStatus === 'Accessible'
                      ? '#67C23A'
                      : '#F56C6C',
                }"
              >
                {{ scope.row.visibleStatus }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Security" width="75">
          <template #default="scope">
            <div class="lock" :style="computedStyle(scope.row.dataCount)">
              <el-tooltip placement="right">
                <template #content>
                  <div>
                    <span>Public : </span>
                    <span>{{ scope.row.dataCount.Public }}</span>
                  </div>
                  <div>
                    <span>Private: </span>
                    <span>{{ scope.row.dataCount.Private }}</span>
                  </div>
                  <div>
                    <span>Restricted: </span>
                    <span>{{ scope.row.dataCount.Restricted }}</span>
                  </div>
                </template>
                <el-icon class="cursor-pointer">
                  <Lock v-if="scope.row.visibleStatus === 'Accessible'" />
                  <Unlock v-else />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="Submission Date"
          width="160"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { listRun } from '@/api/metadata/run';
  import { createAccessToken } from '@/api/login';

  const router = useRouter();

  const { proxy } = getCurrentInstance();
  const { tag } = proxy.useDict('tag');

  onMounted(() => {
    getDataList();
  });

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      noStr: '',
      nos: [],
      pageNum: 1,
      pageSize: 20,
      creatorEmail: '',
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.noStr,
    newVal => {
      data.queryParams.nos = newVal ? newVal.split('\n') : [];
    },
  );

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listRun(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function showDetail(no, creator, type) {
    proxy.$modal.loading('opening, please wait');
    // 预先生成access_token
    createAccessToken({ memberId: creator })
      .then(response => {
        const token = response.data;
        let href = `${
          import.meta.env.VITE_APP_WEB_URL
        }/${type}/detail/${no}?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const computedStyle = security => {
    return {
      '--my-bg-public': security.Public ? '#07bcb4' : '#DDDDDD',
      '--my-bg-private': security.Private ? '#3a78e8' : '#CCCCCC',
      '--my-bg-restricted': security.Restricted ? '#fe7f2b' : '#bebebe',
    };
  };

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/run/exportData',
      {
        query,
      },
      `Run_${new Date().getTime()}.json`,
    );
  }
</script>

<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .export-svg {
    top: 2px;
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-form {
      .el-input {
        //width: 300px;
      }
    }

    .el-textarea__inner {
      border-radius: 12px;
    }
  }

  .lock {
    --bg-public: var(--my-bg-public, #07bcb4);
    --bg-private: var(--my-bg-private, #3a78e8);
    --bg-restricted: var(--my-bg-restricted, #fe7f2b);

    display: inline-flex;
    flex-grow: 1;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: 5px;
    padding: 0.7em 0.7em;
    border-radius: 50%;
    background: radial-gradient(white calc(52% - 1px), transparent 30%),
      conic-gradient(
        from 18deg,
        var(--bg-public) 33.3%,
        var(--bg-private) 0% 66.6%,
        var(--bg-restricted) 0%
      );
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
