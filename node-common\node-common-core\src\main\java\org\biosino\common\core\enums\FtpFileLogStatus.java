package org.biosino.common.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/3/21
 */
public enum FtpFileLogStatus {
    uploading("上传中"),
    unchecked("待校验"),/*文件上传场景下ftp连接断开时对应的状态。可能是传输完毕/连接断开/用户暂定等情况*/
    queuing("排队中"),/*后台开始校验之前的状态*/
    checking("校验中"),/*用户点击“开始校验”按钮之后的状态*/
    checkSuccess("校验成功"), /*linux verify计算成功，并且verify比较MD5的值，两者一致*/
    checkFail("校验失败"),/*1. linux verify直接返回的失败，
                            2. linux verify计算成功，但是verify比较MD5值，发现不一致
                            3. linux verify计算成功，但是verify发现md5文件不存在
                            4. */
    uploaded("上传完成"),/*同步到Node之后的状态，rsync到node之后，ftp_home下的文件应该是已经删除了*/
    deleted("已删除"),/*对应用户主动删除的情况。*/
    transfered("已转移"),/*对应nodeadmin后台用户通过ftp transfer功能转移数据成功的情况*/
    error("错误");/* 一个文件处于uploading状态超过20天，那么就把状态设置为error */

    String status;

    public String getStatus() {
        return status;
    }

    FtpFileLogStatus(String status) {
        this.status = status;
    }

    public static List<String> includeExsistedStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.uploading.getStatus());
        list.add(FtpFileLogStatus.unchecked.getStatus());
        list.add(FtpFileLogStatus.checking.getStatus());
        list.add(FtpFileLogStatus.checkSuccess.getStatus());
        list.add(FtpFileLogStatus.checkFail.getStatus());
        list.add(FtpFileLogStatus.error.getStatus());
        return list;
    }

    public static List<String> allStatusExcludeAfterUploaded() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.uploading.getStatus());
        list.add(FtpFileLogStatus.unchecked.getStatus());
        list.add(FtpFileLogStatus.queuing.getStatus());
        list.add(FtpFileLogStatus.checking.getStatus());
        list.add(FtpFileLogStatus.checkSuccess.getStatus());
        list.add(FtpFileLogStatus.checkFail.getStatus());
        list.add(FtpFileLogStatus.error.getStatus());
        return list;
    }

    public static List<String> includeCheckStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.queuing.getStatus());
        list.add(FtpFileLogStatus.checkFail.getStatus());
        list.add(FtpFileLogStatus.checking.getStatus());
        list.add(FtpFileLogStatus.checkSuccess.getStatus());
        return list;
    }

    public static List<String> includeUncheckStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.uploading.getStatus());
        list.add(FtpFileLogStatus.unchecked.getStatus());
        return list;
    }

    public static List<String> includeCheckingStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.checking.getStatus());
        return list;
    }

    public static List<String> includeCheckFailStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.checkFail.getStatus());
        return list;
    }

    public static List<String> includeNotExsistedStatus() {
        List<String> list = new ArrayList<>();
        list.add(FtpFileLogStatus.uploaded.getStatus());
        list.add(FtpFileLogStatus.deleted.getStatus());
        list.add(FtpFileLogStatus.transfered.getStatus());
        return list;
    }

}
