package org.biosino.qc.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.qc.dto.SubmissionDTO;
import org.biosino.qc.repository.SubmissionCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SubmissionCustomRepositoryImpl implements SubmissionCustomRepository {

    private final MongoTemplate mongoTemplate;

    private static void queryDate(BaseQuery dto, List<Criteria> criteriaList, String createTime) {
        if (ObjectUtil.isNotEmpty(dto.getBeginTime()) && ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())).lte(DateUtil.endOfDay(dto.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getBeginTime())) {
            criteriaList.add(Criteria.where(createTime).gte(DateUtil.beginOfDay(dto.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where(createTime).lte(DateUtil.endOfDay(dto.getEndTime())));
        }
    }

    @Override
    public PageImpl<Submission> findAllPage(SubmissionDTO dto) {

        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(dto.getSubNo())) {
            criteriaList.add(Criteria.where("sub_no").regex(".*?" + dto.getSubNo() + ".*?", "i"));
        }

        String status = dto.getStatus();
        Long auditor = dto.getAuditor();
        if (auditor == null) {
            auditor = SecurityUtils.getUserId();
        }

        /*
          All                          SQL条件：（Auditor为空 & 待审核） OR Auditor = 当前用户
          待审核（Waiting Review）       SQL条件：（Auditor为空  OR Auditor = 当前用户）& Status = 待审核
          失败（Rejected）               SQL条件：Auditor = 当前用户  &  Status = 失败
          打回编辑中（Rejected & Editing）SQL条件：Auditor = 当前用户  &  Status = 编辑中
          完成（Complete）               SQL条件：Auditor = 当前用户  &  Status = 完成
         */
        if ("All".equalsIgnoreCase(status)) {
            Criteria criteria = new Criteria().orOperator(Criteria.where("auditor_id").exists(false)
                            .and("status").is(SubmissionStatusEnum.waiting.name()),
                    Criteria.where("auditor_id").is(auditor));
            criteriaList.add(criteria);

        } else if (SubmissionStatusEnum.waiting.name().equals(status)) {
            Criteria criteria = new Criteria().orOperator(Criteria.where("auditor_id").exists(false),
                    Criteria.where("auditor_id").is(auditor));

            criteria.and("status").is(status);
            criteriaList.add(criteria);

        } else if (SubmissionStatusEnum.rejected.name().equals(status) || SubmissionStatusEnum.complete.name().equals(status)
                || SubmissionStatusEnum.editing.name().equals(status) || SubmissionStatusEnum.reviewing.name().equals(status)) {
            criteriaList.add(Criteria.where("auditor_id").is(auditor).and("status").is(status));
        }

        queryDate(dto, criteriaList, "submit_time");

        criteriaList.add(Criteria.where("status").ne(SubmissionStatusEnum.deleted.name()));

        if (StrUtil.isNotBlank(dto.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(dto.getCreator()));
        }


        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = dto.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, Submission.class);
        query.with(pageable);

        List<Submission> content = mongoTemplate.find(query, Submission.class);
        return new PageImpl<>(content, dto.getPageable(), total);
    }

    @Override
    public List<Submission> findAllNum(BaseQuery dto) {
        List<Criteria> criteriaList = new ArrayList<>();

        queryDate(dto, criteriaList, "create_time");

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        criteriaList.add(Criteria.where("status").is(SubmissionStatusEnum.complete.name()));
        // 只取需要的字段，防止内存溢出
        query.fields().include("proj_num").include("exp_num").include("sap_num").include("anal_num")
                .include("run_num").include("publish_num").include("data_num").include("data_size");

        return mongoTemplate.find(query, Submission.class);
    }

    @Override
    public long countByStatus(BaseQuery dto, String status) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(status)) {
            criteriaList.add(Criteria.where("status").is(status));
        }

        queryDate(dto, criteriaList, "create_time");

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }

        return mongoTemplate.count(query, Submission.class);
    }

    @Override
    public long countByNotStatus(BaseQuery dto, String status) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(status)) {
            criteriaList.add(Criteria.where("status").ne(status));
        }

        queryDate(dto, criteriaList, "create_time");

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }

        return mongoTemplate.count(query, Submission.class);
    }

    @Override
    public long countTotalByStatus(BaseQuery dto, String status) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(status)) {
            criteriaList.add(Criteria.where("status").is(status));
        }

        queryDate(dto, criteriaList, "create_time");

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }

        query.fields().include("total");

        List<Submission> all = mongoTemplate.find(query, Submission.class);

        if (CollUtil.isEmpty(all)) {
            return 0L;
        }

        return all.stream().mapToLong(Submission::getTotal).sum();
    }
}
