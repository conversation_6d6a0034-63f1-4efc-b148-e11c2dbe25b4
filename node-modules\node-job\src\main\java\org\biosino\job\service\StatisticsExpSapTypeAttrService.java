package org.biosino.job.service;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.entity.statistics.StatisticsExpSapTypeAttr;
import org.biosino.job.repository.ExpSampleTypeRepository;
import org.biosino.job.repository.util.RepositoryUtil;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计experiment和sample的属性填写的情况
 *
 * <AUTHOR>
 * @date 2024/12/31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsExpSapTypeAttrService {

    private final MongoTemplate mongoTemplate;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    private final StatisticsTagService statisticsTagService;

    public void generate(String month) {
        // 先删除当前月份的数据
        RepositoryUtil.deleteByMonth(mongoTemplate, month, StatisticsExpSapTypeAttr.class);

        log.info("正在生成{}月份的statistics_exp_sap_type_attr的experiment数据", month);
        generateWithMetadataClass(month, Experiment.class);
        log.info("正在生成{}月份的statistics_exp_sap_type_attr的sample数据", month);
        generateWithMetadataClass(month, Sample.class);
    }

    private <T> void generateWithMetadataClass(String month, Class<T> metaClz) {
        // 查询当前表下有多少标签
        List<String> tags = statisticsTagService.findAllTag(metaClz);
        String metaType = resolvMetaType(metaClz);
        // 查询该类型有多少模板
        List<ExpSampleType> tpls = expSampleTypeRepository.findByType(metaType);

        List<StatisticsExpSapTypeAttr> saveList = new ArrayList<>();
        // 遍历模板
        for (ExpSampleType expSampleType : tpls) {
            // 模板名，也就是Experiment的expType，Sample的subjectType
            String tplName = expSampleType.getName();
            log.info("正在统计 {} 模板", tplName);
            List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            for (ExpSampleType.Attributes attribute : attributes) {
                String attributesField = attribute.getAttributesField();
                // 统计全部的
                StatisticsExpSapTypeAttr item = getStatisticsExpSapTypeAttr(month, null, tplName, attributesField, metaClz);
                item.setTag("all");
                saveList.add(item);

                // 按照标签统计
                for (String tag : tags) {
                    StatisticsExpSapTypeAttr it = getStatisticsExpSapTypeAttr(month, tag, tplName, attributesField, metaClz);
                    saveList.add(it);
                }
            }
        }

        mongoTemplate.insertAll(saveList);
    }

    private String resolvMetaType(Class metaClz) {
        String metaType;
        if (metaClz == Experiment.class) {
            metaType = "experiment";
        } else if (metaClz == Sample.class) {
            metaType = "sample";
        } else {
            throw new RuntimeException("metaClz must be Experiment or Sample");
        }
        return metaType;
    }

    private <T> StatisticsExpSapTypeAttr getStatisticsExpSapTypeAttr(String month, String tag, String tplName, String attributesField, Class<T> metaClz) {
        Long accessible = countByTypeAndTagAndAttrAndVisibleStatus(tplName, tag, attributesField, VisibleStatusEnum.Accessible.name(), metaClz);
        Long totalAccessible = countByTypeAndTagAndAttrAndVisibleStatus(tplName, tag, null, VisibleStatusEnum.Accessible.name(), metaClz);
        Long unaccessible = countByTypeAndTagAndAttrAndVisibleStatus(tplName, tag, attributesField, VisibleStatusEnum.Unaccessible.name(), metaClz);
        Long totalUnaccessible = countByTypeAndTagAndAttrAndVisibleStatus(tplName, tag, null, VisibleStatusEnum.Unaccessible.name(), metaClz);

        StatisticsExpSapTypeAttr item = new StatisticsExpSapTypeAttr();
        item.setMonth(month);
        item.setTag(tag);
        item.setMetadataType(resolvMetaType(metaClz));
        item.setSubjectType(tplName);
        item.setAttrField(attributesField);
        item.setAccessible(accessible);
        item.setTotalAccessible(totalAccessible);
        item.setUnaccessible(unaccessible);
        item.setTotalUnaccessible(totalUnaccessible);
        return item;
    }


    private <T> Long countByTypeAndTagAndAttrAndVisibleStatus(String tplName,
                                                              String tag,
                                                              String attrField,
                                                              String visibleStatus,
                                                              Class<T> clazz) {
        ArrayList<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where(clazz == Experiment.class ? "exp_type" : "subject_type").is(tplName));
        if (StrUtil.isNotBlank(tag)) {
            conditions.add(Criteria.where("source_project").in(tag));
        }
        if (StrUtil.isNotBlank(attrField)) {
            conditions.add(Criteria.where("attributes." + attrField).exists(true).ne(null).ne(""));
        }
        if (StrUtil.isNotBlank(visibleStatus)) {
            conditions.add(Criteria.where("visible_status").is(visibleStatus));
        }
        Query query = new Query(new Criteria().andOperator(conditions));
        return mongoTemplate.count(query, clazz);
    }
}
