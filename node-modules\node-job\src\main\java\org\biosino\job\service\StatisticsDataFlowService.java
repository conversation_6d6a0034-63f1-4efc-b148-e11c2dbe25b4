package org.biosino.job.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.statistics.StatisticsDataFlow;
import org.biosino.job.repository.DataRepository;
import org.biosino.job.repository.DownloadLogRepository;
import org.biosino.job.repository.StatisticsDataFlowRepository;
import org.biosino.job.repository.SubmissionRepository;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

import static org.biosino.common.core.constant.ConfigConstants.START_MONTH;

/**
 * 生成统计模块的数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsDataFlowService {

    private final DataRepository dataRepository;
    private final SubmissionRepository submissionRepository;
    private final DownloadLogRepository downloadLogRepository;
    private final StatisticsDataFlowRepository dataFlowRepository;

    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyy.MM");

    /**
     * 全量刷新数据
     */
    public void calculateAllYears() {
        dataFlowRepository.deleteAll();

        Calendar calendar = Calendar.getInstance();
        // 获取当前年份
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();

        for (int year = ConfigConstants.START_YEAR; year <= currentYear; year++) {
            for (int month = 0; month < 12; month++) {
                if (year == ConfigConstants.START_YEAR && month < START_MONTH) {
                    continue;
                }
                // 设置当前年份和月份
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 获取月份的开始时间
                Date startOfMonth = calendar.getTime();

                LocalDate localDateFromCalendar = LocalDate.of(
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH) + 1,
                        calendar.get(Calendar.DAY_OF_MONTH)
                );

                if (localDateFromCalendar.isAfter(currentDate)) {
                    continue;
                }

                // 格式化月份开始时间
                String formattedStart = FORMATTER.format(startOfMonth);

                // 提交任务到线程池
                generate(formattedStart);
            }
        }
    }

    public void generate(String month) {
        // 计算每种类型的访问次数
        Date startDate;
        Date endDate;

        try {
            // 解析传入的月份字符串
            startDate = FORMATTER.parse(month);

            // 使用Calendar来找到月份的结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 增加一个月
            calendar.add(Calendar.MONTH, 1);
            // 减去一秒以回到当前月的最后一天
            calendar.add(Calendar.SECOND, -1);

            endDate = calendar.getTime();
        } catch (ParseException e) {
            log.error("日期格式错误，解析识别：{}", month);
            return;
        }

        // 清除旧数据
        dataFlowRepository.deletedByMonth(month);

        StatisticsDataFlow dataFlow = new StatisticsDataFlow();
        dataFlow.setMonth(month);

        // 统计数据下载次数
        dataFlow.setDownload(downloadLogRepository.countByDate(startDate, endDate));
        // 下载数据量
        List<String> dataNos = downloadLogRepository.findDataNoByDate(startDate, endDate);

        if (CollUtil.isNotEmpty(dataNos)) {

            Map<String, Long> dataSizeMap = dataRepository.findAllSizeByNo(CollUtil.distinct(dataNos));
            for (String dataNo : dataNos) {
                if (dataSizeMap.containsKey(dataNo)) {
                    Long size = dataSizeMap.get(dataNo);
                    dataFlow.setDownloadDataSize(dataFlow.getDownloadDataSize() + size);
                }
            }
        }

        // 统计上传的Data
        Map<String, Long> dataMap = dataRepository.findDataNoByDate(startDate, endDate);
        if (CollUtil.isNotEmpty(dataMap)) {
            dataFlow.setUploadData((long) dataMap.size());
            dataFlow.setUploadDataSize(dataMap.values().stream().mapToLong(Long::longValue).sum());
        }

        // 统计数据提交情况
        List<Submission> submissionList = submissionRepository.findAllByFirstSubmitTime(startDate, endDate);

        if (CollUtil.isNotEmpty(submissionList)) {
            dataFlow.setSubmission((long) submissionList.size());

            List<String> submissionDataNos = new ArrayList<>();

            for (Submission submission : submissionList) {
                if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                    submissionDataNos.addAll(submission.getRawDataNos());
                }
                if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                    submissionDataNos.addAll(submission.getAnalysisDataNos());
                }
                if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                    submissionDataNos.addAll(submission.getRawDataMultipleNos());
                }
                if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                    submissionDataNos.addAll(submission.getAnalDataMultipleNos());
                }
            }
            if (CollUtil.isNotEmpty(submissionDataNos)) {
                Map<String, Long> dataSizeMap = dataRepository.findAllSizeByNo(CollUtil.distinct(submissionDataNos));

                for (String dataNo : submissionDataNos) {
                    if (dataSizeMap.containsKey(dataNo)) {
                        Long size = dataSizeMap.get(dataNo);
                        dataFlow.setSubmissionDataSize(dataFlow.getSubmissionDataSize() + size);
                    }
                }
            }
        }

        dataFlowRepository.insert(dataFlow);
    }

}
