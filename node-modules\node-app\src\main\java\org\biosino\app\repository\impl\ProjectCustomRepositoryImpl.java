package org.biosino.app.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.ProjectCustomRepository;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.security.utils.SecurityUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ProjectCustomRepositoryImpl implements ProjectCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Project findByProjectNoWithPermission(String projectNo) {
        Query query = new Query();
        query.addCriteria(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Criteria criteria = or(Criteria.where("proj_no").is(projectNo),
                Criteria.where("used_ids").in(projectNo));
        query.addCriteria(criteria);
        Project project = mongoTemplate.findOne(query, Project.class);
        if (project == null) {
            throw new ServiceException("Not found project");
        }
        if (project.getVisibleStatus().equals(VisibleStatusEnum.Unaccessible.name())
                && !project.getCreator().equals(SecurityUtils.getMemberId())
                && !shareToMember(project, SecurityUtils.getMemberEmail())) {
            throw new NotPermissionException("Not permission");
        }
        return project;
    }

    public boolean shareToMember(Project project, String... shareTo) {
        List<String> nos = new ArrayList<>();
        nos.add(project.getProjectNo());
        if (CollUtil.isNotEmpty(project.getUsedIds())) {
            nos.addAll(project.getUsedIds());
        }
        return mongoTemplate.exists(new Query(
                Criteria.where("share_to").in(shareTo)
                        .and("projects.proj_no").in(nos)
                        .and("status").is(ShareStatusEnum.sharing.name())), Share.class);
    }


    @Override
    public Project findByNo(String projectNo) {
        if (projectNo == null) {
            return null;
        }
        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").is(projectNo), Criteria.where("used_ids").in(projectNo)));
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        final Query query = new Query(and(criteriaList));
        return mongoTemplate.findOne(query, Project.class);
    }

    @Override
    public boolean existVisibleByNo(String projectNo) {
        Query query = new Query();

        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").is(projectNo), Criteria.where("used_ids").in(projectNo)));
        criteriaList.add(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()));

        query.addCriteria(and(criteriaList));
        return mongoTemplate.exists(query, Project.class);
    }

    private Criteria and(final List<Criteria> criteriaList) {
        return new Criteria().andOperator(criteriaList);
    }

    private Criteria or(final Criteria... criteriaList) {
        return new Criteria().orOperator(criteriaList);
    }

    @Override
    public Map<String, Object> findStatInfo(String projNo, boolean isOwner) {
        Map<String, Object> result = new LinkedHashMap<>();
        // 查询当前ProjectNo关联了那些Experiment

        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").is(projNo), Criteria.where("used_ids").in(projNo)));
        criteriaList.add(Criteria.where("audited").ne(AuditEnum.init.name()).and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        Query query = new Query(and(criteriaList));
//        query.addCriteria(Criteria.where("proj_no").is(projNo)
//                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query.fields().include("exp_no");

        List<String> allExpNos = mongoTemplate.findDistinct(query, "exp_no", Experiment.class, String.class);

        // 查询这些Experiment关联了那些Run
        Query query1 = new Query();
        query1.addCriteria(Criteria.where("exp_no").in(allExpNos).and("audited").ne(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query1.fields().include("run_no").include("sap_no").include("visible_status");
        List<Run> runList = mongoTemplate.find(query1, Run.class);
        List<String> runNos = runList.stream().map(Run::getRunNo).distinct().collect(Collectors.toList());
        List<String> sapNos = runList.stream().map(Run::getSapNo).distinct().collect(Collectors.toList());

        // 根据这些Run查询关联的Data
        Query query2 = new Query();
        query2.addCriteria(Criteria.where("run_no").in(runNos).and("security").in(SecurityEnum.includeAllSecurity()));
        query2.fields().include("dat_no").include("security").include("file_size");

        List<Data> dataList = mongoTemplate.find(query2, Data.class);

        // 查询Sample相关并统计
        Query query3 = new Query();
        query3.addCriteria(Criteria.where("sap_no").in(sapNos).and("audited").ne(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query3.fields().include("sap_no").include("visible_status");
        List<Sample> sampleList = mongoTemplate.find(query3, Sample.class);

        result.put("SAMPLE", sampleList.stream().filter(x -> StrUtil.equals(x.getVisibleStatus(), VisibleStatusEnum.Accessible.name())).count());
        result.put("RUN", runList.stream().filter(x -> StrUtil.equals(x.getVisibleStatus(), VisibleStatusEnum.Accessible.name())).count());
        List<Data> filterDataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        result.put("FILES", filterDataList.size());
        result.put("VOLUME", FileUtil.readableFileSize(filterDataList.stream().mapToLong(Data::getFileSize).sum()));

        if (isOwner) {
            result.put("SAMPLE", result.get("SAMPLE") + " / " + sampleList.size());
            result.put("RUN", result.get("RUN") + " / " + runList.size());
            result.put("FILES", result.get("FILES") + " / " + dataList.size());
            result.put("VOLUME", result.get("VOLUME") + " / " + FileUtil.readableFileSize(dataList.stream().mapToLong(Data::getFileSize).sum()));
        }

        return result;
    }

    @Override
    public BrowseStatDTO findBrowseStatInfo(String projNo) {
        final BrowseStatDTO result = new BrowseStatDTO();
        if (StrUtil.isBlank(projNo)) {
            return result;
        }
        final Set<String> allExpNos = result.getExpNos();
        final Set<String> expTypes = result.getExpTypes();
        // 查询当前ProjectNo关联了那些Experiment

        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").is(projNo), Criteria.where("used_ids").in(projNo)));
        criteriaList.add(Criteria.where("audited").ne(AuditEnum.init.name()).and("visible_status").is(VisibleStatusEnum.Accessible.name()));

        Query query = new Query(and(criteriaList));
//        query.addCriteria(Criteria.where("proj_no").is(projNo).and("visible_status").is(VisibleStatusEnum.Accessible.name()));
        query.fields().include("exp_no", "exp_type");
        List<Experiment> experimentList = mongoTemplate.find(query, Experiment.class);
        for (Experiment experiment : experimentList) {
            allExpNos.add(experiment.getExpNo());
            expTypes.add(experiment.getExpType());
        }
        ExperimentCustomRepositoryImpl.findRelationalStatInfo(mongoTemplate, allExpNos, result);
        return result;
    }

    @Override
    public List<Project> findDetailByProjNoIn(Collection<String> projNos) {
        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").in(projNos), Criteria.where("used_ids").in(projNos)));
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        Query query = Query.query(and(criteriaList));
        query.fields().include("proj_no")
                .include("name")
                .include("description")
                .include("visible_status");
        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public List<Project> findAllByProjNoIn(Collection<String> projNos) {
        if (CollUtil.isEmpty(projNos)) {
            return Collections.emptyList();
        }
        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").in(projNos), Criteria.where("used_ids").in(projNos)));
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        Query query = Query.query(and(criteriaList));

        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public List<Project> findHasTempDataByProjNoIn(Collection<String> projNos) {
        if (CollUtil.isEmpty(projNos)) {
            return Collections.emptyList();
        }
        final List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(or(Criteria.where("proj_no").in(projNos), Criteria.where("used_ids").in(projNos)));
        criteriaList.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        criteriaList.add(Criteria.where("temp_data").exists(true).ne(null));
        Query query = Query.query(and(criteriaList));

        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public Page<Project> findProjectPage(UserCenterListSearchDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(or(Criteria.where("proj_no").regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        Query query = new Query(and(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, Project.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Project> content = mongoTemplate.find(query, Project.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public void incHitNum(String projId) {
        Query query = Query.query(Criteria.where("_id").is(projId));
        Update update = new Update();
        update.inc("hit_num", 1);

        mongoTemplate.updateFirst(query, update, Project.class);
    }

    @Override
    public Optional<Project> findTopByProjectNo(String projectNo) {
        if (StrUtil.isBlank(projectNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(or(Criteria.where("proj_no").is(projectNo),
                Criteria.where("used_ids").in(projectNo)));
        Query query = new Query(and(condition));

        Project project = mongoTemplate.findOne(query, Project.class);

        return Optional.ofNullable(project);
    }


}
