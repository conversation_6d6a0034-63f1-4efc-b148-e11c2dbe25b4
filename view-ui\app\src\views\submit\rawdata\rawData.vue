<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="UploadData" class="hidden-xs-only" />
      <ChooseData active-menu="rawdata" class="hidden-xs-only" />
      <div class="rawdata-intro">
        <h5>Is the data that needs to be archived in the datalist?</h5>
        <ul class="ml-1">
          <li>
            Files can be compressed using <strong>gzip</strong> or
            <strong>bzip2</strong>, and may be submitted in a tar archive but
            archiving and/or compressing your files is not required.
          </li>
          <li>
            All file names must be <strong>unique</strong>, and not contain any
            sensitive information. Each file must be listed in the archiving
            table. For sequencing data, a run contains two files (forward and
            reverse) in paired sequencing and one file in single sequencing.
          </li>
          <li>
            All files for a new submission had better put in a single directory.
          </li>
          <li>
            You need provide the <strong>MD5 file</strong> for your files. Be
            sure it contains only the 32-digit MD5 value for a single file, and
            that its name matches the name of that file.
          </li>
          <li>
            You will upload files to your private ftp-home using either FTP or
            Express protocol and to ‘My Data->Unarchived Data’ using HTTP
            protocol.
          </li>
        </ul>
      </div>
      <h5 class="data-upload">Data Upload</h5>
      <div class="d-flex mb-2">
        <div class="route-1 radius-14 mr-2">
          <div class="route">
            <span class="text-main-color font-600 mr-2">Route1</span>
            <span class="text-primary font-600">FTP upload</span>
          </div>
          <div class="route-content d-flex">
            <div>
              <div class="text-main-color route-1-item font-600">
                FTP tools upload
              </div>
              <ul class="step-list step-list-ftp mt-1 pl-20">
                <li>
                  1. Run FTP tools e.g.
                  <span class="text-main-color font-600">Filezilla, WinsCP</span
                  >.
                  <div class="d-flex mb-1 ml-1 mt-1">
                    <div class="img">
                      <img src="@/assets/images/upload1.png" alt="" />
                      <a
                        href="https://www.filezilla.cn/download/client"
                        target="_blank"
                        >Filezilla</a
                      >
                    </div>
                    <div class="img ml-2">
                      <img
                        src="@/assets/images/upload2.png"
                        class="wins"
                        alt=""
                      />
                      <a
                        href="https://winscp.net/eng/download.php"
                        target="_blank"
                        >WinsCP</a
                      >
                    </div>
                  </div>
                </li>
                <li>
                  2. Select "SFTP - SSH File Transfer Protocol" in the
                  encryption options.
                </li>

                <li>
                  3. Enter: host name ({{ sftpData.host }}), port ({{
                    sftpData.port
                  }}).
                </li>
                <li>
                  4. Enter: user name (NODE user name), password (NODE
                  password).
                </li>
                <li>
                  5, Put the local file to the remote FTP directory for
                  transmission.
                </li>
              </ul>
              <div
                class="text-main-color d-flex align-items-center route-1-item font-600 mt-1"
              >
                Command-Line upload

                <el-popover
                  placement="right"
                  title="Establish an FTP connection using the credentials below:"
                  :width="500"
                  trigger="hover"
                >
                  <template #reference>
                    <el-icon class="ml-05" size="20">
                      <QuestionFilled />
                    </el-icon>
                  </template>
                  <template #default>
                    <div class="ftp-info">
                      <pre>
1. Enter:<i>sftp -P {{ sftpData.port }} [your NODE account] @{{ sftpData.host }}</i>
   Password: [your NODE password]
2. Create a subfolder(required) with a meaningful name:
    <i>mkdir new_folder</i>
3. Navigate to the target folder you just created:
    <i>cd new_folder</i>
4. Copy your files into the target folder:
    <i>put file_name</i>
5. View the data in the current directory
    <i>ls</i>
</pre>
                    </div>
                  </template>
                </el-popover>
              </div>
              <div class="step-list pl-20">
                <div class="mb-05 mt-1">
                  Make a new subdirectory for each new submission. Your
                  submission subfolder is a temporary holding area and it will
                  be removed once the whole submission is complete. Do not
                  upload complex directory structures or files that do not
                  contain sequence data.
                </div>
                <el-divider />
                <ul class="mt-05 ml-1">
                  <li>Ftp uploading method does not limit the file size.</li>
                  <li>
                    Please use the address/port|username|password above to
                    connect your own ftp directory.
                  </li>
                  <li>
                    Please ensure that file paths and names do not include
                    special characters such as spaces, ampersands (&), percent
                    signs (%), asterisks (*), or Greek letters. It is
                    recommended to use a combination of uppercase letters (A-Z),
                    lowercase letters (a-z), numbers (0-9), underscores (_), and
                    hyphens (-) to construct your file names.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="w-50">
          <div class="route-2 w-100 radius-14 mb-1">
            <div class="route">
              <span class="text-main-color font-600 mr-2">Route2</span>
              <span class="text-primary font-600">Http upload</span>
            </div>
            <div class="route-content pl-20">
              <div class="d-flex">
                <el-upload
                  ref="uploadRef"
                  :limit="1"
                  :headers="upload.headers"
                  :action="upload.url"
                  :disabled="upload.isUploading"
                  :on-progress="handleFileUploadProgress"
                  :on-success="handleFileSuccess"
                  :on-change="handleFileChange"
                  :auto-upload="true"
                  class="upload-demo w-50"
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <upload-filled />
                  </el-icon>
                  <div class="el-upload__text">
                    <div class="font-16">Select your file</div>
                    <span class="text-other-color font-12"
                      >Size limit of 200MB</span
                    >
                  </div>
                </el-upload>
                <ul class="ml-3">
                  <li>This page accepts direct uploading of data file.</li>
                  <li>
                    Prohibited file types for uploading include {{ blacklist }}
                  </li>
                  <li>The size limitation of the file is 200 MB</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="route-3 w-100 radius-14">
            <div class="route">
              <span class="text-main-color font-600 mr-2">Route3</span>
              <span class="text-primary font-600">Express hard drive</span>
            </div>
            <div class="route-content pl-20">
              <div class="d-flex">
                <el-form
                  ref="expressRef"
                  :label-position="labelPosition"
                  label-width="140px"
                  :model="expressInfo"
                  :rules="rules"
                  class="w-60"
                >
                  <el-form-item label="Express Name" prop="expressName">
                    <el-input v-model="expressInfo.expressName" />
                  </el-form-item>
                  <el-form-item label="Tracking Number" prop="trackingNum">
                    <el-input v-model="expressInfo.trackingNum" />
                  </el-form-item>
                  <el-form-item label="File Path" prop="filePath">
                    <el-input
                      v-model="expressInfo.filePath"
                      placeholder="Path to upload on hard disk"
                    />
                  </el-form-item>
                </el-form>
                <ul class="ml-3 mt-1">
                  <li>
                    Due to network condition, massive scale of data may not be
                    safely uploaded.So we provide an alternative choice here.
                  </li>
                  <li>
                    You can express a hard drive, memory card, flash disk,
                    anything containing the data using any reliable express.
                  </li>
                  <li>
                    We will return the device to you after uploading your data
                    manully using physical interfaces.
                  </li>
                  <li>
                    Contact us:
                    <a
                      href="mailto:<EMAIL>"
                      class="text-primary"
                      ><EMAIL></a
                    >
                  </li>
                </ul>
              </div>
              <div class="text-center">
                <el-button
                  type="warning"
                  round
                  class="w-30 mb-1 mt-1"
                  :disabled="data.isSubmitting"
                  @click="submitExpress"
                  >Submit
                </el-button>
              </div>
            </div>
          </div>
          <div class="route-tool w-100 radius-14 mt-1">
            <div class="route">
              <span class="text-main-color font-600 mr-2">Tool</span>
              <span class="text-primary font-600">MD5 File Generator</span>
            </div>
            <div class="route-content pl-20">
              <div class="d-flex">
                <el-upload
                  ref="md5UploadRef"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="handleMd5FileChange"
                  accept=".txt"
                  class="upload-demo w-50"
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <upload-filled />
                  </el-icon>
                  <div class="el-upload__text">
                    <div class="font-16">Select MD5 list file</div>
                    <span class="text-other-color font-12">
                      Upload .txt file with MD5 and filename pairs
                    </span>
                  </div>
                </el-upload>
                <ul class="ml-3">
                  <li>Upload a .txt file containing MD5 values and filenames</li>
                  <li>Each line should have format: [MD5] [filename]</li>
                  <li>Tool will generate individual .md5 files and package them into a ZIP</li>
                  <li>
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="!md5FileContent"
                      @click="generateMd5Files"
                    >
                      Generate & Download ZIP
                    </el-button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card hidden-xs-only">
        <el-tabs
          id="tabs"
          ref="tabsRef"
          v-model="activeName"
          type="card"
          class="demo-tabs"
          @tab-change="handleTabChange"
        >
          <el-tab-pane
            :label="`Unchecked (${rawDataStat.uncheckNum})`"
            name="Unchecked"
          >
            <unchecked-table ref="uncheckedTableRef"></unchecked-table>
          </el-tab-pane>
          <el-tab-pane
            :label="`Checking (${rawDataStat.checkingNum}) `"
            name="Checking"
          >
            <check-table ref="checkTableRef"></check-table>
          </el-tab-pane>
          <el-tab-pane
            :label="`Unarchived Data (${rawDataStat.unarchivedNum})`"
            name="Unarchived Data"
          >
            <unarchived-table ref="unarchivedTableRef"></unarchived-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="text-align-center mt-2 hidden-xs-only">
        <el-button
          type="primary"
          class="btn-primary btn"
          round
          @click="toPath()"
          >Continues Submit MetaData
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import Breadcrumb from '@/components/breadcrumb.vue';
  import ChooseData from '../components/chooseData.vue';
  import { useRouter } from 'vue-router';
  import { getRawDataStat, saveExpressSubmission } from '@/api/metadata/data';
  import { getToken } from '@/utils/auth';
  import UnarchivedTable from '@/views/submit/rawdata/common/UnarchivedTable.vue';
  import UncheckedTable from '@/views/submit/rawdata/common/UncheckedTable.vue';
  import CheckTable from '@/views/submit/rawdata/common/CheckTable.vue';
  import { getConfigKey } from '@/api/system/config';

  const { proxy } = getCurrentInstance();
  const tabsRef = ref(null);
  const labelPosition = ref('top');

  // MD5 工具相关
  const md5FileContent = ref('');
  const md5UploadRef = ref(null);

  const router = useRouter();

  const sftpData = reactive({
    host: '',
    port: '',
  });

  const blacklist = ref('');

  /*** 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/upload/uploadFile',
  });

  /**文件上传中处理 */
  const handleFileUploadProgress = () => {
    upload.isUploading = true;
  };
  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs['uploadRef'].handleRemove(file);
    if (response.code !== 200) {
      proxy.$modal.alertError(response.msg);
      return;
    }
    proxy.$modal.msgSuccess('Upload successful');
    // 激活下方的tab并滚动到地方
    activeName.value = 'Unarchived Data';
    proxy.$refs['unarchivedTableRef'].getDataList();
    document.getElementById('tabs').scrollIntoView({ behavior: 'smooth' });
  };

  // 上传on-change事件
  const handleFileChange = (file, fileList) => {
    // 判断文件大小是否合法，文件限制不能大于200M
    const isLimit = file.size / 1024 / 1024 < 200;
    if (!isLimit) {
      proxy.$modal.alertWarning('The upload file size cannot exceed 200 MB!');
      fileList.splice(-1, 1); //移除当前超出大小的文件
      return false;
    }
  };

  // MD5文件上传处理
  const handleMd5FileChange = (file, fileList) => {
    if (!file.name.endsWith('.txt')) {
      proxy.$modal.alertWarning('Please upload a .txt file!');
      fileList.splice(-1, 1);
      return false;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      md5FileContent.value = e.target.result;
    };
    reader.readAsText(file.raw);
  };

  // 生成MD5文件并打包下载
  const generateMd5Files = async () => {
    if (!md5FileContent.value) {
      proxy.$modal.alertWarning('Please upload a MD5 list file first!');
      return;
    }

    try {
      // 动态导入JSZip
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();

      const lines = md5FileContent.value.split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        proxy.$modal.alertWarning('The uploaded file is empty!');
        return;
      }

      lines.forEach((line) => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 2) {
          const md5Value = parts[0];
          const filename = parts.slice(1).join(' ');
          const md5Filename = `${filename}.md5`;
          zip.file(md5Filename, md5Value);
        }
      });

      const content = await zip.generateAsync({ type: 'blob' });

      // 创建下载链接
      const url = window.URL.createObjectURL(content);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'md5_files.zip';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      proxy.$modal.msgSuccess('MD5 files generated and downloaded successfully!');

      // 清理上传组件
      md5FileContent.value = '';
      proxy.$refs['md5UploadRef'].clearFiles();

    } catch (error) {
      proxy.$modal.alertError('Failed to generate MD5 files: ' + error.message);
    }
  };

  /** 响应式数据 */
  const data = reactive({
    checkTableData: [],
    checkDataTotal: 0,
    checkQueryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
    },
    checkDataRange: [],
    expressInfo: {
      expressName: '',
      trackingNum: '',
      filePath: '',
    },
    rules: {
      expressName: [
        {
          required: true,
          message: 'Express Name is required',
          trigger: 'blur',
        },
      ],
      trackingNum: [
        {
          required: true,
          message: 'Tracking Number is required',
          trigger: 'blur',
        },
      ],
      filePath: [
        { required: true, message: 'File Path is required', trigger: 'blur' },
      ],
    },
    isSubmitting: false,
  });
  // 解构
  const { expressInfo, rules } = toRefs(data);

  /* 提交邮寄信息 */
  function submitExpress() {
    proxy.$refs['expressRef'].validate(valid => {
      if (valid) {
        data.isSubmitting = true;
        saveExpressSubmission(expressInfo.value)
          .then(() => {
            proxy.$modal.alertSuccess('Submitted successfully');
            // 重置表单
            reset();
          })
          .finally(() => {
            data.isSubmitting = false;
          });
      }
    });
  }

  /* 重置表单数据 */
  function reset() {
    expressInfo.value = {
      expressName: '',
      trackingNum: '',
      filePath: '',
    };
    proxy.resetForm('expressRef');
  }

  //Unchecked checking
  const activeName = ref('Unchecked');

  function handleTabChange() {
    getStat();
    if (activeName.value === 'Unchecked') {
      proxy.$refs['uncheckedTableRef'].getDataList();
    } else if (activeName.value === 'Checking') {
      proxy.$refs['checkTableRef'].getDataList();
    } else if (activeName.value === 'Unarchived Data') {
      proxy.$refs['unarchivedTableRef'].getDataList();
    }
  }

  let rawDataStat = ref({
    uncheckNum: 0,
    checkingNum: 0,
    unarchivedNum: 0,
  });

  function getStat() {
    getRawDataStat().then(response => {
      rawDataStat.value = response.data;
      if (response.data?.uncheckNum > 10000) {
        proxy.$modal.alertWarning(
          'You have too much data that has not been integrity checked. Currently, only the most recent 10,000 records are displayed. If you need to view other historical data, please narrow the time range and filter',
        );
      }
    });
  }

  const toPath = () => {
    router.push({
      path: `/submit/metadata`,
    });
  };

  onMounted(() => {
    getConfigKey('sftp.upload.host').then(response => {
      let data = response.msg;
      if (data) {
        sftpData.host = data;
      }
    });
    getConfigKey('sftp.upload.port').then(response => {
      let data = response.msg;
      if (data) {
        sftpData.port = data;
      }
    });
    getConfigKey('node.file.suffixBlackList').then(response => {
      let data = response.msg;
      if (data) {
        blacklist.value = data + '|.md5';
      }
    });

    const userAgent = navigator.userAgent;
    if (
      userAgent.includes('Mobile') ||
      userAgent.includes('Android') ||
      userAgent.includes('iPhone')
    ) {
      proxy.$modal.alertWarning(
        'The data submission is only supported on computers.',
      );
      return;
    }

    getStat();
  });
</script>

<style lang="scss" scoped>
  .submit-page {
    .rawdata-intro {
      background-color: #ffffff;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 14px;
      border: 2px solid #3a78e8;

      li::marker {
        color: #3a78e8;
      }

      h5 {
        font-size: 16px;
        color: #333333;
      }
    }

    .data-upload {
      font-size: 20px;
      color: #333333;
    }

    .route {
      background-color: #d6e5ff;
      border-left: 3px solid #3a78e8;
      padding: 4px 0 4px 20px;
    }

    .route-1,
    .route-2,
    .route-3,
    .route-tool {
      background-color: #ffffff;
      padding: 15px 0;
      width: 50%;
      box-shadow: 0 1px 20px 0 rgba(118, 109, 224, 0.1);

      :deep(.el-upload-dragger) {
        background-color: #f5f9fe;
        padding: 20px;

        .el-icon--upload {
          color: #fe7f2b;
          margin-bottom: 0;
        }
      }

      .el-divider {
        margin: 0;
      }
    }

    .route-3 {
      :deep(
          .el-form--default.el-form--label-top
            .el-form-item
            .el-form-item__label
        ) {
        position: relative;
        padding-left: 10px;

        &:before {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          height: 16px;
          width: 2px;
          background: #fe7f2b;
        }

        & + .el-form-item__content .el-input__wrapper {
          border-radius: 12px;
        }
      }

      .el-button {
        width: 25%;
        border-radius: 25px;
        background-color: #ffebde;
        box-shadow: 2px 4px 8px 0 #ffebde;
        color: #fe7f2b;
        font-weight: 600;
        transition: all 0.4s ease;

        &:hover {
          background-color: #fe7f2b;
          color: #ffffff;
        }
      }
    }

    .route-content {
      padding: 0 20px;
      margin-top: 15px;

      .route-1-item {
        background-color: #f7f8f8;
        border-left: 3px solid #fe7f2b;
        padding: 4px 0 4px 20px;
      }

      .step-list {
        position: relative;

        .img {
          display: flex;
          flex-direction: column;
          align-items: center;

          img {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 4px 5px 4px 4px;
            width: 42px;

            &.wins {
              width: 44px;
              padding: 5px 5px 4px 5px;
            }

            & + a {
              color: #fe7f2b;
              text-decoration: underline;
            }
          }
        }

        &:before {
          position: absolute;
          left: 0px;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          height: 114%;
          width: 1px;
          background: #dee0e0;
        }

        & > li {
          padding: 2px 0;
        }
      }
    }

    //table tab
    :deep(.el-tabs--card > .el-tabs__header) {
      border-bottom: none;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        margin-left: 3px;
        border: none;
        border-radius: 4px;
        background-color: #edf4fe;
      }

      .el-tabs__item.is-active {
        background-color: #3a78e8;
        color: #fff;
        border-radius: 4px;
      }
    }

    .uncheckedTable,
    .checkedTable {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
      }

      .date-to {
        margin: 0 0.5rem;
      }

      :deep(.el-table__header .cell) {
        font-weight: 600;
      }

      :deep(.el-radio__input.is-checked) {
        .el-radio__inner {
          border: none;
          background-color: #fe7f2b;
        }

        & + .el-radio__label {
          color: #333333;
        }
      }

      :deep(.el-table td.el-table__cell div) {
        display: flex;
        align-items: center;
      }
    }

    .btn-primary {
      box-shadow: 0 8px 28px rgba(58, 120, 232, 0.2);
    }

    .el-icon {
      margin-right: 0.5rem;
    }

    :deep(.el-upload-list) {
      margin: 5px 0;
    }
  }

  .ftp-info i {
    font-style: italic;
    font-weight: 600;
    padding: 2px 10px;
    background: #dbdee0;
    border-radius: 15px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
  }

  .step-list-ftp li {
    list-style-type: none;
  }

  ul li {
    list-style-type: disc;
    margin-top: 4px;

    &::marker {
      color: #fe7f2b;
    }
  }

  @media (max-width: 767px) {
    .submit-page {
      text-align: justify;
      word-break: break-all;
    }

    .d-flex {
      flex-wrap: wrap;
    }

    .w-50,
    .submit-page .route-1,
    .submit-page .route-2,
    .submit-page .route-3,
    .submit-page .route-tool {
      width: 100% !important;
    }

    .route-1.mr-2 {
      margin-right: 0;
      margin-bottom: 1rem;
    }
  }
</style>
