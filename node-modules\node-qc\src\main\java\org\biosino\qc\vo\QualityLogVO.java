package org.biosino.qc.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QualityLogVO {

    private long projNum = 0;
    private long projSuccessNum = 0;

    private long expNum = 0;
    private long expSuccessNum = 0;

    private long sapNum = 0;
    private long sapSuccessNum = 0;

    private long analNum = 0;
    private long analSuccessNum = 0;

    private long runNum = 0;
    private long runSuccessNum = 0;

    private long publish = 0;
    private long publishSuccess = 0;

    private long analData = 0;
    private long analDataSize = 0;
    private String analDataSizeStr;

    private long rawData = 0;
    private long rawDataSize = 0;
    private String rawDataSizeStr;

    private List<Reason> topQcOperator;
    private Integer qcOperatorTotal;

    private long startReview = 0;
    private long pass = 0;
    private long reject = 0;

    @Data
    public static class Reason {
        private String name;
        private Integer value;
    }
}
