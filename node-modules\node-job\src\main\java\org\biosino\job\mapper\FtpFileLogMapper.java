package org.biosino.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.biosino.system.api.domain.sftp.FtpFileLog;

/**
 * <AUTHOR>
 */
@Mapper
public interface FtpFileLogMapper extends BaseMapper<FtpFileLog> {

    @Select("SELECT COUNT(*) FROM ftp_file_log WHERE `name` NOT LIKE '%.md5' AND `status` <> '已删除' AND `create_time` BETWEEN #{startTime} AND #{endTime};")
    Long countByDate(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("SELECT SUM(size) FROM ftp_file_log WHERE `name` NOT LIKE '%.md5' AND `status` <> '已删除' AND `create_time` BETWEEN #{startTime} AND #{endTime};")
    Long sumSizeByDate(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 清理状态是uploading的记录且updateTime是20天及以前的记录，将状态修改为error
     */
    @Update("UPDATE ftp_file_log SET status = '错误',update_time = NOW() WHERE status = '上传中' AND update_time < DATE_SUB(NOW(), INTERVAL 20 DAY);")
    Long updateUploadingToError();
}
