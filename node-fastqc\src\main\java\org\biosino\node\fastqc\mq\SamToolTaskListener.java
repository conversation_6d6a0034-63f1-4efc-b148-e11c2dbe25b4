package org.biosino.node.fastqc.mq;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.SamToolTaskStartMsg;
import org.biosino.common.rabbitmq.msg.SamToolTaskStatusMsg;
import org.biosino.node.fastqc.service.SamToolService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SamToolTaskListener {

    private final SamToolService samToolService;

    private final MessageSender messageSender;

    @RabbitListener(queues = "samtool_task_start_queue", concurrency = "5")
    @RabbitHandler
    public void handlerTaskStart(@Payload SamToolTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("samtool_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            samToolService.handlerTaskStart(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 发送错误消息给前端
            SamToolTaskStatusMsg statusMsg = new SamToolTaskStatusMsg();
            statusMsg.setDataNo(msg.getDataNo());
            statusMsg.setStatus(FastQCTaskStatusEnum.failed.name());
            statusMsg.setFailCause("samtool service error, please contact admin");
            statusMsg.setExitCode(-1);
            messageSender.sendDelayMsg("samtool_task_status_routing_key", statusMsg);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    /**
     * high priority
     */
    @RabbitListener(queues = "samtool_hp_task_start_queue", concurrency = "5")
    @RabbitHandler
    public void handlerHpTaskStart(@Payload SamToolTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("samtool_hp_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            samToolService.handlerTaskStart(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 发送错误消息给前端
            SamToolTaskStatusMsg statusMsg = new SamToolTaskStatusMsg();
            statusMsg.setDataNo(msg.getDataNo());
            statusMsg.setStatus(FastQCTaskStatusEnum.failed.name());
            statusMsg.setFailCause("samtool service error, please contact admin");
            statusMsg.setExitCode(-1);
            messageSender.sendDelayMsg("samtool_task_status_routing_key", statusMsg);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }
}
