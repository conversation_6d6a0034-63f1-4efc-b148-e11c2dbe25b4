<template>
  <div
    class="radius-20 plr-20 d-flex align-items-center justify-space-between bg-white"
  >
    <span class="before-circle">{{ title }}</span>
    <div>
      <el-tooltip effect="light" :content="countTip" placement="top">
        <span class="text-main-color font-600 font-28">{{
          formatNumber(count)
        }}</span>
      </el-tooltip>
      <span class="text-other-color font-14 ml-05">{{ unitOne }}</span>
      <span class="font-30 text-other-color ml-05 mr-03">/</span>
      <el-tooltip effect="light" :content="totalTip" placement="top">
        <span class="text-secondary-color font-600 font-28">{{
          formatNumber(total)
        }}</span>
      </el-tooltip>
      <span class="text-other-color font-14 ml-05">{{ unitTwo }}</span>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps } from 'vue';
  import { formatNumber } from '@/utils';
  defineProps({
    title: {
      type: String,
    },
    count: {
      type: Number,
      default: 0,
    },
    countTip: {
      type: String,
    },
    total: {
      type: Number,
      default: 0,
    },
    totalTip: {
      type: String,
    },
    unitOne: {
      type: String,
    },
    unitTwo: {
      type: String,
    },
  });
</script>

<style scoped lang="scss">
  .before-circle:before {
    width: 7px;
    height: 7px;
  }

  .submission-before-circle:before {
    background-color: red;
  }
</style>
