package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.SecurityUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.PublishDTO;
import org.biosino.system.dto.dto.PublishSearchDTO;
import org.biosino.system.dto.mapper.PublishDTOMapper;
import org.biosino.system.repository.*;
import org.biosino.system.vo.metadata.PublishVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PublishService {
    private final PublishRepository publishRepository;
    private final SampleRepository sampleRepository;
    private final ProjectRepository projectRepository;
    private final AnalysisRepository analysisRepository;
    private final ExperimentRepository experimentRepository;
    private final RemoteMemberService remoteMemberService;

    // 新增未审核的项目，文献：在temp_data中，状态：init，删除：否
    // 审核通过，文献：temp_data：null ，状态：audit，删除：否
    // 二次新增文献，文献：temp_data：在temp_data中 ，状态：init，删除：否
    // 删除个别文献，文献：temp_data：在temp_data中 ，状态：init，删除：是，审核通过后删除
    @Transactional(rollbackFor = Exception.class)
    public void save(List<PublishDTO> publishDTO, String type, String typeId, String creator) {

        if (isEmptyPublish(publishDTO)) {
            return;
        }
        List<Publish> publishList = publishRepository.findByTypeId(type, typeId);

        Map<String, Publish> idMap = new HashMap<>();

        if (CollUtil.isNotEmpty(publishList)) {
            idMap = publishList.stream().collect(Collectors.toMap(Publish::getId, Function.identity(), (existingValue, newValue) -> existingValue));
        }

        Set<String> updateIds = new HashSet<>();

        for (PublishDTO dto : publishDTO) {
            if (dto.isEmpty()) {
                dto.setId(null);
                continue;
            }
            String id = dto.getId();
            Publish publish;
            if (idMap.containsKey(id)) {
                publish = idMap.get(id);

                BeanUtil.copyProperties(dto, publish);

                publish.setUpdater(creator);
                publish.setUpdateDate(new Date());
                publishRepository.save(publish);

                updateIds.add(id);
            } else {
                publish = new Publish();
                BeanUtil.copyProperties(dto, publish);

                publish.setId(IdUtil.objectId());
                publish.setType(type);
                publish.setTypeId(typeId);
                publish.setCreator(creator);
                publish.setCreateDate(new Date());
                publish.setUpdater(creator);
                publish.setUpdateDate(new Date());
                publish.setAudited(AuditEnum.audited.name());
                publish.setStatus(ConfigConstants.enable);
                publish.setSort(1);

                publishRepository.save(publish);
            }
        }
        idMap.forEach((id, publish) -> {
            if (!updateIds.contains(id)) {

                publish.setUpdater(creator);
                publish.setUpdateDate(new Date());
                publish.setDeleted(true);

                publishRepository.save(publish);
            }
        });
    }

    public static boolean isEmptyPublish(List<PublishDTO> publishDTOList) {
        if (CollUtil.isEmpty(publishDTOList)) {
            return true;
        }
        boolean result = true;
        for (PublishDTO publishDTO : publishDTOList) {
            if (StrUtil.isNotBlank(publishDTO.getPublication()) || StrUtil.isNotBlank(publishDTO.getDoi()) || StrUtil.isNotBlank(publishDTO.getPmid())
                    || StrUtil.isNotBlank(publishDTO.getArticleName()) || StrUtil.isNotBlank(publishDTO.getReference())) {
                return false;
            }
        }
        return result;
    }

    public List<PublishVO> getPublishVo(String type, String typeId) {
        List<Publish> publishList = publishRepository.findByTypeId(type, typeId);
        List<PublishVO> result = new ArrayList<>();

        for (Publish publish : publishList) {
            PublishVO vo = new PublishVO();
            PublishDTOMapper.INSTANCE.copyToVo(publish, vo);
            result.add(vo);
        }
        return result;
    }


    public void delete(String id) {
        Publish publish = publishRepository.findById(id).orElseThrow(() -> new ServiceException("Not found the publish：" + id));
        publish.setDeleted(true);
        publishRepository.save(publish);
    }

    public Page<PublishVO> list(PublishSearchDTO dto) {
        if (StrUtil.isNotBlank(dto.getCreateEmail())) {
            String creatorEmail = dto.getCreateEmail();
            MemberDTO memberDTO = getMemberInfoByEmail(creatorEmail.trim());
            dto.setCreator(memberDTO.getId());
        }
        PageImpl<Publish> page = publishRepository.findAllPage(dto);
        List<String> creators = page.getContent().stream().map(Publish::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);
        return page.map(x -> {
            PublishVO vo = new PublishVO();
            BeanUtil.copyProperties(x, vo);

            // 设置creatorEmail
            vo.setCreateEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });
    }

    public MemberDTO getOneMemberByMemberId(String memberId) {
        R<MemberDTO> rMember = remoteMemberService.getOneMemberByMemberId(memberId, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember)) {
            throw new ServiceException(rMember.getMsg());
        }
        return rMember.getData();
    }

    public MemberDTO getMemberInfoByEmail(String email) {
        R<MemberDTO> rMember = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember) || rMember.getData() == null) {
            throw new ServiceException(StrUtil.format("User {} not found", email));
        }
        return rMember.getData();
    }

    public Map<String, String> getMemberIdToEmailMap(List<String> memberIds) {
        R<Map<String, String>> r = remoteMemberService.getMemberIdToEmailMapByMemberIds(memberIds, "FtpUser", SecurityConstants.INNER);
        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public void save(PublishSearchDTO dto) {
        // 校验Type No
        String typeNo = dto.getTypeId();

        if (StrUtil.isBlank(typeNo)) {
            throw new ServiceException("Type ID cannot be empty");
        }
        typeNo = typeNo.trim();

        String type;
        String creator;

        if (typeNo.startsWith(SequenceType.PROJECT.getPrefix())) {
            Project project = projectRepository.findByNo(typeNo);
            if (project == null) {
                throw new ServiceException("Add failed, this project ID does not exist");
            }
            type = AuthorizeType.project.name();
            creator = project.getCreator();
        } else if (typeNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
            Experiment experiment = experimentRepository.findByNo(typeNo);
            if (experiment == null) {
                throw new ServiceException("Add failed, this experiment ID does not exist");
            }
            type = AuthorizeType.experiment.name();
            creator = experiment.getCreator();
        } else if (typeNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
            Sample sample = sampleRepository.findByNo(typeNo);
            if (sample == null) {
                throw new ServiceException("Add failed, this experiment ID does not exist");
            }
            type = AuthorizeType.sample.name();
            creator = sample.getCreator();
        } else if (typeNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
            Analysis analysis = analysisRepository.findByNo(typeNo);
            if (analysis == null) {
                throw new ServiceException("Add failed, this analysis ID does not exist");
            }
            type = AuthorizeType.analysis.name();
            creator = analysis.getCreator();
        } else {
            throw new ServiceException("Incorrect Type ID format");
        }

        Long userId = SecurityUtils.getUserId();

        // 编辑
        if (StrUtil.isNotBlank(dto.getId())) {

            Publish publish = publishRepository.findById(dto.getId()).orElseThrow(() -> new ServiceException("Not found the publish：" + dto.getId()));
            BeanUtil.copyProperties(dto, publish);
            publish.setType(type.trim());
            publish.setCreator(creator);
            publish.setUpdater(userId.toString());
            publish.setUpdateDate(new Date());
            publishRepository.save(publish);
        } else {
            // 新增
            Publish publish = new Publish();
            BeanUtil.copyProperties(dto, publish);
            publish.setId(IdUtil.objectId());
            publish.setType(type.trim());
            publish.setCreator(creator);
            publish.setCreateDate(new Date());
            publish.setUpdater(userId.toString());
            publish.setUpdateDate(new Date());
            publish.setAudited(AuditEnum.audited.name());
            publish.setDeleted(false);
            publishRepository.save(publish);
        }
    }

    /**
     * 临时代码，处理publish的creator字段问题
     */
    public void updatePublishCreator() {
        List<Publish> publishes = publishRepository.findAll();

        for (Publish publish : publishes) {

            // 校验Type No
            String typeNo = publish.getTypeId();

            String type = null;
            String creator = null;

            if (typeNo.startsWith(SequenceType.PROJECT.getPrefix())) {
                Project project = projectRepository.findByNo(typeNo);
                if (project == null) {
                    System.out.println(publish.getId() + ": Add failed, this project ID does not exist");
                    continue;
                }
                type = AuthorizeType.project.name();
                creator = project.getCreator();
            } else if (typeNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                Experiment experiment = experimentRepository.findByNo(typeNo);
                if (experiment == null) {
                    System.out.println(publish.getId() + ": Add failed, this experiment ID does not exist");
                    continue;
                }
                type = AuthorizeType.experiment.name();
                creator = experiment.getCreator();
            } else if (typeNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
                Sample sample = sampleRepository.findByNo(typeNo);
                if (sample == null) {
                    System.out.println(publish.getId() + ": Add failed, this experiment ID does not exist");
                    continue;
                }
                type = AuthorizeType.sample.name();
                creator = sample.getCreator();
            } else if (typeNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
                Analysis analysis = analysisRepository.findByNo(typeNo);
                if (analysis == null) {
                    System.out.println(publish.getId() + ": Add failed, this analysis ID does not exist");
                    continue;
                }
                type = AuthorizeType.analysis.name();
                creator = analysis.getCreator();
            } else {
                System.out.println(publish.getId() + ": Incorrect ID format");
            }

            if (StrUtil.isNotBlank(creator)) {
                publish.setCreator(creator);
                publish.setType(type);
                publish.setUpdater(creator);
                publish.setStatus(ConfigConstants.enable);
            }
            if (publish.getCreateDate() == null) {
                publish.setCreateDate(publish.getUpdateDate());
            }
            if (publish.getUpdateDate() == null) {
                publish.setUpdateDate(publish.getCreateDate());
            }

            String priority = publish.getPriority();
            if ("High".equalsIgnoreCase(priority) || "3".equals(priority)) {
                publish.setPriority("3");
            } else if ("Medium".equalsIgnoreCase(priority) || "2".equals(priority)) {
                publish.setPriority("2");
            } else {
                publish.setPriority("1");
            }

            if (publish.getStatus() == null) {
                publish.setStatus(ConfigConstants.disable);
            }
            publishRepository.save(publish);
        }
    }

    public void export(HttpServletResponse response) {
        List<Publish> publishList = publishRepository.findAll();
        ExcelUtil<Publish> util = new ExcelUtil<>(Publish.class);
        util.exportExcel(response, publishList, "sheet1");
    }
}
