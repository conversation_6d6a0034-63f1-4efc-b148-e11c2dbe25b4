package org.biosino.job.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.job.repository.ExpSampleTypeRepository;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计experiment和sample属性填写的完整度
 *
 * <AUTHOR> Li
 * @date 2025/1/1
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsAttrCompletenessService {
    private final MongoTemplate mongoTemplate;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    public void generate(String month) {
        updateExperimentCompleteness();
        updateSampleCompleteness();
    }

    private void updateExperimentCompleteness() {
        List<ExpSampleType> expTpls = expSampleTypeRepository.findByType(AuthorizeType.experiment.name());
        Map<String, ExpSampleType> typeToTpl = expTpls.stream().collect(Collectors.toMap(ExpSampleType::getName, e -> e, (k1, k2) -> k1));
        MongoPagingIterator<Experiment> iterator = new MongoPagingIterator<>(mongoTemplate, Experiment.class, 10000);
        int pageNum = 0;
        while (iterator.hasNext()) {
            pageNum++;
            log.info("当前正在处理Experiment第 {} 页", pageNum);
            BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Experiment.class);
            List<Pair<Query, Update>> updateList = new ArrayList<>();
            List<Experiment> page = iterator.next();
            for (Experiment experiment : page) {
                String expType = experiment.getExpType();
                if (typeToTpl.containsKey(expType)) {
                    ExpSampleType expSampleType = typeToTpl.get(expType);
                    if (CollUtil.isEmpty(expSampleType.getAttributes())) {
                        continue;
                    }
                    int size = expSampleType.getAttributes().size();
                    int attrSize = experiment.getAttributes().size();
                    int completeness = (attrSize * 100) / size;
                    Update update = new Update().set("completeness", completeness);
                    updateList.add(Pair.of(new Query(Criteria.where("_id").is(new ObjectId(experiment.getId()))), update));
                }
            }
            if (CollUtil.isNotEmpty(updateList)) {
                operations.updateMulti(updateList);
                operations.execute();
            }
        }
        log.info("Experiment属性完整度更新完毕");

    }

    private void updateSampleCompleteness() {
        List<ExpSampleType> sapTbls = expSampleTypeRepository.findByType(AuthorizeType.sample.name());
        Map<String, ExpSampleType> typeToTpl = sapTbls.stream().collect(Collectors.toMap(ExpSampleType::getName, e -> e, (k1, k2) -> k1));
        MongoPagingIterator<Sample> sampleIterator = new MongoPagingIterator<>(mongoTemplate, Sample.class, 10000);
        int pageNum = 0;
        while (sampleIterator.hasNext()) {
            pageNum++;
            log.info("当前正在处理Sample第 {} 页", pageNum);
            BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, Sample.class);
            List<Pair<Query, Update>> updateList = new ArrayList<>();
            List<Sample> page = sampleIterator.next();
            for (Sample sample : page) {
                String subjectType = sample.getSubjectType();
                if (typeToTpl.containsKey(subjectType)) {
                    ExpSampleType expSampleType = typeToTpl.get(subjectType);
                    if (CollUtil.isEmpty(expSampleType.getAttributes())) {
                        continue;
                    }
                    int size = expSampleType.getAttributes().size();
                    int attrSize = sample.getAttributes().size();
                    int completeness = (attrSize * 100) / size;
                    Update update = new Update().set("completeness", completeness);
                    updateList.add(Pair.of(new Query(Criteria.where("_id").is(new ObjectId(sample.getId()))), update));
                }
            }
            if (CollUtil.isNotEmpty(updateList)) {
                operations.updateMulti(updateList);
                operations.execute();
            }
        }
        log.info("Sample属性完整度更新完毕");
    }
}
