package org.biosino.job.repository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.enums.PopularDataType;
import org.biosino.job.api.vo.statistics.PopularDataVO;
import org.biosino.job.repository.ProjectCustomRepository;
import org.biosino.job.repository.util.RepositoryUtil;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ProjectCustomRepositoryImpl implements ProjectCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Project findByNo(String projectNo) {
        Query query = new Query();
        Criteria criteria = new Criteria().andOperator(Criteria.where("proj_no").is(projectNo), RepositoryUtil.base());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Project.class);
    }

    @Override
    public List<Project> findAllByDate(Date startTime, Date endTime) {
        Query query = new Query();
        Criteria criteria = new Criteria().andOperator(Criteria.where("submission_date").gte(startTime).lte(endTime), RepositoryUtil.base());
        query.addCriteria(criteria);
        query.fields().include("proj_no").include("visible_status");
        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public Long countAllByStatus(VisibleStatusEnum statusEnum) {
        Query query = new Query();
        Criteria criteria = new Criteria().andOperator(Criteria.where("visible_status").is(statusEnum), RepositoryUtil.auditedBase());
        query.addCriteria(criteria);
        return mongoTemplate.count(query, Project.class);
    }

    @Override
    public List<Project> findAllByMember(String memberId) {
        Query query = new Query();
        Criteria criteria = new Criteria().andOperator(Criteria.where("creator").is(memberId), RepositoryUtil.base());
        query.addCriteria(criteria);
        query.fields().include("proj_no").include("visible_status");
        return mongoTemplate.find(query, Project.class);
    }

    @Override
    public List<PopularDataVO.PopularDataStat> findPopularDataByField(final String field, final PopularDataType popularDataType, final boolean exportFlag) {
        // 创建聚合管道
        final MatchOperation matchOperation = Aggregation.match(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()).and(field).exists(true));
        final AggregationOperation sort = Aggregation.sort(Sort.Direction.DESC, field);
        final AggregationOperation limit = Aggregation.limit(exportFlag ? PopularDataVO.EXPORT_ROWS : PopularDataVO.SHOW_ROWS);
        final ProjectionOperation project = Aggregation.project(popularDataType.getNoField(), "name", field);

        // 构建聚合
        final Aggregation aggregation = Aggregation.newAggregation(matchOperation, sort, limit, project);
        // 执行聚合并返回结果
        final AggregationResults<Document> aggregate = mongoTemplate.aggregate(aggregation, popularDataType.getDbClz(), Document.class);
        final List<Document> mappedResults = aggregate.getMappedResults();
        final List<PopularDataVO.PopularDataStat> data = new ArrayList<>();
        if (CollUtil.isNotEmpty(mappedResults)) {
            for (Document mappedResult : mappedResults) {
                final PopularDataVO.PopularDataStat stat = new PopularDataVO.PopularDataStat();
                stat.setNo(mappedResult.getString(popularDataType.getNoField()));
                stat.setName(mappedResult.getString("name"));
                stat.setNum(getLongVal(mappedResult, field));
                data.add(stat);
            }
        }
        return data;
    }

    private Long getLongVal(Document doc, String field) {
        long val;
        try {
            val = doc.getLong(field);
        } catch (Exception e) {
            Integer intVal = doc.getInteger(field);
            val = intVal != null ? intVal.longValue() : 0;
        }
        return val;
    }

}
