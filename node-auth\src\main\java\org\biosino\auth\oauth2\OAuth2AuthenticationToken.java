package org.biosino.auth.oauth2;

import org.biosino.auth.oauth2.model.OAuthUser;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * OAuth2认证Token
 *
 * <AUTHOR>
 */
public class OAuth2AuthenticationToken extends AbstractAuthenticationToken {

    private final String accessToken;
    private final OAuthUser OAuthUser;
    private final Object principal;

    /**
     * 未认证的Token构造器
     */
    public OAuth2AuthenticationToken(String accessToken) {
        super(null);
        this.accessToken = accessToken;
        this.OAuthUser = null;
        this.principal = null;
        setAuthenticated(false);
    }

    /**
     * 已认证的Token构造器
     */
    public OAuth2AuthenticationToken(Object principal, OAuthUser OAuthUser,
                                     String accessToken, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.OAuthUser = OAuthUser;
        this.accessToken = accessToken;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return accessToken;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public OAuthUser getOAuthUser() {
        return OAuthUser;
    }
}
