<template>
  <div>
    <el-dialog
      v-model="securityDialog"
      title="Security status change confirmation"
      width="1200"
      class="radius-14 security"
    >
      <div class="text-main-color font-600">
        1.Filter the data that you want to modify the security status：
      </div>
      <div class="bg-gray mt-05 form">
        <div class="d-flex align-items-center">
          <div class="font-16 label mr-2">Security</div>
          <el-radio-group v-model="fromSecurity" @change="selectSecurity">
            <el-radio label="Private">Private</el-radio>
            <el-radio label="Restricted">Restricted</el-radio>
            <el-radio label="Public">Public</el-radio>
          </el-radio-group>
        </div>
        <div class="d-flex align-items-center mt-05">
          <div v-if="showExp" class="font-16 label mr-2">Experiment ID</div>
          <el-autocomplete
            v-if="showExp"
            v-model="experimentNo"
            :fetch-suggestions="
              (queryString, cb) => {
                remoteSelect(queryString, 'expNo', cb);
              }
            "
            placeholder="Please enter a keyword"
            :teleported="false"
            :loading="selectLoading"
            style="width: 200px"
            clearable
          >
          </el-autocomplete>
          <div v-if="showSap" class="font-16 label mr-2">Sample</div>
          <el-select
            v-if="showSap"
            v-model="sampleSearchType"
            :teleported="false"
            clearable
            placeholder="Select"
            style="width: 170px"
            @change="changeSample"
          >
            <el-option label="Sample ID" value="sapNo"></el-option>
            <el-option label="Sample Name" value="sapName"></el-option>
            <el-option label="Sample Type" value="sapType"></el-option>
            <el-option label="Sample Organism" value="organism"></el-option>
          </el-select>

          <el-select
            v-if="showSap && !(sampleSearchType === 'sapNo')"
            v-model="sampleSearchText"
            filterable
            class="ml-1"
            clearable
            remote
            reserve-keyword
            placeholder="Please enter a keyword"
            :remote-method="
              (queryString, cb) => {
                remoteSelect(queryString, sampleSearchType);
              }
            "
            :loading="selectLoading"
            style="width: 200px"
          >
            <el-option
              v-for="(value, key) in selectOption[sampleSearchType]"
              :key="'sap-option-' + key"
              :label="value"
              :value="value"
            ></el-option>
          </el-select>

          <el-autocomplete
            v-if="showSap && sampleSearchType === 'sapNo'"
            v-model="sampleSearchText"
            class="ml-1"
            :fetch-suggestions="
              (queryString, cb) => {
                remoteSelect(queryString, sampleSearchType, cb);
              }
            "
            placeholder="Please enter a keyword"
            :teleported="false"
            :loading="selectLoading"
            style="width: 200px"
            clearable
          >
          </el-autocomplete>
          <div v-if="showSap || showExp" class="text-center">
            <el-button
              round
              :icon="Search"
              type="primary"
              class="radius-8 ml-2"
              @click="getDataList(false)"
              >Filter
            </el-button>
          </div>
        </div>
      </div>
      <el-tabs v-model="activeTabName" class="mt-05">
        <el-tab-pane
          :label="type === 'analysis' ? 'Analysis Data' : 'Data'"
          name="Data"
        >
          <el-table
            ref="rawTable"
            v-loading="loading"
            :data="rawData"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            max-height="300"
            border
            tooltip-effect="dark"
            :row-key="row => row.datNo"
            :sort-orders="['ascending', 'descending']"
            @sort-change="relTableSortChange"
            @selection-change="selectionDataChange"
          >
            <el-table-column
              type="selection"
              :reserve-selection="true"
              width="45"
            />
            <el-table-column prop="datNo" label="Data ID" width="120" sortable>
            </el-table-column>
            <el-table-column
              prop="name"
              label="Data Name"
              sortable
              show-overflow-tooltip
            />
            <el-table-column
              v-if="type !== 'analysis'"
              prop="expName"
              label="Experiment"
              sort-by="expNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/experiment/detail/${scope.row.expNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.expNo }} ({{ scope.row.expName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="type !== 'analysis'"
              prop="sapName"
              label="Sample"
              sort-by="sapNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/sample/detail/${scope.row.sapNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.sapNo }} ({{ scope.row.sapName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="type !== 'analysis'"
              prop="runName"
              label="Run"
              sort-by="runNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/run/detail/${scope.row.runNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.runNo }} ({{ scope.row.runName }})
                </router-link>
              </template>
            </el-table-column>

            <el-table-column
              v-if="type === 'analysis'"
              label="Analysis"
              sortable
              sort-by="analNo"
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/analysis/detail/${scope.row.analNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.analNo }} ({{ scope.row.analName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="dataType"
              label="Data Type"
              width="115"
              sortable
            />

            <el-table-column
              width="160"
              prop="uploadTime"
              label="Upload Time"
              sortable
            />
          </el-table>

          <pagination
            v-show="queryPageAndSort.totalCount > 0"
            v-model:page="queryPageAndSort.pageNum"
            v-model:limit="queryPageAndSort.pageSize"
            :page-sizes="[5, 10, 100, 500, 1000]"
            class="mb-1 mt-2 justify-center"
            :total="queryPageAndSort.totalCount"
            @pagination="pageDataList"
          />
        </el-tab-pane>

        <!--Related Analysis Data-->
        <el-tab-pane label="Related Analysis Data" name="Related Analysis Data">
          <el-table
            ref="analysisTable"
            v-loading="loading"
            :data="relatedAnalysisData"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            max-height="300"
            border
            tooltip-effect="light"
            :row-key="row => row.datNo"
            @selection-change="selectionRelatedAnalysisDataChange"
          >
            <el-table-column
              type="selection"
              :reserve-selection="true"
              width="43"
            />
            <el-table-column
              prop="datNo"
              label="Data ID"
              min-width="50"
              sortable
            />
            <el-table-column prop="name" label="Name/Security" sortable>
            </el-table-column>
            <el-table-column
              prop="analNo"
              label="Analysis ID"
              width="120"
              sortable
            >
              <template #default="scope">
                <router-link
                  :to="`/analysis/detail/${scope.row.analNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.analNo }}
                </router-link>
              </template>
            </el-table-column>
            <el-table-column prop="analName" label="Analysis Name" sortable />
            <el-table-column prop="dataType" label="Data Type" sortable />
            <el-table-column prop="uploadTime" label="Upload Time" sortable />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <!--      <div class="mb-05">
              Get all data list, please export data links
              <el-button class="ml-05 radius-8" size="small" type="primary"
                >Export Data Links
              </el-button>
            </div>-->
      <div v-show="fromSecurity !== 'Public'">
        <div class="text-main-color font-600 d-flex align-items-center">
          2.Change the data and analysis data of the above query from &nbsp;
          <el-button class="radius-8" size="small" :type="securityColor">
            {{ fromSecurity }}
          </el-button>
          &nbsp;to:&nbsp;
          <el-radio-group v-model="toSecurity" size="small">
            <el-radio-button
              v-if="fromSecurity === 'Private'"
              label="Restricted"
            />
            <el-radio-button
              v-if="fromSecurity === 'Restricted' || fromSecurity === 'Private'"
              label="Public"
            />
          </el-radio-group>
        </div>
        <div v-if="toSecurity === 'Public'" class="bg-gray p-10-15 mt-05">
          The quality control information of the data will also be disclosed
          synchronously.
        </div>
        <div v-if="toSecurity === 'Restricted'" class="bg-gray p-10-15 mt-05">
          <el-radio-group v-model="restrictedSelect">
            <el-radio label="untilOne"
              >Until
              <el-input
                v-model="untilOne"
                class="ml-1"
                disabled
                style="width: 150px"
              />
            </el-radio>
            <el-radio label="untilTwo"
              >Until
              <el-input
                v-model="untilTwo"
                class="ml-1"
                disabled
                style="width: 150px"
              />
            </el-radio>
            <el-radio label="requireRequest">Require request</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            :disabled="fromSecurity === 'Public'"
            class="radius-8"
            type="primary"
            @click="updateSecurity"
          >
            Confirm
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="securityDialog = false"
            >Cancel
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="recordDialog" width="1000" class="radius-14 security">
      <div class="text-main-color font-600 mb-1">
        According to the Regulations of the People's Republic of China on the
        Management of Human Genetic Resources and the Rules for the
        Implementation of the Regulations on the Management of Human Genetic
        Resources, the disclosure of data on human genetic resources is subject
        to review and filing through the Human Genetic Resources Review. Please
        fill in the corresponding filing number truthfully. If you have any
        questions, please contact
        <a class="text-primary" href="mailto:<EMAIL>"
          ><EMAIL></a
        >
        or 010-88225151
      </div>
      <el-radio-group
        v-if="params.recordSampleType === 'Human'"
        v-model="params.recordRadio"
      >
        <el-radio
          label="Biological samples such as urine, feces, serum, and plasma that may contain very small amounts of shed, residual, or free cells or genes"
          size="large"
        ></el-radio>
        <el-radio label="Other samples" size="large"></el-radio>

        <el-input
          v-show="params.recordRadio === 'Other samples'"
          v-model="params.recordValue"
          placeholder="Please enter the filing number"
        />
      </el-radio-group>

      <el-radio-group
        v-else-if="params.recordSampleType === 'Environment host'"
        v-model="params.recordRadio"
        class="w-100"
      >
        <div class="d-flex align-items-center w-100 mb-1">
          <el-radio label="non-human" size="large"></el-radio>
          <el-radio label="human" size="large"></el-radio>
          <el-input
            v-show="params.recordRadio === 'human'"
            v-model="params.recordValue"
            placeholder="Please enter the filing number"
          />
        </div>
      </el-radio-group>

      <el-radio-group
        v-else-if="params.recordSampleType === 'Cell line'"
        v-model="params.recordRadio"
      >
        <el-radio label="non-human" size="large"></el-radio>
        <el-radio label="human-commercial cell lines" size="large"></el-radio>

        <el-radio
          label="human non-commercial cell lines"
          size="large"
        ></el-radio>
        <el-input
          v-show="params.recordRadio === 'human non-commercial cell lines'"
          v-model="params.recordValue"
          placeholder="Please enter the filing number"
        />
      </el-radio-group>

      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            class="radius-8"
            type="primary"
            @click="submitUpdateDataSecurity"
          >
            Confirm
          </el-button>
          <el-button
            plain
            class="radius-8"
            type="primary"
            @click="recordDialog = false"
            >Cancel
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ElMessageBox } from 'element-plus';
  import { getCurrentInstance, ref } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import {
    getSecurityDataList,
    searchSelectWord,
    updateDataSecurity,
    verifyDataHumanType,
    verifyMetadataEdit,
  } from '@/api/app/security';
  import { parseTime } from '@/utils/nodeCommon';
  import { trimStr } from '@/utils';
  import useUserStore from '@/store/modules/user';

  const { proxy } = getCurrentInstance();
  const { member } = useUserStore();

  const securityDialog = ref(false);

  const type = ref('');
  const typeNo = ref('');

  const queryPageAndSort = ref({
    sortKey: '',
    sortType: '',
    pageNum: 1,
    pageSize: 10,
    totalCount: 0,
  });

  // 最上方的security筛选类型
  const fromSecurity = ref('Private');

  const showExp = ref(true);
  const experimentNo = ref('');

  const showSap = ref(true);

  // sample的检索类型
  const sampleSearchType = ref('');

  // sample的检索值
  const sampleSearchText = ref('');

  // sample的下拉框
  const selectOption = ref({});
  const samples = ref({});

  const activeTabName = ref('Data');

  const loading = ref(false);

  function init(optionType, optionNo) {
    proxy.$modal.loading('Loading...');
    verifyMetadataEdit({ type: optionType, typeNo: optionNo })
      .then(response => {
        // 如果校验失败，则不弹出这个框
        if (response.data && response.data.length > 0) {
          proxy.$modal.alertError(
            `The security modification can't be performed now, because: <br>
             ${response.data.map(it => '- ' + it).join('<br>')}`,
            true,
          );
        } else {
          if (member.auditStatus !== 'pass') {
            proxy.$modal.alertWarning(
              `<p>Your account status is not 'audit pass', can not update data security !</p>
         <span class="text-danger">*</span>If you receive 'audit pass' email recently, but the User Center's Account status is not 'audit pass',
         please log out the website and log in again!`,
              true,
            );
            return;
          }

          type.value = optionType;
          typeNo.value = optionNo;

          if (optionType === 'experiment' || optionType === 'sample') {
            showExp.value = false;
          }

          if (optionType === 'run' || optionType === 'analysis') {
            showExp.value = false;
            showSap.value = false;
          }

          fromSecurity.value = 'Private';

          resetSearchForm();

          getDataList();

          initUntilDate();
          securityDialog.value = true;
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const rawData = ref([]);
  const relatedAnalysisData = ref([]);

  /** 数据分页 */
  function pageDataList(pageData) {
    queryPageAndSort.value.pageSize = pageData.limit;
    queryPageAndSort.value.pageNum = pageData.page;
    getDataList();
  }

  function relTableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryPageAndSort.value.sortKey = prop;
      queryPageAndSort.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      getDataList();
    }
  }

  function getDataList() {
    let params = {
      type: type.value,
      typeNo: typeNo.value,
      security: fromSecurity.value,
      expNo: experimentNo.value,
    };

    if (sampleSearchType.value && sampleSearchText.value) {
      params[sampleSearchType.value] = sampleSearchText.value;
    }

    let pagePram = queryPageAndSort.value;

    params = { ...params, ...pagePram };

    loading.value = true;
    getSecurityDataList(params)
      .then(response => {
        rawData.value = response.data.dataVos || [];

        queryPageAndSort.value.totalCount = response.data.total;

        relatedAnalysisData.value = response.data.relatedAnalysisDataVos || [];

        // 如果RawData没有数据,而relatedAnalysisData有数据
        if (
          rawData.value?.length === 0 &&
          relatedAnalysisData.value?.length !== 0
        ) {
          activeTabName.value = 'Related Analysis Data';
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const selectLoading = ref(false);

  function remoteSelect(queryStr, field, cb) {
    queryStr = trimStr(queryStr);

    let params = {
      type: type.value,
      typeNo: typeNo.value,
      security: fromSecurity.value,
      field: field,
      keyword: queryStr,
    };

    selectLoading.value = true;
    searchSelectWord(params)
      .then(response => {
        selectOption.value[field] = response.data || [];
        if (cb) {
          let results = selectOption.value[field].map(value => ({ value }));
          cb(results);
        }
      })
      .finally(() => {
        selectLoading.value = false;
      });
  }

  const recordDialog = ref(false);

  const params = ref({
    securityType: undefined,
    pubDate: undefined,
    dataNos: undefined,
    recordSampleType: undefined,
    recordRadio: undefined,
    recordValue: undefined,
  });

  function updateSecurity() {
    if (!toSecurity.value) {
      proxy.$modal.alertWarning(
        'Please select the security level to be modified!',
      );
      return;
    }

    if (
      selectedDataRows.value.length === 0 &&
      selectedRelatedAnalysisRows.value.length === 0
    ) {
      proxy.$modal.alertWarning(
        'Please select the data for which you want to update the security level',
      );
      return;
    }

    // 合并选中的data、analysis data、relatedAnalysis
    let rawDataIds = selectedDataRows.value.map(it => it.datNo);
    const relatedAnalysisDataIds = selectedRelatedAnalysisRows.value.map(
      it => it.datNo,
    );

    if (!rawDataIds) {
      rawDataIds = [];
    }

    if (relatedAnalysisDataIds && relatedAnalysisDataIds.length !== 0) {
      rawDataIds = rawDataIds.concat(relatedAnalysisDataIds);
    }

    let pubDate = undefined;
    if (toSecurity.value === 'Restricted') {
      if (restrictedSelect.value === 'untilOne') {
        pubDate = untilOne.value;
      }
      if (restrictedSelect.value === 'untilTwo') {
        pubDate = untilTwo.value;
      }
    }

    params.value.securityType = toSecurity.value;
    params.value.pubDate = pubDate;
    params.value.dataNos = rawDataIds;
    params.value.recordSampleType = undefined;
    params.value.recordRadio = undefined;
    params.value.recordValue = undefined;

    if (toSecurity.value === 'Public') {
      verifyDataHumanType(params.value).then(response => {
        const hasRecordType = response.data;

        if (hasRecordType && hasRecordType.length !== 0) {
          if (hasRecordType.length > 1) {
            proxy.$modal.alertWarning(
              `There is ${hasRecordType.join(', ')} in Sample Type. Please select Sample Type one by one to change the security of data.`,
            );
            return;
          }
          // 弹框要求用户填写备案号
          params.value.recordSampleType = hasRecordType[0];
          if (params.value.recordSampleType === 'Human') {
            params.value.recordRadio = 'Other samples';
          } else if (params.value.recordSampleType === 'Environment host') {
            params.value.recordRadio = 'human';
          } else if (params.value.recordSampleType === 'Cell line') {
            params.value.recordRadio = 'human non-commercial cell lines';
          }
          recordDialog.value = true;
        } else {
          // 直接提交
          submitUpdateDataSecurity();
        }
      });
    } else {
      // 直接提交
      submitUpdateDataSecurity();
    }
  }

  function updateDataSecurityRequest(dataSize) {
    proxy.$modal.loading('Submitting');
    updateDataSecurity(params.value)
      .then(response => {
        if (response.data) {
          proxy.$modal.alertError(response.data.join('</br>'), true);
          proxy.$modal.closeLoading();
        } else {
          let timeout = Math.floor(dataSize / 10) + 6;
          // 最多120s
          if (timeout > 120) {
            timeout = 120;
          }
          // console.log('timeout:', timeout);
          // 延迟刷新，等待ES索引同步完成
          setTimeout(() => {
            proxy.$modal.closeLoading();
            securityDialog.value = false;
            if (timeout < 120) {
              proxy.$modal.msgSuccess('Modified successfully');
              window.location.reload();
            } else {
              ElMessageBox.alert(
                'Modified successfully! If you find that the Security of Data has not changed, please wait patiently for a few minutes and refresh the browser again',
                'Tip',
                {
                  confirmButtonText: 'OK',
                  callback: () => {
                    window.location.reload();
                  },
                },
              );
            }
          }, timeout * 1000);
        }
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }

  function submitUpdateDataSecurity() {
    if (
      params.value.recordRadio === 'Other samples' ||
      params.value.recordRadio === 'human' ||
      params.value.recordRadio === 'human non-commercial cell lines'
    ) {
      if (!params.value.recordValue) {
        proxy.$modal.alertError('Please fill in the filing number');
        return;
      } else {
        const regex = /^[a-zA-Z0-9-]+$/;
        if (!regex.test(params.value.recordValue)) {
          proxy.$modal.alertError(
            'Invalid format, only supports numbers、letters、-',
          );
          return;
        }
      }
    } else {
      params.value.recordValue = undefined;
    }

    let dataSize = params.value.dataNos.length;
    if (dataSize > 1000) {
      proxy.$modal.alertError(
        'The number of selected data exceeds the limit of 1000',
      );
      return;
    }
    if (toSecurity.value === 'Public') {
      proxy.$modal
        .confirm(
          'After the data security level is changed to "Public", the "Data QC info" will also be visible.',
          '',
        )
        .then(() => {
          updateDataSecurityRequest(dataSize);
        });
    } else {
      updateDataSecurityRequest(dataSize);
    }
  }

  function changeSample() {
    selectOption[sampleSearchType] = [];
    sampleSearchText.value = '';
  }

  /** 清空过滤条件 */
  function resetSearchForm() {
    experimentNo.value = '';

    sampleSearchType.value = 'sapType';
    sampleSearchText.value = '';

    activeTabName.value = 'Data';

    samples.value = [];

    proxy.$refs['rawTable']?.clearSelection();
    proxy.$refs['analysisTable']?.clearSelection();

    queryPageAndSort.value = {
      sortKey: '',
      sortType: '',
      pageNum: 1,
      pageSize: 100,
      totalCount: 0,
    };
  }

  const securityColor = ref('danger');
  const toSecurity = ref('Restricted');

  const untilOne = ref('');
  const untilTwo = ref('');
  const restrictedSelect = ref('untilOne');

  function initUntilDate() {
    let d1 = new Date();
    // 未来1年
    d1.setFullYear(d1.getFullYear() + 1);
    untilOne.value = parseTime(d1, '{yy}-{mm}-{dd}');

    // 未来2年
    d1.setFullYear(d1.getFullYear() + 1);
    untilTwo.value = parseTime(d1, '{yy}-{mm}-{dd}');
  }

  const selectedDataRows = ref([]);

  function selectionDataChange(selection) {
    selectedDataRows.value = selection;
  }

  const selectedRelatedAnalysisRows = ref([]);

  function selectionRelatedAnalysisDataChange(selection) {
    selectedRelatedAnalysisRows.value = selection;
  }

  const selectSecurity = () => {
    if (fromSecurity.value === 'Private') {
      securityColor.value = 'danger';
    } else if (fromSecurity.value === 'Restricted') {
      securityColor.value = 'warning';
      toSecurity.value = 'Public';
    } else {
      securityColor.value = 'success';
    }
    resetSearchForm();
    getDataList();
  };

  defineExpose({
    init,
  });
</script>

<style lang="scss" scoped>
  .form {
    padding: 6px 10px;
  }

  .svg-security {
    width: 35px;
    height: 35px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .selected-data {
    padding: 10px;
    font-size: 16px;
  }

  .label {
    font-weight: 600;
    width: 120px;
    text-align: right;
  }

  :deep(.el-dialog__body) {
    padding: 0 20px !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  .collapse {
    height: 46px;
    overflow: hidden;
  }

  .arrow {
    cursor: pointer;
    position: relative;
    top: 6px;
  }

  .security {
    :deep(.el-dialog__body) {
      padding-top: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
  }

  :deep(.el-dialog .el-dialog__body) {
    flex: 1;
    overflow: auto;
  }

  .text-main-color {
    text-align: justify;
  }
</style>
