package org.biosino.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.FtpFileMd5Status;
import org.biosino.common.core.exception.ServiceException;

import java.io.File;
import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Node业务层次相关的工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class NodeUtils {

    public final static Pattern pattern = Pattern.compile("^([A-Za-z]+)([0-9]{2,8})$");


    public static List<String> cleanRelatedLinks(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        // 剔除空白字符串，并且去重
        return CollUtil.distinct(CollUtil.removeBlank(list));
    }

    public static float div(Long v1, Long v2) {
        // 检查除数是否为零
        if (v2 == 0) {
            return 0F;
        }

        // 将Long类型转换为float类型进行除法运算
        float percentage = (float) v1 / v2 * 100;

        // 使用Math.round保留两位小数
        percentage = Math.round(percentage * 100) / 100.0f;

        return percentage;
    }

    public static double convertToTB(long bytes) {
        // 1 TB = 1024^4 字节
        double tb = (double) bytes / (1024L * 1024 * 1024 * 1024);

        // 保留两位小数
        tb = Math.round(tb * 100) / 100D;

        return tb;
    }

    public static double convertToGB(long bytes) {
        // 1 GB = 1024^3 字节
        double gb = (double) bytes / (1024L * 1024 * 1024);

        // 保留两位小数
        gb = Math.round(gb * 100) / 100D;

        return gb;
    }

    public static boolean isBeanEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value;
            try {
                value = field.get(obj);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            if (value != null && !value.toString().trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public static String readMd5(String path) {
        return readMd5(FileUtil.file(path));
    }

    public static String readMd5(File file) {
        if (!FileUtil.exist(file)) {
            throw new ServiceException("md5 file not found");
        }
        String md5FileContent = FileUtil.readUtf8String(file);
        String[] split = md5FileContent.split("\\s+", 2);
        for (String s : split) {
            String trimmed = s.trim();
            if (trimmed.length() == 32 && trimmed.matches("^[A-Fa-f0-9]+$")) {
                return trimmed;
            }
        }
        throw new ServiceException("md5 content error");
    }

    public static String getFileMd5Status(String path) {
        return getFileMd5Status(FileUtil.file(path + ".md5"));
    }

    public static String getFileMd5Status(File file) {
        if (!FileUtil.exist(file)) {
            return FtpFileMd5Status.not_provided.getStatus();
        }
        try {
            readMd5(file);
        } catch (Exception e) {
            log.error("read md5 error: {}", file.getAbsolutePath());
            return FtpFileMd5Status.invalid_format.getStatus();
        }
        return FtpFileMd5Status.provided.getStatus();
    }


    public static List<String> getAllNos(String no, List<String> usedIds) {
        Set<String> set = new HashSet<>();
        if (StrUtil.isNotBlank(no)) {
            set.add(no);
        }
        if (CollUtil.isNotEmpty(usedIds)) {
            set.addAll(usedIds);
        }
        return CollUtil.newArrayList(set);
    }

    public static String get8NumberNo(String oldNo) {
        if (StrUtil.isBlank(oldNo)) {
            return oldNo;
        }
        oldNo = oldNo.trim();
        Matcher matcher = pattern.matcher(oldNo);

        if (matcher.matches()) {
            String prefix = matcher.group(1);
            String num = matcher.group(2);
            // 补到8位
            String no = StrUtil.padPre(num, 8, '0');
            return prefix + no;
        }
        return oldNo;
    }
}
