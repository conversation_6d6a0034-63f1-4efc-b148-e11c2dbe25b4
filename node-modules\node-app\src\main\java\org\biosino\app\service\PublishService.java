package org.biosino.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.mapper.PublishDTOMapper;
import org.biosino.app.repository.*;
import org.biosino.app.vo.PublishVO;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.SubmissionDataTypeEnum;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.model.Member;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishService {

    private final PublishRepository publishRepository;
    private final ProjectRepository projectRepository;
    private final SampleRepository sampleRepository;
    private final ExperimentRepository experimentRepository;
    private final AnalysisRepository analysisRepository;
    private final SubmissionRepository submissionRepository;

    @Value("${node.api.plosp-api:https://idc.biosino.org/plosp-api}")
    private String PLOSP_API;

    @Value("${node.api.plosp-token:de615fc84a036a8db4c9b41d9eb5dd3c4184954e8405db80eaab1726d97b09f1}")
    private String PLOSP_TOKEN;

    public List<PublishVO> findPublishedPublish(Integer limit) {
        List<Publish> publishList = publishRepository.findAllPublishedPublish(limit);

        if (CollUtil.isEmpty(publishList)) {
            return null;
        }
        List<PublishVO> tempList = getPublishVOS(publishList);

        List<PublishVO> resultList = new ArrayList<>();

        // 判断关联的Type是否开放
        for (PublishVO publishVO : tempList) {
            List<String> relatedIds = publishVO.getRelatedId();
            if (CollUtil.isEmpty(relatedIds)) {
                continue;
            }

            List<String> resultIds = new ArrayList<>();
            for (String relatedId : relatedIds) {
                if (visibleId(relatedId)) {
                    resultIds.add(relatedId);
                }
            }
            if (CollUtil.isNotEmpty(resultIds)) {
                publishVO.setRelatedId(resultIds);
                resultList.add(publishVO);
            }
        }

        if (limit == null) {
            limit = Integer.MAX_VALUE;
        }

        return resultList.stream().sorted(Comparator.comparing(PublishVO::getSort).reversed()).limit(limit).collect(Collectors.toList());
    }

    public boolean visibleId(String typeNo) {
        if (typeNo.startsWith(SequenceType.PROJECT.getPrefix())) {
            return projectRepository.existVisibleByNo(typeNo);
        } else if (typeNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
            return experimentRepository.existVisibleByNo(typeNo);
        } else if (typeNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
            return sampleRepository.existVisibleByNo(typeNo);
        } else if (typeNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
            return analysisRepository.existVisibleByNo(typeNo);
        }
        return false;
    }

    public List<PublishVO> findUserPublish() {

        String memberId = SecurityUtils.getMemberId();
        List<Publish> publishList = publishRepository.findAllByUser(memberId);

        if (CollUtil.isEmpty(publishList)) {
            return null;
        }

        List<PublishVO> resultList = getPublishVOS(publishList);

        return resultList;
    }

    private static List<PublishVO> getPublishVOS(List<Publish> publishList) {
        Map<String, PublishVO> result = new HashMap<>();

        for (Publish publish : publishList) {
            String doi = publish.getDoi();
            if (StrUtil.isBlank(doi)) {
                continue;
            }
            if (!result.containsKey(doi)) {
                PublishVO publishVO = new PublishVO();

                BeanUtils.copyProperties(publish, publishVO);

                publishVO.setRelatedId(CollUtil.newArrayList(publish.getTypeId()));

                result.put(doi, publishVO);
            } else {
                PublishVO publishVO = result.get(doi);
                List<String> relatedId = publishVO.getRelatedId();

                // 以最近的数据为主
                if (publishVO.getCreateDate().before(publish.getCreateDate())) {

                    PublishVO newPublish = new PublishVO();

                    BeanUtils.copyProperties(publish, newPublish);
                    relatedId.add(publish.getTypeId());
                    newPublish.setRelatedId(relatedId);

                    result.put(doi, newPublish);
                }
            }
        }

        List<PublishVO> resultList = new ArrayList<>(result.values());
        // 根据 updateTime 字段降序排列
        resultList.sort((vo1, vo2) -> vo2.getCreateDate().compareTo(vo1.getCreateDate()));
        return resultList;
    }

    public PublishVO findPublishById(String id) {

        String memberId = SecurityUtils.getMemberId();
        Publish masterPublish = publishRepository.findById(id).orElseThrow(() -> new ServiceException("Not fount the publish"));
        Publish tempData = masterPublish.getTempData();

        PublishVO publishVO = new PublishVO();
        BeanUtils.copyProperties(masterPublish, publishVO, "subNo");

        List<Publish> publishList = publishRepository.findAllByUserAndDOI(memberId, masterPublish.getDoi());

        if (tempData != null) {
            String subNo = tempData.getSubNo();
            BeanUtils.copyProperties(tempData, publishVO);
            publishList = publishRepository.findTempBySubNo(subNo);
        }

        publishVO.setRelatedId(CollUtil.newArrayList(masterPublish.getTypeId()));

        if (CollUtil.isNotEmpty(publishList)) {
            List<String> relatedId = publishVO.getRelatedId();
            for (Publish publish : publishList) {
                relatedId.add(publish.getTypeId());
            }
            publishVO.setRelatedId(CollUtil.distinct(relatedId));
        }
        return publishVO;
    }

    public void deletePublish(String doi) {
        String memberId = SecurityUtils.getMemberId();
        if (memberId == null) {
            throw new NotPermissionException("Not permission");
        }
        List<Publish> publishList = publishRepository.findAllByUserAndDOI(memberId, doi);

        if (CollUtil.isEmpty(publishList)) {
            throw new ServiceException("No publish found for deletion");
        }
        // 校验数据是否存在公开的
        Set<String> publicData = new HashSet<>();
        for (Publish publish : publishList) {
            String typeId = publish.getTypeId();
            if (visibleId(typeId)) {
                publicData.add(typeId);
            }
        }

        if (CollUtil.isNotEmpty(publicData)) {
            String join = CollUtil.join(publicData, "; ");
            throw new ServiceException(StrUtil.format("Delete failed, {} has been made public. Please contact the administrator", join));
        }

        for (Publish publish : publishList) {
            publish.setDeleted(true);
            publishRepository.save(publish);
        }
    }

    public void existRelatedId(String typeNo, String memberId) {
        if (typeNo == null) {
            throw new ServiceException("ID cannot be empty");
        }

        typeNo = typeNo.trim();
        if (typeNo.startsWith(SequenceType.PROJECT.getPrefix())) {
            Project project = projectRepository.findByNo(typeNo);
            if (project == null) {
                throw new ServiceException("Add failed, this project ID does not exist");
            }
            if (memberId != null && !memberId.equals(project.getCreator())) {
                throw new ServiceException("Add failed, this project is not your data");
            }
        } else if (typeNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
            Experiment experiment = experimentRepository.findByNo(typeNo);
            if (experiment == null) {
                throw new ServiceException("Add failed, this experiment ID does not exist");
            }
            if (memberId != null && !memberId.equals(experiment.getCreator())) {
                throw new ServiceException("Add failed, this experiment is not your data");
            }
        } else if (typeNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
            Sample sample = sampleRepository.findByNo(typeNo);
            if (sample == null) {
                throw new ServiceException("Add failed, this sample ID does not exist");
            }
            if (memberId != null && !memberId.equals(sample.getCreator())) {
                throw new ServiceException("Add failed, this sample is not your data");
            }
        } else if (typeNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
            Analysis analysis = analysisRepository.findByNo(typeNo);
            if (analysis == null) {
                throw new ServiceException("Add failed, this analysis ID does not exist");
            }
            if (memberId != null && !memberId.equals(analysis.getCreator())) {
                throw new ServiceException("Add failed, this analysis is not your data");
            }
        } else {
            throw new ServiceException("Incorrect ID format");
        }

    }

    public String savePublish(PublishVO dto) {

        String publishId = dto.getId();
        final String memberId = SecurityUtils.getMemberId();
        if (memberId == null) {
            throw new NotPermissionException("Not permission");
        }
        if (StrUtil.isBlank(dto.getDoi())) {
            throw new ServiceException("Save Publish failed, DOI cannot be empty");
        }

        if (!StringUtils.isDOIFormat(dto.getDoi())) {
            throw new ServiceException("Save Publish failed, DOI format is incorrect");
        }

        if (StrUtil.isNotBlank(dto.getPmid()) && !StringUtils.isPMIDFormat(dto.getPmid())) {
            throw new ServiceException("Save Publish failed, PMID format is incorrect");
        }

        HashSet<String> idSet = CollUtil.newHashSet(dto.getRelatedId());

        Publish tempPublish = new Publish();
        BeanUtils.copyProperties(dto, tempPublish, "id");
        tempPublish.setUpdateDate(new Date());

        Set<String> updateId = new HashSet<>();

        String subNo = dto.getSubNo();

        Submission submission = new Submission();
        if (StrUtil.isBlank(subNo)) {
            // 新增submission
            Submitter submitter = new Submitter();
            Member member = SecurityUtils.getMember();
            BeanUtil.copyProperties(member, submitter);
            submitter.setMemberId(memberId);

            submission.setSubmitter(submitter);
            submission.setDataType(SubmissionDataTypeEnum.publish.name());
            submission.setCreator(memberId);
            submission.setCreateTime(new Date());
            submission.setUpdateTime(new Date());
            submission.setStatus(SubmissionStatusEnum.editing.name());
        } else {
            submission = submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("not fount the submission"));
        }

        // 编辑
        if (StrUtil.isNotBlank(publishId)) {
            Publish masterPublish = publishRepository.findById(publishId).orElseThrow(() -> new ServiceException("not fount the publish"));

            List<Publish> publishList;
            if (StrUtil.isBlank(subNo)) {
                submission.setPublishId(masterPublish.getId());
                submission = submissionRepository.save(submission);
                publishList = publishRepository.findAllByUserAndDOI(memberId, masterPublish.getDoi());
            } else {
                publishList = publishRepository.findTempBySubNo(subNo);
                submission.setStatus(SubmissionStatusEnum.editing.name());
                submission.setUpdateTime(new Date());
                submissionRepository.save(submission);
            }

            for (Publish publish : publishList) {

                Publish targetPublish = new Publish();
                // 拷贝原有的字段属性值
                if (publish.getTempData() != null) {
                    BeanUtils.copyProperties(publish.getTempData(), targetPublish);
                } else {
                    BeanUtils.copyProperties(publish, targetPublish);
                }

                // 赋予新修改的字段属性值
                targetPublish.setArticleName(tempPublish.getArticleName());
                targetPublish.setDoi(tempPublish.getDoi());
                targetPublish.setPmid(tempPublish.getPmid());
                targetPublish.setPublication(tempPublish.getPublication());
                targetPublish.setReference(tempPublish.getReference());
                targetPublish.setUpdateDate(tempPublish.getUpdateDate());
                targetPublish.setAudited(AuditEnum.unaudited.name());
                targetPublish.setSubNo(submission.getSubNo());

                publish.setTempData(targetPublish);

                // 不在新的ID范围的内的表示要删除
                if (!idSet.contains(publish.getTypeId())) {
                    publish.setTempData(null);
                    publish.setDeleted(true);
                }
                publishRepository.save(publish);

                updateId.add(publish.getTypeId());
            }

            // 没有处理的ID，待新增
            for (String id : idSet) {
                if (!updateId.contains(id)) {

                    tempPublish.setId(IdUtil.objectId());

                    tempPublish.setType(getType(id));

                    tempPublish.setTypeId(id);
                    tempPublish.setCreator(memberId);
                    tempPublish.setCreateDate(new Date());
                    tempPublish.setUpdater(memberId);
                    tempPublish.setUpdateDate(new Date());
                    tempPublish.setAudited(AuditEnum.unaudited.name());
                    tempPublish.setStatus(ConfigConstants.enable);
                    tempPublish.setSort(1);
                    tempPublish.setSubNo(submission.getSubNo());

                    tempPublish.setTempData(PublishDTOMapper.INSTANCE.copy(tempPublish));
                    publishRepository.save(tempPublish);
                }
            }

        } else {
            // 新增Publish
            tempPublish.setId(IdUtil.objectId());

            submission.setPublishId(tempPublish.getId());
            submission = submissionRepository.save(submission);

            int i = 0;
            for (String id : idSet) {
                if (i != 0) {
                    tempPublish.setId(IdUtil.objectId());
                }

                tempPublish.setType(getType(id));

                tempPublish.setTypeId(id);
                tempPublish.setCreator(memberId);
                tempPublish.setCreateDate(new Date());
                tempPublish.setUpdater(memberId);
                tempPublish.setUpdateDate(new Date());
                tempPublish.setAudited(AuditEnum.unaudited.name());
                tempPublish.setStatus(ConfigConstants.enable);
                tempPublish.setSort(1);
                tempPublish.setSubNo(submission.getSubNo());

                tempPublish.setTempData(PublishDTOMapper.INSTANCE.copy(tempPublish));
                publishRepository.save(tempPublish);
                i++;
            }
        }
        return submission.getSubNo();
    }

    private String getType(String id) {
        if (id.startsWith(SequenceType.PROJECT.getPrefix())) {
            return AuthorizeType.project.name();
        } else if (id.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
            return AuthorizeType.experiment.name();
        } else if (id.startsWith(SequenceType.SAMPLE.getPrefix())) {
            return AuthorizeType.sample.name();
        } else if (id.startsWith(SequenceType.ANALYSIS.getPrefix())) {
            return AuthorizeType.analysis.name();
        }
        return null;
    }

    public PublishVO getPubInfoFromPlosp(String doi) {
        if (StrUtil.isBlank(doi)) {
            throw new ServiceException("doi can not be null");
        }
        try {
            HashMap<String, Object> params = new HashMap<>();
            params.put("doi", doi);
            params.put("token", PLOSP_TOKEN);
            String result = HttpUtil.get(PLOSP_API + "/api/article/findByDois.do", params, 10 * 1000);
            JSONObject jsonObject = JSON.parseObject(result);
            String status = jsonObject.getString("status");
            if ("success".equals(status)) {
                JSONArray articles = jsonObject.getJSONArray("articles");
                if (CollUtil.isNotEmpty(articles)) {
                    JSONObject article = articles.getJSONObject(0);
                    String journalTitle = article.getString("journalTitle");
                    String pmid = article.getString("pmid");
                    String title = article.getString("title");

                    String author = article.getString("author");
                    String journalAbbr = article.getString("journalAbbr");
                    String year = article.getString("year");
                    String volume = article.getString("volume");
                    String journalIssnPrint = article.getString("journalIssnPrint");

                    String reference = "";

                    List<String> authorList = Arrays.stream(author.split("; ")).collect(Collectors.toList());

                    List<String> names = formatNames(authorList);

                    if (names.size() > 3) {
                        reference = reference + CollUtil.join(names.subList(0, 3), ", ") + ", et al. ";
                    } else {
                        reference = reference + CollUtil.join(names, ", ") + ". ";
                    }

                    int publishCountByTitle = getPublishCountByTitle(title);

                    if (publishCountByTitle >= 2) {
                        throw new ServiceException("This doi article has been updated, please fill in the Publication information manually!");
                    }
                    if (StrUtil.isNotBlank(title)) {
                        reference = reference + title;
                    }
                    if (StrUtil.isNotBlank(journalAbbr)) {
                        reference = reference + " " + journalAbbr;
                    }
                    if (StrUtil.isNotBlank(year)) {
                        reference = reference + ". " + year + ";";
                    }
                    if (StrUtil.isNotBlank(volume)) {
                        reference = reference + volume + ";";
                    }
                    if (StrUtil.isNotBlank(journalIssnPrint)) {
                        reference = reference + journalIssnPrint + ". ";
                    }
                    reference = reference + "doi:" + doi;


                    PublishVO publishVO = new PublishVO();
                    publishVO.setArticleName(title);
                    publishVO.setDoi(doi);
                    publishVO.setPmid(pmid);
                    publishVO.setPublication(journalTitle);
                    publishVO.setReference(reference);

                    return publishVO;
                } else {
                    return null;
                }
            } else {
                throw new ServiceException("System error, please contact the administrator");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public static List<String> formatNames(List<String> inputNames) {
        List<String> formattedNames = new ArrayList<>();

        for (String name : inputNames) {
            if (name.trim().isEmpty()) continue; // 跳过空字符串
            String[] parts = name.split("\\s+");
            if (parts.length < 2) continue; // 确保至少有名字和姓氏

            String lastName = parts[parts.length - 1];
            StringBuilder initials = new StringBuilder();

            for (int i = 0; i < parts.length - 1; i++) {
                String part = parts[i];
                if (part.contains("-")) {
                    String[] subParts = part.split("-");
                    for (String subPart : subParts) {
                        initials.append(subPart.charAt(0));
                    }
                } else {
                    initials.append(part.charAt(0));
                }
            }

            formattedNames.add(lastName + " " + initials.toString());
        }

        return formattedNames;
    }

    public int getPublishCountByTitle(String title) {
        if (StrUtil.isBlank(title)) {
            return 0;
        }
        try {
            HashMap<String, Object> params = new HashMap<>();
            params.put("title", title);
            params.put("token", PLOSP_TOKEN);
            String result = HttpUtil.get(PLOSP_API + "/api/article/findByTitle.do", params, 10 * 1000);
            JSONObject jsonObject = JSON.parseObject(result);
            String status = jsonObject.getString("status");
            if ("success".equals(status)) {
                JSONArray articles = jsonObject.getJSONArray("articles");
                if (CollUtil.isNotEmpty(articles)) {
                    return articles.size();
                } else {
                    return 0;
                }
            } else {
                throw new ServiceException("System error, please contact the administrator");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

}
