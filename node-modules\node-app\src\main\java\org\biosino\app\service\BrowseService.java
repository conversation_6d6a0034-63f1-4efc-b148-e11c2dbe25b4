package org.biosino.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.biosino.app.dto.mapper.BrowseStatEsMapper;
import org.biosino.app.dto.tree.TreeItemField;
import org.biosino.app.repository.*;
import org.biosino.app.vo.ExpIconInfoVO;
import org.biosino.app.vo.PublishVO;
import org.biosino.app.vo.search.EsSearchResult;
import org.biosino.app.vo.search.EsSearchResultItem;
import org.biosino.app.vo.search.EsSelectQueryVO;
import org.biosino.app.vo.search.NodeEsQueryVO;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.es.PublicOrControlled;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.es.entity.NodeEs;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.biosino.common.es.mapper.NodeEsMapper;
import org.biosino.common.mongo.dto.TreeItemDTO;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.BrowseStatEsDTO;
import org.biosino.es.api.vo.BrowseStatResVO;
import org.dromara.easyes.common.params.SFunction;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.biosino.app.service.ExpTypeGroupService.*;

/**
 * elasticsearch 索引查询服务层
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BrowseService extends BaseService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;

    private final NodeEsMapper nodeEsMapper;
    private final ProjectRepository projectRepository;
    private final ExperimentRepository experimentRepository;
    private final SampleRepository sampleRepository;
    private final AnalysisRepository analysisRepository;
    private final ExpTypeGroupService expTypeGroupService;
    private final ExpSampleTypeRepository expSampleTypeRepository;

    private static final String FIELD_MODIFIED_DATE = "Modified Date";
    private static final String FIELD_USED_IDS = "Used Ids";
    private static final String FIELD_ACCESS = "Access";
    private static final String FIELD_LEVEL = "Level";
    private static final String FIELD_PUBLISHED = "Published";
    private static final String FIELD_EXPERIMENT_TYPE = "Experiment Type";
    private static final String FIELD_SAMPLE_TYPE = "Sample Type";
    private static final List<String> FAST_QC_FIELDS = CollUtil.toList("num_seqs", "bases", "Q20(%)", "Q30(%)");

    /**
     * Browse界面查询
     */
    public EsSearchResult search(final NodeEsQueryVO searchVO) {
        final String advQueryWord = searchVO.getAdvQueryWord();
        final boolean isAdv = StrUtil.isNotBlank(advQueryWord);
        final String queryWord = searchVO.getQueryWord();
        final LambdaEsQueryWrapper<NodeEs> queryWrapper;
        if (isAdv) {
            // 高级检索
            queryWrapper = parseAdvQuery(advQueryWord);
        } else if (StrUtil.isNotBlank(queryWord)) {
            // 全文检索
            queryWrapper = initQueryWrapper(queryWord);
        } else {
            // 条件为空时，查询全部数据
            queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        }

//        final String source = nodeEsMapper.getSource(queryWrapper);
//        System.out.println(source);

        // 左侧聚合统计信息
        final EsSearchResult result = new EsSearchResult();
        final Map<String, TreeItemField> allAggFieldData = allAggField();
        genStatInfo(queryWrapper, result, allAggFieldData);

        // 左侧统计项筛选
        leftStatQuery(queryWrapper, searchVO, allAggFieldData);

        final String orderField = searchVO.getSortKey();
        if (StrUtil.isNotBlank(orderField)) {
            final boolean isAsc = "asc".equalsIgnoreCase(searchVO.getSortType());
            /*if ("typeId".equals(orderField)) {
                // 使用脚本排序
                // 提取编号并转换为整数
                Script script = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG,
                        "Integer.parseInt(doc['typeId'].value.substring(3))", Collections.emptyMap());
                Script script2 = new Script(ScriptType.INLINE, "painless",
                        "if (doc['id'].value.startsWith('OEP')) { return Integer.parseInt(doc['id'].value.substring(3)); }" +
                                "else if (doc['id'].value.startsWith('HTE')) { return 1000 + Integer.parseInt(doc['id'].value.substring(4)); }" +
                                "else if (doc['id'].value.startsWith('ORT')) { return 2000 + Integer.parseInt(doc['id'].value.substring(4)); }" +
                                "throw new IllegalArgumentException('Unknown id format');", Collections.emptyMap());
                ScriptSortBuilder scriptSortBuilder = new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER)
                        .order(isAsc ? SortOrder.ASC : SortOrder.DESC);
                queryWrapper.sort(scriptSortBuilder);
            } else {
                queryWrapper.orderBy(true, isAsc, orderField);
            }*/
            queryWrapper.orderBy(true, isAsc, orderField);
        } else {
            // 默认排序
            queryWrapper.orderByDesc(NodeEs::getModifiedDate);
        }
        // 查询分页列表
        final Pageable pageable = searchVO.initPageInfo();
//        log.info("查询条件：{}", nodeEsMapper.getSource(queryWrapper));
        // Easy es分页页码从1开始
        final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(queryWrapper, pageable.getPageNumber() + 1, pageable.getPageSize());
//        result.setQueryWord(queryWord);
        // 分页查询结果
        final List<NodeEs> list = pageInfo.getList();
        result.setPageInfo(new PageImpl<>(initResult(list), pageable, pageInfo.getTotal()));
        return result;
    }

    /**
     * 左侧统计项筛选
     */
    private void leftStatQuery(final LambdaEsQueryWrapper<NodeEs> queryWrapper, final NodeEsQueryVO searchVO,
                               final Map<String, TreeItemField> allAggFieldData) {
        final LinkedHashSet<NodeEsQueryVO.LeftStatQuery> leftStatQueries = searchVO.getLeftStatQueries();
        // 左侧树选中项筛选
        if (CollUtil.isNotEmpty(leftStatQueries)) {
            final Map<String, Set<String>> leftStatQueryMap = new HashMap<>();
            for (NodeEsQueryVO.LeftStatQuery leftStatQuery : leftStatQueries) {
                final String type = leftStatQuery.getType();
                Set<String> set = leftStatQueryMap.get(type);
                if (set == null) {
                    set = new HashSet<>();
                }
                set.add(leftStatQuery.getName());
                leftStatQueryMap.put(type, set);
            }
            for (Map.Entry<String, Set<String>> entry : leftStatQueryMap.entrySet()) {
                final String key = entry.getKey();
                TreeItemField treeItemField;
                if (FIELD_MULTIPLE_OMICS.equals(key)) {
                    queryWrapper.eq(NodeEs::getType, NodeEsTypeEnum.project.name());
                    treeItemField = allAggFieldData.get(FIELD_EXPERIMENT_TYPE);
                } else {
                    treeItemField = allAggFieldData.get(key);
                }
                if (treeItemField != null) {
                    final SFunction<NodeEs, ?> fieldFunc = treeItemField.getFieldFunc();
                    final Set<String> value = entry.getValue();
                    if (CollUtil.isNotEmpty(value)) {
                        // 是否存在doi筛选
                        if (FIELD_MULTIPLE_OMICS.equals(key)) {
                            // 多组学筛选
                            if (value.contains(FIELD_NO)) {
                                if (value.size() > 1) {
                                    queryWrapper.in(NodeEs::getMultiOmics, CollUtil.toList(true, false));
                                } else {
                                    queryWrapper.eq(NodeEs::getMultiOmics, false);
                                }
                            } else {
                                queryWrapper.eq(NodeEs::getMultiOmics, true);
                            }
                            value.remove(FIELD_NO);
                            value.remove(FIELD_YES);
                            if (CollUtil.isNotEmpty(value)) {
                                queryWrapper.in(fieldFunc, value);
                            }
                        } else {
                            queryWrapper.in(fieldFunc, value);
                        }
                    }
                }
            }
        }

        // Access筛选
        LinkedHashSet<String> accessQueries = searchVO.getAccessQueries();
        if (CollUtil.isNotEmpty(accessQueries)) {
            final TreeItemField treeItemField = allAggFieldData.get(FIELD_ACCESS);
            queryWrapper.in(treeItemField.getFieldFunc(), accessQueries);
        }
    }

    public static SearchSourceBuilder copyQuery(final NodeEsMapper nodeEsMapper, final LambdaEsQueryWrapper<NodeEs> originalWrapper) {
        final SearchSourceBuilder searchSourceBuilder = nodeEsMapper.getSearchSourceBuilder(originalWrapper);
        searchSourceBuilder.size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
        return searchSourceBuilder;
    }

    private void genStatInfo(final LambdaEsQueryWrapper<NodeEs> originalWrapper, final EsSearchResult result, final Map<String, TreeItemField> allAggFieldData) {
        // easy es不支持groupBy指定字段大小，使用原生SearchSourceBuilder获取聚合统计结果
        final SearchSourceBuilder searchSourceBuilder = copyQuery(nodeEsMapper, originalWrapper);
        // 所有分组字段聚合
        for (Map.Entry<String, TreeItemField> entry : allAggFieldData.entrySet()) {
            // 设置聚合字段
            searchSourceBuilder.aggregation(initAggregation(entry.getKey(), entry.getValue().getFieldFunc()));

//            final SearchSourceBuilder countSearch = nodeEsMapper.getSearchSourceBuilder(originalWrapper);
//            final String fieldName = fieldName(entry.getValue());
//            countSearch.query(QueryBuilders.boolQuery().must(countSearch.query()).must(QueryBuilders.existsQuery(fieldName)));
//            final Long count = nodeEsMapper.selectCount(EsWrappers.lambdaQuery(NodeEs.class).setSearchSourceBuilder(countSearch));
//            log.info("Aggregation group count: " + count);
        }
        final LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper.setSearchSourceBuilder(searchSourceBuilder);
        final SearchResponse searchResponse = nodeEsMapper.search(wrapper);
        // 聚合结果
        final Aggregations aggregations = searchResponse.getAggregations();

        TreeItemDTO accessData = null;
        final List<TreeItemDTO> treeItems = new ArrayList<>();
        final Map<String, Aggregation> aggregationMap = aggregations.asMap();
        int level1Id = 0;


        for (Map.Entry<String, TreeItemField> entry : allAggFieldData.entrySet()) {
            final String key = entry.getKey();
            final TreeItemField treeItemField = entry.getValue();
            final boolean upperFirst = treeItemField.isUpperFirst();
            final TreeItemDTO treeItemDTO = new TreeItemDTO(key + (level1Id++), key, upperFirst);
            Aggregation aggregation = aggregationMap.get(key);
            if (aggregation != null) {
                List<TreeItemDTO> children = getTreeItemDTOS((ParsedTerms) aggregation, key, level1Id, upperFirst);
                if (FIELD_SAMPLE_TYPE.equals(key)) {
                    children = expTypeGroupService.handleSampleType(children);
                }
                treeItemDTO.setData(CollUtil.isNotEmpty(children) ? children : null);
            }
            if (FIELD_ACCESS.equals(key)) {
                // Access数据
                accessData = addAccess(treeItemDTO);
            } else {
                // 左侧树数据
                /*if (FIELD_EXPERIMENT_TYPE.equals(key)) {
                    treeItemDTO.getData().removeIf(child -> "Yes".equals(child.getName()) || "No".equals(child.getName()));
                }*/
                if (FIELD_LEVEL.equals(key)) {
                    // Level数据排序
                    final List<String> levelList = NodeEsTypeEnum.findNameList();
                    treeItemDTO.setData(CollUtil.sort(treeItemDTO.getData(), Comparator.comparingInt(o -> levelList.indexOf(o.getFieldName()))));
                }
                treeItems.add(treeItemDTO);
                if (treeItems.size() == 1) {
                    treeItems.get(0).setIsExpand(true);
                }
            }
        }
        result.setAccessData(accessData);
        // 添加多组学信息
        final TreeItemDTO multipleOmics = expTypeGroupService.findMultipleOmics(originalWrapper);
        if (multipleOmics != null) {
            treeItems.add(1, multipleOmics);
        }
        result.setTreeItems(treeItems);
    }

    /**
     * Access 同时包含public和controlled
     *
     * @return
     */
    private TreeItemDTO addAccess(final TreeItemDTO treeItemDTO) {
        final List<TreeItemDTO> data = treeItemDTO.getData();
        final int size = CollUtil.size(data);
        if (size == 2) {
            return treeItemDTO;
        }
        final Map<String, TreeItemDTO> accessMap = new HashMap<>();
        if (size > 0) {
            for (TreeItemDTO child : data) {
                String fieldName = child.getFieldName();
                if (fieldName != null) {
                    accessMap.put(child.getName().toLowerCase(), child);
                }
            }
        }
        PublicOrControlled[] publicOrControlleds = PublicOrControlled.values();
        List<TreeItemDTO> result = new ArrayList<>();
        for (int i = 0; i < publicOrControlleds.length; i++) {
            PublicOrControlled item = publicOrControlleds[i];
            TreeItemDTO treeItem = accessMap.get(item.name().toLowerCase());
            if (treeItem == null) {
                treeItem = new TreeItemDTO("Access_key_" + i, item.name());
                treeItem.setNumber(0);
            }
            result.add(treeItem);
        }
        result = result.stream().sorted((x1, x2) -> x2.getNumber().compareTo(x1.getNumber())).collect(Collectors.toList());
        treeItemDTO.setData(result);
        return treeItemDTO;
    }

    private static List<TreeItemDTO> getTreeItemDTOS(final ParsedTerms terms, final String key, final int level1Id, final boolean upperFirst) {
        final List<TreeItemDTO> children = new ArrayList<>();
        final List<? extends Terms.Bucket> buckets = terms.getBuckets();
        int childId = 0;
//        log.debug(key + " getBuckets: " + buckets.size());
        for (Terms.Bucket bucket : buckets) {
            final TreeItemDTO childItemDTO = new TreeItemDTO(key + level1Id + "_" + (childId++), bucket.getKeyAsString(), upperFirst);
            childItemDTO.setNumber((int) bucket.getDocCount());
            children.add(childItemDTO);
        }
        return children;
    }

    /**
     * 全部聚合分组字段
     */
    private Map<String, TreeItemField> allAggField() {
        final Map<String, TreeItemField> data = new LinkedHashMap<>();
        data.put(FIELD_ACCESS, new TreeItemField(NodeEs::getRelaAccess));
        data.put(FIELD_LEVEL, new TreeItemField(NodeEs::getType, true));
        data.put(FIELD_EXPERIMENT_TYPE, new TreeItemField(NodeEs::getRelaExpType));
        data.put("Library Strategy", new TreeItemField(NodeEs::getRelaLibraryStrategy));
        data.put("Library Layout", new TreeItemField(NodeEs::getRelaLibraryLayout));
        data.put(FIELD_SAMPLE_TYPE, new TreeItemField(NodeEs::getRelaSampleType));
        data.put("Organism", new TreeItemField(NodeEs::getRelaOrganism));
        data.put("Analysis Type", new TreeItemField(NodeEs::getRelaAnalysisType));
        data.put("File Type", new TreeItemField(NodeEs::getRelaFileType));
        data.put(FIELD_PUBLISHED, new TreeItemField(NodeEs::getPublished));
        data.put("Tissue", new TreeItemField(NodeEs::getRelaTissue));
        return data;
    }


    public static TermsAggregationBuilder initAggregation(String key, SFunction<NodeEs, ?> column) {
        // 配置字段分组结果最大个数，若不配置，则默认值为10个
        return AggregationBuilders.terms(key).field(fieldName(column)).size(100);
    }

    public static String fieldName(SFunction<NodeEs, ?> column) {
        return FieldUtils.getFieldName(column);
    }

    /**
     * 包装查询结构
     */
    private List<EsSearchResultItem> initResult(final List<NodeEs> list) {
        final List<EsSearchResultItem> items = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {

            Set<String> prjNoSet = new HashSet<>();
            Set<String> expNoSet = new HashSet<>();
            Set<String> sapNoSet = new HashSet<>();
            Set<String> anaNoSet = new HashSet<>();
            for (NodeEs es : list) {
                final String typeVal = es.getType();
                if (typeVal == null) {
                    continue;
                }
                final String type = typeVal.toLowerCase();
                final NodeEsTypeEnum typeEnum = NodeEsTypeEnum.findByName(type).orElseThrow(() -> new ServiceException("Unknown type: " + type));
                final String id = es.getId();
                switch (typeEnum) {
                    case project:
                        prjNoSet.add(id);
                        break;
                    case experiment:
                        expNoSet.add(id);
                        break;
                    case sample:
                        sapNoSet.add(id);
                        break;
                    case analysis:
                        anaNoSet.add(id);
                        break;
                    default:
                        throw new ServiceException("Unknown browse type");
                }
            }
            final BrowseStatEsDTO statDTO = new BrowseStatEsDTO();
            statDTO.setCurrMemberId(SecurityUtils.getMemberId());
            statDTO.setPrjNoSet(prjNoSet);
            statDTO.setExpNoSet(expNoSet);
            statDTO.setSapNoSet(sapNoSet);
            statDTO.setAnaNoSet(anaNoSet);

            final R<Map<String, BrowseStatResVO>> browseStatR = remoteNodeESSearchService.findBrowseStatNum(statDTO, SecurityConstants.INNER);
            Map<String, BrowseStatResVO> statMap = new HashMap<>();
            if (R.isSuccess(browseStatR)) {
                statMap = browseStatR.getData();
            }

            final Map<String, String> nameAndDescMap = NodeEsTypeEnum.nameIgnoreCaseAndDesc();
            for (final NodeEs es : list) {
                final EsSearchResultItem item = new EsSearchResultItem();
                final String id = es.getId();
                item.setId(id);
                // 解析转义字符，防止xss攻击
                item.setName(StringEscapeUtils.unescapeHtml4(es.getName()));
                final String typeVal = es.getType();
                if (typeVal == null) {
                    continue;
                }
                final String type = typeVal.toLowerCase();
                item.setTypeEnum(type);
                item.setType(nameAndDescMap.get(type));
                final Date modifiedDate = es.getModifiedDate();
                if (modifiedDate != null) {
                    item.setLastModified(modifiedDate);
                } else {
                    item.setLastModified(es.getCreateDate());
                }
                Submitter submitter = null;
                final NodeEsTypeEnum typeEnum = NodeEsTypeEnum.findByName(type).orElseThrow(() -> new ServiceException("Unknown type: " + type));
                List<PublishVO> publishVOs;

                final BrowseStatResVO statResVO = statMap.get(id);
                BrowseStatEsMapper.INSTANCE.esVoToItem(statResVO, item);
                // 统计信息和关联数据
                switch (typeEnum) {
                    case project:
                        final Project project = projectRepository.findTopByProjectNo(id).orElseThrow(() -> new ServiceException("Project not found: " + id));
                        item.setDescription(project.getDescription());
                        item.setUsedIds(project.getUsedIds());
                        submitter = project.getSubmitter();

                        publishVOs = getPublishVos(AuthorizeType.project, id);
                        setPublishVOs(item, publishVOs);

                        if (statResVO != null) {
                            item.setExpTypes(statResVO.getExpTypes());
                            item.setSapTypes(statResVO.getSapTypes());
                        }

                        break;
                    case experiment:
                        final Experiment experiment = experimentRepository.findTopByExpNo(id).orElseThrow(() -> new ServiceException("Experiment not found: " + id));
                        item.setDescription(experiment.getDescription());
                        item.setUsedIds(experiment.getUsedIds());
                        submitter = experiment.getSubmitter();

                        publishVOs = getPublishVos(AuthorizeType.experiment, id);
                        setPublishVOs(item, publishVOs);


                        item.setExpTypes(CollUtil.newLinkedHashSet(es.getExpType()));
                        if (statResVO != null) {
                            item.setSapTypes(statResVO.getSapTypes());
                        }

                        break;
                    case sample:
                        final Sample sample = sampleRepository.findTopBySapNo(id).orElseThrow(() -> new ServiceException("Sample not found: " + id));
                        item.setDescription(sample.getDescription());
                        item.setUsedIds(sample.getUsedIds());
                        item.setOrganism(sample.getOrganism());
                        item.setTissue(sample.getTissue());
                        submitter = sample.getSubmitter();

                        publishVOs = getPublishVos(AuthorizeType.sample, id);
                        setPublishVOs(item, publishVOs);

                        item.setSapTypes(CollUtil.newLinkedHashSet(es.getSampleType()));
                        if (statResVO != null) {
                            item.setExpTypes(statResVO.getExpTypes());
                        }

                        break;
                    case analysis:
                        final Analysis analysis = analysisRepository.findTopByAnalysisNo(id).orElseThrow(() -> new ServiceException("Analysis not found: " + id));
                        item.setDescription(analysis.getDescription());
                        item.setUsedIds(analysis.getUsedIds());
                        submitter = analysis.getSubmitter();

                        publishVOs = getPublishVos(AuthorizeType.analysis, id);
                        setPublishVOs(item, publishVOs);

                        item.setAnaType(analysis.getAnalysisType());

                        break;
                }

                /*if (statInfo != null) {
                    item.setExpTypes(statInfo.getExpTypes());
                    item.setExpNum(statInfo.getExpNos().size());

                    item.setSapTypes(statInfo.getSapTypes());
                    item.setSapNum(statInfo.getSapNos().size());

                    item.setFileNum(statInfo.getDataNos().size());
                }*/

                if (submitter != null) {
                    // 二期不需要显示组织机构
                    item.setSubName(StringUtils.joinNotEmpty(StrUtil.SPACE, StrUtil.trimToNull(submitter.getFirstName()), StrUtil.trimToNull(submitter.getLastName())));
//                    item.setSubmitter(StringUtils.joinNotEmpty(", ", item.getSubName(), item.getOrgName()));
                    item.setSubmitter(item.getSubName());
                    item.setOrgName(submitter.getOrgName());
                }
                items.add(item);
            }
        }

        return items;
    }

    private void setPublishVOs(final EsSearchResultItem item, List<PublishVO> publishVOs) {
        if (CollUtil.isNotEmpty(publishVOs)) {
            // doi去重
            final Map<String, PublishVO> publishInfo = new LinkedHashMap<>();
            for (PublishVO publishVO : publishVOs) {
                final String doi = publishVO.getDoi();
                if (StrUtil.isNotBlank(doi) && !publishInfo.containsKey(doi)) {
                    publishInfo.put(doi, publishVO);
                }
            }
            item.setPublishVOs(new ArrayList<>(publishInfo.values()));
        }
    }

    /**
     * 全文检索条件
     */
    private LambdaEsQueryWrapper<NodeEs> initQueryWrapper(String queryWord) {
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        // 全文检索
        final String escapeWord = ReUtil.escape(queryWord);
        if (StrUtil.isNotBlank(escapeWord)) {
            queryWrapper.and(i -> i.like(NodeEs::getTypeId, escapeWord).or()
                    .like(NodeEs::getRelaTypeIds, escapeWord).or()
                    .like(NodeEs::getRelaNames, escapeWord).or()
                    .like(NodeEs::getDescription, escapeWord).or()
                    .like(NodeEs::getRelaUsedIds, escapeWord).or()
                    .like(NodeEs::getRelaProtocol, escapeWord).or()
                    .like(NodeEs::getRelaExpType, escapeWord).or()
                    .like(NodeEs::getRelaLibraryStrategy, escapeWord).or()
                    .like(NodeEs::getRelaLibraryLayout, escapeWord).or()
                    .like(NodeEs::getRelaLibrarySelection, escapeWord).or()
                    .like(NodeEs::getRelaLibraryName, escapeWord).or()
                    .like(NodeEs::getRelaPlatform, escapeWord).or()
                    .like(NodeEs::getRelaMatePair, escapeWord).or()
                    .like(NodeEs::getRelaFileType, escapeWord).or()
                    .like(NodeEs::getRelaSampleType, escapeWord).or()
                    .like(NodeEs::getRelaOrganism, escapeWord).or()
                    .like(NodeEs::getRelaTissue, escapeWord).or()
                    .like(NodeEs::getRelaSubjectId, escapeWord).or()
                    .like(NodeEs::getRelaBiomaterialProvider, escapeWord).or()
                    .like(NodeEs::getRelaDisease, escapeWord).or()
                    .like(NodeEs::getRelaDisPhenotype, escapeWord).or()
                    .like(NodeEs::getRelaMutationType, escapeWord).or()
                    .like(NodeEs::getRelaSampleLoc, escapeWord).or()
                    .like(NodeEs::getRelaGender, escapeWord).or()
                    .like(NodeEs::getRelaExtractedMolType, escapeWord).or()
                    .like(NodeEs::getRelaDevStage, escapeWord).or()
                    .like(NodeEs::getRelaBiome, escapeWord).or()
                    .like(NodeEs::getRelaEnvBiome, escapeWord).or()
                    .like(NodeEs::getRelaEnvMaterial, escapeWord).or()
                    .like(NodeEs::getRelaEnvFeature, escapeWord).or()
                    .like(NodeEs::getRelaAnalysisType, escapeWord).or()
                    .like(NodeEs::getRelaPipelineProgram, escapeWord).or()
                    .like(NodeEs::getRelaSubName, escapeWord).or()
                    .like(NodeEs::getRelaSubOrg, escapeWord).or()
                    .like(NodeEs::getRelaSubCountry, escapeWord).or()
                    .like(NodeEs::getRelaDoi, escapeWord).or()
                    .like(NodeEs::getRelaPmid, escapeWord).or()
                    .like(NodeEs::getRelaArticleName, escapeWord)
            );
        }
        return queryWrapper;
    }

    /**
     * 高级查询获取字段函数, 默认搜索关联字段
     */
    public static SFunction<NodeEs, ?> initFieldFunc(final String field) {
        return initFieldFunc(field, true);
    }

    /**
     * 高级查询获取字段函数
     *
     * @param field          前端传入的字段名称
     * @param searchRelation 是否关联查询
     * @return
     */
    public static SFunction<NodeEs, ?> initFieldFunc(final String field, final boolean searchRelation) {
        switch (field) {
            case "ID":
                if (searchRelation) {
                    return NodeEs::getRelaTypeIds;
                } else {
                    return NodeEs::getTypeId;
                }
            case "Name":
                if (searchRelation) {
                    return NodeEs::getRelaNames;
                } else {
                    return NodeEs::getName;
                }
            case "Description":
                return NodeEs::getDescription;
            case FIELD_USED_IDS:
                if (searchRelation) {
                    return NodeEs::getRelaUsedIds;
                } else {
                    return NodeEs::getUsedIds;
                }
            case FIELD_MODIFIED_DATE:
                return NodeEs::getModifiedDate;
            case "Protocol":
                if (searchRelation) {
                    return NodeEs::getRelaProtocol;
                } else {
                    return NodeEs::getProtocol;
                }
            case "Experiment Type":
                if (searchRelation) {
                    return NodeEs::getRelaExpType;
                } else {
                    return NodeEs::getExpType;
                }
            case "Library Strategy":
                if (searchRelation) {
                    return NodeEs::getRelaLibraryStrategy;
                } else {
                    return NodeEs::getLibraryStrategy;
                }
            case "Library Layout":
                if (searchRelation) {
                    return NodeEs::getRelaLibraryLayout;
                } else {
                    return NodeEs::getLibraryLayout;
                }
            case "Library Selection":
                if (searchRelation) {
                    return NodeEs::getRelaLibrarySelection;
                } else {
                    return NodeEs::getLibrarySelection;
                }
            case "Platform":
                if (searchRelation) {
                    return NodeEs::getRelaPlatform;
                } else {
                    return NodeEs::getPlatform;
                }
            case "Mate Pair":
                if (searchRelation) {
                    return NodeEs::getRelaMatePair;
                } else {
                    return NodeEs::getMatePair;
                }
            case "File Type":
                if (searchRelation) {
                    return NodeEs::getRelaFileType;
                } else {
                    return NodeEs::getFileType;
                }
            case FIELD_SAMPLE_TYPE:
                if (searchRelation) {
                    return NodeEs::getRelaSampleType;
                } else {
                    return NodeEs::getSampleType;
                }
            case "Organism":
                if (searchRelation) {
                    return NodeEs::getRelaOrganism;
                } else {
                    return NodeEs::getOrganism;
                }
            case "Tissue":
                if (searchRelation) {
                    return NodeEs::getRelaTissue;
                } else {
                    return NodeEs::getTissue;
                }
            case "Subject Id":
                if (searchRelation) {
                    return NodeEs::getRelaSubjectId;
                } else {
                    return NodeEs::getSubjectId;
                }
            case "Biomaterial Provider":
                if (searchRelation) {
                    return NodeEs::getRelaBiomaterialProvider;
                } else {
                    return NodeEs::getBiomaterialProvider;
                }
            case "Disease":
                if (searchRelation) {
                    return NodeEs::getRelaDisease;
                } else {
                    return NodeEs::getDisease;
                }
            case "Dis Phenotype":
                if (searchRelation) {
                    return NodeEs::getRelaDisPhenotype;
                } else {
                    return NodeEs::getDisPhenotype;
                }
            case "Mutation Type":
                if (searchRelation) {
                    return NodeEs::getRelaMutationType;
                } else {
                    return NodeEs::getMutationType;
                }
            case "Sample Loc":
                if (searchRelation) {
                    return NodeEs::getRelaSampleLoc;
                } else {
                    return NodeEs::getSampleLoc;
                }
            case "Gender":
                if (searchRelation) {
                    return NodeEs::getRelaGender;
                } else {
                    return NodeEs::getGender;
                }
            case "Extracted Mol Type":
                if (searchRelation) {
                    return NodeEs::getRelaExtractedMolType;
                } else {
                    return NodeEs::getExtractedMolType;
                }
            case "Dev Stage":
                if (searchRelation) {
                    return NodeEs::getRelaDevStage;
                } else {
                    return NodeEs::getDevStage;
                }
            case "Biome":
                if (searchRelation) {
                    return NodeEs::getRelaBiome;
                } else {
                    return NodeEs::getBiome;
                }
            case "Env Biome":
                if (searchRelation) {
                    return NodeEs::getRelaEnvBiome;
                } else {
                    return NodeEs::getEnvBiome;
                }
            case "Env Material":
                if (searchRelation) {
                    return NodeEs::getRelaEnvMaterial;
                } else {
                    return NodeEs::getEnvMaterial;
                }
            case "Env Feature":
                if (searchRelation) {
                    return NodeEs::getRelaEnvFeature;
                } else {
                    return NodeEs::getEnvFeature;
                }
            case "Analysis Type":
                if (searchRelation) {
                    return NodeEs::getRelaAnalysisType;
                } else {
                    return NodeEs::getAnalysisType;
                }
            case "Pipeline Program":
                if (searchRelation) {
                    return NodeEs::getRelaPipelineProgram;
                } else {
                    return NodeEs::getPipelineProgram;
                }
            case "DOI":
                if (searchRelation) {
                    return NodeEs::getRelaDoi;
                } else {
                    return NodeEs::getDoi;
                }
            case "PMID":
                if (searchRelation) {
                    return NodeEs::getRelaPmid;
                } else {
                    return NodeEs::getPmid;
                }
            case "Article Name":
                if (searchRelation) {
                    return NodeEs::getRelaArticleName;
                } else {
                    return NodeEs::getArticleName;
                }
            case "Organization":
                if (searchRelation) {
                    return NodeEs::getRelaSubOrg;
                } else {
                    return NodeEs::getSubOrg;
                }
            case "Submitter":
                if (searchRelation) {
                    return NodeEs::getRelaSubName;
                } else {
                    return NodeEs::getSubName;
                }
            case "Country":
                if (searchRelation) {
                    return NodeEs::getRelaSubCountry;
                } else {
                    return NodeEs::getSubCountry;
                }
            case "num_seqs":
                return NodeEs::getRelaNumSeqs;
            case "bases":
                return NodeEs::getRelaSumLen;
            case "Q20(%)":
                return NodeEs::getRelaQ20;
            case "Q30(%)":
                return NodeEs::getRelaQ30;
            default:
                throw new ServiceException("Unknown field");
        }
    }

    /**
     * 解析高级查询字符串
     * 例：(((a[f1]) OR b[f2]) NOT c[f3]) AND d[f4]
     *
     * @return
     */
    private LambdaEsQueryWrapper<NodeEs> parseAdvQuery(String advQueryWord) {
        try {
            if (advQueryWord.startsWith("(") && advQueryWord.endsWith(")")) {
                advQueryWord = advQueryWord.substring(1, advQueryWord.length() - 1);
            }

            final String endConditionFlag = "]) ";
            final Integer[] indexArr = findIndexOf(advQueryWord, endConditionFlag);
            final int length = indexArr.length;
            int start = length - 1;
            String preCondition = null;
            final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
            for (int i = 0; i < length; i++) {
                final String condition = advQueryWord.substring(start--, indexArr[i]);
                final String oneCondition = condition + endConditionFlag;
                if (i == 0) {
                    initAdvQuery(oneCondition, queryWrapper);
                } else {
                    final String relationCondition = StrUtil.replaceFirst(oneCondition, preCondition, StrUtil.EMPTY);
                    initAdvQuery(relationCondition, queryWrapper);
                }
                preCondition = oneCondition;
            }
            int startIndex = 0;
            if (preCondition != null) {
                startIndex = preCondition.length();
            }
            initAdvQuery(advQueryWord.substring(startIndex), queryWrapper);
            return queryWrapper;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("高级查询语句解析出错", e);
            throw new ServiceException("Query string format error");
        }
    }

    /**
     * 查询指定字符串所有所在位置索引
     */
    public static Integer[] findIndexOf(String text, String searchStr) {
        if (text == null || searchStr == null) {
            return null;
        }
        final List<Integer> indexList = new ArrayList<>();
        int start = 0;
        searchIndex(text, searchStr, start, indexList);
        return indexList.toArray(new Integer[0]);
    }

    /**
     * 递归搜索指定字符串
     */
    private static void searchIndex(String text, String searchStr, int start, List<Integer> indexList) {
        int i = StrUtil.indexOf(text, searchStr, start, false);
        if (i > CharSequenceUtil.INDEX_NOT_FOUND) {
            indexList.add(i);
            searchIndex(text, searchStr, i + 1, indexList);
        }
    }

    /**
     * 获取高级查询字符串中指定条件
     */
    public static void initAdvQuery(String queryStr, final LambdaEsQueryWrapper<NodeEs> queryWrapper) {
        String s = StrUtil.trimToNull(queryStr);
        if (s.startsWith("(") && s.endsWith(")")) {
            s = s.substring(1, s.length() - 1);
        }
        if (!s.contains("[") || !s.contains("]")) {
            // 防止漏扫接口乱传参数
            return;
        }
        final int fieldStart = s.indexOf("[");

        try {
            final String field = s.substring(fieldStart + 1, s.indexOf("]"));
            final SFunction<NodeEs, ?> fieldFunc = initFieldFunc(field);
            final String val = s.substring(0, fieldStart);

            if (StrUtil.startWithIgnoreCase(val, "AND ")) {
                handleSelectData(field, fieldFunc, val.substring("AND ".length()), "AND", queryWrapper);
            } else if (StrUtil.startWithIgnoreCase(val, "OR ")) {
                handleSelectData(field, fieldFunc, val.substring("OR ".length()), "OR", queryWrapper);
            } else if (StrUtil.startWithIgnoreCase(val, "NOT ")) {
                handleSelectData(field, fieldFunc, val.substring("NOT ".length()), "NOT", queryWrapper);
            } else {
                handleSelectData(field, fieldFunc, val, "AND", queryWrapper);
            }
        } catch (Exception e) {
            log.warn("查询参数异常:{}..., error msg:{}", queryStr.substring(0, Math.min(queryStr.length(), 50)), e.getMessage());
        }
    }

    private static void handleSelectData(final String field, final SFunction<NodeEs, ?> fieldFunc, final String keyword,
                                         final String relationFlag, final LambdaEsQueryWrapper<NodeEs> queryWrapper) {
        final boolean isFastQc = FAST_QC_FIELDS.contains(field);
        if (isFastQc) {
            // 确保只查询Public权限的数据，并且不包含Controlled权限
            queryWrapper.and(i -> i.eq(NodeEs::getRelaAccess, PublicOrControlled.Public.name())
                    .not(j -> j.eq(NodeEs::getRelaAccess, PublicOrControlled.Controlled.name())));
        }
        if (FIELD_MODIFIED_DATE.equals(field) || isFastQc) {
            // 日期处理
            final String[] dateArr = keyword.split(",");
            if (dateArr.length == 2) {
                if (isFastQc) {
                    final Double v1 = parseDouble(dateArr[0]);
                    final Double v2 = parseDouble(dateArr[1]);
                    if (v1 != null && v2 != null) {
                        addRelation(relationFlag, queryWrapper).between(fieldFunc, v1, v2);
                    } else if (v1 != null) {
                        addRelation(relationFlag, queryWrapper).ge(fieldFunc, v1);
                    } else if (v2 != null) {
                        addRelation(relationFlag, queryWrapper).le(fieldFunc, v2);
                    } else {
                        throw new ServiceException("Has invalid number format: " + keyword);
                    }
                } else {
                    if (!isDateField(dateArr[0]) || !isDateField(dateArr[1])) {
                        throw new ServiceException("Date format error:" + keyword);
                    }
                    addRelation(relationFlag, queryWrapper).between(fieldFunc, dateArr[0] + " 00:00:00", dateArr[1] + " 23:59:59");
                }
            } else {
                if (isFastQc) {
                    final Double v1 = parseDouble(dateArr[0]);
                    if (v1 != null) {
                        addRelation(relationFlag, queryWrapper).ge(fieldFunc, v1);
                    } else {
                        throw new ServiceException("Has invalid number format: " + keyword);
                    }
                } else {
                    addRelation(relationFlag, queryWrapper).ge(fieldFunc, dateArr[0] + " 00:00:00");
                }
            }
        } else {
            addRelation(relationFlag, queryWrapper).like(fieldFunc, ReUtil.escape(keyword));
        }
    }

    private static boolean isDateField(final String dateStr) {
        try {
            DateUtils.parseDateStrictly(dateStr, DateUtils.YYYY_MM_DD);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    private static Double parseDouble(String s) {
        if (StrUtil.isBlank(s)) {
            return null;
        }
        try {
            return Double.parseDouble(s);
        } catch (NumberFormatException ignore) {
            return null;
        }
    }

    private static LambdaEsQueryWrapper<NodeEs> addRelation(final String relationFlag, final LambdaEsQueryWrapper<NodeEs> queryWrapper) {
        if ("OR".equals(relationFlag)) {
            queryWrapper.or();
        } else if ("NOT".equals(relationFlag)) {
            queryWrapper.not();
        }
        return queryWrapper;
    }

    /**
     * 高级检索--字段自动补全检索
     */
    public Set<String> searchSelectData(final EsSelectQueryVO queryVO) {
        final String field = queryVO.getField();
        if (StrUtil.isBlank(field)) {
            return new LinkedHashSet<>();
        }
        try {
            final String keyword = StrUtil.trimToNull(queryVO.getKeyword());
            if (keyword == null) {
                return new LinkedHashSet<>();
            }
            // 获取字段函数
            final SFunction<NodeEs, ?> fieldFunc = initFieldFunc(field, false);

            // 查询指定字段值
            final LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
            wrapper.like(fieldFunc, ReUtil.escape(keyword));
            if (!FIELD_USED_IDS.equals(field)) {
                // distinct底层使用的es collapse实现，注意数组字段无法使用collapse
                wrapper.distinct(fieldFunc);
            }
            wrapper.select(fieldFunc);
            final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(wrapper, 1, 50);
            final List<NodeEs> list = pageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                final Set<String> data = new LinkedHashSet<>();
                for (NodeEs es : list) {
                    Object valObj = fieldFunc.apply(es);
                    if (valObj instanceof Collection) {
                        final Collection jsonArray = (Collection) valObj;
                        if (CollUtil.isNotEmpty(jsonArray)) {
                            for (final Object o : jsonArray) {
                                final String valStr = o.toString();
                                if (StrUtil.containsIgnoreCase(valStr, keyword)) {
                                    data.add(valStr);
                                }
                            }
                        }
                    } else {
                        data.add(valObj.toString());
                    }
                }
                return data;
            } else {
                return new LinkedHashSet<>();
            }
        } catch (Exception e) {
            log.info("自动补全输入框查询出错", e);
            return new LinkedHashSet<>();
        }
    }

    public Map<String, ExpIconInfoVO> getExpIconInfo() {
        List<ExpSampleType> expTypeList = expSampleTypeRepository.findAllByTypeOrderBySortAsc("experiment");
        return expTypeList.stream().map(x -> BeanUtil.copyProperties(x, ExpIconInfoVO.class))
                .collect(Collectors.toMap(ExpIconInfoVO::getName, Function.identity(), (existingValue, newValue) -> existingValue));
    }
}
