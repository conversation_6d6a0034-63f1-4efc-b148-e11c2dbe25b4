package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.DataQueryDTO;
import org.biosino.system.repository.DataCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR> Li
 * @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    public Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity());
    }

    @Override
    public List<Data> findDetailByRunNoIn(Collection<String> runNos) {
        Query query = Query.query(Criteria.where("run_no").in(runNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no")
                .include("name")
                .include("run_no")
                .include("data_type")
                .include("file_name")
                .include("security")
                .include("file_size")
                .include("creator")
                .include("submission_date");
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findDetailByAnalNoIn(Collection<String> analNos) {
        Query query = Query.query(Criteria.where("anal_no").in(analNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no")
                .include("name")
                .include("anal_no")
                .include("data_type")
                .include("security")
                .include("file_size")
                .include("update_date")
                .include("creator")
                .include("submission_date");
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findAllByRunNoIn(List<String> runNos) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("run_no").in(runNos));
        return mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Data.class);
    }

    @Override
    public List<Data> findAllByAnalNoIn(Collection<String> analNos) {
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("anal_no").in(analNos));
        return mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Data.class);
    }

    @Override
    public List<Data> findTempByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return new ArrayList<>();
        }
        Query query = Query.query(Criteria.where(tempKey("run_no")).in(runNos)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public void updateToDeleteAllByDatNoIn(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        dataNos.forEach(this::updateToDeleteByDataNo);
    }

    public void updateToDeleteByDataNo(String dataNo) {
        Data data = mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(dataNo)
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
        if (data == null) {
            return;
        }
        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), data.getSecurity())) {
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("dat_no").is(dataNo));
        Update update = new Update();
        update.set("security", data.getSecurity() + "_Delete");
        update.set("update_date", new Date());
        mongoTemplate.updateFirst(query, update, Data.class);
    }

    @Override
    public void updateCreatorByDatNoIn(Collection<String> dataNos, String creator) {
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        Query query = new Query(Criteria.where("dat_no").in(dataNos));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Data.class);
    }

    @Override
    public Page<Data> findDataPage(DataQueryDTO queryDTO) {
        Query query = getDataQuery(queryDTO);

        // 查询数据量
        long total = mongoTemplate.count(query, Data.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Data> content = mongoTemplate.find(query, Data.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getDataQuery(DataQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("archived").is(queryDTO.getArchived()));
        if (CollUtil.isNotEmpty(queryDTO.getSecurity())) {
            criteriaList.add(Criteria.where("security").in(queryDTO.getSecurity()));
        } else {
            criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        }

        if (CollUtil.isNotEmpty(queryDTO.getDataNos())) {
            criteriaList.add(Criteria.where("dat_no").in(queryDTO.getDataNos()));
        }

        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getAnalNos())) {
            criteriaList.add(Criteria.where("anal_no").in(queryDTO.getAnalNos()));
        }

        if (queryDTO.getRunNos() != null) {
            criteriaList.add(Criteria.where("run_no").in(queryDTO.getRunNos()));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        if (queryDTO.isExistAnalysis()) {
            criteriaList.add(Criteria.where("anal_no").exists(true));
        }
        if (queryDTO.isExistRun()) {
            criteriaList.add(Criteria.where("run_no").exists(true));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));
        return query;
    }

    @Override
    public Optional<Data> findFirstByFilePath(String relativepath) {
        Query query = new Query();
        query.addCriteria(Criteria.where("file_path").is(relativepath)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return Optional.ofNullable(mongoTemplate.findOne(query, Data.class));
    }

    @Override
    public List<String> findDataCheckNotPass() {
        Query query = Query.query(Criteria.where("complete").is(false));
        return mongoTemplate.findDistinct(query, "dat_no", Data.class, String.class);
    }

    @Override
    public List<Data> findAllByDataNoIn(List<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("dat_no").in(dataNos));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findAllByDataNoIn(Collection<String> dataNos, String creator) {
        return mongoTemplate.find(Query.query(Criteria.where("dat_no").in(dataNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
    }

    @Override
    public void updateCompleteByDatNoIn(List<String> dataNos, Boolean complete) {
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("dat_no").in(dataNos));
        Query query = new Query(new Criteria().andOperator(condition));
        Update update = new Update().set("complete", complete)
                .set("update_date", new Date());
        mongoTemplate.updateMulti(query, update, Data.class);
    }

    @Override
    public MongoPagingIterator<Data> getPagingIterator(DataQueryDTO queryDTO) {
        Query query = getDataQuery(queryDTO);
        return new MongoPagingIterator<>(mongoTemplate, Data.class, query, 5000);
    }
}
