package org.biosino.node.fastqc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR> li
 */
@ConfigurationProperties(prefix = "node.fastqc")
@Data
public class FastqcProperties {

    private String nodeDataPath = "/data/node/data";

    private String fastqcPath = "/data/node/fastqc";

    private String imagePath = "/data/node/data/analysis/images";

    private String scriptPath = "/data/node/data/analysis/scripts";

    private String fastqcVersion = "v0.12.1";

    private String seqkitVersion = "v2.8.0";

    // 分析脚本执行前需要执行的命令
    private String initCommand = "";

    private String qsubmode = "local";

    private String nodeName = "";

    private String queueName = "";

    private String thread = "4";

    private String memory = "4G";

    // 大于多少就是视为大文件，单位必须后面带B，例如GB、MB、TB ,不能是G、M、T
    private String bigFileSize = "20GB";

    // 大文件时使用的线程数量
    private String bigFileThread = "16";

    // 大文件时使用的内存数量
    private String bigFileMemory = "16G";

}
