import request from '@/utils/request';

const baseURL = '/system/metadata/data';

// 获取用户未归档的data
export function listUnarchived(params) {
  return request({
    url: `${baseURL}/listUnarchived`,
    method: 'get',
    params: params,
  });
}

// 获取用户未归档的data
export function listRawData(data) {
  return request({
    url: `${baseURL}/listRawData`,
    method: 'post',
    data: data,
  });
}

// 获取用户未归档的data
export function listAnalysisData(data) {
  return request({
    url: `${baseURL}/listAnalysisData`,
    method: 'post',
    data: data,
  });
}

export function deleteByDataNos(dataNos) {
  return request({
    url: `${baseURL}/deleteByDataNos/${dataNos}`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
  });
}

export function checkPath(path) {
  return request({
    url: `${baseURL}/allocDataNo4Path/checkPath?path=${path}`,
    method: 'get',
  });
}

export function checkEmail(email) {
  return request({
    url: `${baseURL}/allocDataNo4Path/checkEmail?email=${email}`,
    method: 'get',
  });
}

export function startAlloc(params) {
  return request({
    url: `${baseURL}/allocDataNo4Path/startAlloc`,
    method: 'get',
    timeout: 0,
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

export function getFtpFileRecordCount(params) {
  return request({
    url: `${baseURL}/ftpPreLoadFileSync/getFtpFileRecordCount`,
    method: 'get',
    params: params,
  });
}

export function checkFtpFileLog(params) {
  return request({
    url: `${baseURL}/ftpPreLoadFileSync/checkFtpFileLog`,
    method: 'get',
    params: params,
  });
}

export function startSync(params) {
  return request({
    url: `${baseURL}/ftpPreLoadFileSync/startSync`,
    method: 'get',
    timeout: 0,
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

export function listFtpPreLoad(params) {
  return request({
    url: `${baseURL}/ftpPreLoad/list`,
    method: 'get',
    params: params,
  });
}

export function getMemberFtpHomePath(params) {
  return request({
    url: `${baseURL}/getMemberFtpHomePath`,
    method: 'get',
    params: params,
  });
}

export function listPrivateData(params) {
  return request({
    url: `${baseURL}/listPrivateData`,
    method: 'get',
    params: params,
  });
}

export function checkFtpFile(list) {
  return request({
    url: `${baseURL}/ftpFile/checkFtpFile`,
    method: 'post',
    data: list,
  });
}

export function verifyFtpFile(list) {
  return request({
    url: `${baseURL}/ftpFile/verify`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: list,
  });
}

export function deleteFtpFile(list) {
  return request({
    url: `${baseURL}/ftpPreLoad/delete`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    data: list,
  });
}

export function listFtpHomeFile(params) {
  return request({
    url: `${baseURL}/ftpHomeFile/list`,
    method: 'get',
    params: params,
  });
}

export function scanFtpHomeFile() {
  return request({
    url: `${baseURL}/ftpHomeFile/scanFtpHomeFile`,
    method: 'get',
    headers: {
      repeatSubmit: true,
    },
  });
}

// Batch Modify Integrity
export function batchModifyIntegrity(data) {
  return request({
    url: `${baseURL}/batchModifyIntegrity`,
    method: 'post',
    timeout: 0,
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}
