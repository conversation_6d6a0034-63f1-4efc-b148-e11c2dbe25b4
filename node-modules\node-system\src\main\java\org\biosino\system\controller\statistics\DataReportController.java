package org.biosino.system.controller.statistics;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.statistics.StatisticsNodeMember;
import org.biosino.common.mongo.entity.statistics.StatisticsPublish;
import org.biosino.common.mongo.entity.statistics.StatisticsSubMethod;
import org.biosino.common.mongo.entity.statistics.StatisticsSubmission;
import org.biosino.system.service.statistics.DataReportService;
import org.biosino.system.service.statistics.DataVolumeService;
import org.biosino.system.vo.excel.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 统计模块 - Data Report页面
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics/report")
@RequiredArgsConstructor
public class DataReportController extends BaseController {

    private final DataReportService dataReportService;
    private final DataVolumeService dataVolumeService;

    /**
     * Meta Data
     */
    @PostMapping("/export/metadata")
    @Log(module1 = "统计", module2 = "Meta Data", businessType = BusinessType.EXPORT)
    public void exportMetadata(HttpServletResponse response) {
        List<MetaDataExcel> excelList = dataVolumeService.getDataVolumeMetadata();
        ExcelUtil<MetaDataExcel> util = new ExcelUtil<>(MetaDataExcel.class);
        util.exportExcel(response, excelList, "Meta Data");
    }

    /**
     * Raw Data
     */
    @PostMapping("/export/rawData")
    @Log(module1 = "统计", module2 = "Raw Data", businessType = BusinessType.EXPORT)
    public void exportRawData(HttpServletResponse response) {
        List<DataExcel> excelList = dataVolumeService.getDataStatistics(true);
        ExcelUtil<DataExcel> util = new ExcelUtil<>(DataExcel.class);
        util.exportExcel(response, excelList, "Raw Data");
    }

    /**
     * Analysis Data
     */
    @PostMapping("/export/analysisData")
    @Log(module1 = "统计", module2 = "Analysis Data", businessType = BusinessType.EXPORT)
    public void exportAnalysisData(HttpServletResponse response) {
        List<DataExcel> excelList = dataVolumeService.getDataStatistics(false);
        ExcelUtil<DataExcel> util = new ExcelUtil<>(DataExcel.class);
        util.exportExcel(response, excelList, "Analysis Data");
    }

    /**
     * Data Flow
     */
    @PostMapping("/export/dataFlow")
    @Log(module1 = "统计", module2 = "Data Flow", businessType = BusinessType.EXPORT)
    public void exportDataFlow(HttpServletResponse response) {
        List<DataFlowExcel> dataFlow = dataVolumeService.getDataFlow();
        ExcelUtil<DataFlowExcel> util = new ExcelUtil<>(DataFlowExcel.class);
        util.exportExcel(response, dataFlow, "Data Flow");
    }

    /**
     * 数据汇交统计
     */
    @PostMapping("/export/submission")
    @Log(module1 = "统计", module2 = "数据汇交", businessType = BusinessType.EXPORT)
    public void exportSubmission(HttpServletResponse response) {
        List<StatisticsSubmission> excelList = dataReportService.getSubmissionData();
        ExcelUtil<StatisticsSubmission> util = new ExcelUtil<>(StatisticsSubmission.class);
        util.exportExcel(response, excelList, "Submission");
    }

    /**
     * 文献
     */
    @PostMapping("/export/publication")
    @Log(module1 = "统计", module2 = "文献", businessType = BusinessType.EXPORT)
    public void exportPublication(HttpServletResponse response) {
        List<StatisticsPublish> excelList = dataReportService.getPublication();
        ExcelUtil<StatisticsPublish> util = new ExcelUtil<>(StatisticsPublish.class);
        util.exportExcel(response, excelList, "Publication");
    }

    /**
     * 热门数据导出
     */
    @PostMapping("/export/populardata/{statType}")
    @Log(module1 = "统计", module2 = "热门数据", businessType = BusinessType.EXPORT)
    public void exportPopularData(@PathVariable("statType") String statType, HttpServletRequest request, HttpServletResponse response) {
        dataReportService.exportPopularData(statType, request, response);
    }

    /**
     * 临时数据统计
     */
    @PostMapping("/export/exportTempData")
    @Log(module1 = "统计", module2 = "临时数据", businessType = BusinessType.EXPORT)
    public void exportTempData(HttpServletResponse response) {
        List<TempDataExcel> excelList = dataReportService.getTempData();
        ExcelUtil<TempDataExcel> util = new ExcelUtil<>(TempDataExcel.class);
        util.exportExcel(response, excelList, "Statistics temp data");
    }

    /**
     * 临时数据统计
     */
    @PostMapping("/export/exportTempFtpData")
    @Log(module1 = "统计", module2 = "临时ftp数据", businessType = BusinessType.EXPORT)
    public void exportTempFtpData(HttpServletResponse response) {
        List<TempFtpDataExcel> excelList = dataReportService.getTempFtpData();
        ExcelUtil<TempFtpDataExcel> util = new ExcelUtil<>(TempFtpDataExcel.class);
        util.exportExcel(response, excelList, "Statistics temp ftp data");
    }

    /**
     * 下载
     */
    @PostMapping("/export/download")
    @Log(module1 = "统计", module2 = "下载", businessType = BusinessType.EXPORT)
    public void exportDownload(HttpServletResponse response) {
        List<DownloadExcel> excelList = dataReportService.getDownloadLong();
        ExcelUtil<DownloadExcel> util = new ExcelUtil<>(DownloadExcel.class);
        util.exportExcel(response, excelList, "Download");
    }

    /**
     * 数据提交方式
     */
    @PostMapping("/export/subMethod")
    @Log(module1 = "统计", module2 = "数据提交方式", businessType = BusinessType.EXPORT)
    public void exportSubMethod(HttpServletResponse response) {
        List<StatisticsSubMethod> excelList = dataReportService.subMethod();
        ExcelUtil<StatisticsSubMethod> util = new ExcelUtil<>(StatisticsSubMethod.class);
        util.exportExcel(response, excelList, "Submission Method");
    }

    /**
     * 标签数据统计导出
     */
    @PostMapping("/export/tags")
    @Log(module1 = "统计", module2 = "标签数据统计", businessType = BusinessType.EXPORT)
    public void exportTags(HttpServletResponse response) {
        List<TagExcel> excelList = dataReportService.getTagExcelList();
        ExcelUtil<TagExcel> util = new ExcelUtil<>(TagExcel.class);
        util.exportExcel(response, excelList, "Tags");
    }

    /**
     * 用户数统计导出
     */
    @PostMapping("/export/nodeMember")
    @Log(module1 = "统计", module2 = "用户数统计", businessType = BusinessType.EXPORT)
    public void exportUsers(HttpServletResponse response) {
        List<StatisticsNodeMember> excelList = dataReportService.getNodeMemberList();
        ExcelUtil<StatisticsNodeMember> util = new ExcelUtil<>(StatisticsNodeMember.class);
        util.exportExcel(response, excelList, "member");
    }

    /**
     * 模板字段填写统计
     */
    @PostMapping("/export/expSapAttr/{type}")
    @Log(module1 = "统计", module2 = "模板字段填写统计", businessType = BusinessType.EXPORT)
    public void exportExpSapAttr(HttpServletRequest request, HttpServletResponse response, @PathVariable("type") String type) throws IOException {
        File resultFile = dataReportService.getExpSapTypeAttrReportFile(type);
        DownloadUtils.download(request, response, resultFile);
    }

}
