<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="ID" prop="nos">
          <el-input
            v-model="queryParams.noStr"
            placeholder="Search for ID"
            style="width: 240px"
            type="textarea"
            :rows="1"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="Search for Name"
            style="width: 300px"
            clearable
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Target ID" prop="targetNos">
          <el-input
            v-model="queryParams.targetNoStr"
            placeholder="Search for Target ID"
            style="width: 240px"
            type="textarea"
            :rows="1"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Creator" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            clearable
            style="width: 250px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="Submitter Email" prop="submitterEmail">
          <el-input
            v-model="queryParams.submitterEmail"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="Submitter Organization" prop="submitterOrgName">
          <el-input
            v-model="queryParams.submitterOrgName"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item prop="sourceProject" label="Tag">
          <el-select
            v-model="queryParams.tags"
            clearable
            style="width: 220px"
            :teleported="false"
            multiple
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Submission Date" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
          <el-button
            v-hasPermi="['metadata:analysis:export']"
            type="info"
            icon="download"
            @click="exportData"
            >Export
          </el-button>
        </el-form-item>
      </el-form>
      <div class="select-exptype bg-gray p-10-15 radius-8 mb-1">
        <el-form-item label="Analysis Type:" style="margin-bottom: 0">
          <el-checkbox-group
            v-model="queryParams.analTypes"
            @change="getDataList"
          >
            <el-checkbox
              v-for="item in analTypeOpts"
              :key="item"
              :value="item"
              :label="item"
              >{{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="analysisNo" label="ID" sortable min-width="115">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row)"
            >
              {{ scope.row.analysisNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="Name"
          min-width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="analysisType"
          label="Analysis Type"
          min-width="130"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{
              scope.row.analysisType === 'Other' && scope.row.customAnalysisType
                ? `Other(${scope.row.customAnalysisType})`
                : scope.row.analysisType
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="dataNum"
          label="Data Number"
          width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag type="success">{{ scope.row.dataNum }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="Description"
          min-width="160"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="submitter"
          label="Submitter"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="creatorEmail"
          label="Creator"
          width="165"
          show-overflow-tooltip
        />
        <el-table-column
          prop="visibleStatus"
          label="Status"
          min-width="130"
          sortable
        >
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.visibleStatus === 'Accessible'"
                color="#67C23A"
              >
                <View />
              </el-icon>
              <el-icon v-else color="#F56C6C">
                <Hide />
              </el-icon>
              <div
                class="ml-05"
                :style="{
                  color:
                    scope.row.visibleStatus === 'Accessible'
                      ? '#67C23A'
                      : '#F56C6C',
                }"
              >
                {{ scope.row.visibleStatus }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Security" width="75">
          <template #default="scope">
            <div class="lock" :style="computedStyle(scope.row.dataCount)">
              <el-tooltip placement="right">
                <template #content>
                  <div>
                    <span>Public : </span>
                    <span>{{ scope.row.dataCount.Public }}</span>
                  </div>
                  <div>
                    <span>Private: </span>
                    <span>{{ scope.row.dataCount.Private }}</span>
                  </div>
                  <div>
                    <span>Restricted: </span>
                    <span>{{ scope.row.dataCount.Restricted }}</span>
                  </div>
                </template>
                <el-icon class="cursor-pointer">
                  <Lock v-if="scope.row.visibleStatus === 'Accessible'" />
                  <Unlock v-else />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="Submission Date"
          width="160"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column label="Operate" fixed="right" width="210">
          <template #default="scope">
            <el-tooltip content="Change Creator">
              <svg-icon
                icon-class="creator"
                class-name="meta-svg"
                @click="preChangeCheck(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Edit">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="toEdit(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Change Security">
              <svg-icon
                icon-class="security"
                class-name="meta-svg"
                @click="openSecurityDialog(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Delete">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="preDeleteCheck(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Export Data">
              <svg-icon
                icon-class="export-data-links"
                class-name="export-svg"
                @click="exportDataByNo(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="刷新索引">
              <svg-icon
                icon-class="index"
                class-name="meta-svg"
                @click="refreshEsIndex(scope.row.analysisNo)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
    <Security ref="securityRef"></Security>

    <delete-log ref="deleteLog" curr-type="Analysis"></delete-log>
    <!-- change creator -->
    <change-creator-confirm
      v-model:new-creator="newCreator"
      v-model:show-dialog="showChangeDialog"
      :delete-check-result="deleteCheckResult"
      @change-creator-method="confirmChange"
    >
    </change-creator-confirm>
    <delete-confirm
      v-model:show-dialog="showDeleteDialog"
      :delete-check-result="deleteCheckResult"
      @delete-method="confirmDelete"
    ></delete-confirm>
  </div>
</template>
<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import { createAccessToken } from '@/api/login';
  import {
    analysisDeleteCheck,
    deleteAnalysisAll,
    getAuditedAnalType,
    listAnalysis,
    refreshIndex,
    updateAnalysisCreator,
  } from '@/api/metadata/analysis';
  import Security from '@/components/Security/index.vue';
  import ChangeCreatorConfirm from '@/views/metadata/common/ChangeCreatorConfirm.vue';
  import DeleteConfirm from '@/views/metadata/common/DeleteConfirm.vue';
  import DeleteLog from '@/views/metadata/common/DeleteLog.vue';

  const router = useRouter();

  const { proxy } = getCurrentInstance();
  const { tag } = proxy.useDict('tag');

  let analTypeOpts = ref([]);

  onMounted(() => {
    getAuditedAnalType().then(response => {
      analTypeOpts.value = response.data;
    });
    getDataList();
  });

  const openSecurityDialog = analNo => {
    proxy.$refs['securityRef'].init('analysis', analNo);
  };

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      noStr: '',
      nos: [],
      targetNoStr: '',
      targetNos: [],
      creatorEmail: '',
      submitterEmail: '',
      submitterOrgName: '',
      tags: [],
      pageNum: 1,
      pageSize: 20,
      creator: '',
      analTypes: [],
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.noStr,
    newVal => {
      data.queryParams.nos = newVal ? newVal.split('\n') : [];
    },
  );

  watch(
    () => data.queryParams.targetNoStr,
    newVal => {
      data.queryParams.targetNos = newVal ? newVal.split('\n') : [];
    },
  );

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listAnalysis(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.noStr = '';
    proxy.resetForm('queryRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  const toEdit = no => {
    router.push({
      path: `/metadata/edit/analysis/${no}`,
    });
  };

  let showDeleteDialog = ref(false);
  let analNo = ref('');
  let newCreator = ref('');
  let deleteCheckResult = ref({});

  function preDeleteCheck(no) {
    proxy.$modal.loading('Opening Delete Dialog, please wait');
    analNo.value = no;
    analysisDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showDeleteDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function exportDataByNo(analNo) {
    let param = {
      type: 'analysis',
      typeNo: analNo,
    };
    proxy.download(
      '/system/metadata/analysis/downloadData',
      param,
      `${analNo}_Data_${new Date().getTime()}.xlsx`,
    );
  }

  let showChangeDialog = ref(false);

  function preChangeCheck(no) {
    proxy.$modal.loading('Opening Change Creator Dialog, please wait');
    analNo.value = no;
    analysisDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showChangeDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading('deleting, please wait');
    deleteAnalysisAll({
      analNo: analNo.value,
    })
      .then(() => {
        showDeleteDialog.value = false;
        proxy.$modal.alertSuccess('Delete successful');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmChange() {
    proxy.$modal.loading('changing, please wait');
    updateAnalysisCreator({
      analNo: analNo.value,
      newCreator: newCreator.value,
    })
      .then(() => {
        showChangeDialog.value = false;
        newCreator.value = '';
        proxy.$modal.alertSuccess('Change Creator successful');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function refreshEsIndex(analNo) {
    proxy.$modal
      .confirm('确认刷新该Analysis下的Elasticsearch索引?')
      .then(function () {
        refreshIndex(analNo);
      })
      .then(() => {
        proxy.$modal.msgSuccess('执行成功，请等待几分钟后查看数据');
      })
      .catch(() => {});
  }

  function showDetail(row) {
    proxy.$modal.loading('opening, please wait');
    // 预先生成access_token
    createAccessToken({ memberId: row.creator })
      .then(response => {
        const token = response.data;
        let href = `${import.meta.env.VITE_APP_WEB_URL}/analysis/detail/${
          row.analysisNo
        }?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const computedStyle = security => {
    return {
      '--my-bg-public': security.Public ? '#07bcb4' : '#DDDDDD',
      '--my-bg-private': security.Private ? '#3a78e8' : '#CCCCCC',
      '--my-bg-restricted': security.Restricted ? '#fe7f2b' : '#bebebe',
    };
  };

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/analysis/exportData',
      {
        query: JSON.stringify(queryParams.value),
      },
      `Analysis_${new Date().getTime()}.json`,
    );
  }
</script>

<style scoped lang="scss">
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .export-svg {
    top: 2px;
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .lock {
    --bg-public: var(--my-bg-public, #07bcb4);
    --bg-private: var(--my-bg-private, #3a78e8);
    --bg-restricted: var(--my-bg-restricted, #fe7f2b);

    display: inline-flex;
    flex-grow: 1;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: 5px;
    padding: 0.7em 0.7em;
    border-radius: 50%;
    background: radial-gradient(white calc(52% - 1px), transparent 30%),
      conic-gradient(
        from 18deg,
        var(--bg-public) 33.3%,
        var(--bg-private) 0% 66.6%,
        var(--bg-restricted) 0%
      );
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
