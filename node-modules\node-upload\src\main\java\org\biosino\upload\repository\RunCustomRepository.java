package org.biosino.upload.repository;

import org.biosino.common.mongo.entity.Run;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface RunCustomRepository {
    Run findByNo(String runNo);

    Run findTempByNo(String runNo);

    List<Run> findAllByCreator(Set<String> collection, String creator);

    List<Run> findAllInitByNames(Set<String> collection, String creator);

    List<Run> findAllByCreator(String creator);

    List<Run> findAllNameByFieldAndNoIn(String field, Collection<String> nos);

    boolean validateRunName(String creator, String name);

    Page<Run> findAllByPage(ArchivedSelectQueryDTO queryDTO);

    List<Run> findAllNameByFieldAndNo(String field, String no);

    void deletedUselessRun(String creator);

    List<Run> findTempByExpNoIn(Collection<String> expNos);

    List<Run> findDetailBySapNoIn(Collection<String> sapNos);

    void updateToDeleteAllByRunNoIn(Collection<String> runNos);

    List<Run> findAllBySapNoIn(Collection<String> sapNos);

    List<Run> findTempBySapNoIn(Collection<String> sapNos);

    Optional<Run> findFirstByRunNo(String runNo);

    List<Run> findAllByExpNoIn(Collection<String> expNos);

    List<Run> findAllByRunNoIn(Collection<String> runNos);

    void deleteByRunNoInAndAuditedInit(Collection<String> runNos);
}
