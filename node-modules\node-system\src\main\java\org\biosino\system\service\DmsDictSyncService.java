package org.biosino.system.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.DmsDictSyncStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Biome;
import org.biosino.common.mongo.entity.DmsDictSync;
import org.biosino.common.mongo.entity.HumanDiseaseOntology;
import org.biosino.common.mongo.entity.TaxonomyNode;
import org.biosino.common.redis.service.RedisService;
import org.biosino.system.remote.DmsApiClient;
import org.biosino.system.repository.BiomeRepository;
import org.biosino.system.repository.DmsDictSyncRepository;
import org.biosino.system.repository.HumanDiseaseOntologyRepository;
import org.biosino.system.repository.TaxonomyNodeRepository;
import org.biosino.system.service.es.CreateBiomeService;
import org.biosino.system.service.es.CreateDiseaseService;
import org.biosino.system.service.es.CreateTaxonomyService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * <AUTHOR> Li
 * @date 2024/10/10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DmsDictSyncService {
    private final static int PAGE_SIZE = 10000;

    private final static String TAXONOMY = "Taxonomy";
    private final static String HUMAN_DISEASE_ONTOLOGY = "HumanDiseaseOntology";
    private final static String HOST_BIOME = "host_biome";
    private final static String NON_HOST_BIOME = "non_host_biome";
    private final static String ENV_BIOME = "env_biome";
    private final static String ENV_BIOME_WATER = "env_biome_water";

    private final static String TASK_SUFFIX = "_dms_dict_sync_task";

    private final static Long EXPIRE_TIME = 2L;

    private final TaxonomyNodeRepository taxonomyNodeRepository;

    private final BiomeRepository biomeRepository;

    private final HumanDiseaseOntologyRepository humanDiseaseOntologyRepository;

    private final DmsDictSyncRepository dmsDictSyncRepository;

    private final DmsApiClient dmsApiClient;

    private final RedisService redisService;

    private final CreateDiseaseService createDiseaseService;

    private final CreateTaxonomyService createTaxonomyService;

    private final CreateBiomeService createBiomeService;

    public List<DmsDictSync> list() {
        return dmsDictSyncRepository.findAll();
    }

    public void syncDictToDb(String dictName) {
        switch (dictName) {
            case TAXONOMY:
                syncToDB(TAXONOMY, this::handleTaxonomyToDb);
                break;
            case HUMAN_DISEASE_ONTOLOGY:
                syncToDB(HUMAN_DISEASE_ONTOLOGY, this::handleDiseaseToDb);
                break;
            case HOST_BIOME:
                syncToDB(HOST_BIOME, this::handleHostBiomeToDb);
                break;
            case ENV_BIOME:
                syncToDB(ENV_BIOME, this::handleEnvBiomeToDb);
                break;
            default:
                break;
        }

    }


    public void syncDictToEs(String dictName) {
        switch (dictName) {
            case TAXONOMY:
                syncToIndex(TAXONOMY, createTaxonomyService::createTaxonomy);
                break;
            case HUMAN_DISEASE_ONTOLOGY:
                syncToIndex(HUMAN_DISEASE_ONTOLOGY, createDiseaseService::createDisease);
                break;
            case HOST_BIOME:
                syncToIndex(HOST_BIOME, createBiomeService::createBiome);
                break;
            case ENV_BIOME:
                syncToIndex(ENV_BIOME, createBiomeService::createBiome);
                break;
            default:
                break;
        }
    }

    public void syncToDB(String dictName, Consumer<String> customTask) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(dictName)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        if (redisService.hasKey(dictName + TASK_SUFFIX)) {
            throw new ServiceException("Sync task is running, Please be patient !");
        }

        redisService.setCacheObject(dictName + TASK_SUFFIX, true, EXPIRE_TIME, TimeUnit.HOURS);

        ThreadUtil.execAsync(() -> {
            try {
                // 处理
                customTask.accept(dictName);
            } catch (Exception e) {
                dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_mongo_failed.name());
                dmsDictSyncRepository.save(dmsDictSync);
                e.printStackTrace();
            } finally {
                redisService.deleteObject(dictName + TASK_SUFFIX);
                log.info("同步{}数据到数据库完成", dictName);
            }
        });

    }

    private void handleTaxonomyToDb(String dictName) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(dictName)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        String abbreviation = dmsDictSync.getAbbreviation();
        int currentPage = 1;

        long lastRecordCount = taxonomyNodeRepository.count();
        dmsDictSync.setLastRecordCount(lastRecordCount);
        dmsDictSync.setStatus(DmsDictSyncStatusEnum.syncing_to_mongo.name());
        dmsDictSyncRepository.save(dmsDictSync);

        // 清空mongo数据库
        taxonomyNodeRepository.deleteAll();

        while (true) {
            try {
                String result = dmsApiClient.getEntitiesByAbbreviation(abbreviation, currentPage++, PAGE_SIZE, DmsApiClient.EXTRA_FIELDS_LINEAGE);
                JSONObject jsonObject = JSON.parseObject(result);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    List<TaxonomyNode> saveList = new ArrayList<>();
                    final JSONArray data = jsonObject.getJSONArray("data");
                    if (CollUtil.isNotEmpty(data)) {
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject line = data.getJSONObject(i);
                            final String id = StrUtil.trimToNull(line.getString("id"));
                            final String name = StrUtil.trimToNull(line.getString("name"));
                            if (name == null || id == null) {
                                continue;
                            }
                            TaxonomyNode item = new TaxonomyNode();
                            item.setTaxId(id);
                            item.setScientificName(name);

                            final JSONArray alias = line.getJSONArray("alias");
                            if (CollUtil.isNotEmpty(alias)) {
                                final List<String> aliasList = new ArrayList<>();
                                for (Object obj : alias) {
                                    if (obj != null) {
                                        final String aliaStr = StrUtil.trimToNull(obj.toString());
                                        if (aliaStr != null) {
                                            aliasList.add(aliaStr);
                                        }
                                    }
                                }
                                item.setAlias(CollUtil.isEmpty(aliasList) ? null : aliasList);
                            }

                            final JSONObject extraFields = line.getJSONObject("extraFields");
                            if (extraFields != null) {
                                item.setLineage(StrUtil.trimToNull(extraFields.getString("lineage")));
                            }

                            saveList.add(item);
                        }
                        if (CollUtil.isNotEmpty(saveList)) {
                            taxonomyNodeRepository.saveAll(saveList);
                            saveList.clear();
                        }
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_mongo_success.name());
        dmsDictSync.setUpdateTime(new Date());
        dmsDictSync.setRecordCount(taxonomyNodeRepository.count());
        dmsDictSyncRepository.save(dmsDictSync);
    }

    private void handleDiseaseToDb(String dictName) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(dictName)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        String abbreviation = dmsDictSync.getAbbreviation();
        int currentPage = 1;
        int pageSize = 10000;

        long lastRecordCount = humanDiseaseOntologyRepository.count();
        dmsDictSync.setLastRecordCount(lastRecordCount);
        dmsDictSync.setStatus(DmsDictSyncStatusEnum.syncing_to_mongo.name());
        dmsDictSyncRepository.save(dmsDictSync);

        // 清空mongo数据库
        humanDiseaseOntologyRepository.deleteAll();

        while (true) {
            try {
                String result = dmsApiClient.getEntitiesByAbbreviation(abbreviation, currentPage++, pageSize, StrUtil.EMPTY);
                JSONObject jsonObject = JSON.parseObject(result);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    List<HumanDiseaseOntology> saveList = new ArrayList<>();
                    final JSONArray data = jsonObject.getJSONArray("data");
                    if (CollUtil.isNotEmpty(data)) {
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject line = data.getJSONObject(i);
                            String lbl = line.getString("name");
                            if (StrUtil.isBlank(lbl)) {
                                continue;
                            }
                            HumanDiseaseOntology item = new HumanDiseaseOntology();
                            item.setLbl(lbl);
                            saveList.add(item);
                        }
                        if (CollUtil.isNotEmpty(saveList)) {
                            humanDiseaseOntologyRepository.saveAll(saveList);
                            saveList.clear();
                        }
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_mongo_success.name());
        dmsDictSync.setUpdateTime(new Date());
        dmsDictSync.setRecordCount(humanDiseaseOntologyRepository.count());
        dmsDictSyncRepository.save(dmsDictSync);
    }

    private void handleHostBiomeToDb(String type) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(type)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        List<String> types = CollUtil.newArrayList(HOST_BIOME, NON_HOST_BIOME);

        String abbreviation = dmsDictSync.getAbbreviation();

        int currentPage = 1;
        int pageSize = 10000;

        long lastRecordCount = biomeRepository.countByTypeIn(types);
        dmsDictSync.setLastRecordCount(lastRecordCount);
        dmsDictSync.setStatus(DmsDictSyncStatusEnum.syncing_to_mongo.name());
        dmsDictSyncRepository.save(dmsDictSync);

        // 清空mongo数据库
        biomeRepository.deleteAllByTypeIn(types);

        while (true) {
            try {
                String result = dmsApiClient.getEntitiesByAbbreviation(abbreviation, currentPage++, pageSize, StrUtil.EMPTY);
                JSONObject jsonObject = JSON.parseObject(result);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    List<Biome> saveList = new ArrayList<>();
                    final JSONArray data = jsonObject.getJSONArray("data");
                    if (CollUtil.isNotEmpty(data)) {
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject line = data.getJSONObject(i);
                            String id = line.getString("id");
                            String name = line.getString("name");
                            JSONArray aliasArr = line.getJSONArray("alias");
                            if (CollUtil.isEmpty(aliasArr)) {
                                continue;
                            }
                            String alias = aliasArr.getString(0);
                            if (StrUtil.isBlank(name) || StrUtil.isBlank(alias)) {
                                continue;
                            }
                            Biome item = new Biome();
                            item.setType(alias);
                            item.setValue(name);
                            saveList.add(item);
                        }
                        if (CollUtil.isNotEmpty(saveList)) {
                            biomeRepository.saveAll(saveList);
                            saveList.clear();
                        }
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_mongo_success.name());
        dmsDictSync.setUpdateTime(new Date());
        dmsDictSync.setRecordCount(biomeRepository.countByTypeIn(types));
        dmsDictSyncRepository.save(dmsDictSync);
    }

    private void handleEnvBiomeToDb(String type) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(type)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        String abbreviation = dmsDictSync.getAbbreviation();

        List<String> types = CollUtil.newArrayList(ENV_BIOME, ENV_BIOME_WATER);

        int currentPage = 1;
        int pageSize = 10000;

        long lastRecordCount = biomeRepository.countByTypeIn(types);
        dmsDictSync.setLastRecordCount(lastRecordCount);
        dmsDictSync.setStatus(DmsDictSyncStatusEnum.syncing_to_mongo.name());
        dmsDictSyncRepository.save(dmsDictSync);

        // 清空mongo数据库
        biomeRepository.deleteAllByTypeIn(types);

        while (true) {
            try {
                String result = dmsApiClient.getEntitiesByAbbreviation(abbreviation, currentPage++, pageSize, StrUtil.EMPTY);
                JSONObject jsonObject = JSON.parseObject(result);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    List<Biome> saveList = new ArrayList<>();
                    final JSONArray data = jsonObject.getJSONArray("data");
                    if (CollUtil.isNotEmpty(data)) {
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject line = data.getJSONObject(i);
                            String id = line.getString("id");
                            String name = line.getString("name");
                            if (StrUtil.isBlank(id) || StrUtil.isBlank(name)) {
                                continue;
                            }
                            String value = name + " (" + id + ")";
                            if (StrUtil.startWithIgnoreCase(id, "ENVO_")) {
                                Biome item = new Biome();
                                item.setType(ENV_BIOME_WATER);
                                item.setValue(value);
                                saveList.add(item);
                            }
                            Biome item = new Biome();
                            item.setType(ENV_BIOME);
                            item.setValue(value);
                            saveList.add(item);
                        }
                        if (CollUtil.isNotEmpty(saveList)) {
                            biomeRepository.saveAll(saveList);
                            saveList.clear();
                        }
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_mongo_success.name());
        dmsDictSync.setUpdateTime(new Date());
        dmsDictSync.setRecordCount(biomeRepository.countByTypeIn(types));
        dmsDictSyncRepository.save(dmsDictSync);
    }


    public void syncToIndex(String type, Consumer<String> customTask) {
        DmsDictSync dmsDictSync = dmsDictSyncRepository.findFirstByDictName(type)
                .orElseThrow(() -> new ServiceException("Dict not found"));

        if (redisService.hasKey(type + TASK_SUFFIX)) {
            throw new ServiceException("Sync task is running, Please be patient !");
        }
        redisService.setCacheObject(type + TASK_SUFFIX, 1, EXPIRE_TIME, TimeUnit.HOURS);


        ThreadUtil.execAsync(() -> {
            try {
                // 先把状态改成正在同步
                dmsDictSync.setStatus(DmsDictSyncStatusEnum.syncing_to_es.name());
                dmsDictSyncRepository.save(dmsDictSync);

                // todo 将es的数据删除，将新数据同步到es索引
                customTask.accept(type);

                dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_es_success.name());
                dmsDictSyncRepository.save(dmsDictSync);
            } catch (Exception e) {
                dmsDictSync.setStatus(DmsDictSyncStatusEnum.synced_to_es_failed.name());
                dmsDictSyncRepository.save(dmsDictSync);
                e.printStackTrace();
            } finally {
                redisService.deleteObject(type + TASK_SUFFIX);
                log.info("同步{}步数据到ES完成", type);
            }
        });
    }

}
