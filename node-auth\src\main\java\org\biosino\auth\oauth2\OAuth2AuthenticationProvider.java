package org.biosino.auth.oauth2;

import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.oauth2.model.OAuthUser;
import org.biosino.auth.service.OAuth2UserDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 * OAuth2认证Provider
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OAuth2AuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private OAuth2UserDetailsService OAuth2UserDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        OAuth2AuthenticationToken token = (OAuth2AuthenticationToken) authentication;

        try {
            // 获取OAuth2用户信息
            OAuthUser OAuthUser = token.getOAuthUser();
            if (OAuthUser == null) {
                throw new AuthenticationException("OAuth2用户信息为空") {
                };
            }

            // 根据OAuth2用户信息加载本地用户详情
            UserDetails userDetails = OAuth2UserDetailsService.loadUserByOAuthUser(OAuthUser);

            // 创建已认证的Token
            return new OAuth2AuthenticationToken(
                    userDetails,
                    OAuthUser,
                    token.getAccessToken(),
                    userDetails.getAuthorities()
            );

        } catch (Exception e) {
            log.error("OAuth2认证失败", e);
            throw new AuthenticationException("OAuth2认证失败: " + e.getMessage()) {
            };
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return OAuth2AuthenticationToken.class.isAssignableFrom(authentication);
    }
}
