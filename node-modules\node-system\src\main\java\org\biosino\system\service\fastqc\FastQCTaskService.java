package org.biosino.system.service.fastqc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.FastQCTaskQueryDTO;
import org.biosino.system.repository.FastQCTaskRepository;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Component
@RequiredArgsConstructor
public class FastQCTaskService {

    private final FastQCTaskRepository fastQCTaskRepository;

    public Page<FastQCTask> list(FastQCTaskQueryDTO queryDTO) {
        Page<FastQCTask> page = fastQCTaskRepository.findPage(queryDTO);
        return page;
    }

    public FastQCTask findByTaskNo(String no) {
        FastQCTask qcTask = fastQCTaskRepository.findFirstByDataNo(no).orElseThrow(() -> new ServiceException("FastQCTask is not found!"));
        return qcTask;
    }

    public void changePriority(List<String> dataNos, Integer priority) {
        if (priority == null || CollUtil.isEmpty(dataNos)) {
            throw new ServiceException("Params cannot be empty!");
        }
        fastQCTaskRepository.updatePriority(dataNos, priority);
    }

    public void retry(List<String> dataNos) {
        fastQCTaskRepository.retryTask(dataNos);
    }

    public File exportFastqcTask(FastQCTaskQueryDTO queryDTO) {
        MongoPagingIterator<FastQCTask> iterator = fastQCTaskRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "FastQCTask.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<FastQCTask> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (FastQCTask fastQCTask : next) {
                    jsonWriter.writeObject(fastQCTask);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
