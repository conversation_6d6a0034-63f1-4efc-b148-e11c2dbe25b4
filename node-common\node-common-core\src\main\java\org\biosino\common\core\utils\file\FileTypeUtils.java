package org.biosino.common.core.utils.file;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Objects;

/**
 * 文件类型工具类
 *
 * <AUTHOR>
 */
public class FileTypeUtils {

    private final static String[] FQ_SUFFIXES = {".fq.gz", ".fq", ".fq.bz2", ".fq.xz", ".fq.rfq"};
    private final static String[] FASTQ_SUFFIXES = {".fastq.gz", ".fastq", ".fastq.bz2", ".fastq.xz", ".fastq.rfq"};
    private final static String[] BAM_SUFFIXES = {".bam", ".bam.gz", ".bam.bz2"};
    private final static String[] FASTA_SUFFIXES = {".fasta.gz", ".fasta", ".fasta.bz2", ".fasta.xz", ".fasta.rfq"};
    private final static String[] VCF_SUFFIXES = {".vcf", ".vcf.gz", ".vcf.bz2", ".vcf.xz", ".vcf.zip", ".vcf.7z"};

    /**
     * 获取文件类型
     * <p>
     * 例如: Node.txt, 返回: txt
     *
     * @param file 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(File file) {
        if (null == file) {
            return StringUtils.EMPTY;
        }
        return getFileType(file.getName());
    }

    /**
     * 获取文件类型
     * <p>
     * 例如: Node.txt, 返回: txt
     *
     * @param fileName 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(String fileName) {
        int separatorIndex = fileName.lastIndexOf(".");
        if (separatorIndex < 0) {
            return "";
        }
        return fileName.substring(separatorIndex + 1).toLowerCase();
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }

    /**
     * 获取文件类型
     *
     * @param photoByte 文件字节码
     * @return 后缀（不含".")
     */
    public static String getFileExtendName(byte[] photoByte) {
        String strFileExtendName = "JPG";
        if ((photoByte[0] == 71) && (photoByte[1] == 73) && (photoByte[2] == 70) && (photoByte[3] == 56)
                && ((photoByte[4] == 55) || (photoByte[4] == 57)) && (photoByte[5] == 97)) {
            strFileExtendName = "GIF";
        } else if ((photoByte[6] == 74) && (photoByte[7] == 70) && (photoByte[8] == 73) && (photoByte[9] == 70)) {
            strFileExtendName = "JPG";
        } else if ((photoByte[0] == 66) && (photoByte[1] == 77)) {
            strFileExtendName = "BMP";
        } else if ((photoByte[1] == 80) && (photoByte[2] == 78) && (photoByte[3] == 71)) {
            strFileExtendName = "PNG";
        }
        return strFileExtendName;
    }


    /**
     * 获取生信文件的常见类型
     */
    public static String getDataTypeByName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "unknown";
        }

        if (StrUtil.endWithAnyIgnoreCase(fileName, FQ_SUFFIXES)) {
            return "fq";
        } else if (StrUtil.endWithAnyIgnoreCase(fileName, FASTQ_SUFFIXES)) {
            return "fastq";
        } else if (StrUtil.endWithAnyIgnoreCase(fileName, BAM_SUFFIXES)) {
            return "bam";
        } else if (StrUtil.endWithAnyIgnoreCase(fileName, FASTA_SUFFIXES)) {
            return "fasta";
        } else if (StrUtil.endWithAnyIgnoreCase(fileName, VCF_SUFFIXES)) {
            return "vcf";
        }

        return getFileType(fileName);
    }
}
