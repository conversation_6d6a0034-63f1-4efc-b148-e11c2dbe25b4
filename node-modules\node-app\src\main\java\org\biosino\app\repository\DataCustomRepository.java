package org.biosino.app.repository;

import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.common.mongo.entity.Data;
import org.biosino.system.api.model.Member;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataCustomRepository {

    Data findByNo(String datNo);

    List<Data> findDetailByRunNos(Collection<String> runNos);

    List<Data> findDetailByAnalNoIn(Collection<String> analNos);

    List<Data> findByDataNos(Collection<String> dataNo);

    /**
     * 查询浏览界面数据编号
     */
    BrowseStatDTO findBrowseDataNoByRunNoIn(Collection<String> runNos);

    List<Data> findByRunNosAndSecurity(List<String> runNos, String security);

    List<Data> findByAnalNos(List<String> analysisNo);

    Page<Data> findDataPage(UserCenterListSearchDTO queryDTO);

    Optional<Data> findByDatNo(String datNo);

    void updateDataHumanRecordNo(String dataNo, String recordOption, String humanRecordNo);

    boolean checkAccessPermission(String dataNo, Member member);
}
