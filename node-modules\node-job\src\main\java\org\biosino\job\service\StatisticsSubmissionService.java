package org.biosino.job.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.statistics.StatisticsSubmission;
import org.biosino.job.mapper.FtpFileLogMapper;
import org.biosino.job.repository.DataRepository;
import org.biosino.job.repository.RunRepository;
import org.biosino.job.repository.StatisticsSubmissionRepository;
import org.biosino.job.repository.SubmissionRepository;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;

import static org.biosino.common.core.constant.ConfigConstants.START_MONTH;

/**
 * 生成统计模块的数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsSubmissionService {

    private final RunRepository runRepository;
    private final DataRepository dataRepository;
    private final SubmissionRepository submissionRepository;
    private final StatisticsSubmissionRepository statisticsSubmissionRepository;

    public static final FastDateFormat FORMATTER = FastDateFormat.getInstance(DateUtils.YYYY_DOT_MM);

    /**
     * 全量刷新数据
     */
    public void calculateAllYears() {
        statisticsSubmissionRepository.deleteAll();

        Calendar calendar = Calendar.getInstance();
        // 获取当前年份
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();

        for (int year = ConfigConstants.START_YEAR; year <= currentYear; year++) {
            for (int month = 0; month < 12; month++) {
                if (year == ConfigConstants.START_YEAR && month < START_MONTH) {
                    continue;
                }
                // 设置当前年份和月份
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 获取月份的开始时间
                Date startOfMonth = calendar.getTime();

                LocalDate localDateFromCalendar = LocalDate.of(
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH) + 1,
                        calendar.get(Calendar.DAY_OF_MONTH)
                );

                if (localDateFromCalendar.isAfter(currentDate)) {
                    continue;
                }

                // 格式化月份开始时间
                String formattedStart = FORMATTER.format(startOfMonth);

                // 提交任务到线程池
                generate(formattedStart);
            }
        }
    }

    public void generate(String month) {
        // 计算每种类型的访问次数
        Date startDate;
        Date endDate;

        try {
            // 解析传入的月份字符串
            startDate = FORMATTER.parse(month);

            // 使用Calendar来找到月份的结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 增加一个月
            calendar.add(Calendar.MONTH, 1);
            // 减去一秒以回到当前月的最后一天
            calendar.add(Calendar.SECOND, -1);

            endDate = calendar.getTime();
        } catch (ParseException e) {
            log.error("日期格式错误，解析识别：{}", month);
            return;
        }

        // 清除旧数据
        statisticsSubmissionRepository.deletedByMonth(month);

        long submissionNum = submissionRepository.countByDate(startDate, endDate);

        if (submissionNum == 0) {
            return;
        }

        StatisticsSubmission statisticsSubmission = new StatisticsSubmission();
        statisticsSubmission.setMonth(month);
        statisticsSubmission.setSubmission(submissionNum);

        List<Submission> submissions = submissionRepository.findAllCompleteByDate(startDate, endDate);

        if (CollUtil.isEmpty(submissions)) {
            statisticsSubmissionRepository.insert(statisticsSubmission);
            return;
        }

        Set<String> project = new HashSet<>();
        Set<String> sample = new HashSet<>();
        Set<String> experiment = new HashSet<>();
        Set<String> analysis = new HashSet<>();
        Set<String> dataNos = new HashSet<>();
        for (Submission submission : submissions) {
            if (submission.getProjNo() != null) {
                project.add(submission.getProjNo());
            }
            if (submission.getSapSingleNo() != null) {
                sample.add(submission.getSapSingleNo());
            }
            if (submission.getSapMultipleData() != null) {
                List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                    sample.addAll(sapMultipleDatum.getNos());
                }
            }
            if (submission.getExpSingleNo() != null) {
                experiment.add(submission.getExpSingleNo());
            }
            if (submission.getExpMultipleNos() != null) {
                experiment.addAll(submission.getExpMultipleNos());
            }
            if (submission.getAnalSingleNo() != null) {
                analysis.add(submission.getAnalSingleNo());
            }
            if (submission.getAnalMultipleNos() != null) {
                analysis.addAll(submission.getAnalMultipleNos());
            }
            // Data
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                dataNos.addAll(submission.getRawDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                dataNos.addAll(submission.getAnalysisDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                dataNos.addAll(submission.getRawDataMultipleNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                dataNos.addAll(submission.getAnalDataMultipleNos());
            }
        }

        Map<String, Long> dataSizeMap = dataRepository.findAllSizeByNo(dataNos);
        statisticsSubmission.setApprovedSubmission((long) submissions.size());
        statisticsSubmission.setProject((long) project.size());
        statisticsSubmission.setSample((long) sample.size());
        statisticsSubmission.setExperiment((long) experiment.size());
        statisticsSubmission.setAnalysis((long) analysis.size());

        statisticsSubmission.setData((long) dataNos.size());
        statisticsSubmission.setSubmissionDataSize(dataSizeMap.values().stream().mapToLong(Long::longValue).sum());

        long runCount = runRepository.countAuditByDate(startDate, endDate);
        statisticsSubmission.setRun(runCount);

        statisticsSubmissionRepository.insert(statisticsSubmission);
    }

}
