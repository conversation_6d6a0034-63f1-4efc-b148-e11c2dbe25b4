package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.factory.RemoteMemberFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * NODE用户接口查询服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteMemberService", value = ServiceNameConstants.NODE_MEMBER_SERVICE, fallbackFactory = RemoteMemberFallbackFactory.class)
public interface RemoteMemberService {
    /**
     * 根据用户ID查询用户详细信息
     *
     * @param memberId         用户ID
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getOneMemberByMemberId/{memberId}/{currentUserEmail}")
    public R<MemberDTO> getOneMemberByMemberId(@PathVariable("memberId") String memberId, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据用户BioID查询用户详细信息
     *
     * @param bioId            用户BioID
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getOneMemberByBioId/{bioId}/{currentUserEmail}")
    public R<MemberDTO> getOneMemberByBioId(@PathVariable("bioId") String bioId, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据用户邮箱查询用户详细信息
     *
     * @param memberEmail      用户邮箱
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getMemberInfoByEmail/{memberEmail}/{currentUserEmail}")
    public R<MemberDTO> getMemberInfoByEmail(@PathVariable("memberEmail") String memberEmail, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据邮箱关键字模糊查询用户列表
     *
     * @param email            邮箱查询关键字
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getMemberListByMemberEmailLike/{email}/{currentUserEmail}")
    public R<List<MemberDTO>> getMemberListByMemberEmailLike(@PathVariable("email") String email, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询所有NODE用户邮箱列表
     *
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getAllMemberEmailList/{currentUserEmail}")
    public R<List<String>> getAllMemberEmailList(@PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据时间范围查询node用户数量
     *
     * @param startDate        开始时间
     * @param endDate          结束时间
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getNodeMemberByCreateDateRange/{currentUserEmail}")
    public R<Long> getNodeMemberByCreateDateRange(@RequestParam(value = "startDate") Date startDate, @RequestParam(value = "endDate") Date endDate, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询所有NODE用户邮箱列表
     *
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getNodeMemberEmailList/{currentUserEmail}")
    public R<List<String>> getNodeMemberEmailList(@PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询所有NODE用户信息
     *
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getNodeMemberInfoList/{currentUserEmail}")
    public R<List<MemberDTO>> getNodeMemberInfoList(@PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/getMemberIdToEmailMapByMemberIds/{currentUserEmail}")
    public R<Map<String, String>> getMemberIdToEmailMapByMemberIds(@RequestBody Collection<String> memberIds, @PathVariable("currentUserEmail") String currentUserEmail, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
