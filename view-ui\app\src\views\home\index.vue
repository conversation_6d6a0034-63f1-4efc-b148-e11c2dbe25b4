<template>
  <div class="banner container-fluid">
    <div class="title mb-2 text-center" style="padding-top: 60px">
      <span class="text-primary font-600 font-30"> NODE:</span>
      <span class="text-warning font-600 font-30">
        National Omics Data Encyclopedia
      </span>
    </div>
    <div class="w-75 advance-search" style="margin: 0 auto">
      <div class="mb-2 mt-1">
        <div class="d-flex align-items-center justify-space-between">
          <div class="browse-search w-75 mr-2">
            <el-input
              v-model="searchInput"
              placeholder="Search for"
              @keydown.enter="doSearch"
            />
            <el-icon
              size="large"
              class="cursor-pointer"
              @click="searchInput = ''"
            >
              <Close />
            </el-icon>
            <el-divider direction="vertical"></el-divider>
            <el-button
              type="warning"
              round
              class="bg-round-warning ml-05"
              @click="doSearch"
            >
              <el-icon size="large">
                <Search />
              </el-icon>
            </el-button>
          </div>
          <el-button
            type="primary"
            round
            class="advanced-btn hidden-xs-only hidden-sm-only"
            @click="toBrowse(true)"
            >Advanced Search
          </el-button>
        </div>
        <div class="mt-1">
          <span class="mr-1">e.g</span>
          <span
            v-for="item in example"
            :key="item"
            class="text-primary font-600 hidden-xs-only mr-2 cursor-pointer"
            @click="searchInput = item"
            >{{ item }}</span
          >
          <span
            v-for="(item, index) in example"
            v-show="index < 2"
            :key="item"
            class="text-primary font-600 hidden-sm-and-up mr-2 cursor-pointer"
            @click="searchInput = item"
            >{{ item }}</span
          >
        </div>
      </div>
    </div>
    <el-row :gutter="10" class="hidden-sm-and-up row-gap-10">
      <el-col
        v-for="(it, index) in cardItems"
        :key="index"
        :span="index === cardItems.length - 1 ? 24 : 12"
        @click="$router.push(it.path)"
      >
        <div class="card-item text-center">
          <svg-icon :icon-class="it.iconClass" class-name="svg-card"></svg-icon>
          <p class="text-primary font-600 font-16 text-center">
            {{ it.dataName }}
          </p>
        </div>
      </el-col>
    </el-row>

    <div class="d-flex hidden-xs-only gap-12" style="margin-top: 4rem">
      <div
        v-for="(it, index) in cardItems"
        :key="index"
        class="card-item text-center"
        @click="$router.push(it.path)"
      >
        <svg-icon :icon-class="it.iconClass" class-name="svg-card"></svg-icon>
        <p class="text-primary font-600 font-16 text-center">
          {{ it.dataName }}
        </p>
      </div>
    </div>
    <div
      v-if="noticeContent"
      class="d-flex align-items-center mt-2 justify-center"
    >
      <svg-icon icon-class="notice" class-name="svg-notice"></svg-icon>
      <span class="text-other-color" v-html="noticeContent"></span>
    </div>
  </div>
  <div class="container-fluid mt-2">
    <h3 class="text-main-color font-20 text-center">Contact Us</h3>
    <el-row :gutter="15" class="contact-us">
      <el-col :span="13" :xs="24" :sm="24" :md="13">
        <div
          class="d-flex align-items-center justify-space-between h-100 bg-white"
        >
          <div class="d-flex align-items-center">
            <svg-icon icon-class="email" class-name="svg-email" />
            <el-divider
              direction="vertical"
              class="hidden-xs-only"
            ></el-divider>
            <div>
              <div>
                <svg-icon icon-class="phone" class-name="svg-phone" />
                <span class="text-secondary-color font-600 ml-05"
                  >+86-21-54920550</span
                >
              </div>
              <div class="mt-1">
                <svg-icon icon-class="mailbox" class-name="svg-phone" />
                <span class="text-secondary-color ml-05">
                  <a href="mailto:<EMAIL>" class="font-600"
                    ><EMAIL></a
                  >
                </span>
              </div>
              <div class="mt-1">
                <svg-icon icon-class="chat" class-name="svg-chat" />
                <span class="text-secondary-color font-600">
                  Feel free to contact
                  <a href="mailto:<EMAIL>" class="text-primary"
                    ><EMAIL></a
                  >
                  to join the NODE WeChat group.
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="7" :xs="24" :sm="24" :md="7">
        <div class="font-600 d-flex h-100 bg-white">
          <svg-icon icon-class="address" class-name="svg-phone" />
          <div class="ml-05 address">
            <div class="text-secondary-color">Bio-Med Big Data Center</div>
            <div class="mt-03 text-secondary-color">
              Shanghai Institute of Nutrition and Health (SINH)
            </div>
            <div class="text-secondary-color">Chinese Academy of Sciences</div>
            <div class="text-secondary-color">320 Yueyang Rd.</div>
            <div class="text-secondary-color">Shanghai 200031, China</div>
          </div>
        </div>
      </el-col>
      <el-col :span="4" :xs="24" :sm="24" :md="4">
        <div class="h-100 official-account bg-white text-center">
          <img src="@/assets/images/weChat.png" alt="" style="width: 110px" />
          <div class="font-12">WeChat official account</div>
        </div>
      </el-col>
    </el-row>
  </div>
  <div
    v-show="featureDataStatus === ConfigEnum.Enable"
    class="container-fluid prl-0 mt-2"
  >
    <h3 class="text-main-color font-20 text-center">Featured Data</h3>
    <el-row :gutter="15" class="feature-microbe">
      <el-col :span="13" :xs="24" :sm="24" :md="13" class="pl-0">
        <div class="bg-white">
          <span
            class="font-600 human-resource text-main-color font-18 line-before mb-1 mt-1"
            >Human Resource</span
          >
          <span
            class="text-primary font-14 float-right cursor-pointer"
            @click="toFeatureData('human')"
            >More>></span
          >
          <el-row v-loading="featureDataLoading" class="row-gap mt-1">
            <el-col
              v-for="(it, index) in humanResource"
              :key="index"
              :span="8"
              :xs="12"
              :sm="12"
              :md="8"
            >
              <FeatureData
                :key="`humRes_${index}`"
                :feature-card="it"
                :curr-fd-type="'human'"
              ></FeatureData>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :span="11" :xs="24" :sm="24" :md="11">
        <div class="microbe bg-white">
          <span
            class="font-600 microbe-resource font-18 text-main-color line-before mb-1 mt-1"
            >Microbe Resource</span
          >
          <span
            class="text-primary font-14 float-right cursor-pointer"
            @click="toFeatureData('microbe')"
            >More>></span
          >
          <div class="human-feature mt-1">
            <el-row v-loading="featureDataLoading" class="mt-05 row-gap">
              <el-col
                v-for="(it, index) in microbeResource"
                :key="index"
                :span="12"
                :xs="24"
                :sm="24"
                :md="12"
              >
                <FeatureData
                  :key="`micRes_${index}`"
                  :feature-card="it"
                  :curr-fd-type="'microbe'"
                ></FeatureData>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <div
    v-show="featureDataStatus === ConfigEnum.Enable"
    class="container-fluid mt-1 bg-white"
  >
    <span
      class="font-600 omics-resource font-18 text-main-color line-before mb-1 mt-1"
      >Omics Resource</span
    >
    <span
      class="text-primary font-14 float-right cursor-pointer"
      @click="toFeatureData('omics')"
      >More>></span
    >
    <div class="human-feature mt-1">
      <el-row v-loading="featureDataLoading" :gutter="20" class="row-gap">
        <el-col
          v-for="(it, index) in omicsResource"
          :key="index"
          :span="4"
          :xs="12"
          :sm="12"
          :md="4"
        >
          <FeatureData
            :key="`omiRes_${index}`"
            :feature-card="it"
            :num-title="'experiments'"
            @to-omics-more="toFeatureData('omics')"
          ></FeatureData>
        </el-col>
      </el-row>
    </div>
  </div>
  <div class="container-fluid mt-1-5">
    <el-row :gutter="30">
      <el-col :span="16" :xs="24" :sm="24" :md="16" style="padding-left: 0">
        <h3 class="font-20 text-main-color text-center">How to Cite</h3>
        <div class="bg-white" style="height: calc(100% - 40px)">
          <div style="margin-top: 0.3rem" class="before-circle">
            After successfully submitting your data to NODE, we recommend using
            the following wording to describe the data deposition in your
            manuscript:<svg-icon
              icon-class="copy"
              class-name="svg-copy"
              @click="
                copyText(
                  'All data are accessible in NODE (' +
                    originUrl +
                    ') with the accession number XXX (e.g., OEP00000073) or through the URL:' +
                    hrefUrl,
                )
              "
            ></svg-icon>
          </div>
          <div class="cite-content">
            All data are accessible in NODE (
            <a :href="originUrl" class="text-primary" target="_blank">{{
              originUrl
            }}</a>
            ) with the accession number XXX (e.g., OEP00000073) or through the
            URL:
            <a :href="hrefUrl" class="text-primary" target="_blank">{{
              hrefUrl
            }}</a>
          </div>
          <div>
            <div class="before-circle mt-1">
              Please cite the following publication:<svg-icon
                icon-class="copy"
                class-name="svg-copy"
                @click="
                  copyText(
                    'Advances in multi-omics big data sharing platform research. Chinese Bulletin of Life Sciences. 2023, 35(12): 1553-1560. DOI:10.13376/j.cbls/2023169',
                  )
                "
              ></svg-icon>
            </div>
            <div style="padding-left: 12px; text-align: justify">
              <span class="font-600"
                >Advances in multi-omics big data sharing platform
                research.</span
              >
              <span style="font-style: italic">
                Chinese Bulletin of Life Sciences.</span
              >
              2023, 35(12): 1553-1560. DOI:
              <a
                href="https://lifescience.sinh.ac.cn/article.php?id=3716"
                class="text-primary"
                target="_blank"
                >10.13376/j.cbls/2023169</a
              >
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8" :xs="24" :sm="24" :md="8">
        <h3 class="font-20 text-main-color text-center">Tools</h3>
        <div class="tools text-center">
          <a href="https://www.biosino.org/vipmap/home" target="_blank"
            ><svg-icon icon-class="vipmap" class-name="svg-tool"></svg-icon
          ></a>
          <a
            href="https://www.biosino.org/iMAC/index"
            target="_blank"
            class="ml-1"
          >
            <svg-icon icon-class="imac" class-name="svg-tool"></svg-icon
          ></a>
        </div>
      </el-col>
    </el-row>
  </div>

  <div class="mb-2 mt-2">
    <div
      v-if="!publishLoading && publishData.length !== 0"
      class="container-fluid pos-relative"
    >
      <h3 class="text-main-color font-20 text-center">Latest Data</h3>
      <span
        v-loading.fullscreen.lock="allPublishLoading"
        class="text-primary font-14 last latest-more cursor-pointer"
        @click="getAllPublish"
        >More>></span
      >
    </div>
    <div
      v-if="publishData.length !== 0"
      v-loading="publishLoading"
      class="container-fluid bg-white"
    >
      <home-publish
        v-model:publish-data="publishData"
        v-loading="false"
        :show-title="false"
      ></home-publish>
    </div>
  </div>

  <el-dialog
    v-model="publishDialogVisible"
    title="Publications"
    width="1200"
    destroy-on-close
    center
  >
    <template #header="{ titleId, titleClass }">
      <h4 :id="titleId" :class="titleClass">Publications</h4>
      <div class="font-14 font-600">
        Total Number: {{ allPublishData.length }}
      </div>
    </template>
    <div style="max-height: 70vh; overflow-y: auto">
      <home-publish
        v-model:publish-data="allPublishData"
        v-loading="false"
        :show-title="false"
      ></home-publish>
    </div>
  </el-dialog>
  <div class="dataset radius-8" @click="toFeatureData('hmds')">
    <svg-icon icon-class="dataset" class-name="dataset-svg"></svg-icon>
    Hydrosphere
  </div>
</template>
<script setup>
  import FeatureData from '@/views/home/<USER>';
  import HomePublish from '@/views/home/<USER>';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { trimStr } from '@/utils';
  import { getAllPublishedPublish, getTopPublish } from '@/api/app/publish';
  import { fdHomeStat } from '@/api/system/featureData';
  import { getNoticeInfo } from '@/api/system/notice';
  import { ConfigEnum } from '@/utils/enums';

  const { proxy } = getCurrentInstance();

  const featureDataStatus = proxy.getConfigVal(ConfigEnum.FeatureData_Status);

  let originUrl = ref('');
  let hrefUrl = ref('');

  // 检测设备类型函数
  const isPC = ref(true);

  onMounted(() => {
    // 通过检测设备的userAgent来判断是否为PC端
    const checkPC = () => {
      const userAgent = navigator.userAgent;
      isPC.value = !(
        userAgent.includes('Mobile') ||
        userAgent.includes('Android') ||
        userAgent.includes('iPhone')
      );
    };
    checkPC();

    // 监听窗口大小变化，实时更新设备类型
    window.onresize = () => {
      checkPC();
    };

    let publicPath = import.meta.env.VITE_APP_PUBLIC_PATH;
    if (publicPath === '/' || !publicPath) {
      publicPath = '';
    }
    originUrl.value = window.location.origin + publicPath;
    hrefUrl.value = originUrl.value + '/project/detail/XXX';

    loadNoticeInfo();
    getTop10Publish();
    getFdHomeStat();
  });

  const router = useRouter();
  const cardItems = reactive([
    {
      iconClass: 'homeDataSubmit',
      dataName: 'Data Submission',
      path: '/submit/rawdata',
    },
    {
      iconClass: 'dataBrowse',
      dataName: 'Data Browsing',
      path: '/browse',
    },
    {
      iconClass: 'dataStatistics',
      dataName: 'Data Statistics',
      path: '/statistic',
    },
    {
      iconClass: 'dataDownload',
      dataName: 'Data Download',
      path: '/download',
    },
    {
      iconClass: 'homeHelp',
      dataName: 'Help Documents',
      path: '/help',
    },
  ]);
  const searchInput = ref('');
  const example = ref([
    'OEP00000073',
    'NODEP00000033',
    '10.1016/j.cell.2020.07.023',
    'Genomic',
    'PD-1',
    'lung cancer',
  ]);

  const featureDataLoading = ref(false);
  const humanResource = reactive([]);
  const microbeResource = reactive([]);
  const omicsResource = reactive([]);

  function getFdHomeStat() {
    featureDataLoading.value = true;
    humanResource.length = 0;
    microbeResource.length = 0;
    omicsResource.length = 0;
    fdHomeStat()
      .then(res => {
        featureDataLoading.value = false;
        let { data } = res;
        if (data) {
          let humanResourceStat = data.humanResourceStat;
          let len = humanResourceStat.length;
          for (let i = 0; i < len; i++) {
            if (!isPC.value && i > 1) {
              continue;
            }
            if (i < 6) {
              humanResource.push(humanResourceStat[i]);
            }
          }
          microbeResource.push(...data.microbeResourceStat);
          omicsResource.push(...data.omicsResourceStat);
        }
      })
      .catch(() => {
        featureDataLoading.value = false;
      });
  }

  const toBrowse = toAdvance => {
    router.push({
      name: 'Browse',
      state: { advanceVal: toAdvance },
    });
  };

  function doSearch() {
    const browsePath = '/browse';
    const searchVal = trimStr(searchInput.value);
    router.push({
      path: browsePath,
      query: { keyword: searchVal },
    });
  }

  /** 加载publish */
  let publishData = reactive([]);
  let publishLoading = ref(false);

  function getTop10Publish() {
    publishLoading.value = true;
    getTopPublish()
      .then(response => {
        publishData.push(...response.data);
      })
      .finally(() => {
        publishLoading.value = false;
      });
  }

  let allPublishData = reactive([]);
  let publishDialogVisible = ref(false);
  let allPublishLoading = ref(false);

  function getAllPublish() {
    allPublishLoading.value = true;
    getAllPublishedPublish()
      .then(response => {
        allPublishData = response.data;
        publishDialogVisible.value = true;
      })
      .finally(() => {
        allPublishLoading.value = false;
      });
  }

  const noticeContent = ref('');

  function loadNoticeInfo() {
    getNoticeInfo().then(response => {
      noticeContent.value = response?.msg;
    });
  }

  function copyText(text) {
    // 添加一个input元素放置需要的文本内容
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    // 选中并复制文本到剪切板
    input.select();
    document.execCommand('copy');
    // 移除input元素
    document.body.removeChild(input);
    proxy.$modal.msgSuccess('Copy successfully');
  }

  const toFeatureData = type => {
    router.push({
      name: 'FeatureData',
      params: { fdType: type },
    });
  };
</script>
<style lang="scss" scoped>
  .cite-content {
    margin-top: 0.3rem;
    font-style: italic;
    padding-left: 12px;
  }

  .banner {
    position: relative;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 6px;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('@/assets/images/banner.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      filter: blur(3px);
      z-index: -1;
    }
  }

  .browse-search {
    padding: 4px;
    border: 2px solid #e4e4e4;
    background-color: #fff;
    border-radius: 20px;
    position: relative;
    width: 90%;
    display: flex;
    align-items: center;

    .bg-round-warning {
      width: 60px;
    }

    :deep(.el-input__wrapper) {
      border-color: transparent;
      box-shadow: 0 0 0 0 var(--el-input-border-color, var(--el-border-color))
        inset;
    }

    & + .advanced-btn {
      padding: 18px 15px;
      box-shadow: 0 3px 5px 0 rgba(47, 85, 212, 0.3);
    }
  }

  .card-item {
    flex: 1;
    cursor: pointer;
    border-radius: 12px;
    padding: 15px 0;
    background-color: rgba(255, 255, 255, 0.8);
    transition: all ease 0.3s;

    &:hover {
      transform: translateY(-4px);
    }
  }

  .official-account {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .svg-card {
    width: 100px;
    height: 50px;
  }

  .svg-notice {
    width: 50px;
    height: 30px;
  }

  .prl-0 {
    padding: 0 !important;
  }

  .svg-email {
    width: 70px;
    height: 70px;

    & + .el-divider--vertical {
      height: 120px;
      margin: 0 35px;
    }
  }

  .svg-phone {
    position: relative;
    top: 5px;
    width: 20px;
    height: 20px;
  }

  .svg-chat {
    position: relative;
    top: 5px;
    left: -2px;
    width: 25px;
    height: 25px;
  }

  .row-gap {
    row-gap: 40px;
  }

  .human-resource:before {
    background: #ff8181;
  }

  .microbe-resource:before {
    background: #07bcb4;
  }

  .omics-resource:before {
    background: #3a78e8;
  }

  .bg-white {
    padding: 18px 20px;
    border-radius: 8px;
  }

  .tools {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    opacity: 1;
    border-radius: 4px;
    background-color: #ffffff;
    height: calc(100% - 40px);
    //background: linear-gradient(
    //  to right,
    //  rgba(47, 86, 148, 1),
    //  rgba(59, 132, 185, 1)
    //);
    .svg-tool {
      width: 200px;
      height: 120px;
    }
    .text-white {
      color: #ffffff;
    }
  }

  .microbiome {
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 10px;

    a {
      text-decoration: underline;
    }
  }

  .address {
    & > div {
      color: #8f8f8f;
      font-weight: 600;
      margin-top: 0.2rem;
      font-size: 15px;
    }
  }

  :deep(.el-tag__content) {
    font-weight: 600;
  }

  .latest-more {
    position: absolute;
    top: 10%;
    right: 0;
  }

  .before-circle:before {
    top: 14px;
  }

  @media (max-width: 767px) {
    .title {
      padding-top: 40px !important;
    }
    .svg-email {
      display: none;
    }
    .svg-chat ~ span,
    .cite-content {
      text-align: justify;
      word-break: break-all;
    }
  }

  @media (max-width: 1800px) {
    .tools {
      flex-direction: column !important;

      .svg-tool {
        height: 100px;
      }
      a {
        margin-left: 0 !important;
      }
    }
  }

  .svg-copy {
    position: relative;
    top: 3px;
    width: 16px;
    height: 16px;
    margin-left: 0.5rem;
    cursor: pointer;
    &:focus {
      outline: none;
    }
  }
  .dataset {
    width: 160px;
    right: 20px;
    bottom: 10%;
    height: auto;
    position: fixed;
    z-index: 50;
    background-color: #ffffff;
    //padding: 10px 10px;
    color: #3a78e8;
    font-weight: 600;
    text-align: center;
    padding: 10px 0;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    .dataset-svg {
      height: 70px;
    }
    &:hover {
      cursor: pointer;
    }
  }
</style>
