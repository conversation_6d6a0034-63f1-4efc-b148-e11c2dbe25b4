package org.biosino.upload.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.api.msg.VerificationCreateMsg;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.DataQuery;
import org.biosino.upload.dto.mapper.DataDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ArchivedAnalysisDataExportVO;
import org.biosino.upload.vo.ArchivedRawDataExportVO;
import org.biosino.upload.vo.DataVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2023/12/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataService extends BaseService {

    private final RunRepository runRepository;
    private final MessageSender messageSender;
    private final DataRepository dataRepository;
    private final SampleRepository sampleRepository;
    private final ProjectRepository projectRepository;
    private final FtpFileLogService ftpFileLogService;
    private final Md5OpLogRepository md5OpLogRepository;
    private final AnalysisRepository analysisRepository;
    private final ExperimentRepository experimentRepository;
    private final RedisService redisService;

    private final static String VERIFICATION_TASK_KEY = "verification_task_key_";

    public List<String> verifyFtpFile(List<String> ids) {
        String memberId = SecurityUtils.getMemberId();
        // 防止一个用户同时重复提交多次
        String lockKey = VERIFICATION_TASK_KEY + memberId;
        if (redisService.hasKey(lockKey)) {
            throw new ServiceException("You already have a verification request being processed, please try again later!");
        }

        try {
            long timeout = 12L;
            redisService.setCacheObject(lockKey, true, timeout, TimeUnit.HOURS);

            List<String> errMsgs = new ArrayList<>();
            String memberFtpPath = SecurityUtils.getMemberFtpPath();
            for (String id : ids) {
                // 校验选择的文件和对应的md5是否存在
                String msg = verifyFtpFileExist(id, memberFtpPath);
                if (StrUtil.isNotBlank(msg)) {
                    errMsgs.add(msg);
                } else {
                    FtpFileLog ftpFileLog = ftpFileLogService.getByIdAndCreator(id, SecurityUtils.getMemberId());
                    if (!StrUtil.equals(ftpFileLog.getStatus(), FtpFileLogStatus.unchecked.getStatus())
                            && !StrUtil.equals(ftpFileLog.getStatus(), FtpFileLogStatus.checkFail.getStatus())) {
                        log.info("Err in verifyUploadedFtpHomeFile : ftpFile already checked! path = {}", ftpFileLog.getPath());
                        continue;
                    }
                    String filePath = ftpFileLog.getPath();
                    // 发送md5校验的消息给task服务
                    VerificationCreateMsg checkMd5Msg = new VerificationCreateMsg(ftpFileLog.getId(), SecurityUtils.getMemberId(), filePath);
                    messageSender.sendDelayMsg("verification_create_key", checkMd5Msg);
                    ftpFileLog.setStatus(FtpFileLogStatus.queuing.getStatus());
                    ftpFileLog.setUpdateTime(new Date());
                    ftpFileLogService.saveOrUpdate(ftpFileLog);
                    // 保存操作日志
                    Optional<Md5OpLog> optional = md5OpLogRepository.findFirstByFilePath(filePath);
                    Md5OpLog md5OpLog;
                    if (!optional.isPresent()) {
                        md5OpLog = new Md5OpLog();
                        md5OpLog.setCreateDate(new Date());
                    } else {
                        md5OpLog = optional.get();
                    }
                    md5OpLog.setFilePath(filePath);
                    md5OpLog.setReplyDate(new Date());
                    md5OpLog.setStatus("init");
                    md5OpLogRepository.save(md5OpLog);
                }
            }
            return errMsgs;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            redisService.deleteObject(lockKey);
        }
    }

    public List<String> checkFtpFile(List<String> ids) {
        List<String> errMsgs = new ArrayList<>();
        String memberFtpPath = SecurityUtils.getMemberFtpPath();
        for (String id : ids) {
            String msg = verifyFtpFileExist(id, memberFtpPath);
            if (StrUtil.isNotBlank(msg)) {
                errMsgs.add(msg);
            }
        }
        return errMsgs;
    }

    private String verifyFtpFileExist(String id, String memberFtpPath) {
        FtpFileLog ftpFileLog = ftpFileLogService.getByIdAndCreator(id, SecurityUtils.getMemberId());
        // 查询数据库中是否存在对应的记录
        if (ftpFileLog == null) {
            return StrUtil.format("{}  verify failed. Cause:  file does not exists!");
        }
        String filePath = ftpFileLog.getPath();
        String relativePath = filePath.replace(memberFtpPath, "");
        File file = new File(filePath);
        // 如果文件不存在
        if (!file.exists()) {
            log.info("Err in verifyUploadedFtpHomeFile : ftpFile does not exists! path = {}", relativePath);
            return StrUtil.format("{}  verify failed. Cause:  file does not exists!", relativePath);
        }
        File md5File = new File(filePath + ".md5");
        // 如果对应的md5文件不存在
        if (!md5File.exists()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File does not exists! path = {}", relativePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File does not exists!", relativePath);
        }
        // md5文件内容校验已放在node-sftp中处理
        // 如果对应的md5不是文件
        if (!md5File.isFile()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File is not a file! path = {}", relativePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File is not a file!", relativePath);
        }
        // 如果对应的md5文件不可读
        if (!md5File.canRead()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File can not read! path = {}", relativePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File can not read!", relativePath);
        }
        // if (!MyFileUtils.isValidFilepath(relativePath)) {
        //     log.info("Err in verifyUploadedFtpHomeFile : invalid filepath! path = {}", relativePath);
        //     return StrUtil.format("{}  verify failed. Cause:  invalid filepath! Must match the following regular expression: {}", relativePath, MyFileUtils.FILEPATH_PATTERN);
        // }

        try {
            // 读取md5
            ftpFileLog.setMd5FileContent(NodeUtils.readMd5(md5File));
        } catch (Exception e) {
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return StrUtil.format(md5File + " content error! ");
        }

        ftpFileLogService.updateById(ftpFileLog);

        return null;
    }

    public Page<DataVO> listUnarchivedData(DataQuery query) {
        query.setCreator(SecurityUtils.getMemberId());
        query.setArchived(ArchiveEnum.no.name());
        query.setSecurities(CollUtil.newArrayList(SecurityEnum._private.getDesc()));
        PageImpl<Data> page = dataRepository.findTempDataPage(query);

        List<DataVO> content = createDataVOs(page.getContent());

        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    public void deleteDataByIds(List<String> ids) {
        for (String id : ids) {
            Optional<Data> optional = dataRepository.findById(id);
            if (!optional.isPresent()) {
                continue;
            }
            Data data = optional.get();
            if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), data.getSecurity())) {
                continue;
            }
            data.setUpdateDate(new Date());
            data.setSecurity(data.getSecurity() + "_Delete");

            Data tempData = DataDTOMapper.INSTANCE.copy(data);
            data.setTempData(tempData);

            dataRepository.save(data);
        }
        // 通知删除索引; 打日志
        if (CollUtil.isNotEmpty(ids)) {
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_delete_key.name(), new IndexUpdateMsg(AuthorizeType.data.name(), ids));
        }
    }

    public void exportUnarchived(HttpServletResponse response, DataQuery query) {
        query.setSecurities(CollUtil.newArrayList(SecurityEnum._private.getDesc()));
        query.setCreator(SecurityUtils.getMemberId());

        // 分页条数设置为最大
        query.setPageSize(-1);
        PageImpl<Data> page = dataRepository.findTempDataPage(query);
        List<DataVO> content = createDataVOs(page.getContent());

        ExcelUtil<DataVO> util = new ExcelUtil<>(DataVO.class);
        util.exportExcel(response, content, "sheet1");
    }

    /**
     * single模式——Archiving Data表格
     */
    public Page<DataVO> listArchivingData(DataQuery queryDTO) {

        Page<Data> page = findArchivingDataPage(queryDTO);
        List<DataVO> contents = createDataVOs(page.getContent());

        return new PageImpl<>(contents, page.getPageable(), page.getTotalElements());
    }

    public Page<Data> findArchivingDataPage(DataQuery queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setSecurities(CollUtil.newArrayList(SecurityEnum._private.getDesc()));
        return dataRepository.findTempDataPage(queryDTO);
    }

    /**
     * single模式——archived raw data表格
     */
    public Page<DataVO> listArchivedRawData(DataQuery queryDTO) {

        Page<Data> page = findArchivedRawDataPage(queryDTO);

        List<DataVO> content = createDataVOs(page.getContent());

        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    public Page<Data> findArchivedRawDataPage(DataQuery queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setSecurities(CollUtil.newArrayList(SecurityEnum._private.getDesc()));
        queryDTO.setExistRunNo(true);
        return dataRepository.findDataPage(queryDTO);
    }

    /**
     * single模式——archived analysis data表格
     */
    public Page<DataVO> listArchivedAnalysisData(DataQuery queryDTO) {

        Page<Data> page = findArchivedAnalysisDataPage(queryDTO);

        List<DataVO> content = createDataVOs(page.getContent());

        return new PageImpl<>(content, page.getPageable(), page.getTotalElements());
    }

    public Page<Data> findArchivedAnalysisDataPage(DataQuery queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setSecurities(CollUtil.newArrayList(SecurityEnum._private.getDesc()));
        queryDTO.setExistAnalysisNo(true);
        return dataRepository.findDataPage(queryDTO);
    }

    /**
     * 导出未归档的数据
     */
    public void exportPreArchive(HttpServletResponse response, DataQuery queryDTO) {
        queryDTO.setPageSize(-1);

        Page<Data> page = findArchivingDataPage(queryDTO);

        List<DataVO> result = createDataVOs(page.getContent());

        ExcelUtil<DataVO> util = new ExcelUtil<>(DataVO.class);
        util.exportExcel(response, result, "sheet1");
    }

    /**
     * 导出归档到RawData的数据
     */
    public void exportArchivedRawData(HttpServletResponse response, DataQuery queryDTO) {
        queryDTO.setPageSize(-1);

        Page<Data> page = findArchivedRawDataPage(queryDTO);

        List<DataVO> exportDataVOs = createDataVOs(page.getContent());
        List<ArchivedRawDataExportVO> result = BeanUtil.copyToList(exportDataVOs, ArchivedRawDataExportVO.class);

        ExcelUtil<ArchivedRawDataExportVO> util = new ExcelUtil<>(ArchivedRawDataExportVO.class);
        util.exportExcel(response, result, "sheet1");
    }

    /**
     * 导出归档到AnalysisData的数据
     */
    public void exportArchivedAnalysisData(HttpServletResponse response, DataQuery queryDTO) {
        queryDTO.setPageSize(-1);

        Page<Data> page = findArchivedAnalysisDataPage(queryDTO);

        List<DataVO> exportDataVOs = createDataVOs(page.getContent());

        List<ArchivedAnalysisDataExportVO> result = BeanUtil.copyToList(exportDataVOs, ArchivedAnalysisDataExportVO.class);

        ExcelUtil<ArchivedAnalysisDataExportVO> util = new ExcelUtil<>(ArchivedAnalysisDataExportVO.class);
        util.exportExcel(response, result, "sheet1");
    }

    private List<DataVO> createDataVOs(List<Data> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 找出所有的runNo,包括tempData中的runNo
        List<String> runNos = list.stream().flatMap(x -> {
            ArrayList<String> nos = new ArrayList<>();
            nos.add(x.getRunNo());
            if (x.getTempData() != null) {
                nos.add(x.getTempData().getRunNo());
            }
            return nos.stream();
        }).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 找出所有的analNos，包括tempData中的analNo
        List<String> analNos = list.stream().flatMap(x -> {
            ArrayList<String> nos = new ArrayList<>();
            nos.add(x.getAnalNo());
            if (x.getTempData() != null) {
                nos.add(x.getTempData().getAnalNo());
            }
            return nos.stream();
        }).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 查询除所有的run
        List<Run> runs = runRepository.findByRunNoIn(runNos);

        // 把run中的expNo提取出来
        List<String> expNos = runs.stream().map(Run::getExpNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 把run中的sapNo提取出来
        List<String> sapNos = runs.stream().map(Run::getSapNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 根据expNos查询exp
        List<Experiment> experiments = experimentRepository.findByExpNoIn(expNos);

        // 根据sapNos查询sap
        List<Sample> samples = sampleRepository.findBySapNoIn(sapNos);

        // 提取experiments 中所有的projNo
        List<String> projNos = experiments.stream().map(Experiment::getProjectNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 根据projNos查询project
        List<Project> projects = projectRepository.findByProjectNoIn(projNos);

        // 根据analNos查询analysis
        List<Analysis> analyses = analysisRepository.findByAnalysisNoIn(analNos);

        // 转换成Map
        Map<String, Project> projNoToProjectMap = projects.stream().collect(Collectors.toMap(Project::getProjectNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Experiment> expNoToExpMap = experiments.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Sample> sapNoToSapMap = samples.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Run> runNoToRunMap = runs.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Analysis> analNoToAnalMap = analyses.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));


        List<DataVO> resultDataVOs = new ArrayList<>();


        for (Data data : list) {
            Data tempData = data.getTempData();
            if (tempData == null) {
                tempData = data;
            }

            DataVO vo = new DataVO();
            DataDTOMapper.INSTANCE.copyToVo(tempData, vo);

            // 设置sourcePath
            if (StrUtil.isNotBlank(tempData.getSourcePath())) {
                vo.setSourcePath(MyFileUtils.absoluteToRelativePath(tempData.getSourcePath(), SecurityUtils.getMemberFtpPath()));
            }

            // 计算文件大小
            vo.setReadableFileSize(FileUtil.readableFileSize(tempData.getFileSize()));

            // 如果有runNo
            if (StrUtil.isNotBlank(tempData.getRunNo())) {
                Run run = runNoToRunMap.get(tempData.getRunNo());
                if (run == null) {
                    continue;
                }

                if (!run.getRunNo().startsWith(SequenceType.RUN.getPrefix())) {
                    vo.setRunNo(run.getName());
                } else {
                    vo.setRunNo(run.getRunNo());
                    vo.setRunName(run.getName());
                }

                // 查询Experiment
                Experiment experiment = expNoToExpMap.get(run.getExpNo());
                if (experiment == null) {
                    continue;
                }

                if (!experiment.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                    vo.setExpNo(experiment.getName());
                } else {
                    vo.setExpNo(experiment.getExpNo());
                    vo.setExpName(experiment.getName());
                }


                // 查询Project
                Project project = projNoToProjectMap.get(experiment.getProjectNo());
                if (project == null) {
                    continue;
                }

                if (!experiment.getProjectNo().startsWith(SequenceType.PROJECT.getPrefix())) {
                    vo.setProjectNo(project.getName());
                } else {
                    vo.setProjectNo(project.getProjectNo());
                    vo.setProjectName(project.getName());
                }

                // 查询Sample
                Sample sample = sapNoToSapMap.get(run.getSapNo());
                if (sample == null) {
                    continue;
                }

                if (!run.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                    vo.setSapNo(sample.getName());
                } else {
                    vo.setSapNo(sample.getSapNo());
                    vo.setSapName(sample.getName());
                }

            }

            if (StrUtil.isNotBlank(tempData.getAnalNo())) {
                // 查询Analysis
                Analysis analysis = analNoToAnalMap.get(tempData.getAnalNo());
                if (analysis == null) {
                    continue;
                }

                if (!tempData.getAnalNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    vo.setAnalNo(analysis.getName());
                } else {
                    vo.setAnalNo(analysis.getAnalysisNo());
                    vo.setAnalName(analysis.getName());
                }
            }
            resultDataVOs.add(vo);
        }
        return resultDataVOs;
    }

    public DeleteCheckResultVO deleteCheck(String dataNo) {
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Not found Data"));
        if (!StrUtil.equals(data.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }
        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (data.getTempData() != null && data.getTempData().getSubNo() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(dataNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }

        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setDataNos(CollUtil.newArrayList(data.getDatNo()));
        resultVO.setErrors(new ArrayList<>(errors));
        return resultVO;
    }


    public void deleteDataAll(String dataNo) {
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Not found Data"));
        if (!StrUtil.equals(data.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }

        DeleteCheckResultVO checkResultVO = deleteCheck(dataNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The Data cannot be deleted because it is associated with other data");
        }

        // 添加删除的日志
        addUserCenterDeleteLog(dataNo, AuthorizeType.data.name(), checkResultVO);

        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());

        // 通知删除es索引
        updateEsData(AuthorizeType.data.name(), dataNo);
    }

    public JSONObject getRawDataStat() {
        // 统计数据量
        long uncheckNum = ftpFileLogService.countByStatusInAndCreator(CollUtil.newArrayList(FtpFileLogStatus.unchecked.getStatus()), SecurityUtils.getMemberId());
        // 统计checking的数据量
        long checkingNum = ftpFileLogService.countByStatusInAndCreator(FtpFileLogStatus.includeCheckStatus(), SecurityUtils.getMemberId());
        // 统计unarchived的数据量
        long unarchivedNum = dataRepository.countUnarchivedByCreator(SecurityUtils.getMemberId());
        JSONObject result = new JSONObject();
        result.put("uncheckNum", uncheckNum);
        result.put("checkingNum", checkingNum);
        result.put("unarchivedNum", unarchivedNum);
        return result;
    }

    public void editFileName(String dataNo, String name) {
        if (StrUtil.isBlank(dataNo) || StrUtil.isBlank(name)) {
            throw new ServiceException("Parameter cannot be empty");
        }

        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Not found Data"));
        if (!StrUtil.equals(data.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }

        String fileNameSuffix = MyFileUtils.getFileNameSuffix(data.getName());
        name = name + "." + fileNameSuffix;

        if (!MyFileUtils.isValidFilename(name)) {
            throw new ServiceException("The file name contains illegal characters is not allowed. Such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters");
        }

        // 如果数据是未归档的，直接修改外层的名称
        if (StrUtil.equals(data.getArchived(), ArchiveEnum.no.name())) {
            data.setName(name);
            data.setFileName(name);
        }

        Data tempData = data.getTempData();
        if (tempData == null) {
            throw new ServiceException("The data is not editable");
        }
        tempData.setName(name);
        tempData.setFileName(name);
        tempData.setUpdateDate(new Date());
        data.setTempData(tempData);
        dataRepository.save(data);
    }
}
