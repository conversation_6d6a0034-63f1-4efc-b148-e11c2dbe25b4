package org.biosino.system.api.factory;

import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Node用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteMemberFallbackFactory implements FallbackFactory<RemoteMemberService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteMemberFallbackFactory.class);

    @Override
    public RemoteMemberService create(Throwable throwable) {
        // 该服务很重要,后期可以考虑接入监控预警系统中,BMDC的API失效马上报警
        log.error("NODE用户服务调用失败:{}", throwable.getMessage());
        return new RemoteMemberService() {
            @Override
            public R<MemberDTO> getOneMemberByMemberId(String memberId, String currentUserEmail, String source) {
                return R.fail("getOneMemberByMemberId调用失败:" + throwable.getMessage());
            }

            @Override
            public R<MemberDTO> getOneMemberByBioId(String bioId, String currentUserEmail, String source) {
                return R.fail("getOneMemberByBioId调用失败:" + throwable.getMessage());
            }

            @Override
            public R<MemberDTO> getMemberInfoByEmail(String memberEmail, String currentUserEmail, String source) {
                return R.fail("getMemberInfoByEmail调用失败:" + throwable.getMessage());
            }

            @Override
            public R<List<MemberDTO>> getMemberListByMemberEmailLike(String email, String currentUserEmail, String source) {
                return R.fail("getMemberListByMemberEmailLike调用失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getAllMemberEmailList(String currentUserEmail, String source) {
                return R.fail("getAllMemberEmailList调用失败:" + throwable.getMessage());
            }

            @Override
            public R<Long> getNodeMemberByCreateDateRange(Date startDate, Date endDate, String currentUserEmail, String source) {
                return R.fail("getNodeMemberByCreateDateRange调用失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getNodeMemberEmailList(String currentUserEmail, String source) {
                return R.fail("getNodeMemberEmailList调用失败:" + throwable.getMessage());
            }

            @Override
            public R<List<MemberDTO>> getNodeMemberInfoList(String currentUserEmail, String source) {
                return R.fail("getNodeMemberInfoList调用失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, String>> getMemberIdToEmailMapByMemberIds(Collection<String> memberIds, String currentUserEmail, String source) {
                return R.fail("getMemberIdToEmailMapByMemberIds调用失败:" + throwable.getMessage());
            }
        };
    }
}
