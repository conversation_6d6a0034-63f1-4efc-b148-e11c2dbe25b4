# 堡垒机：box.biosino.org:60022，账号luoruijin
004: mgmt-analysis-45903

# 前端挂载路径：
/node2/nginx/html/node

# 部署jar包挂载路径：
/node2/jar
# 重启jar服务
kubectl --kubeconfig=/home/<USER>/.kube/node2-config delete po node-app-

# 查看所有pod
kubectl --kubeconfig=/home/<USER>/.kube/node2-config get po

# 查看日志
kubectl --kubeconfig=/home/<USER>/.kube/node2-config logs -f node-system-55b96f9f6c-j8jb2

# 重启所有服务(慎用)
kubectl --kubeconfig=/home/<USER>/.kube/node2-config scale --replicas=0 deployment --all
kubectl --kubeconfig=/home/<USER>/.kube/node2-config scale --replicas=1 deployment --all

# 打开mongo终端
kubectl --kubeconfig=/home/<USER>/.kube/node2-config exec node-mongo-0 -it bash
# 进入mongo命令行
mongo localhost:27017 -u node -p node --authenticationDatabase node;
# 查看所有库名称
show dbs;
# 切换到node库
use node;
db.getCollection("sample").find({ "sap_no": "OES00025992" });