package org.biosino.system.dto.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataQueryDTO extends BaseQuery {

    private List<String> dataNos;

    private String name;

    private String creatorEmail;

    private String creator;

    private List<String> analNos;

    private List<String> expNos;

    private List<String> sapNos;

    private List<String> runNos;

    private boolean existAnalysis = false;

    private boolean existRun = false;

    private String archived;

    private List<String> security;

    public void setName(String name) {
        this.name = StrUtil.trimToNull(name);
    }

    public void setCreatorEmail(String creatorEmail) {
        this.creatorEmail = StrUtil.trimToNull(creatorEmail);
    }

}
