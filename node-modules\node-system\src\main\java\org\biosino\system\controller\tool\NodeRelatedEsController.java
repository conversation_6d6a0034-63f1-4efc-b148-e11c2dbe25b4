package org.biosino.system.controller.tool;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nodeRelatedEs")
public class NodeRelatedEsController {

    private final RemoteDataService remoteDataService;

    /**
     * 列出
     */
    @PostMapping("/findRelatedDataPage")
    public AjaxResult findRelatedDataPage(@RequestBody RelatedEsSearchVO searchVO) {
        R<EsPageInfo<RelatedDataDTO>> r = remoteDataService.findRelatedDataPage(searchVO, SecurityConstants.INNER);
        return AjaxResult.success(r.getData());
    }

    /**
     * 下载文件
     */
    @RequestMapping("/download")
    @Log(module1 = "Tool", module2 = "Raw Data Search", businessType = BusinessType.EXPORT)
    public void download(String query, HttpServletResponse response) {
        RelatedEsSearchVO searchVO = JSON.parseObject(query, RelatedEsSearchVO.class);
        searchVO.setPageSize(50000);
        searchVO.setPageNum(1);
        R<EsPageInfo<RelatedDataDTO>> r = remoteDataService.findRelatedDataPage(searchVO, SecurityConstants.INNER);
        EsPageInfo<RelatedDataDTO> data = r.getData();
        List<RelatedDataDTO> list = data.getList();
        ExcelUtil<RelatedDataDTO> util = new ExcelUtil<>(RelatedDataDTO.class);
        util.exportExcel(response, list, "sheet1");
    }

}
