package org.biosino.node.fastqc.service;

import ch.qos.logback.core.util.FileSize;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.FastQCTaskStartMsg;
import org.biosino.common.rabbitmq.msg.FastQCTaskStatusMsg;
import org.biosino.node.fastqc.config.FastqcProperties;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> Li
 * @date 2024/6/2
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FastqcService {

    private final FastqcProperties fileProperties;

    private final Configuration freemarkerConfig;

    private final MessageSender messageSender;

    private final FastqcProperties fastqcProperties;


    public void handlerTaskStart(FastQCTaskStartMsg msg) throws IOException, TemplateException {
        // 发送正在运行的消息
        FastQCTaskStatusMsg statusMsg = new FastQCTaskStatusMsg();
        statusMsg.setDataNo(msg.getDataNo());
        statusMsg.setStatus(FastQCTaskStatusEnum.running.name());
        messageSender.sendDelayMsg("fastqc_task_status_routing_key", statusMsg);


        String dataNo = msg.getDataNo();
        File qcDir = FileUtil.file(fastqcProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo);
        FileUtil.mkdir(qcDir);
        Template template = freemarkerConfig.getTemplate("fastqc.ftl");

        Map<String, Object> model = BeanUtil.beanToMap(msg);
        // 判断输入文件是否是大文件
        File sourceFile = FileUtil.file(fileProperties.getNodeDataPath(), msg.getDataFilePath());
        String thread = fastqcProperties.getThread();
        String memory = fastqcProperties.getMemory();
        if (sourceFile.length() > FileSize.valueOf(fileProperties.getBigFileSize()).getSize()) {
            thread = fastqcProperties.getBigFileThread();
            memory = fastqcProperties.getBigFileMemory();
        }
        model.putAll(BeanUtil.beanToMap(fileProperties));
        model.put("thread", thread);

        String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);

        // 将脚本写入qush文件夹下
        File fastqcSh = FileUtil.file(qcDir, "qsub", "qsub.fastqc.sh");

        FileUtil.writeUtf8String(content, fastqcSh);

        // tasklogprefix
        String tasklogprefix = FilenameUtils.separatorsToUnix(FileUtil.file(qcDir, "qsub", "qsub.fastqc." + dataNo).getAbsolutePath());

        boolean success = qusb(fastqcSh.getAbsolutePath(), tasklogprefix, thread, memory, fastqcProperties.getQsubmode());

        if (success) {
            FastQCTaskStatusMsg successMsg = obtainSuccessMsg(msg);
            messageSender.sendDelayMsg("fastqc_task_status_routing_key", successMsg);
        } else {
            FastQCTaskStatusMsg errorMsg = obtainErrorMsg(msg);
            messageSender.sendDelayMsg("fastqc_task_status_routing_key", errorMsg);
        }
    }

    private FastQCTaskStatusMsg obtainSuccessMsg(FastQCTaskStartMsg msg) {
        String dataNo = msg.getDataNo();
        FastQCTaskStatusMsg statusMsg = new FastQCTaskStatusMsg();
        statusMsg.setDataNo(dataNo);
        statusMsg.setStatus(FastQCTaskStatusEnum.success.name());
        FastQCTaskStatusMsg.ResultData resultData = new FastQCTaskStatusMsg.ResultData();

        File seqkitFile = FileUtil.file(fileProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo, msg.getDataFileName() + ".seqkit.stats.txt");
        File fastqcHtmlFile = FileUtil.file(fileProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo, msg.getDataFileName() + "_fastqc.html");
        File fastqcZipFile = FileUtil.file(fileProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo, msg.getDataFileName() + "_fastqc.zip");

        resultData.setSeqkitFilepath(MyFileUtils.absoluteToRelativePath(seqkitFile.getAbsolutePath(), fileProperties.getFastqcPath()));
        resultData.setFastqcHtmlFilepath(MyFileUtils.absoluteToRelativePath(fastqcHtmlFile.getAbsolutePath(), fileProperties.getFastqcPath()));
        resultData.setFastqcZipFilepath(MyFileUtils.absoluteToRelativePath(fastqcZipFile.getAbsolutePath(), fileProperties.getFastqcPath()));
        resultData.setFastqcVersion(fastqcProperties.getFastqcVersion());
        resultData.setSeqkitVersion(fastqcProperties.getSeqkitVersion());

        statusMsg.setData(resultData);

        return statusMsg;
    }

    private FastQCTaskStatusMsg obtainErrorMsg(FastQCTaskStartMsg msg) {
        String dataNo = msg.getDataNo();
        FastQCTaskStatusMsg statusMsg = new FastQCTaskStatusMsg();
        statusMsg.setDataNo(dataNo);
        statusMsg.setStatus(FastQCTaskStatusEnum.failed.name());
        // 错误日志
        File errlogFile = FileUtil.file(fastqcProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo, "qsub", "qsub.fastqc.err.txt");
        if (!errlogFile.exists()) {
            statusMsg.setFailCause("fastqc execution failed but no error log, please contact admin");
        } else {
            statusMsg.setFailCause("invalid FASTA/Q format");
            statusMsg.setErrorLogPath(MyFileUtils.absoluteToRelativePath(errlogFile.getAbsolutePath(), fastqcProperties.getFastqcPath()));
        }
        return statusMsg;
    }

    private static int execForExitcode(String cmd) {
        log.info("开始执行命令：{}", cmd);
        if (FileUtil.isWindows()) {
            return 0;
        }
        Process process = RuntimeUtil.exec("sh", "-c", cmd);
        int exitCode = 0;
        try {
            exitCode = process.waitFor();
            log.info("执行命令：{} 完成，退出状态码:{}", cmd, exitCode);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return exitCode;
    }

    private boolean qusb(String shPath, String tasklogprefix, String thread, String memory, String qusbmode) {
        String cmd;
        if (StrUtil.equals("local", qusbmode)) {
            cmd = StrUtil.format("bash {} > {}.log 2> {}.err", shPath, tasklogprefix, tasklogprefix);
        } else if (StrUtil.equals("qsub", fastqcProperties.getQsubmode())) {
            String lstring = "";
            if (StrUtil.isNotBlank(fastqcProperties.getNodeName())) {
                lstring = StrUtil.format(" --nodelist={} --cpus-per-task={}", fastqcProperties.getNodeName(), thread);
            } else {
                lstring = StrUtil.format(" --cpus-per-task={}", thread);
            }
            String mstring = "";
            if (StrUtil.isNotBlank(memory)) {
                mstring = StrUtil.format(" --mem={}", memory);
            }
            String qstring = "";
            if (StrUtil.isNotBlank(fastqcProperties.getQueueName())) {
                qstring = StrUtil.format(" -p {}", fastqcProperties.getQueueName());
            }
            String ostring = "";
            if (StrUtil.isNotBlank(shPath)) {
                String parent = FileUtil.getParent(shPath, 1);
                ostring = StrUtil.format(" --output {}/slurm.out", parent);
            }

            cmd = StrUtil.format("sbatch -W {} {} {} {} {} > {}.log 2> {}.err", lstring, qstring, mstring, ostring, shPath, tasklogprefix, tasklogprefix);
        } else {
            throw new RuntimeException("qsub mode error");
        }
        int exitCode = execForExitcode(cmd);
        return exitCode == 0;
    }
}
