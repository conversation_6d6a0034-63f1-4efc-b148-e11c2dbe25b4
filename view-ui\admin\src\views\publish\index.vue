<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="searchForm" :model="queryParams" :inline="true">
        <el-form-item label="Article Name" prop="articleName">
          <el-input
            v-model="queryParams.articleName"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="Publication" label-width="95" prop="publication">
          <el-input
            v-model="queryParams.publication"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="Type ID" label-width="95" prop="typeId">
          <el-input
            v-model="queryParams.typeId"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="DOI" prop="doi">
          <el-input
            v-model="queryParams.doi"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="Creator" prop="createEmail">
          <el-input
            v-model="queryParams.createEmail"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>

        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 200px"
          >
            <el-option value="enable">Enable</el-option>
            <el-option value="disable">Disable</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="Deleted" prop="deleted">
          <el-select
            v-model="queryParams.deleted"
            clearable
            style="width: 200px"
          >
            <el-option :value="1" label="True">True</el-option>
            <el-option :value="0" label="False">False</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="Create Date" label-width="95">
          <el-date-picker
            v-model="dateRange"
            clearable
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 240px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button icon="Plus" type="primary" @click="handleAdd"
            >Add
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            icon="Download"
            type="warning"
            style="float: right"
            @click="handleExport"
            >Export
          </el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        :tooltip-options="{
          enterable: true,
          popperOptions: { strategy: 'fixed' },
        }"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        :default-sort="defaultSort"
        border
        @sort-change="handleSortChange"
      >
        <el-table-column
          show-overflow-tooltip
          prop="id"
          label="Record No"
          width="210"
        />

        <el-table-column
          show-overflow-tooltip
          prop="articleName"
          label="Article Name"
          min-width="140"
        />
        <el-table-column
          show-overflow-tooltip
          prop="publication"
          sortable
          label="Publication"
          min-width="160"
        />
        <el-table-column prop="type" label="Type" width="95" sortable />

        <el-table-column
          prop="typeID"
          label="Type ID"
          show-overflow-tooltip
          width="115"
        >
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row)"
            >
              {{ scope.row.typeId }}
            </a>
          </template>
        </el-table-column>

        <el-table-column
          prop="doi"
          label="DOI"
          sortable
          show-overflow-tooltip
          min-width="140"
        >
          <template #default="scope">
            <a
              target="_blank"
              :href="`https://doi.org/${scope.row.doi}`"
              class="text-primary cursor-pointer"
              >{{ scope.row.doi }}</a
            >
          </template>
        </el-table-column>

        <el-table-column
          prop="pmid"
          label="PMID"
          width="100"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            <a
              target="_blank"
              :href="`https://pubmed.ncbi.nlm.nih.gov/${scope.row.pmid}/`"
              class="text-primary cursor-pointer"
            >
              {{ scope.row.pmid }}</a
            >
          </template>
        </el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="reference"
          label="Reference"
          min-width="120"
        />

        <el-table-column
          prop="createEmail"
          label="Creator"
          width="200"
          show-overflow-tooltip
        />

        <el-table-column
          show-overflow-tooltip
          prop="sort"
          label="Sort"
          width="80"
          sortable
        />

        <el-table-column label="Status" align="center" prop="status" width="90">
          <template #default="scope">
            <el-tag
              :disable-transitions="true"
              :type="scope.row.status === 'disable' ? 'danger' : 'primary'"
              >{{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="createDate"
          label="Create Date"
          width="160"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="deleted" label="Deleted" width="100">
          <template #default="scope">
            <el-tag
              :disable-transitions="true"
              :type="scope.row.deleted ? 'danger' : 'primary'"
              >{{ scope.row.deleted }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="Operate" width="90" fixed="right">
          <template #default="scope">
            <div v-if="!scope.row.deleted">
              <el-tooltip content="Edit">
                <svg-icon
                  icon-class="edits"
                  class-name="meta-svg"
                  @click="handleEdit(scope.row)"
                ></svg-icon>
              </el-tooltip>
              <el-tooltip content="Delete">
                <svg-icon
                  icon-class="delete"
                  class-name="meta-svg"
                  @click="handleDelete(scope.row.id)"
                ></svg-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>

    <!-- 添加修改public -->
    <el-dialog
      v-model="displayDialog"
      :title="addType ? 'Add Publish' : 'Edit Publish'"
      class="edit-publish"
      width="680"
      append-to-body
    >
      <el-form
        ref="publishEditRef"
        :model="form"
        :rules="rules"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="Article Name" prop="articleName">
          <el-input v-model="form.articleName" />
        </el-form-item>

        <el-form-item label="Publication" prop="publication">
          <el-input v-model="form.publication" />
        </el-form-item>

        <el-form-item label="Type ID" prop="typeId" required>
          <el-input v-model="form.typeId" />
        </el-form-item>

        <el-form-item label="DOI" prop="doi">
          <el-input v-model="form.doi" style="width: 470px" />
          <el-tooltip content="Click to auto-fill.">
            <img
              src="@/assets/images/plosp.png"
              alt=""
              style="margin-left: 2px; width: 20px; cursor: pointer"
              @click="getFromPlosp(form.doi)"
            />
          </el-tooltip>
        </el-form-item>

        <el-form-item label="PMID" prop="pmid">
          <el-input v-model="form.pmid" />
        </el-form-item>

        <el-form-item label="Reference" prop="reference">
          <el-input
            v-model="form.reference"
            type="textarea"
            rows="3"
            style="width: 500px"
          />
        </el-form-item>

        <el-form-item label="Sort" prop="sort" required>
          <el-input v-model="form.sort" type="number" min="1" max="99999" />
        </el-form-item>

        <el-form-item label="Status" prop="status" required>
          <el-radio-group v-model="form.status" class="ml-4">
            <el-radio label="enable">Enable</el-radio>
            <el-radio label="disable">Disable</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="editPublish">Confirm</el-button>
          <el-button @click="displayDialog = false">Cancel</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="previewDialog"
      title="Preview"
      class="preview-dialog radius-14"
    >
      <div class="d-flex preview">
        <div class="w-100">
          <span class="title">Journal</span>
          <span class="content" v-text="plospInfo.publication"></span>
        </div>
        <div class="w-100">
          <span class="title">DOI</span>
          <span class="content" v-text="plospInfo.doi"></span>
        </div>
        <div class="w-100">
          <span class="title">PMID</span>
          <span class="content" v-text="plospInfo.pmid"></span>
        </div>
        <div class="w-100">
          <span class="title">Title</span>
          <span class="content" v-text="plospInfo.articleName"></span>
        </div>
        <div class="w-100">
          <span class="title">Reference</span>
          <span class="content" v-text="plospInfo.reference"></span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <div class="text-align-center">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="fillIn"
              >Fill In</el-button
            >
            <el-button
              round
              class="btn-primary btn btn-round"
              @click="previewDialog = false"
              >Cancel</el-button
            >
          </div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    addPublish,
    deletePublish,
    getPubInfoFromPlosp,
    listPublish,
    updatePublish,
  } from '@/api/publish';
  import { createAccessToken } from '@/api/login';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  /** 响应式数据 */
  const data = reactive({
    addType: true,
    displayDialog: false,
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 20,
      createEmail: '',
      orderByColumn: 'sort',
      isAsc: 'descending',
    },
    form: {
      status: 'enable',
      sort: 1,
    },
    rules: {
      articleName: [
        {
          required: true,
          message: 'Please input title',
          trigger: 'blur',
        },
      ],
      publication: [
        {
          required: true,
          message: 'Please input publication',
          trigger: 'blur',
        },
      ],
      typeId: [
        {
          required: true,
          message: 'Please input type Id',
          trigger: 'blur',
        },
      ],
      doi: [
        {
          required: true,
          message: 'Please input DOI',
          trigger: 'blur',
        },
        {
          pattern: /^10\.\d{4,9}\/[-._;()\/:A-Z0-9]+$/i,
          message: 'DOI format is incorrect',
          trigger: 'blur',
        },
      ],
      pmid: [
        {
          pattern: /\b\d{8,9}\b/,
          message: 'PMID format is incorrect',
          trigger: 'blur',
        },
      ],
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const {
    tableData,
    total,
    queryParams,
    dateRange,
    loading,
    form,
    defaultSort,
    addType,
    rules,
    displayDialog,
  } = toRefs(data);

  onMounted(() => {
    getDataList();
  });

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listPublish(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function handleExport() {
    if (tableData.value.length === 0) {
      proxy.$modal.msgError('No data to export');
      return;
    }
    proxy.download(
      '/system/publish/export',
      {},
      `publish_${new Date().getTime()}.xlsx`,
    );
  }

  /** 提交 */
  function editPublish() {
    proxy.$refs['publishEditRef'].validate(valid => {
      if (valid) {
        if (form.value.id !== undefined) {
          updatePublish(form.value).then(() => {
            proxy.$modal.msgSuccess('Modified successfully');
            displayDialog.value = false;
            getDataList();
          });
        } else {
          addPublish(form.value).then(() => {
            proxy.$modal.msgSuccess('Add successfully');
            displayDialog.value = false;
            getDataList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(id) {
    proxy.$modal
      .confirm(
        'Are you sure to delete the data item with publish id "' + id + '"',
      )
      .then(function () {
        return deletePublish(id);
      })
      .then(() => {
        getDataList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }

  function handleAdd() {
    form.value = {
      status: 'enable',
      priority: '1',
    };
    addType.value = true;
    displayDialog.value = true;
    proxy.$refs['publishEditRef'].resetFields();
  }

  function handleEdit(row) {
    addType.value = false;
    form.value = JSON.parse(JSON.stringify(row));
    displayDialog.value = true;
    proxy.$refs['publishEditRef'].resetFields();
  }

  function resetQuery() {
    proxy.resetForm('searchForm');
    dateRange.value = [];
    getDataList();
  }

  function showDetail(row) {
    // 预先生成access_token
    createAccessToken({ memberId: row.creator }).then(response => {
      const token = response.data;
      let href = `${import.meta.env.VITE_APP_WEB_URL}/${row.type}/detail/${
        row.typeId
      }?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  let previewDialog = ref(false);

  let plospInfo = ref({
    id: undefined, // ID
    publication: undefined, // Journal
    doi: undefined,
    pmid: undefined,
    articleName: undefined, // Title
    reference: undefined,
  });

  function getFromPlosp(doi) {
    if (isStrBlank(doi) || !/10\.\d{4,}\/\S+/.test(doi)) {
      proxy.$modal.msgError('Please enter correct DOI!');
      return;
    }

    // 从PLOSP获取数据
    proxy.$modal.loading('Loading...');
    getPubInfoFromPlosp(doi)
      .then(response => {
        if (!response.data) {
          proxy.$modal.alertError('Publication information not found!');
          return;
        }
        plospInfo.value = response.data;
        previewDialog.value = true;
      })
      .catch(e => {
        proxy.$modal.alertError(e);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function fillIn() {
    proxy.$modal.msgSuccess('Publication filled successfully!');

    form.value.publication = plospInfo.value.publication;
    form.value.pmid = plospInfo.value.pmid;
    form.value.doi = plospInfo.value.doi;
    form.value.articleName = plospInfo.value.articleName;
    form.value.reference = plospInfo.value.reference;

    previewDialog.value = false;
  }
</script>

<style lang="scss" scoped>
  .el-dialog {
    .el-input,
    .el-select {
      width: 500px;
    }
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper.is-dark {
    max-width: 350px !important;
  }
</style>
