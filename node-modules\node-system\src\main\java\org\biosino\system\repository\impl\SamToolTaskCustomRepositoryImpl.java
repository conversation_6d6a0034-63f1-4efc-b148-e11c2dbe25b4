package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.SamToolTaskQueryDTO;
import org.biosino.system.repository.SamToolTaskCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;

@RequiredArgsConstructor
public class SamToolTaskCustomRepositoryImpl implements SamToolTaskCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<SamToolTask> findPage(SamToolTaskQueryDTO queryDTO) {
        Query query = getSamToolQuery(queryDTO);
        // 查询数据量
        long total = mongoTemplate.count(query, SamToolTask.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<SamToolTask> content = mongoTemplate.find(query, SamToolTask.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getSamToolQuery(SamToolTaskQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (CollUtil.isNotEmpty(queryDTO.getDataNos())) {
            criteriaList.add(Criteria.where("data_no").in(queryDTO.getDataNos()));
        }
        if (StrUtil.isNotBlank(queryDTO.getStatus())) {
            criteriaList.add(Criteria.where("status").is(queryDTO.getStatus()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getExpNos())) {
            criteriaList.add(Criteria.where("exp_no").in(queryDTO.getExpNos()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getSapNos())) {
            criteriaList.add(Criteria.where("sap_no").in(queryDTO.getSapNos()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getAnalNos())) {
            criteriaList.add(Criteria.where("anal_no").in(queryDTO.getAnalNos()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getSubNos())) {
            criteriaList.add(Criteria.where("sub_no").in(queryDTO.getSubNos()));
        }
        if (queryDTO.getPriority() != null) {
            criteriaList.add(Criteria.where("priority").is(queryDTO.getPriority()));
        }
        if (queryDTO.getExitCode() != null) {
            criteriaList.add(Criteria.where("exit_code").is(queryDTO.getExitCode()));
        }
        if (StrUtil.isNotBlank(queryDTO.getFailCause())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getFailCause() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("fail_cause").regex(pattern));
        }
        Query query;
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        } else {
            query = new Query();
        }
        return query;
    }

    @Override
    public void updatePriority(Collection<String> dataNos, Integer priority) {
        Query query = new Query();
        query.addCriteria(Criteria.where("data_no").in(dataNos));
        query.addCriteria(Criteria.where("status").in(FastQCTaskStatusEnum.listWaitStatus()));
        Update update = new Update();
        update.set("priority", priority);
        mongoTemplate.updateMulti(query, update, SamToolTask.class);
    }

    @Override
    public void retryTask(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("data_no").in(dataNos));
        query.addCriteria(Criteria.where("status").is(FastQCTaskStatusEnum.failed.name()));
        Update update = new Update();
        update.unset("fail_cause");
        update.unset("use_time");
        update.unset("exit_code");
        update.set("status", FastQCTaskStatusEnum.ready.name());
        mongoTemplate.updateMulti(query, update, SamToolTask.class);
    }

    @Override
    public MongoPagingIterator<SamToolTask> getPagingIterator(SamToolTaskQueryDTO queryDTO) {
        Query query = getSamToolQuery(queryDTO);
        return new MongoPagingIterator<>(mongoTemplate, SamToolTask.class, query, 5000);
    }
}
