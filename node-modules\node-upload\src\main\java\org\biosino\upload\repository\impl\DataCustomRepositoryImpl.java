package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.ArchiveEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.DataQuery;
import org.biosino.upload.dto.SelectQueryDTO;
import org.biosino.upload.repository.DataCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR> Li
 * @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    private final static String TEMP_DATA = "temp_data.";
    private final static String TEMP_FIELD = "temp_data";

    @Override
    public PageImpl<Data> findDataPage(DataQuery queryVO) {
        List<Criteria> criteriaList = new ArrayList<>();

        // 导出操作：根据ID查询
        if (CollUtil.isNotEmpty(queryVO.getIds())) {
            criteriaList.add(Criteria.where("id").in(queryVO.getIds()));
        }

        if (StrUtil.isNotBlank(queryVO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryVO.getCreator()));
        }
        criteriaList.add(Criteria.where(TEMP_FIELD).exists(false));

        if (CollUtil.isNotEmpty(queryVO.getSecurities())) {
            criteriaList.add(Criteria.where("security").in(queryVO.getSecurities()));
        } else {
            criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        }

        // 过滤是否已归档数据
        if (StrUtil.isNotBlank(queryVO.getArchived())) {
            criteriaList.add(Criteria.where("archived").is(queryVO.getArchived()));
        }

        // 查询已归档的 analysis data数据
        if (ObjectUtil.isNotEmpty(queryVO.getExistAnalysisNo())) {
            criteriaList.add(Criteria.where("anal_no").exists(queryVO.getExistAnalysisNo()));
        }
        // 查询已归档的 查询raw data数据
        if (ObjectUtil.isNotEmpty(queryVO.getExistRunNo())) {
            criteriaList.add(Criteria.where("run_no").exists(queryVO.getExistRunNo()));
        }

        // 查询文件名称
        if (StrUtil.isNotBlank(queryVO.getName())) {
            criteriaList.add(Criteria.where("name").regex(".*?" + queryVO.getName() + ".*?", "i"));
        }

        // 查询文件提交时间
        if (ObjectUtil.isNotEmpty(queryVO.getBeginTime()) && ObjectUtil.isNotEmpty(queryVO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryVO.getBeginTime())).lte(DateUtil.endOfDay(queryVO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryVO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryVO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryVO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryVO.getEndTime())));
        }

        // 加入分页
        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = queryVO.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, Data.class);
        query.with(pageable);

        // 添加排序
        query.with(Sort.by(Sort.Direction.DESC, "archived"));

        List<Data> content = mongoTemplate.find(query, Data.class);
        return new PageImpl<>(content, queryVO.getPageable(), total);
    }

    @Override
    public PageImpl<Data> findTempDataPage(DataQuery queryVO) {
        List<Criteria> criteriaList = new ArrayList<>();

        // 导出操作：根据ID查询
        if (CollUtil.isNotEmpty(queryVO.getIds())) {
            criteriaList.add(Criteria.where("id").in(queryVO.getIds()));
        }

        if (StrUtil.isNotBlank(queryVO.getCreator())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "creator").is(queryVO.getCreator()));
        }
        criteriaList.add(Criteria.where(TEMP_FIELD).exists(true));

        if (CollUtil.isNotEmpty(queryVO.getSecurities())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "security").in(queryVO.getSecurities()));
        } else {
            criteriaList.add(Criteria.where(TEMP_DATA + "security").in(SecurityEnum.includeAllSecurity()));
        }

        // 查询属于当前submission 或 未进行任何归档的数据
        if (StrUtil.isNotBlank(queryVO.getSubNo())) {
            criteriaList.add(new Criteria().orOperator(Criteria.where(TEMP_DATA + "sub_no").is(queryVO.getSubNo()),
                    Criteria.where(TEMP_DATA + "sub_no").exists(false)));
        } else {
            criteriaList.add(Criteria.where(TEMP_DATA + "sub_no").exists(false));
        }

        // 过滤是否已归档数据
        if (StrUtil.isNotBlank(queryVO.getArchived())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "archived").is(queryVO.getArchived()));
        }

        // 查询已归档的 analysis data数据
        if (ObjectUtil.isNotEmpty(queryVO.getExistAnalysisNo())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "anal_no").exists(queryVO.getExistAnalysisNo()));
        }
        // 查询已归档的 查询raw data数据
        if (ObjectUtil.isNotEmpty(queryVO.getExistRunNo())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "run_no").exists(queryVO.getExistRunNo()));
        }

        // 过滤出包含 analysis data数据和未归档的数据
        if (queryVO.getRawData() != null) {
            if (queryVO.getRawData()) {
                criteriaList.add(new Criteria().orOperator(Criteria.where(TEMP_DATA + "run_no").exists(true).and(TEMP_DATA + "anal_no").exists(false),
                        Criteria.where(TEMP_DATA + "run_no").exists(false).and(TEMP_DATA + "anal_no").exists(false)));
            } else {
                criteriaList.add(new Criteria().orOperator(Criteria.where(TEMP_DATA + "run_no").exists(false).and(TEMP_DATA + "anal_no").exists(true),
                        Criteria.where(TEMP_DATA + "run_no").exists(false).and(TEMP_DATA + "anal_no").exists(false)));
            }
        } else {
            criteriaList.add(Criteria.where(TEMP_DATA + "run_no").exists(false).and(TEMP_DATA + "anal_no").exists(false));
        }

        // 查询文件名称
        if (StrUtil.isNotBlank(queryVO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryVO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where(TEMP_DATA + "name").regex(pattern),
                    Criteria.where(TEMP_DATA + "dat_no").regex(pattern),
                    Criteria.where(TEMP_DATA + "md5").is(queryVO.getName())));
        }

        // 查询文件提交时间
        if (ObjectUtil.isNotEmpty(queryVO.getBeginTime()) && ObjectUtil.isNotEmpty(queryVO.getEndTime())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "submission_date").gte(DateUtil.beginOfDay(queryVO.getBeginTime())).lte(DateUtil.endOfDay(queryVO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryVO.getBeginTime())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "submission_date").gte(DateUtil.beginOfDay(queryVO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryVO.getEndTime())) {
            criteriaList.add(Criteria.where(TEMP_DATA + "submission_date").lte(DateUtil.endOfDay(queryVO.getEndTime())));
        }

        // 加入分页
        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = queryVO.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, Data.class);
        query.with(pageable);

        // 添加排序
        query.with(Sort.by(Sort.Direction.DESC, TEMP_DATA + "archived"));

        List<Data> content = mongoTemplate.find(query, Data.class);
        return new PageImpl<>(content, queryVO.getPageable(), total);
    }

    @Override
    public Page<Data> getAccessableDataPage(SelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("creator").is(queryDTO.getCreator())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity());
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("dat_no").regex(pattern), Criteria.where("name").regex(pattern));
        }
        Query query = new Query(criteria);
        long total = mongoTemplate.count(query, Data.class);
        query.with(queryDTO.getPageable());
        List<Data> content = mongoTemplate.find(query, Data.class);
        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public Data getNoOpenDataByDatNo(String datNo) {
        return mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(datNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.notPublicSecurity())), Data.class);
    }

    @Override
    public List<Data> getNoOpenDataByDatNos(Collection<String> datNos) {
        return mongoTemplate.find(Query.query(Criteria.where("dat_no").in(datNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.notPublicSecurity())), Data.class);
    }

    @Override
    public Data findByNo(String datNo) {
        return mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(datNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Data.class);
    }

    @Override
    public Data findTempByNo(String datNo) {
        return mongoTemplate.findOne(Query.query(Criteria.where(tempKey("dat_no")).is(datNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Data.class);
    }

    @Override
    public List<Data> findAllTempByDataNoIn(Collection<String> datNos) {
        return mongoTemplate.find(Query.query(Criteria.where(tempKey("dat_no")).in(datNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Data.class);
    }

    @Override
    public List<Data> findAllByNos(Collection<String> dataNos, String creator) {
        return mongoTemplate.find(Query.query(Criteria.where("dat_no").in(dataNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.notPublicSecurity())), Data.class);
    }

    @Override
    public List<Data> findAllPrivateByDataNoIn(Collection<String> dataNos, String creator) {
        return mongoTemplate.find(Query.query(Criteria.where("dat_no").in(dataNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum._private.getDesc())), Data.class);
    }

    @Override
    public List<Data> findAllByDataNoIn(Collection<String> dataNos, String creator) {
        return mongoTemplate.find(Query.query(Criteria.where("dat_no").in(dataNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
    }

    @Override
    public boolean existsUserData(String datNo, String creator) {
        return mongoTemplate.exists(Query.query(Criteria.where("dat_no").is(datNo)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
    }

    @Override
    public List<Data> findTempDetailByAnalNo(String analNo) {
        Query query = Query.query(Criteria.where(tempKey("anal_no")).is(analNo)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findTempByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return new ArrayList<>();
        }
        Query query = Query.query(Criteria.where(tempKey("run_no")).in(runNos)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public void updateToDeleteAllByDatNoIn(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        dataNos.forEach(this::updateToDeleteByDataNo);
    }

    public void updateToDeleteByDataNo(String dataNo) {
        Data data = mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(dataNo)
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
        if (data == null) {
            return;
        }
        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), data.getSecurity())) {
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("dat_no").is(dataNo));
        Update update = new Update();
        update.set("security", data.getSecurity() + "_Delete");
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }

        mongoTemplate.updateFirst(query, update, Data.class);
    }

    @Override
    public List<Data> findAllByAnalysisNo(String analNo) {
        Query query = Query.query(Criteria.where("anal_no").is(analNo)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findTempByAnalysisNo(String analNo) {
        Query query = Query.query(Criteria.where(tempKey("anal_no")).is(analNo)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public Optional<Data> findByDatNo(String datNo) {
        if (StrUtil.isBlank(datNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(new Criteria().orOperator(
                Criteria.where("dat_no").is(datNo),
                Criteria.where("used_ids").in(datNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Data data = mongoTemplate.findOne(query, Data.class);
        return Optional.ofNullable(data);
    }

    @Override
    public List<Data> findAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("run_no").in(runNos));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public Optional<Data> findFirstUnarchivedByMd5(String md5, String creator) {
        if (StrUtil.isBlank(md5) || StrUtil.isBlank(creator)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("md5").is(md5));
        condition.add(Criteria.where("creator").is(creator));
        condition.add(Criteria.where("archived").is(ArchiveEnum.no.name()));
        Query query = new Query(new Criteria().andOperator(condition));
        Data data = mongoTemplate.findOne(query, Data.class);
        return Optional.ofNullable(data);
    }

    @Override
    public long countUnarchivedByCreator(String memberId) {
        if (StrUtil.isBlank(memberId)) {
            return 0;
        }
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where(TEMP_FIELD).exists(true));
        criteriaList.add(Criteria.where(TEMP_DATA + "archived").is(ArchiveEnum.no.name()));
        criteriaList.add(Criteria.where(TEMP_DATA + "creator").is(memberId));
        criteriaList.add(Criteria.where(TEMP_DATA + "security").in(SecurityEnum.includeAllSecurity()));
        Query query = new Query(new Criteria().andOperator(criteriaList));
        return mongoTemplate.count(query, Data.class);
    }

    @Override
    public boolean existsByAnalNo(String analysisNo) {
        if (StrUtil.isBlank(analysisNo)) {
            return false;
        }
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(new Criteria().orOperator(
                Criteria.where("anal_no").is(analysisNo),
                Criteria.where(TEMP_DATA + "anal_no").is(analysisNo)
        ));
        criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        Query query = new Query(new Criteria().andOperator(criteriaList));
        return mongoTemplate.exists(query, Data.class);
    }

    @Override
    public List<Data> findByTempDataRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return new ArrayList<>();
        }
        return mongoTemplate.find(Query.query(Criteria.where(tempKey("run_no")).in(runNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Data.class);
    }
}
