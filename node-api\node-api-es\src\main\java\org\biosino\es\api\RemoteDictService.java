package org.biosino.es.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.biosino.es.api.factory.RemoteDictFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * ES字典检索服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDictService", value = ServiceNameConstants.NODE_ES_SERVICE, fallbackFactory = RemoteDictFallbackFactory.class)
public interface RemoteDictService {

    /**
     * 根据Taxonomy name查询tax Id
     *
     * @param name 检索关键字
     */
    @GetMapping("/getTaxIdByName")
    R<List<String>> getTaxIdByName(@RequestParam("name") String name, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询platform是否存在
     */
    @GetMapping("/existPlatform")
    R<Boolean> existPlatform(@RequestParam("name") String name, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询Disease是否存在
     */
    @GetMapping("/existDisease")
    R<Boolean> existDisease(@RequestParam("name") String name, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询Organism是否存在
     */
    /*@GetMapping("/findTaxIdByExistName")
    R<String> findTaxIdByExistName(@RequestParam("name") String name, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);*/

    /**
     * 通过taxId查询taxonomy
     */
    @GetMapping("/getTaxByTaxId")
    R<List<TaxonomyNodeDTO>> getTaxByTaxId(@RequestParam("taxId") String taxId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 判断biome是否存在
     */
    @GetMapping("/existBiome")
    R<Boolean> existBiome(@RequestParam("type") String type, @RequestParam("value") String value, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
