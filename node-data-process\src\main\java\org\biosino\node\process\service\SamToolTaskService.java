package org.biosino.node.process.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.node.process.config.NodeMongoConfig;
import org.biosino.node.process.enums.ArchiveEnum;
import org.biosino.node.process.enums.SecurityEnum;
import org.biosino.node.process.iterator.MongoPagingIterator;
import org.biosino.node.process.service.common.ConversionCommon;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SamToolTaskService {
    @Resource
    @Qualifier(NodeMongoConfig.MONGO_TEMPLATE)
    private MongoTemplate mongoTemplate;

    public void process() {

        mongoTemplate.dropCollection(SamToolTask.class);
        int pageSize = 5000;
        int pageNum = 0;
        int totalData = 0;
        int totalTask = 0;
        MongoPagingIterator<Data> iterator = new MongoPagingIterator<>(mongoTemplate, Data.class, pageSize);

        ArrayList<SamToolTask> list = new ArrayList<>();

        while (iterator.hasNext()) {
            List<Data> page = iterator.next();

            List<String> runNos = page.stream().map(Data::getRunNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

            List<Run> runs = mongoTemplate.find(Query.query(Criteria.where("run_no").in(runNos)), Run.class);

            Map<String, Run> runNoToRunMap = runs.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

            for (Data data : page) {
                if (StrUtil.endWithAnyIgnoreCase(data.getFileName(),
                        ".bam", ".sam")
                        && CollUtil.contains(SecurityEnum.includeAllSecurity(), data.getSecurity())
                        && StrUtil.equals(ArchiveEnum.yes.name(), data.getArchived())) {
                    SamToolTask item = new SamToolTask();
                    item.setPriority(1);
                    // id升级
                    item.setDataNo(ConversionCommon.getNewNo(data.getDatNo()));
                    item.setDataFileName(data.getFileName());
                    item.setDataFilePath(data.getFilePath());
                    item.setDataFileSize(data.getFileSize());
                    item.setDataCreateDate(data.getCreateDate());
                    item.setCreateDate(new Date());
                    item.setStatus("ready");

                    // 看对应的任务的expNo、sapNo是否需要补充
                    if (data.getRunNo() != null && runNoToRunMap.containsKey(data.getRunNo())) {
                        Run run = runNoToRunMap.get(data.getRunNo());
                        item.setExpNo(run.getExpNo());
                        item.setSapNo(run.getSapNo());
                    }
                    if (data.getAnalNo() != null) {
                        item.setAnalNo(data.getAnalNo());
                    }
                    item.setSubNo(data.getSubNo());

                    list.add(item);
                }
            }
            log.info("当前页：{}，Data条数：{}，SamToolTask条数：{}", pageNum, page.size(), list.size());
            pageNum++;
            totalData += page.size();
            totalTask += list.size();
            if (CollUtil.isNotEmpty(list)) {
                mongoTemplate.insertAll(list);
                list.clear();
            }
        }
        log.info("总Data条数：{}，总SamToolTask条数：{}", totalData, totalTask);
    }
}
