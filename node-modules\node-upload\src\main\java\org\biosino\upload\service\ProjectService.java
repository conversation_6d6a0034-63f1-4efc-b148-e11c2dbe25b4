package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import org.biosino.common.core.domain.Select;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.ProjectDTO;
import org.biosino.upload.dto.SelectOption;
import org.biosino.upload.dto.mapper.ProjectDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ProjectVO;
import org.biosino.upload.vo.PublishVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectService extends BaseService {
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private ExperimentRepository experimentRepository;
    @Autowired
    private RunRepository runRepository;
    @Autowired
    private SampleRepository sampleRepository;
    @Autowired
    private DataRepository dataRepository;
    @Autowired
    private PublishRepository publishRepository;
    @Autowired
    private MessageSender messageSender;

    public ProjectVO getProjectByNo(String projNo) {
        if (projNo == null) {
            throw new ServiceException("Project ID cannot be empty");
        }
        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("Not found data"));

        ProjectVO result = new ProjectVO();
        if (project.getAudited().equals(AuditEnum.audited.name()) && project.getTempData() == null) {
            ProjectDTOMapper.INSTANCE.copyToVo(project, result);
            List<PublishVO> publishVo = getPublishVO(AuthorizeType.project, projNo);
            result.setPublish(publishVo);
        } else {
            ProjectDTOMapper.INSTANCE.copyToVo(project.getTempData(), result);
            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.project, projNo);
            result.setPublish(publishVo);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public ProjectVO save(ProjectDTO projectDTO) {
        String subNo = projectDTO.getSubNo();

        Submission submission = getEditSubmissionByNo(subNo);
        String projNo = submission.getProjNo();

        // 校验项目名称
        Project existProj = projectRepository.validateProjectName(SecurityUtils.getMemberId(), projNo, projectDTO.getName());
        if (existProj != null && existProj.getTempData() != null) {
            throw new ServiceException("This project name already exists in submission: " + existProj.getTempData().getSubNo());
        }

        // 新增项目
        Project project;
        if (projNo == null) {
            project = new Project();
            ProjectDTOMapper.INSTANCE.copyToDb(projectDTO, project);
            // 临时ID
            project.setId(IdUtil.objectId());
            project.setSubNo(subNo);
            project.setProjectNo(IdUtil.fastSimpleUUID());
            project.setCreateDate(new Date());
            project.setUpdateDate(new Date());
            project.setCreator(SecurityUtils.getMemberId());
            project.setOwnership(OwnershipEnum.self_support.getDesc());
            project.setSubmitter(submission.getSubmitter());
            project.setHitNum(0L);
            project.setAudited(AuditEnum.init.name());
            project.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());

            // 保存文献(会抛出异常，所以要在其他save前执行)
            savePublish(projectDTO.getPublish(), AuthorizeType.project, project.getProjectNo());

            submission.setProjNo(project.getProjectNo());
            saveEditSubmission(submission);
        } else {
            // 编辑project
            project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("Not found data"));
            ProjectDTOMapper.INSTANCE.copyToDb(projectDTO, project);
            project.setUpdateDate(new Date());

            // 保存文献
            savePublish(projectDTO.getPublish(), AuthorizeType.project, project.getProjectNo());
        }

        project.setRelatedLinks(NodeUtils.cleanRelatedLinks(project.getRelatedLinks()));
        project.setTempData(ProjectDTOMapper.INSTANCE.copy(project));

        projectRepository.save(project);
        return getProjectByNo(project.getProjectNo());
    }

    public List<Select> getProjectList() {
        List<Project> projects = projectRepository.findAllByCreator(SecurityUtils.getMemberId());
        if (CollUtil.isEmpty(projects)) {
            return null;
        }
        List<Select> selectList = new ArrayList<>();
        for (Project project : projects) {
            Select select = new Select();
            select.setValue(project.getProjectNo());
            if (project.getProjectNo().startsWith("OEP")) {
                select.setLabel(project.getProjectNo() + " (" + project.getName() + ")");
            } else {
                select.setLabel(project.getName());
            }
            selectList.add(select);
        }
        return selectList;
    }

    public String validateProjectName(String projNo, String name) {
        if (StringUtil.isBlank(name)) {
            return "The project name cannot be blank";
        }
        Project existProj = projectRepository.validateProjectName(SecurityUtils.getMemberId(), projNo, name);
        if (existProj != null && existProj.getTempData() != null) {
            return "This project name already exists in submission: " + existProj.getTempData().getSubNo();
        }
        return null;
    }

    public Page<SelectOption> getProjectOptionsByPage(ArchivedSelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        Page<Project> page = projectRepository.findAllByPage(queryDTO);
        return page.map(project -> {
            SelectOption option = new SelectOption();
            option.setValue(project.getProjectNo());
            if (project.getProjectNo().startsWith(SequenceType.PROJECT.getPrefix())) {
                option.setLabel(project.getProjectNo() + " (" + project.getName() + ")");
            } else {
                option.setLabel(project.getName());
            }
            return option;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public List<DeleteErrorMsgVO> delete(String subNo) {

        if (StrUtil.isBlank(subNo)) {
            throw new ServiceException("The request parameter is illegal");
        }

        Submission submission = getEditSubmissionByNo(subNo);
        String projNo = submission.getProjNo();

        if (projNo == null) {
            throw new ServiceException("No project found for deletion");
        }

        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("Not found data"));

        List<DeleteErrorMsgVO> vos = new ArrayList<>();

        // 查询在experiment中是否被占用
        List<Experiment> experiments = experimentRepository.findTempByProjNo(projNo);

        if (CollUtil.isNotEmpty(experiments)) {

            for (Experiment experiment : experiments) {

                Experiment tempData = experiment.getTempData();

                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();

                vo.setTarget(project.getName());

                vo.setName(tempData.getName());
                vo.setType(AuthorizeType.experiment.name());

                String tempSubNo = tempData.getSubNo();
                vo.setSubNo(tempSubNo.equals(subNo) ? tempSubNo + " (Current Submission)" : tempSubNo);

                vo.setNo(AuditEnum.init.name().equals(experiment.getAudited()) ? "Unassigned formal ID" : tempData.getExpNo());

                vos.add(vo);
            }

            return vos;
        }

        // 未分配正式ID的Project直接删除
        if (AuditEnum.init.name().equals(project.getAudited())) {
            projectRepository.delete(project);
        } else {
            // 有正式ID的，回滚temp
            project.setTempData(null);
            projectRepository.save(project);
        }

        submission.setProjNo(null);
        saveEditSubmission(submission);

        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public ProjectVO saveEdit(ProjectDTO projectDTO) {
        String subNo = projectDTO.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);

        String projNo = projectDTO.getProjectNo();
        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("Not found data"));

        if (!project.getVisibleStatus().equals(VisibleStatusEnum.Unaccessible.name())) {
            throw new ServiceException("No editing allowed!");
        }

        Project tempData;
        if (project.getTempData() == null) {
            tempData = ProjectDTOMapper.INSTANCE.copy(project);
        } else {
            tempData = project.getTempData();
        }

        // 将数据拷贝到project中
        ProjectDTOMapper.INSTANCE.copyToDb(projectDTO, tempData);

        tempData.setSubmitter(submission.getSubmitter());
        tempData.setRelatedLinks(NodeUtils.cleanRelatedLinks(tempData.getRelatedLinks()));
        tempData.setUpdateDate(new Date());
        tempData.setAudited(AuditEnum.unaudited.name());
        project.setTempData(tempData);

        saveEditPublish(projectDTO.getPublish(), AuthorizeType.project, project.getProjectNo());

        projectRepository.save(project);

        submission.setProjNo(project.getProjectNo());
        saveEditSubmission(submission);
        return getProjectByNo(project.getProjectNo());
    }

    public DeleteCheckResultVO deleteCheck(String projNo, String memberId, boolean validateShare) {
        // 找到这个project
        Project project = projectRepository.findTopByProjectNo(projNo).orElseThrow(() -> new ServiceException("No Project found"));
        if (!StrUtil.equals(project.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }
        // 找到这个project下面的experiment
        List<Experiment> expList = experimentRepository.findAllByProjectNo(projNo);
        // 根据exp_no查询关联的Run
        List<String> expNos = expList.stream().map(Experiment::getExpNo).distinct().collect(Collectors.toList());
        List<Run> runList = runRepository.findAllByExpNoIn(expNos);

        // 过滤出sap_no查询相关的Sample 简要信息
        List<String> sapNos = runList.stream().map(Run::getSapNo).distinct().collect(Collectors.toList());

        List<Sample> sampleList = sampleRepository.findAllBySapNoIn(sapNos);

        List<String> runNos = runList.stream().map(Run::getRunNo).distinct().collect(Collectors.toList());

        List<Data> dataList = dataRepository.findAllByRunNoIn(runNos);
        List<String> dataNos = dataList.stream().map(Data::getDatNo).distinct().collect(Collectors.toList());

        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (project.getTempData() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(projNo);
            vo.setNo(project.getTempData().getProjectNo());
            vo.setType(AuthorizeType.project.name());
            vo.setName(project.getTempData().getName());
            vo.setSubNo(project.getTempData().getSubNo());
            errors.add(vo);
        }

        // 遍历expList
        for (Experiment experiment : expList) {
            // 如果有tempData，证明数据被编辑且未被审核通过，不允许被删除
            if (experiment.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(projNo);
                vo.setNo(experiment.getTempData().getExpNo());
                vo.setType(AuthorizeType.experiment.name());
                vo.setName(experiment.getTempData().getName());
                vo.setSubNo(experiment.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        // 看有没有其他的Experiment指向这个projNo
        List<Experiment> tempExpList = experimentRepository.findTempByProjNo(projNo);
        for (Experiment experiment : tempExpList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(projNo);
            vo.setNo(experiment.getTempData().getExpNo());
            vo.setType(AuthorizeType.experiment.name());
            vo.setName(experiment.getTempData().getName());
            vo.setSubNo(experiment.getTempData().getSubNo());
            errors.add(vo);
        }

        for (Run run : runList) {
            if (run.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(projNo);
                vo.setNo(run.getTempData().getRunNo());
                vo.setType(AuthorizeType.run.name());
                vo.setName(run.getTempData().getName());
                vo.setSubNo(run.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        // 在tempData中查询和ExpNos相关联的
        List<Run> tempRunList = runRepository.findTempByExpNoIn(expNos);
        for (Run run : tempRunList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(projNo);
            vo.setNo(run.getTempData().getRunNo());
            vo.setType(AuthorizeType.run.name());
            vo.setName(run.getTempData().getName());
            vo.setSubNo(run.getTempData().getSubNo());
            errors.add(vo);
        }

        for (Sample sample : sampleList) {
            if (sample.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(projNo);
                vo.setNo(sample.getTempData().getSapNo());
                vo.setType(AuthorizeType.sample.name());
                vo.setName(sample.getTempData().getName());
                vo.setSubNo(sample.getTempData().getSubNo());
                errors.add(vo);
            }
        }
        List<Sample> tempSampleList = sampleRepository.findTempBySapNoIn(sapNos);
        for (Sample sample : tempSampleList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(projNo);
            vo.setNo(sample.getTempData().getSapNo());
            vo.setType(AuthorizeType.sample.name());
            vo.setName(sample.getTempData().getName());
            vo.setSubNo(sample.getTempData().getSubNo());
            errors.add(vo);
        }
        for (Data data : dataList) {
            if (data.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(projNo);
                vo.setNo(data.getTempData().getDatNo());
                vo.setType(AuthorizeType.data.name());
                vo.setName(data.getTempData().getName());
                vo.setSubNo(data.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        Map<String, String> runNoSapNoMap = runRepository.findDetailBySapNoIn(sapNos).stream().collect(Collectors.toMap(Run::getRunNo, Run::getSapNo, (existingValue, newValue) -> existingValue));
        runNoSapNoMap.forEach((k, v) -> {
            // 如果sapNo被其他的run用了，删除这个sapNo
            if (!CollUtil.contains(runNos, k)) {
                sapNos.remove(v);
            }
        });

        List<Data> tempDataList = dataRepository.findTempByRunNoIn(runNos);

        for (Data data : tempDataList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(projNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }

        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setProjNos(Collections.singletonList(projNo));
        resultVO.setExpNos(expNos);
        resultVO.setRunNos(runNos);
        resultVO.setSapNos(sapNos);
        resultVO.setDataNos(dataNos);
        resultVO.setErrors(new ArrayList<>(errors));
        // 是否需要校验被删除的数据在share review request里面使用
        if (validateShare) {
            validateShareAndReviewAndRequest(resultVO, memberId);
        }
        return resultVO;
    }

    public void deleteProjectAll(String projectNo, String memberId) {
        Project project = projectRepository.findTopByProjectNo(projectNo).orElseThrow(() -> new ServiceException("No Project found"));
        if (!StrUtil.equals(project.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }

        DeleteCheckResultVO checkResultVO = deleteCheck(projectNo, memberId, false);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The project cannot be deleted because it is associated with other data");
        }
        // 添加删除的日志
        addUserCenterDeleteLog(projectNo, AuthorizeType.project.name(), checkResultVO);

        // 将visible_status更新为delete
        projectRepository.updateToDeleteAllByProjectNo(checkResultVO.getProjNos());
        experimentRepository.updateToDeleteAllByExpNoIn(checkResultVO.getExpNos());
        runRepository.updateToDeleteAllByRunNoIn(checkResultVO.getRunNos());
        sampleRepository.updateToDeleteAllBySapNoIn(checkResultVO.getSapNos());
        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());

        // 删除相关联的publish
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.project.name(), checkResultVO.getProjNos());
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.experiment.name(), checkResultVO.getExpNos());
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos());

        // 通知删除es索引
        updateEsData(AuthorizeType.project.name(), project.getProjectNo());
    }
}
