package org.biosino.app.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.app.repository.DataRepository;
import org.biosino.app.service.BrowseDetailService;
import org.biosino.app.service.FastQCTaskService;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 检索明细控制层
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/browseDetail")
public class BrowseDetailController extends BaseController {
    private final BrowseDetailService browseDetailService;

    private final FastQCTaskService fastQCTaskService;

    private final DataRepository dataRepository;

    /**
     * 获取DataList
     */
    @PostMapping("/getDataList")
    public AjaxResult getDataList(@RequestBody DataListSearchVO searchVO) {
        EsPageInfo<RelatedDataDTO> dataList = browseDetailService.getDataList(searchVO);
        return AjaxResult.success(dataList);
    }

    /**
     * 前端项目详情导出data列表数据
     */
    @PostMapping("/downloadData")
    public void downloadData(DataListSearchVO searchVO, HttpServletRequest request, HttpServletResponse response) {
        browseDetailService.downloadData(searchVO, request, response);
    }

    @PostMapping("/getPublicDataList")
    public AjaxResult getPublicDataList(@RequestBody DataListSearchVO searchVO) {
        EsPageInfo<RelatedDataDTO> dataList = browseDetailService.getPublicDataList(searchVO);
        return AjaxResult.success(dataList);
    }

    /**
     * 获取FastQC信息
     */
    @PostMapping("/getDataQCInfoList")
    public TableDataInfo getDataQCInfoList(@RequestBody DataListSearchVO searchVO) {
        // 从es获取qc结果信息
        searchVO.setFastqcFinished(true);
        EsPageInfo<RelatedDataDTO> dataList = browseDetailService.getDataList(searchVO);
        if (dataList == null) {
            return new TableDataInfo();
        }
        List<String> dataNos = dataList.getList().stream().map(RelatedDataDTO::getDatNo).collect(Collectors.toList());

        Map<String, Boolean> dataNoToAccessibleMap = dataList.getList().stream().collect(Collectors.toMap(RelatedDataDTO::getDatNo, RelatedDataDTO::getAccessible, (oldValue, newValue) -> oldValue));

        Map<String, Data> dataNoToDataMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.toMap(Data::getDatNo, data -> data, (oldValue, newValue) -> oldValue));

        List<FastQCTask> list = fastQCTaskService.findAllByDataNoIn(dataNos);
        list.forEach(x -> {
            Data data = dataNoToDataMap.get(x.getDataNo());
            x.setDataFileName(data.getName());
            if (!dataNoToAccessibleMap.get(x.getDataNo())) {
                x.setSeqkitResult(null);
            }
            x.setDataSecurity(data.getSecurity());
        });
        return new TableDataInfo(list, (int) dataList.getTotal());
    }

}
