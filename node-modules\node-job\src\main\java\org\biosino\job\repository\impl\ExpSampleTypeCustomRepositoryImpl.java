package org.biosino.job.repository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.job.repository.ExpSampleTypeCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
public class ExpSampleTypeCustomRepositoryImpl implements ExpSampleTypeCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Set<String> findAllNameByType(final ExpSampleTypeEnum type) {
        final Criteria criteria = Criteria.where("type").is(type.name())
                .and("status").is(DataStatusEnum.enable.name());
        final Query query = new Query(criteria);
        query.fields().include("name");
        final List<ExpSampleType> list = mongoTemplate.find(query, getClz());
        final Set<String> names = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(list)) {
            for (ExpSampleType expSampleType : list) {
                names.add(expSampleType.getName());
            }
        }
        return names;
    }

    @Override
    public List<ExpSampleType> findByType(String type) {
        final Criteria criteria = Criteria.where("type").is(type).and("status").is(DataStatusEnum.enable.name());
        return mongoTemplate.find(new Query(criteria), ExpSampleType.class);
    }
}
