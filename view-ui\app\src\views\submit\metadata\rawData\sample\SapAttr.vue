<template>
  <el-form
    ref="sapAttrForm"
    label-position="top"
    :model="attributes"
    :inline="true"
    :rules="rules"
    :scroll-to-error="true"
    class="exist-form"
  >
    <recommend-tip
      v-if="recommendNum > 0"
      :recommend-num="recommendNum"
      :recommend-tip="recommendTipText"
    ></recommend-tip>
    <transition v-loading="!show" name="el-fade-in-linear">
      <div v-if="show" class="w-100">
        <div
          v-for="(group, idx) in attrList"
          :key="'sap-group-' + idx"
          class="bg-gray radius-12 p-15 d-flex w-100 mt-1 flex-wrap"
        >
          <el-form-item
            v-for="item in group"
            :key="item.attributesField"
            :prop="item.attributesField"
          >
            <!--字段标题-->
            <template #label>
              <!--推荐填写-->
              <recommend-icon
                v-if="item.required === 'recommend'"
              ></recommend-icon>

              <!--非推荐填写，有字段描述信息-->
              <el-popover
                v-if="item.description"
                width="500"
                :teleported="false"
              >
                <template #reference>
                  <span style="font-weight: bold">{{
                    item.attributesName
                  }}</span>
                </template>
                <span v-html="item.description"></span>
              </el-popover>

              <!--无字段描述信息-->
              <span v-else class="font-bold">{{ item.attributesName }}</span>
            </template>

            <el-input
              v-if="item.dataType === 'Input'"
              v-model="attributes[item.attributesField]"
            />

            <el-input-number
              v-if="item.dataType === 'Number_int'"
              v-model="attributes[item.attributesField]"
              :precision="0"
              class="w-100"
              :controls="false"
            />

            <el-input-number
              v-if="item.dataType === 'Number_double'"
              v-model="attributes[item.attributesField]"
              class="w-100"
              :controls="false"
            />

            <el-input
              v-if="item.dataType === 'Textarea'"
              v-model="attributes[item.attributesField]"
              :rows="2"
              type="textarea"
            />

            <el-select
              v-if="item.dataType === 'Select'"
              v-model="attributes[item.attributesField]"
              :allow-create="item.allowCreate"
              :teleported="false"
              filterable
              clearable
              placeholder="Select"
            >
              <el-option
                v-for="val in item.valueRange"
                :key="val"
                :label="val"
                :value="val"
              />
            </el-select>

            <el-date-picker
              v-if="item.dataType === 'Date'"
              v-model="attributes[item.attributesField]"
              clearable
              :teleported="false"
              class="w-100"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetime"
            />

            <!--自定义属性-->
            <el-select
              v-if="item.dataSource === 'disease_name'"
              v-model="attributes[item.attributesField]"
              class="w-100"
              filterable
              clearable
              :allow-create="item.allowCreate"
              remote
              :teleported="false"
              reserve-keyword
              placeholder="Please input search disease"
              :remote-method="querySearchAsync"
              :loading="diseaseLoading"
            >
              <el-option
                v-for="disease in diseaseOptions"
                :key="'diseaseOption-' + disease.value"
                :label="disease.label"
                :value="disease.value"
              />
            </el-select>

            <el-select
              v-if="
                item.dataSource === 'host_biome' ||
                item.dataSource === 'non_host_biome' ||
                item.dataSource === 'env_biome' ||
                item.dataSource === 'env_biome_water'
              "
              v-model="attributes[item.attributesField]"
              class="w-100"
              filterable
              clearable
              :fit-input-width="false"
              :allow-create="item.allowCreate"
              remote
              :teleported="false"
              reserve-keyword
              placeholder="Please input search"
              :remote-method="
                query => {
                  querySearchBiomeAsync(query, item.dataSource);
                }
              "
              :loading="diseaseLoading[item.dataSource]"
            >
              <el-option
                v-for="biome in biomeOptions[item.dataSource]"
                :key="
                  item.attributesField +
                  item.dataSource +
                  'Option-' +
                  biome.value
                "
                :title="biome.label"
                :label="biome.label"
                :value="biome.value"
              />
            </el-select>

            <el-select
              v-if="item.dataSource === 'Taxonomy'"
              v-model="attributes[item.attributesField]"
              class="w-100"
              filterable
              clearable
              :allow-create="item.allowCreate"
              remote
              :fit-input-width="false"
              :teleported="false"
              reserve-keyword
              placeholder="Please input search taxonomy"
              :remote-method="querySearchTaxonomyAsync"
              :loading="taxonomyLoading"
            >
              <el-option
                v-for="taxonomyItem in taxonomyOptions"
                :key="`${item.attributesField}_${item.dataSource}_taxOpt-${taxonomyItem.label} [${taxonomyItem.value}]`"
                :label="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
                :value="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
                :title="`Lineage: ${trimStr(taxonomyItem.title)}`"
              />

              <!--<el-option
                v-for="tax in taxonomyOptions"
                :key="
                  item.attributesField + item.dataSource + 'Option-' + tax.value
                "
                :label="tax.label"
                :value="tax.value"
              />-->
            </el-select>
          </el-form-item>
        </div>
      </div>
    </transition>
  </el-form>
</template>

<script setup>
  import { defineProps, getCurrentInstance, onMounted, ref, watch } from 'vue';
  import { getExpSapData } from '@/api/metadata/dict';
  import {
    findBiomeLike,
    findDiseaseLike,
    findTaxonomyLike,
  } from '@/api/search';
  import RecommendTip from '@/views/submit/metadata/rawData/common/RecommendTip.vue';
  import RecommendIcon from '@/views/submit/metadata/rawData/common/RecommendIcon.vue';
  import { trimStr } from '@/utils';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    attributes: {
      type: Object,
      default() {
        return {};
      },
    },
    sampleType: {
      type: String,
      required: true,
    },
    recommendFilledCount: {
      type: Number,
      required: true,
      default: 0,
    },
  });

  const attributes = ref(props.attributes);
  const sapType = ref(props.sampleType);
  const attrList = ref([]);

  const sapAttrForm = ref();

  const rules = ref([]);
  const show = ref(false);
  const recommendNum = ref();
  const recommendTipText = ref('');

  /** 加载当前系统所拥有的组学类型数据字典 */
  function loadExperimentData(type) {
    show.value = false;
    getExpSapData(type).then(response => {
      recommendNum.value = response.data.recommendNum;
      recommendTipText.value = response.data.recommendTip;
      attrList.value = convertValueRange(response.data.attributes);
      rules.value = generateRules(response.data.attributes);

      updateFiledNum(attributes.value);
      show.value = true;

      updateRecommendFilledCount();
    });
  }

  function convertValueRange(jsonData) {
    const groupedData = {};
    for (const item of jsonData) {
      const group = item.group;
      if (!groupedData[group]) {
        groupedData[group] = [];
      }
      groupedData[group].push(item);
    }
    return Object.values(groupedData);
  }

  /** 生成表单的rules对象*/
  function generateRules(data) {
    const rules = {};

    data.forEach(function (item) {
      const fieldName = item.attributesField;
      const attributesName = item.attributesName;

      // 如果必填
      if (item.required === 'required') {
        if (
          item.dataType === 'Input' ||
          item.dataType === 'Textarea' ||
          item.dataType === 'Number_double' ||
          item.dataType === 'Number_int'
        ) {
          rules[fieldName] = [
            {
              required: true,
              message: 'Please input ' + attributesName,
              trigger: 'blur',
            },
          ];
        } else if (
          item.dataType === 'Select' ||
          item.dataType === 'Select2' ||
          item.dataType === 'Date' ||
          item.dataType === 'Custom'
        ) {
          rules[fieldName] = [
            {
              required: true,
              message: 'Please choose ' + attributesName,
              trigger: 'change',
            },
          ];
        }
      }

      // 如果有正则校验
      if (item.valueRegex) {
        if (!rules[fieldName] || rules[fieldName].length === 0) {
          rules[fieldName] = [];
        }
        rules[fieldName].push({
          pattern: item.valueRegex,
          message: `Data format error, please refer to: ${item.valueFormat}`,
          trigger: 'blur',
        });
      }

      let target = {
        validator: proxy.$validateChinese,
        trigger: 'blur',
      };
      if (!Object.prototype.hasOwnProperty.call(rules, fieldName)) {
        rules[fieldName] = [];
      }
      rules[fieldName].push(target);
    });

    return rules;
  }

  /** 重置表单 */
  const resetForm = () => {
    proxy.resetForm('sapAttrForm');
    attributes.value = {};
  };

  const diseaseOptions = ref([]);
  const diseaseLoading = ref(false);

  /** 查询ES中的Disease */
  const querySearchAsync = query => {
    diseaseLoading.value = true;
    findDiseaseLike({ keyword: query })
      .then(response => {
        diseaseOptions.value = response.data;
      })
      .finally(() => {
        diseaseLoading.value = false;
      });
  };

  const biomeOptions = ref({});
  const biomeLoading = ref({
    host_biome: false,
    non_host_biome: false,
    env_biome: false,
    env_biome_water: false,
  });
  /** 查询ES中的Biome */
  const querySearchBiomeAsync = (query, type) => {
    biomeLoading.value[type] = true;
    findBiomeLike({ type: type, keyword: query })
      .then(response => {
        biomeOptions.value[type] = response.data;
      })
      .finally(() => {
        biomeLoading.value[type] = false;
      });
  };

  const taxonomyOptions = ref([]);
  const taxonomyLoading = ref(false);

  /** 查询ES中的Taxonomy */
  const querySearchTaxonomyAsync = query => {
    taxonomyLoading.value = true;
    findTaxonomyLike({ keyword: query })
      .then(response => {
        taxonomyOptions.value = response.data;
      })
      .finally(() => {
        taxonomyLoading.value = false;
      });
  };

  onMounted(() => {
    if (sapType.value === '') {
      return;
    }
    loadExperimentData(sapType.value);

    if (
      sapType.value === 'Human' ||
      sapType.value === 'Animalia' ||
      sapType.value === 'Cell line'
    ) {
      querySearchAsync(attributes.value?.disease_name);
    }
  });

  /** 切换实验类型 */
  watch(
    () => props.sampleType,
    newValue => {
      sapType.value = newValue;
      resetForm();
      loadExperimentData(newValue);
    },
  );

  const recommendFilledNum = ref(0);

  function updateFiledNum(newAttr) {
    // 监听表单中 推荐填写的个数
    recommendFilledNum.value = 0;
    attrList.value.forEach(group => {
      group.forEach(item => {
        if (item.required === 'recommend') {
          let attrVal = newAttr[item.attributesField];
          if (attrVal && attrVal.toString().trim() !== '') {
            recommendFilledNum.value++;
          }
        }
      });
    });
    updateRecommendFilledCount();
  }

  function updateRecommendFilledCount() {
    proxy.$emit('update:recommendFilledCount', recommendFilledNum.value);
  }

  // 监听用户输入的值，动态修改父组件的值
  watch(
    attributes,
    newAttr => {
      updateFiledNum(newAttr);
      proxy.$emit('update:attributes', newAttr);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  defineExpose({
    sapAttrForm,
  });
</script>
<style scoped>
  .exist-form {
    padding-top: 8px;
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 30px;

      .el-select {
        width: 100%;
      }
    }
  }

  :deep(.el-input__inner[type='number']) {
    text-align: left;
  }

  /*和fit-input-width="false"冲突*/
  /*:deep(.el-popper) {
    max-width: 100% !important;
  }*/
</style>
