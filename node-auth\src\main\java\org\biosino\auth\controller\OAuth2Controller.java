package org.biosino.auth.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.oauth2.OAuth2AuthenticationProvider;
import org.biosino.auth.oauth2.OAuth2AuthenticationToken;
import org.biosino.auth.oauth2.OAuth2Properties;
import org.biosino.auth.oauth2.model.OAuthUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2认证控制器
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class OAuth2Controller {

    @Autowired
    private OAuth2Properties OAuth2Properties;

    @Autowired
    private OAuth2AuthenticationProvider authenticationProvider;

    @Value("${app.client.url}")
    private String appClientUrl;


    /**
     * OAuth2回调处理
     */
    @GetMapping("/login/oauth2")
    public void callback(@RequestParam("code") String code,
                         @RequestParam(value = "state", required = false) String state,
                         HttpServletRequest request,
                         HttpServletResponse response) throws IOException {
        try {
            log.info("收到OAuth2回调, code: {}", code);

            // 1. 使用code获取access_token
            String accessToken = getOktaAccessToken(code);
            // AccessTokenResponse tokenResponse = getAccessToken(code);

            // 2. 使用access_token获取用户信息
            OAuthUser OAuthUser = getOktaUserInfo(accessToken);

            // 3. 进行认证
            OAuth2AuthenticationToken authToken = new OAuth2AuthenticationToken(
                    null, OAuthUser, accessToken, null
            );
            Authentication authentication = null;
            try {
                authentication = authenticationProvider.authenticate(authToken);
            } catch (AuthenticationException e) {
                // 重定向到bmdcRegister http://localhost:18081/bmdcRegist/binding?bioId=OAuthUser.getId()&bioEmail=OAuthUser.getEmail()
                response.sendRedirect(
                        StrUtil.format("http://localhost:18081/bmdcRegist/binding?bioId={}&bioEmail={}", OAuthUser.getId(), OAuthUser.getEmail())
                );
            } catch (Exception e) {
                throw new ServerException(e.getMessage());
            }
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 5. 重定向到前端，携带token
            String redirectUrl = appClientUrl;
            log.info("OAuth2登录成功，重定向到: {}", redirectUrl);
            response.sendRedirect(redirectUrl);

        } catch (Exception e) {
            log.error("OAuth2回调处理失败", e);
            // 重定向到前端错误页面
            response.sendRedirect(appClientUrl + "?error=oauth_callback_failed");
        }
    }


    private String getOktaAccessToken(String code) {

        // 2. 准备POST请求的表单数据. [6]
        Map<String, Object> formData = new HashMap<>();
        formData.put("grant_type", "authorization_code");
        formData.put("code", code);
        formData.put("redirect_uri", OAuth2Properties.getRedirectUri());
        // 3. 使用 HttpRequest 构建请求. [7, 8]
        HttpResponse response = HttpRequest.post(OAuth2Properties.getTokenUri())
                .basicAuth(OAuth2Properties.getClientId(), OAuth2Properties.getClientSecret())
                .form(formData)
                .timeout(20000)
                .execute();
        String body = response.body();
        JSONObject entries = JSONUtil.parseObj(body);
        return entries.getStr("access_token");
    }


    private OAuthUser getOktaUserInfo(String accessToken) {
        HttpResponse response = HttpRequest.get(OAuth2Properties.getUserInfoUri())
                .header("Accept", "application/json") // 期望接收 JSON 格式的响应
                // 或者使用 Hutool 更语义化的方法：
                .bearerAuth(accessToken)
                .timeout(20000) // 设置超时
                .execute(); // 执行请求
        String body = response.body();
        OAuthUser result = JSONUtil.toBean(body, OAuthUser.class);
        result.setId(JSONUtil.parseObj(body).getStr("sub"));
        return result;
    }
}
