package org.biosino.app.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.AnalysisCustomRepository;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.security.utils.SecurityUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/1/13
 */
@RequiredArgsConstructor
public class AnalysisCustomRepositoryImpl implements AnalysisCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Analysis findByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Analysis.class);
    }


    @Override
    public List<Analysis> findAllByNos(Collection<String> analNos) {
        if (CollUtil.isEmpty(analNos)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").in(analNos);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Analysis.class);
    }

    @Override
    public List<Analysis> findHasTempDataByAnalNoIn(Collection<String> analNos) {
        if (CollUtil.isEmpty(analNos)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").in(analNos);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("temp_data").exists(true).ne(null);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Analysis.class);
    }

    @Override
    public boolean existVisibleByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analNo);
        criteria.and("visible_status").is(VisibleStatusEnum.Accessible.name());
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Analysis.class);
    }

    @Override
    public Analysis findByAnalNoWithPermission(String analNo) {
        Query query = new Query();
        query.addCriteria(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Criteria criteria = new Criteria().orOperator(Criteria.where("anal_no").is(analNo), Criteria.where("used_ids").in(analNo));
        query.addCriteria(criteria);
        Analysis analysis = mongoTemplate.findOne(query, Analysis.class);
        if (analysis == null) {
            throw new ServiceException("Not found analysis");
        }
        if (analysis.getVisibleStatus().equals(VisibleStatusEnum.Unaccessible.name())
                && !analysis.getCreator().equals(SecurityUtils.getMemberId())
                && !shareToMember(analysis, SecurityUtils.getMemberEmail())) {
            throw new NotPermissionException("Not permission");
        }
        return analysis;
    }

    public boolean shareToMember(Analysis item, String... shareTo) {
        List<String> nos = new ArrayList<>();
        nos.add(item.getAnalysisNo());
        if (CollUtil.isNotEmpty(item.getUsedIds())) {
            nos.addAll(item.getUsedIds());
        }
        return mongoTemplate.exists(new Query(
                Criteria.where("share_to").in(shareTo)
                        .and("analysis.anal_no").in(nos)
                        .and("status").is(ShareStatusEnum.sharing.name())), Share.class);
    }

    @Override
    public Map<String, Object> getStatInfo(String analNo, boolean isOwner) {
        Map<String, Object> result = new LinkedHashMap<>();

        // 查询关联的Data
        Query query = new Query();
        query.addCriteria(Criteria.where("anal_no").is(analNo)
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no").include("security").include("file_size");

        List<Data> dataList = mongoTemplate.find(query, Data.class);

        List<Data> filterDataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        result.put("FILES", filterDataList.size());
        result.put("VOLUME", FileUtil.readableFileSize(filterDataList.stream().mapToLong(Data::getFileSize).sum()));

        if (isOwner) {
            result.put("FILES", result.get("FILES") + " / " + dataList.size());
            result.put("VOLUME", result.get("VOLUME") + " / " + FileUtil.readableFileSize(dataList.stream().mapToLong(Data::getFileSize).sum()));
        }

        return result;
    }

    @Override
    public List<Analysis> findDetailByTargetIn(String projNo, String targetType) {
        Criteria criteria = Criteria.where("target.type").is(targetType).
                and("target.nos").in(CollUtil.newArrayList(projNo))
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("anal_no").include("name").include("analysis_type").include("custom_analysis_type").include("submission_date");
        return mongoTemplate.find(query, Analysis.class);
    }

    @Override
    public BrowseStatDTO getBrowseStatInfo(String analysisNo) {
        final BrowseStatDTO result = new BrowseStatDTO();
        DataCustomRepositoryImpl.findBrowseDataNoByAnalNo(mongoTemplate, CollUtil.toList(analysisNo), result);
        return result;
    }

    @Override
    public List<Analysis> getByTargetAndCreator(String typeNo, String creator) {
        List<Analysis> analysisList = new ArrayList<>();
        if (typeNo == null) {
            return analysisList;
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("target.nos").is(typeNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name());
        if (StrUtil.isNotBlank(creator)) {
            criteria.and("creator").is(creator);
        }
        query.addCriteria(criteria);
        analysisList = mongoTemplate.find(query, Analysis.class);
        return analysisList;
    }

    @Override
    public Page<Analysis> findAnalysisPage(UserCenterListSearchDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where("anal_no").regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, Analysis.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Analysis> content = mongoTemplate.find(query, Analysis.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public void incHitNum(String analId) {
        Query query = Query.query(Criteria.where("_id").is(analId));
        Update update = new Update();
        update.inc("hit_num", 1);
        mongoTemplate.updateFirst(query, update, Analysis.class);
    }

    @Override
    public Optional<Analysis> findTopByAnalysisNo(String analNo) {
        if (StrUtil.isBlank(analNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("anal_no").is(analNo),
                Criteria.where("used_ids").in(analNo)));
        Query query = new Query(new Criteria().andOperator(condition));

        Analysis analysis = mongoTemplate.findOne(query, Analysis.class);
        return Optional.ofNullable(analysis);
    }
}
