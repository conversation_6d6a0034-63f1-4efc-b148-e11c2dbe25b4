package org.biosino.qc.repository;

import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.qc.dto.FastQCTaskQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SamToolTaskCustomRepository {
    Page<SamToolTask> findPage(FastQCTaskQueryDTO queryDTO);

    Map<String, Long> getStatusStatByDataNoIn(List<String> dataList);
}
