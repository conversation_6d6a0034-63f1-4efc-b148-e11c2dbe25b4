package org.biosino.system.service.statistics;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.statistics.StatisticsDataVolume;
import org.biosino.common.mongo.entity.statistics.StatisticsPrjMultiExp;
import org.biosino.common.mongo.entity.statistics.StatisticsSapMultiExp;
import org.biosino.system.dto.mapper.MultipleOmicsMapper;
import org.biosino.system.repository.StatisticsDataVolumeRepository;
import org.biosino.system.repository.util.RepositoryUtil;
import org.biosino.system.vo.PrjMultiExpStatVO;
import org.biosino.system.vo.SapMultiExpStatVO;
import org.biosino.system.vo.excel.PrjMultiExpExcel;
import org.biosino.system.vo.excel.SapMultiExpExcel;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.biosino.system.service.statistics.SampleStatService.rawDataTotalSize;
import static org.biosino.system.service.statistics.SampleStatService.sapTotal;

/**
 * <AUTHOR>
 * @date 2024/8/1
 */
@Service
@RequiredArgsConstructor
public class MultipleOmicsStatService {
    private final MongoTemplate mongoTemplate;

    private final StatisticsDataVolumeRepository statisticsDataVolumeRepository;

    public PrjMultiExpStatVO statData() {
        final Query monthQuery = new Query().with(PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "month")));
        final StatisticsPrjMultiExp one = mongoTemplate.findOne(monthQuery, StatisticsPrjMultiExp.class);

        PrjMultiExpStatVO vo = null;
        if (one != null) {
            vo = new PrjMultiExpStatVO();
            vo.setMulti(one.getMulti());
            vo.setSingle(one.getSingle());
        }
        return vo;
    }

    public List<PrjMultiExpExcel> exportPrjOmics() {
//        final Query monthQuery = new Query().with(PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "month")));
//        final StatisticsPrjMultiExp one = mongoTemplate.findOne(monthQuery, StatisticsPrjMultiExp.class);
        final List<StatisticsPrjMultiExp> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsPrjMultiExp.class);
        List<PrjMultiExpExcel> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(all)) {
            for (StatisticsPrjMultiExp one : all) {
                final PrjMultiExpExcel excel = MultipleOmicsMapper.INSTANCE.dbToExcel(one);
                final Optional<StatisticsDataVolume> dataVolumeOptional = statisticsDataVolumeRepository.findFirstByMonth(excel.getMonth());
                if (dataVolumeOptional.isPresent()) {
                    final StatisticsDataVolume dataVolume = dataVolumeOptional.get();

                    excel.setMultiPercentage(NodeUtils.div(one.getMulti(), prjTotal(dataVolume)));
                    excel.setSinglePercentage(NodeUtils.div(one.getSingle(), prjTotal(dataVolume)));

                    excel.setMultiSizePercentage(NodeUtils.div(one.getMultiFileSize(), rawDataTotalSize(dataVolume)));
                }

                excel.setAccessibleMultiSizePercentage(NodeUtils.div(one.getAccessibleMultiFileSize(), one.getMultiFileSize()));
                list.add(excel);
            }
        }
        return list;
    }

    private long prjTotal(final StatisticsDataVolume dataVolume) {
        return dataVolume.getProjAccessible() + dataVolume.getProjUnAccessible();
    }

    public List<SapMultiExpStatVO> sapStatData() {
        List<StatisticsSapMultiExp> all = RepositoryUtil.findLatestMonthData(mongoTemplate, StatisticsSapMultiExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }
        return MultipleOmicsMapper.INSTANCE.dbToSapVo(all);
    }

    public List<SapMultiExpExcel> exportSapOmics() {
        final List<StatisticsSapMultiExp> all = RepositoryUtil.findBaseList(mongoTemplate, true, StatisticsSapMultiExp.class);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No statistic data");
        }

        final List<SapMultiExpExcel> list = new ArrayList<>();

        final Optional<StatisticsDataVolume> dataVolumeOptional = statisticsDataVolumeRepository.findFirstByMonth(all.get(0).getMonth());
        StatisticsDataVolume dataVolume = null;
        if (dataVolumeOptional.isPresent()) {
            dataVolume = dataVolumeOptional.get();
        }

        for (StatisticsSapMultiExp one : all) {
            final SapMultiExpExcel excel = MultipleOmicsMapper.INSTANCE.dbToSapExcel(one);

            if (dataVolume != null) {
                excel.setMultiPercentage(NodeUtils.div(one.getMulti(), sapTotal(dataVolume)));
                excel.setSinglePercentage(NodeUtils.div(one.getSingle(), sapTotal(dataVolume)));

                excel.setMultiSizePercentage(NodeUtils.div(one.getMultiFileSize(), rawDataTotalSize(dataVolume)));
            }

            excel.setAccessibleMultiSizePercentage(NodeUtils.div(one.getAccessibleMultiFileSize(), one.getMultiFileSize()));
            list.add(excel);
        }

        return list;
    }


}
