package org.biosino.app.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.dto.FastQCTaskQueryDTO;
import org.biosino.app.repository.DataRepository;
import org.biosino.app.service.FastQCTaskService;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.security.auth.AuthUtil;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.model.Member;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR> Li
 * @date 2024/5/23
 */
@Controller
@RequestMapping("/fastqc")
@RequiredArgsConstructor
@Slf4j
public class FastQCController {

    private final FastQCTaskService fastQCTaskService;

    private final DataRepository dataRepository;

    /**
     * 展示fastqc html网页
     */
    @RequestMapping(value = "/{dataNo}")
    public void forwardReportPage(@PathVariable String dataNo, FastQCTaskQueryDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Member member = null;
        try {
            member = AuthUtil.getLoginUser(dto.getToken()).getMember();
        } catch (Exception e) {
            log.error("加载用户信息失败，token:{}", dto.getToken());
        }

        boolean accessible = dataRepository.checkAccessPermission(dataNo, member);
        if (!accessible) {
            response.getWriter().write("No Permission!");
        }

        FastQCTask task = fastQCTaskService.findFirstByDataNo(dataNo);
        String dirName = FileUtil.file(DirConstants.FASTQC_HOME).getName();
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("data not exist!"));
        String baseDir = DateUtils.dateTimeFormat4Ftp(task.getDataCreateDate());
        // String htmlFilePath = FilenameUtils.separatorsToUnix(task.getFastqcResult().getHtmlFilePath());
        // if (htmlFilePath.startsWith("/")) {
        //     htmlFilePath = htmlFilePath.substring(1);
        // }
        // 将结果写出
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            File file = FileUtil.file(DirConstants.FASTQC_HOME, baseDir, dataNo, MyFileUtils.getFileNamePrefix(data.getName()) + "_fastqc.html");
            response.setContentType("text/html;charset=UTF-8");
            IoUtil.write(outputStream, true, FileUtil.readBytes(file));
        }
    }

    /**
     * 展示fastqc html网页
     */
    @RequestMapping("/errorLog/{dataNo}")
    public String forwardErrorLogPage(@PathVariable String dataNo, HttpServletRequest request, HttpServletResponse response) {
        FastQCTask task = fastQCTaskService.findFirstByDataNo(dataNo);
        String dirName = FileUtil.file(DirConstants.FASTQC_HOME).getName();
        if (StrUtil.isBlank(task.getErrorLogPath())) {
            throw new ServiceException("error log not exist!");
        }
        // fastqcProperties.getFastqcPath(), msg.getResultBaseDir(), dataNo, "qsub", "qsub.fastqc.err.txt"
        // String htmlFilePath = FilenameUtils.separatorsToUnix(task.getErrorLogPath());
        // if (htmlFilePath.startsWith("/")) {
        //     htmlFilePath = htmlFilePath.substring(1);
        // }
        String baseDir = DateUtils.dateTimeFormat4Ftp(task.getDataCreateDate());
        // 请求转发
        return "forward:/fastqcreport/" + dirName + "/" + baseDir + "/" + dataNo + "/qsub/qsub.fastqc.err.txt";
    }

    /**
     * 下载fastqc结果报告
     */
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Browse", module2 = "Fast QC Info", desc = "下载FastQC结果")
    @RequestMapping("/downloadReport/{dataNo}")
    public void downloadReport(@PathVariable String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        boolean accessible = dataRepository.checkAccessPermission(dataNo, SecurityUtils.getMember());
        if (!accessible) {
            throw new ServiceException("No Permission!");
        }
        fastQCTaskService.downloadReport(dataNo, request, response);
    }

    /**
     * 展示samtool错误日志
     */
    @RequestMapping("/samtool/errorLog/{dataNo}")
    public String forwardSamToolErrorLogPage(@PathVariable String dataNo, HttpServletRequest request, HttpServletResponse response) {
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("data not exist!"));
        String dirName = FileUtil.file(DirConstants.FASTQC_HOME).getName();

        String baseDir = DateUtils.dateTimeFormat4Ftp(data.getCreateDate());
        // 请求转发
        return "forward:/fastqcreport/" + dirName + "/" + baseDir + "/" + dataNo + "/qsub/qsub.samtool.err.txt";
    }

}
