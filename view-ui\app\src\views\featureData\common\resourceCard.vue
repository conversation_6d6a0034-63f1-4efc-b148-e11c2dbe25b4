<template>
  <div class="item" :style="itemCard.svgName ? '' : 'width: 225px'">
    <svg-icon
      v-if="itemCard.svgName"
      :icon-class="itemCard.svgName"
      width="60px"
      height="60px"
    />
    <div class="ml-05">
      <div style="width: 185px" class="text-main-color it-name">
        {{ itemCard.name }}
      </div>
      <div>
        <span class="text-warning">{{ itemCard.readSize }}</span>
        <span class="tb text-warning">{{ itemCard.readSizeUnit }}</span>
        <span class="text-primary number">{{
          formatNumber(itemCard.sapNumber)
        }}</span>
        <span class="text-primary sample">Samples</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, reactive } from 'vue';
  import { formatNumber } from '@/utils/nodeCommon';

  const props = defineProps({
    featureCard: {
      type: Object,
    },
  });
  const itemCard = reactive(props.featureCard);
</script>

<style lang="scss" scoped>
  .item {
    display: flex;
    //min-width: 350px;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s linear;
    &.active {
      border-color: #3a78e8;
    }
    .it-name {
      color: #505050;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 0.2rem;
    }
    &:hover {
      cursor: pointer;
      transform: translateY(-4px);
    }
  }
  .tb {
    font-size: 14px;
    margin-left: 0.3rem;
  }
  .number {
    margin-left: 1.5rem;
  }
  .sample {
    font-size: 14px;
    margin-left: 0.5rem;
  }
  .font-13 {
    font-size: 13px;
  }
</style>
