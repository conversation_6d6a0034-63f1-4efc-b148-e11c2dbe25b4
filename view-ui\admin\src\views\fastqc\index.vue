<template>
  <div class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form ref="searchFormRef" :model="queryParams" :inline="true">
            <el-form-item label="Data ID" prop="dataNo">
              <el-input
                v-model="queryParams.dataNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="Data ID"
              />
            </el-form-item>
            <el-form-item label="Experiment ID" prop="expNo">
              <el-input
                v-model="queryParams.expNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="Experiment ID"
              />
            </el-form-item>
            <el-form-item label="Sample ID" prop="sapNo">
              <el-input
                v-model="queryParams.sapNoStr"
                style="width: 200px"
                type="textarea"
                :rows="1"
                clearable
                placeholder="Sample ID"
              />
            </el-form-item>
            <el-form-item label="Priority" prop="priority">
              <el-select
                v-model="queryParams.priority"
                clearable
                style="width: 100px"
              >
                <el-option
                  v-for="(item, index) in Object.keys(priorityMap)"
                  :key="index"
                  :label="priorityMap[item]"
                  :value="Number(item)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Fail Cause" prop="failCause">
              <el-input
                v-model="queryParams.failCause"
                style="width: 200px"
                clearable
                placeholder="Fail Cause"
                @keyup.enter="getDataList"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >Search
              </el-button>
              <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
              <el-button
                v-hasPermi="['fastqctask:export']"
                type="info"
                icon="download"
                @click="exportData"
                >Export
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="d-flex align-items-center mb-05">
        <span class="font-600 text-secondary-color mr-1">Status: </span>
        <el-radio-group v-model="queryParams.status" @change="getDataList">
          <el-radio value="" label="All">All</el-radio>
          <el-radio value="ready" label="Ready">Ready</el-radio>
          <el-radio value="queuing" label="Queuing">Queuing</el-radio>
          <el-radio value="running" label="Running">Running</el-radio>
          <el-radio value="success" label="Success">Success</el-radio>
          <el-radio value="failed" label="Failed">Failed</el-radio>
        </el-radio-group>
      </div>

      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :row-key="row => row.id"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          :selectable="selectHandle"
          align="center"
          width="50"
        />
        <el-table-column prop="dataNo" label="Data ID" width="120" />
        <el-table-column
          prop="dataFileName"
          label="Data File Name"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column prop="priority" label="Task Priority" width="150">
          <template #default="scope">
            {{ priorityMap[scope.row.priority] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="Status" width="120" sortable>
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.status === 'success'"
                color="#07BCB4"
                size="17"
              >
                <CircleCheckFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'failed'"
                size="17"
                color="#FF8181"
              >
                <CircleCloseFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'ready'"
                color="#409EFF"
                size="17"
              >
                <InfoFilled />
              </el-icon>
              <el-icon
                v-else-if="scope.row.status === 'running'"
                color="#E6A23C"
                size="17"
              >
                <Loading />
              </el-icon>
              <span class="ml-05">{{ scope.row.status }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="dataFilePath"
          label="Data File Path"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column
          prop="fastqcFilePath"
          label="Fastqc File Path"
          show-overflow-tooltip
          min-width="180"
        >
          <template #default="scope">
            {{ scope.row?.fastqcResult?.htmlFilePath }}
          </template>
        </el-table-column>
        <el-table-column
          prop="failCause"
          label="Fail Cause"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="dataCreateDate"
          label="Data Create Date"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.dataCreateDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="Task Create Date"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateDate"
          label="Status Update Date"
          width="180"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.updateDate) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          min-width="110"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="View">
              <svg-icon
                v-if="
                  scope.row.status === 'failed' ||
                  scope.row.status === 'success'
                "
                icon-class="view"
                class-name="meta-svg"
                @click="showDetail(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="View Error Log">
              <svg-icon
                v-if="scope.row.status === 'failed'"
                icon-class="attr"
                class-name="meta-svg"
                @click="toErrorLogPage(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Change Priority">
              <svg-icon
                v-if="
                  scope.row.status === 'ready' || scope.row.status === 'failed'
                "
                icon-class="job"
                class-name="meta-svg"
                @click="showPriority(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Retry">
              <svg-icon
                v-if="scope.row.status === 'failed'"
                icon-class="retry"
                class-name="meta-svg"
                @click="retryTask(scope.row.dataNo)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        style="float: left"
        type="danger"
        @click="showPriority()"
        >Batch Change Priority
      </el-button>
      <el-button
        v-show="tableData.length > 0"
        :disabled="selectedRows.length === 0"
        style="float: left"
        type="warning"
        @click="batchRetry()"
        >Batch Retry
      </el-button>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
    <!-- 详情 -->
    <el-dialog v-model="showDialog" title="Details" width="850px">
      <el-form
        :model="taskInfo"
        label-width="200px"
        label-position="left"
        style="max-height: 70vh; overflow-y: auto"
      >
        <el-form-item label="Data ID：">{{ taskInfo.dataNo }}</el-form-item>
        <el-form-item label="File Name："
          >{{ taskInfo.dataFileName }}
        </el-form-item>
        <el-form-item label="Status：">{{ taskInfo.status }}</el-form-item>
        <div v-if="taskInfo.status === 'success'">
          <el-form-item label="Fastqc Html File Path："
            >{{ taskInfo.fastqcResult.htmlFilePath }}
          </el-form-item>
          <!--          <el-form-item label="Fastqc Report File Path："
                      >{{ taskInfo.fastqcResult.reportFilePath }}
                    </el-form-item>-->
          <el-form-item label="Fastqc Zip File Path："
            >{{ taskInfo.fastqcResult.zipFilePath }}
          </el-form-item>
          <el-form-item label="Fastqc Version："
            >{{ taskInfo.fastqcResult.version }}
          </el-form-item>
          <el-form-item label="SeqKit File Path："
            >{{ taskInfo.seqkitResult.filePath }}
          </el-form-item>
          <el-form-item label="Seqkit File Content">
            <el-descriptions :column="1" border :size="'small'">
              <el-descriptions-item
                v-for="(key, index) in Object.keys(aliasMap)"
                :key="index"
                direction="vertical"
                :label="aliasMap[key]"
              >
                {{ taskInfo.seqkitResult[key] }}
              </el-descriptions-item>
            </el-descriptions>
          </el-form-item>
          <el-form-item label="SeqKit Version："
            >{{ taskInfo.seqkitResult.version }}
          </el-form-item>
        </div>
        <el-form-item label="Task Create Date："
          >{{ parseTime(taskInfo.createDate) }}
        </el-form-item>
        <el-form-item label="Data Create Date："
          >{{ parseTime(taskInfo.dataCreateDate) }}
        </el-form-item>
        <el-form-item label="Status Update Date："
          >{{ parseTime(taskInfo.updateDate) }}
        </el-form-item>
        <el-form-item label="Consuming：">{{ taskInfo.useTime }}</el-form-item>
        <el-form-item v-if="taskInfo.status === 'failed'" label="Fail Cause："
          >{{ taskInfo.failCause }}
        </el-form-item>
        <el-form-item
          v-if="taskInfo.status === 'failed'"
          label="Error Log Path："
          >{{ taskInfo.errorLogPath }}
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button @click="showDialog = false">Close</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 优先级 -->
    <el-dialog
      v-model="showPriorityDialog"
      title="Change Priority"
      width="300px"
      class="dialog radius-14"
      @close="showPriorityDialog = false"
    >
      <el-form ref="priorityForm" label-width="80px">
        <el-form-item label="Priority">
          <el-select v-model="priority" clearable style="width: 200px">
            <el-option
              v-for="(item, index) in Object.keys(priorityMap)"
              :key="index"
              :label="priorityMap[item]"
              :value="Number(item)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            :disabled="!priority"
            type="primary"
            class="btn-primary btn btn-s btn-shadow"
            round
            @click="handleChangePriority"
            >Confirm</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    changeTaskPriority,
    getFastQCTaskInfo,
    listFastQCTask,
    retryFastQCTask,
  } from '@/api/qc/fastqc';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNoStr: '',
      dataNos: [],
      expNoStr: '',
      expNos: [],
      sapNoStr: '',
      sapNos: [],
      status: '',
      failCause: '',
      priority: undefined,
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'dataCreateDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'dataCreateDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.dataNoStr,
    newVal => {
      data.queryParams.dataNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.expNoStr,
    newVal => {
      data.queryParams.expNos = newVal ? newVal.split('\n') : [];
    },
  );
  watch(
    () => data.queryParams.sapNoStr,
    newVal => {
      data.queryParams.sapNos = newVal ? newVal.split('\n') : [];
    },
  );

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');
    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function getDataList() {
    loading.value = true;
    listFastQCTask(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  let taskInfo = ref({});
  let showDialog = ref(false);

  function showDetail(no) {
    proxy.$modal.loading('opening, please wait');
    getFastQCTaskInfo(no)
      .then(response => {
        taskInfo.value = response.data;
        showDialog.value = true;
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  let showPriorityDialog = ref(false);
  let dataNos = ref([]);
  let priority = ref('');

  function showPriority(no) {
    showPriorityDialog.value = true;
    if (no) {
      dataNos.value = [no];
    } else {
      dataNos.value = selectedRows.value.map(it => it.dataNo);
    }
  }

  function handleChangePriority() {
    changeTaskPriority({
      dataNos: dataNos.value.join(','),
      priority: priority.value,
    }).then(() => {
      proxy.$modal.alertSuccess('Priority has been changed');
      // 清空选中
      proxy.$refs['table'].clearSelection();
      getDataList();
      showPriorityDialog.value = false;
    });
  }

  function batchRetry() {
    dataNos.value = selectedRows.value.map(it => it.dataNo);
    proxy.$modal.confirm(`Are you sure to batch retry task?`).then(() => {
      retryFastQCTask({
        dataNos: dataNos.value.join(','),
      })
        .then(response => {
          proxy.$modal.alertSuccess('Task has been resubmitted');
          // 清空选中
          proxy.$refs['table'].clearSelection();
          getDataList();
        })
        .finally(() => {});
    });
  }

  function retryTask(no) {
    proxy.$modal.confirm(`Are you sure to retry task ${no}?`).then(() => {
      retryFastQCTask({
        dataNos: no,
      })
        .then(response => {
          proxy.$modal.alertSuccess('Task has been resubmitted');
          getDataList();
        })
        .finally(() => {});
    });
  }

  function selectHandle(row, index) {
    return row.status === 'ready' || row.status === 'failed';
  }

  let selectedRows = ref([]);

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  let aliasMap = {
    format: 'format',
    type: 'type',
    numSeqs: 'num_seqs',
    sumLen: 'bases',
    minLen: 'min_len',
    avgLen: 'avg_len',
    maxLen: 'max_len',
    q1: 'Q1',
    q2: 'Q2',
    q3: 'Q3',
    sumGap: 'sum_gap',
    n50: 'N50',
    n50Num: 'N50_num',
    q20: 'Q20(%)',
    q30: 'Q30(%)',
    avgQual: 'AvgQual',
    gc: 'GC(%)',
  };

  let priorityMap = {
    1: 'low',
    4: 'lower medium',
    5: 'medium',
    10: 'high',
  };

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/fastqcTask/exportData',
      {
        query,
      },
      `FastQCTask_${new Date().getTime()}.json`,
    );
  }

  function toErrorLogPage(dataNo) {
    window.open(
      `${import.meta.env.VITE_APP_BASE_API}/app/fastqc/errorLog/${dataNo}`,
    );
  }
</script>

<style lang="scss" scoped>
  .el-dialog {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  :deep(.el-dialog .el-dialog__body) {
    padding-top: 0;
    padding-bottom: 0;
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
