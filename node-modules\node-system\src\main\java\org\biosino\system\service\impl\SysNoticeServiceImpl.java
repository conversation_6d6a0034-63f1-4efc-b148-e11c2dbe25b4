package org.biosino.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.system.domain.SysNotice;
import org.biosino.system.mapper.SysNoticeMapper;
import org.biosino.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {
    @Autowired
    private SysNoticeMapper noticeMapper;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice) {
        return noticeMapper.selectNoticeList(notice);
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int insertNotice(SysNotice notice) {
        return noticeMapper.insertNotice(notice);
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int updateNotice(SysNotice notice) {
        noticeMapper.updateOtherStatus(notice.getNoticeId());
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds) {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }

    @Override
    public String getInfo() {
        SysNotice sysNotice = noticeMapper.selectValidNotice();

        if (sysNotice == null) {
            return null;
        }

        Date currentTime = new Date();

        String noticeContent = sysNotice.getNoticeContent();

        Date startTime = sysNotice.getStartTime();
        Date endTime = sysNotice.getEndTime();

        if (endTime != null) {
            // endTime 不为空时，判断当前时间是否在 startTime 和 endTime 之间
            if (currentTime.after(startTime) && currentTime.before(endTime)) {
                return noticeContent;
            } else {
                return null;
            }
        } else {
            // endTime 为空时，判断当前时间是否大于 startTime
            if (currentTime.after(startTime)) {
                return noticeContent;
            } else {
                return null;
            }
        }
    }
}
