import request from '@/utils/request';

let baseURL = '/upload/data';

export function listUnchecked(query) {
  return request({
    url: `${baseURL}/list/unchecked`,
    method: 'get',
    params: query,
  });
}

export function listChecking(query) {
  return request({
    url: `${baseURL}/list/checking`,
    method: 'get',
    params: query,
  });
}

export function listUnarchived(query) {
  return request({
    url: `${baseURL}/list/unarchived`,
    method: 'get',
    params: query,
  });
}

export function checkFtpFile(list) {
  return request({
    url: `${baseURL}/ftpFile/checkFtpFile`,
    method: 'post',
    data: list,
  });
}

export function verifyFtpFile(list) {
  return request({
    url: `${baseURL}/ftpFile/verify`,
    method: 'post',
    timeout: 0,
    headers: {
      repeatSubmit: true,
    },
    data: list,
  });
}

export function deleteFtpFile(list) {
  return request({
    url: `${baseURL}/ftpFile/delete`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    data: list,
  });
}

export function deleteUnarchivedData(list) {
  return request({
    url: `${baseURL}/unarchived/delete`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    data: list,
  });
}

// 新增快递信息
export function saveExpressSubmission(data) {
  return request({
    url: `/upload/rawdata/saveExpressSubmission`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// 查询analysisData已归档的data数据
export function listArchivedAnalysisData(query) {
  return request({
    url: `${baseURL}/list/archived/analysisData`,
    method: 'get',
    params: query,
  });
}

// 查询raw Data已归档的data数据
export function listArchivedRawData(query) {
  return request({
    url: `${baseURL}/list/archived/rawData`,
    method: 'get',
    params: query,
  });
}

// 查询预归档以及需未归档的数据
export function listPreArchivedData(query) {
  return request({
    url: `${baseURL}/list/preArchive`,
    method: 'get',
    params: query,
  });
}

// data删除预检查
export function dataDeleteCheck(dataNo) {
  return request({
    url: `${baseURL}/deleteCheck/${dataNo}`,
    method: 'get',
  });
}

// 删除data所有以及相关联的
export function deleteDataAll(params) {
  return request({
    url: `${baseURL}/deleteDataAll`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 获取rawData的统计信息
export function getRawDataStat() {
  return request({
    url: `${baseURL}/getRawDataStat`,
    method: 'get',
  });
}

// 修改Data Name
export function editFileName(params) {
  return request({
    url: `${baseURL}/editFileName`,
    method: 'get',
    params: params,
  });
}
