package org.biosino.job.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.entity.HttpUploadLog;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.statistics.StatisticsSubMethod;
import org.biosino.job.mapper.FtpFileLogMapper;
import org.biosino.job.repository.HttpUploadRepository;
import org.biosino.job.repository.StatisticsSubMethodRepository;
import org.biosino.job.repository.SubmissionRepository;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.biosino.common.core.constant.ConfigConstants.START_MONTH;

/**
 * 生成统计模块的数据
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsSubMethodService {

    private final FtpFileLogMapper ftpFileLogMapper;
    private final HttpUploadRepository httpUploadRepository;
    private final SubmissionRepository submissionRepository;
    private final StatisticsSubMethodRepository subMethodRepository;

    public static final FastDateFormat FORMATTER = FastDateFormat.getInstance(DateUtils.YYYY_DOT_MM);

    /**
     * 全量刷新数据
     */
    public void calculateAllYears() {
        subMethodRepository.deleteAll();

        Calendar calendar = Calendar.getInstance();
        // 获取当前年份
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();

        for (int year = ConfigConstants.START_YEAR; year <= currentYear; year++) {
            for (int month = 0; month < 12; month++) {
                if (year == ConfigConstants.START_YEAR && month < START_MONTH) {
                    continue;
                }
                // 设置当前年份和月份
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 获取月份的开始时间
                Date startOfMonth = calendar.getTime();

                LocalDate localDateFromCalendar = LocalDate.of(
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH) + 1,
                        calendar.get(Calendar.DAY_OF_MONTH)
                );

                if (localDateFromCalendar.isAfter(currentDate)) {
                    continue;
                }

                // 格式化月份开始时间
                String formattedStart = FORMATTER.format(startOfMonth);

                // 提交任务到线程池
                generate(formattedStart);
            }
        }
    }

    public void generate(String month) {
        // 计算每种类型的访问次数
        Date startDate;
        Date endDate;

        try {
            // 解析传入的月份字符串
            startDate = FORMATTER.parse(month);

            // 使用Calendar来找到月份的结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 增加一个月
            calendar.add(Calendar.MONTH, 1);
            // 减去一秒以回到当前月的最后一天
            calendar.add(Calendar.SECOND, -1);

            endDate = calendar.getTime();
        } catch (ParseException e) {
            log.error("日期格式错误，解析识别：{}", month);
            return;
        }

        // 清除旧数据
        subMethodRepository.deletedByMonth(month);

        StatisticsSubMethod result = new StatisticsSubMethod();
        result.setMonth(month);

        List<Submission> submissions = submissionRepository.findAllByDate(startDate, endDate);

        List<HttpUploadLog> httpUploadLogList = httpUploadRepository.findAllFileSizeByDate(startDate, endDate);
        if (CollUtil.isNotEmpty(httpUploadLogList)) {
            result.setHttp((long) httpUploadLogList.size());
            result.setHttpSize(httpUploadLogList.stream().mapToLong(HttpUploadLog::getFileSize).sum());
        }

        Long ftpFileCount = ftpFileLogMapper.countByDate(DateUtils.formatDateTime(startDate), DateUtils.formatDateTime(endDate));
        result.setFtp(ftpFileCount == null ? 0L : ftpFileCount);
        Long ftpFileSum = ftpFileLogMapper.sumSizeByDate(DateUtils.formatDateTime(startDate), DateUtils.formatDateTime(endDate));
        result.setFtpSize(ftpFileSum == null ? 0L : ftpFileSum);

        for (Submission submission : submissions) {
            if (submission.getProjNo() != null) {
                result.setProject(result.getProject() + 1);
            }
            if (submission.getExpSingleNo() != null) {
                result.setExpSingle(result.getExpSingle() + 1);
            }
            if (CollUtil.isNotEmpty(submission.getExpMultipleNos())) {
                result.setExpMultiple(result.getExpMultiple() + submission.getExpMultipleNos().size());
            }
            if (submission.getSapSingleNo() != null) {
                result.setExpSingle(result.getExpSingle() + 1);
            }
            if (CollUtil.isNotEmpty(submission.getSapMultipleData())) {
                long sapNum = result.getSapMultiple();
                List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                    List<String> nos = sapMultipleDatum.getNos();
                    if (CollUtil.isNotEmpty(nos)) {
                        sapNum += nos.size();
                    }
                }
                result.setExpMultiple(sapNum);
            }
            // 分析数据提交
            if (submission.getAnalSingleNo() != null) {
                result.setAnalSingle(result.getAnalSingle() + 1);
            }
            if (CollUtil.isNotEmpty(submission.getAnalMultipleNos())) {
                result.setAnalMultiple(result.getAnalMultiple() + submission.getAnalMultipleNos().size());
            }
            // 分析归档data
            if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                result.setAnalDataArchivingSingle(result.getAnalDataArchivingSingle() + submission.getAnalysisDataNos().size());
            }
            if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                result.setAnalDataArchivingMultiple(result.getAnalDataArchivingMultiple() + submission.getAnalDataMultipleNos().size());
            }
            // Raw data归档data
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                result.setRawDataArchivingSingle(result.getRawDataArchivingSingle() + submission.getRawDataNos().size());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                result.setRawDataArchivingMultiple(result.getRawDataArchivingMultiple() + submission.getRawDataMultipleNos().size());
            }
        }

        subMethodRepository.insert(result);
    }

}
