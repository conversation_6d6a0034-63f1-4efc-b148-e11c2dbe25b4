package org.biosino.common.core.domain;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * select结构实体类
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Select implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Option名称
     */
    private String label;

    /**
     * Option具体值
     */
    private String value;

    private String title;

    public Select(String label, String value) {
        this.label = label;
        this.value = value;
    }

}
