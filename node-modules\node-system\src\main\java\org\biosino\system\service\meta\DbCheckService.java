package org.biosino.system.service.meta;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.enums.ExperimentTypeEnum;
import org.biosino.common.security.enums.SampleTypeEnum;
import org.biosino.es.api.RemoteDictService;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.biosino.system.api.domain.DbCheckLog;
import org.biosino.system.repository.DataRepository;
import org.biosino.system.repository.ExpSampleTypeRepository;
import org.biosino.system.service.IDbCheckLogService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.io.File;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DbCheckService extends BaseService {

    private final MongoTemplate mongoTemplate;

    private final DataRepository dataRepository;

    private final IDbCheckLogService dbCheckLogService;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    private final RemoteDictService remoteDictService;

    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private final static String Custom = "Custom";

    private final static String disease = "disease";

    private final static String CHECK_DB_TASK_KEY = "check_db_task_key";

    private final static String RELATE_DATA_CHECK_KEY = "relate_data_check_key";

    private final RedisService redisService;

    private final static Long EXPIRE_TIME = 1L;

    // 新版要求至少到日
    private static final String[] DATE_PATTERNS = {"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyyMMdd HH:mm:ss",
            DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.getPattern()};


    public synchronized void checkAllDB() {
        if (redisService.getCacheObject(CHECK_DB_TASK_KEY) != null) {
            throw new ServiceException("Checking task is running, please try again later!");
        }

        redisService.setCacheObject(CHECK_DB_TASK_KEY, true, EXPIRE_TIME, TimeUnit.DAYS);
        // 保存前清空数据库
        dbCheckLogService.remove(Wrappers.emptyWrapper());
        ThreadUtil.execute(() -> {
            try {
                System.out.println("开始数据一致性检查");
                // 保存之前清空数据库
                checkProjectDB();
                checkExperimentDB();
                checkSampleDB();
                checkRunDB();
                checkAnalysisDB();
                checkDataDB();
                checkShareDB();
                checkReviewDB();
                checkPublishDB();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                redisService.deleteObject(CHECK_DB_TASK_KEY);
                System.out.println("数据一致性检查结束");
            }
        });
    }


    public void checkProjectDB() {

        MongoPagingIterator<Project> iterator = new MongoPagingIterator<>(mongoTemplate, Project.class, 1000);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();
        String metadataType = AuthorizeType.project.name();
        int i = 0;
        while (iterator.hasNext()) {
            log.info("project 开始校验第{}页数据", i++);
            List<Project> page = iterator.next();
            for (Project dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited()) || !StrUtil.startWith(dto.getProjectNo(), SequenceType.PROJECT.getPrefix())) {
                    continue;
                }
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                Set<ConstraintViolation<Project>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Project> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }
                        DbCheckLog checkLog = generateCheckLog(dto.getId(), metadataType, dto.getProjectNo(), fieldName, message, creator, createDate, null, dto.getSourceProject());
                        saveLogs.add(checkLog);
                    }
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    public void checkExperimentDB() {
        Query query = new Query(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        // 迭代器全表扫描
        MongoPagingIterator<Experiment> iterator = new MongoPagingIterator<>(mongoTemplate, Experiment.class, 1000);

        // experiment 中所有的模板
        Map<String, ExpSampleType> expTypeMap = nameToExpType();

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();
        String metadataType = AuthorizeType.experiment.name();

        TypeInformation projTypeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.project.name());
        int i = 0;
        while (iterator.hasNext()) {
            log.info("experiment 开始校验第{}页数据", i++);

            List<Experiment> page = iterator.next();
            List<String> existProjNos = dbExistNos(projTypeInfo.getField(), page.stream().map(Experiment::getProjectNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()), Project.class);
            for (Experiment dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited()) || !StrUtil.startWith(dto.getExpNo(), SequenceType.EXPERIMENT.getPrefix())) {
                    continue;
                }
                String dbId = dto.getId();
                String dbNo = dto.getExpNo();
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                String expType = dto.getExpType();
                List<String> sourceProject = dto.getSourceProject();
                // 先使用Validator校验
                Set<ConstraintViolation<Experiment>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Experiment> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, expType, sourceProject);
                        saveLogs.add(checkLog);
                    }
                }
                // 校验experiment所独有的关联关系
                if (StrUtil.isNotBlank(dto.getProjectNo())) {
                    if (!existProjNos.contains(dto.getProjectNo())) {
                        String message = dto.getProjectNo() + "在Project表中不存在!";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "proj_no", message, creator, createDate, expType, sourceProject);
                        saveLogs.add(checkLog);
                    }
                }
                if (!expTypeMap.containsKey(expType) && !StrUtil.equals(expType, "Other")) {
                    String message = "exp_type:" + expType + "不在模板范围内";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "exp_type", message, creator, createDate, expType, sourceProject);
                    saveLogs.add(checkLog);
                    continue;
                }
                // 校验attributes的内容是否符合模板中的数据
                if (expTypeMap.containsKey(expType)) {
                    ExpSampleType expSampleType = expTypeMap.get(expType);

                    // 这个v需要转成String然后校验
                    Map<String, Object> attributes = dto.getAttributes();

                    // 模板类型
                    String type = expSampleType.getType();

                    Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
                    // 字典中配置的推荐属性必填个数
                    Integer recommendNum = expSampleType.getRecommendNum();
                    // 属性校验
                    int recommendCount = 0;

                    // TODO 上线开启
                    for (String attrField : rangeMap.keySet()) {
                        ExpSampleType.Attributes attrTpl = rangeMap.get(attrField);
                        // 将object转value
                        String attrValue = strVal(attributes.get(attrField));
                        // 如果是必填项
                        if (StrUtil.equals(BaseAttrType.required.name(), attrTpl.getRequired())) {
                            if (StrUtil.isBlank(attrValue)) {
                                String message = "attributes." + attrField + "为必填项!";
                                DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrField, message, creator, createDate, expType, sourceProject);
                                saveLogs.add(checkLog);
                            } else {
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }
                        } else if (StrUtil.equals(BaseAttrType.recommend.name(), attrTpl.getRequired())) {
                            if (StrUtil.isNotBlank(attrValue)) {
                                // 统计值不为空的推荐项数量
                                recommendCount++;
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }

                        } else {
                            if (StrUtil.isNotBlank(attrValue)) {
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }
                        }
                        // 如果这个模板有推荐字段数，且填实际写的字段小于推荐字段数量，则报错
                        /*if (recommendNum != null && recommendCount < recommendNum) {
                            String message = "attributes填写的推荐字段数量小于推荐的数量" + recommendNum;
                            DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrField, message, creator, createDate);
                            saveLogs.add(checkLog);
                        }*/
                    }
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }


    public void checkSampleDB() {
//        Query query = new Query(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));

        MongoPagingIterator<Sample> iterator = new MongoPagingIterator<>(mongoTemplate, Sample.class, 1000);

        // sample 中所有的模板
        Map<String, ExpSampleType> sampleTypeMap = nameToSampleType();

        Map<String, Boolean> organismMap = new HashMap<>();
        Map<String, Boolean> taxIdMap = new HashMap<>();
        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();
        String metadataType = AuthorizeType.sample.name();
        int i = 0;
        while (iterator.hasNext()) {
            log.info("sample 开始校验第{}页数据", i++);

            List<Sample> page = iterator.next();
            for (Sample dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited()) || !StrUtil.startWith(dto.getSapNo(), SequenceType.SAMPLE.getPrefix())) {
                    continue;
                }
                String dbId = dto.getId();
                String dbNo = dto.getSapNo();
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                String subjectType = dto.getSubjectType();
                List<String> sourceProject = dto.getSourceProject();
                // 先使用Validator校验
                Set<ConstraintViolation<Sample>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Sample> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, subjectType, sourceProject);
                        saveLogs.add(checkLog);
                    }
                }
                if (!sampleTypeMap.containsKey(subjectType)) {
                    String message = "Sample Type:" + subjectType + "不在模板范围内";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "subject_type", message, creator, createDate, subjectType, sourceProject);
                    saveLogs.add(checkLog);
                    continue;
                }
                // 校验sample独有的字段
                if (StrUtil.isNotBlank(dto.getOrganism())) {
                    String organism = dto.getOrganism();
                    if (!organismMap.containsKey(organism)) {
                        List<String> taxId = remoteDictService.getTaxIdByName(organism, SecurityConstants.INNER).getData();
                        organismMap.put(organism, CollUtil.isNotEmpty(taxId));
                    }
                    if (!organismMap.containsKey(organism)) {
                        if (!taxIdMap.containsKey(dto.getTaxId())) {
                            List<TaxonomyNodeDTO> data = remoteDictService.getTaxByTaxId(dto.getTaxId(), SecurityConstants.INNER).getData();
                            taxIdMap.put(dto.getTaxId(), CollUtil.isNotEmpty(data));
                        }
                        if (!taxIdMap.containsKey(dto.getTaxId())) {
                            String message = "organism:" + dto.getOrganism() + "不在合法范围内";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "organism", message, creator, createDate, subjectType, sourceProject);
                            saveLogs.add(checkLog);
                        }
                    }
                }

                // TODO 上线开启
                // 校验attributes的内容是否符合模板中的数据
                if (sampleTypeMap.containsKey(subjectType)) {
                    // sampleType对于的模板
                    ExpSampleType expSampleType = sampleTypeMap.get(subjectType);

                    // map的value需要转成String然后校验
                    Map<String, String> attributes = dto.getAttributes();

                    String type = expSampleType.getType();

                    Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
                    // 字典中配置的推荐属性必填个数
                    Integer recommendNum = expSampleType.getRecommendNum();
                    // 属性校验
                    int recommendCount = 0;
                    if (StrUtil.equals(BaseAttrType.required.name(), expSampleType.getProtocol()) && StrUtil.isBlank(dto.getProtocol())) {
                        String message = "当type为" + type + "时protocol不能为空";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "protocol", message, creator, createDate, subjectType, sourceProject);
                        saveLogs.add(checkLog);
                    }

                    for (String attrFieldName : rangeMap.keySet()) {
                        ExpSampleType.Attributes attrTpl = rangeMap.get(attrFieldName);
                        // 将object转string
                        String attrValue = strVal(attributes.get(attrFieldName));
                        // 如果是必填项
                        if (StrUtil.equals(BaseAttrType.required.name(), attrTpl.getRequired())) {
                            if (StrUtil.isBlank(attrValue)) {
                                String message = "attributes." + attrFieldName + "为必填项!";
                                DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrFieldName, message, creator, createDate, subjectType, sourceProject);
                                saveLogs.add(checkLog);
                            } else {
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }
                        } else if (StrUtil.equals(BaseAttrType.recommend.name(), attrTpl.getRequired())) {
                            if (StrUtil.isNotBlank(attrValue)) {
                                // 统计值不为空的推荐项数量
                                recommendCount++;
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }

                        } else {
                            if (StrUtil.isNotBlank(attrValue)) {
                                // 校验数据类型
                                validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, sourceProject);
                            }
                        }
                        // 如果这个模板有推荐字段数，且填实际写的字段小于推荐字段数量，则报错
                        /*if (recommendNum != null && recommendCount < recommendNum) {
                            String message = "attribute推荐填写的字段数量小于" + recommendNum;
                            DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrFieldName, message, creator, createDate);
                            saveLogs.add(checkLog);
                        }*/
                    }
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    public void checkRunDB() {

        MongoPagingIterator<Run> iterator = new MongoPagingIterator<>(mongoTemplate, Run.class, 1000);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.run.name();

        TypeInformation expTypeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.experiment.name());
        TypeInformation sapTypeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.sample.name());
        int i = 0;
        while (iterator.hasNext()) {
            log.info("run 开始校验第{}页数据", i++);

            List<Run> page = iterator.next();

            List<String> expNos = page.stream().map(Run::getExpNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            List<String> sapNos = page.stream().map(Run::getSapNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

            List<String> existExpNos = dbExistNos(expTypeInfo.getMongoField(), expNos, expTypeInfo.getClazz());
            List<String> existSapNos = dbExistNos(sapTypeInfo.getMongoField(), sapNos, sapTypeInfo.getClazz());

            for (Run dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited()) || !StrUtil.startWith(dto.getRunNo(), SequenceType.RUN.getPrefix())) {
                    continue;
                }
                String dbId = dto.getId();
                String dbNo = dto.getRunNo();
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                List<String> sourceProject = dto.getSourceProject();
                // 先使用Validator校验
                Set<ConstraintViolation<Run>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Run> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, null, sourceProject);
                        saveLogs.add(checkLog);
                    }
                }

                // 校验Run独有的关联关系
                if (StrUtil.isNotBlank(dto.getExpNo()) && !existExpNos.contains(dto.getExpNo())) {
                    String message = dto.getExpNo() + "在Experiment表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, expTypeInfo.getMongoField(), message, creator, createDate, null, sourceProject);
                    saveLogs.add(checkLog);
                }
                if (StrUtil.isNotBlank(dto.getSapNo()) && !existSapNos.contains(dto.getSapNo())) {
                    String message = dto.getSapNo() + "在Sample表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, sapTypeInfo.getMongoField(), message, creator, createDate, null, sourceProject);
                    saveLogs.add(checkLog);
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    public void checkDataDB() {
        MongoPagingIterator<Data> iterator = new MongoPagingIterator<>(mongoTemplate, Data.class, 1000);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.data.name();

        TypeInformation analTypeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.analysis.name());
        TypeInformation runTypeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.run.name());

        int i = 0;
        while (iterator.hasNext()) {
            log.info("data 开始校验第{}页数据", i++);

            List<Data> page = iterator.next();

            List<String> analNos = page.stream().map(Data::getAnalNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            List<String> runNos = page.stream().map(Data::getRunNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

            List<String> existAnalNos = dbExistNos(analTypeInfo.getMongoField(), analNos, analTypeInfo.getClazz());
            List<String> existRunNos = dbExistNos(runTypeInfo.getMongoField(), runNos, runTypeInfo.getClazz());

            for (Data dto : page) {
                String dbId = dto.getId();
                String dbNo = dto.getDatNo();
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                // 先使用Validator校验
                Set<ConstraintViolation<Data>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Data> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }

                // 校验Run独有的关联关系
                if (StrUtil.isNotBlank(dto.getRunNo()) && !existRunNos.contains(dto.getRunNo())) {
                    String message = dto.getRunNo() + "在Run表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, runTypeInfo.getMongoField(), message, creator, createDate, null, null);
                    saveLogs.add(checkLog);
                }
                if (StrUtil.isNotBlank(dto.getAnalNo()) && !existAnalNos.contains(dto.getAnalNo())) {
                    String message = dto.getAnalNo() + "在Analysis表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, analTypeInfo.getMongoField(), message, creator, createDate, null, null);
                    saveLogs.add(checkLog);
                }
                if (!CollUtil.contains(SecurityEnum.checkSecurityByShare(), dto.getSecurity())) {
                    String message = "security不在取值范围内";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "security", message, creator, createDate, null, null);
                    saveLogs.add(checkLog);
                }
                if (StrUtil.isNotBlank(dto.getArchived())) {
                    if (StrUtil.equals(ArchiveEnum.yes.name(), dto.getArchived())
                            && StrUtil.isBlank(dto.getRunNo())
                            && StrUtil.isBlank(dto.getAnalNo())) {
                        String message = "archived为yes时，run_no和anal_no不能同时为空";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "archived", message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }
                // TODO 上线开启
                if (StrUtil.isNotBlank(dto.getFilePath()) && !StrUtil.containsIgnoreCase(dto.getSecurity(), "_Delete")) {
                    File file = FileUtil.file(DirConstants.DATA_HOME, dto.getFilePath());
                    if (!file.exists()) {
                        String message = "文件在磁盘中不存在";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "file_path", message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    } else {
                        if (file.isDirectory()) {
                            String message = dto.getFilePath() + "不是文件，是路径!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "file_path", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                        if (!file.canRead()) {
                            String message = dto.getFilePath() + "不可读";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "file_path", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                        if (!file.canWrite()) {
                            String message = dto.getFilePath() + "不可写";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "file_path", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    public void checkAnalysisDB() {

        MongoPagingIterator<Analysis> iterator = new MongoPagingIterator<>(mongoTemplate, Analysis.class, 1000);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.analysis.name();

        int i = 0;
        while (iterator.hasNext()) {
            log.info("analysis 开始校验第{}页数据", i++);
            List<Analysis> page = iterator.next();
            Map<String, Set<String>> typeToExistNosMap = collectNosFromTargetsAndPipelines(page);
            for (Analysis dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited()) || !StrUtil.startWith(dto.getAnalysisNo(), SequenceType.ANALYSIS.getPrefix())) {
                    continue;
                }
                String dbId = dto.getId();
                String dbNo = dto.getAnalysisNo();
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                String analysisType = dto.getAnalysisType();
                List<String> sourceProject = dto.getSourceProject();
                // 先使用Validator校验
                Set<ConstraintViolation<Analysis>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Analysis> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, analysisType, sourceProject);
                        saveLogs.add(checkLog);
                    }
                }

                // 校验analysis的独有关系
                if (CollUtil.isNotEmpty(dto.getTarget())) {
                    for (AnalysisTarget target : dto.getTarget()) {
                        if (StrUtil.isBlank(target.getType()) || CollUtil.isEmpty(target.getNos())) {
                            String message = "target的type和nos不能为空";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target", message, creator, createDate, analysisType, sourceProject);
                            saveLogs.add(checkLog);
                        } else {
                            if (!TypeInformation.typeInfoMap.containsKey(target.getType())) {
                                String message = "target的type不在取值范围内";
                                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target.type", message, creator, createDate, analysisType, sourceProject);
                                saveLogs.add(checkLog);
                            } else {
                                for (String no : target.getNos()) {
                                    if (!typeToExistNosMap.get(target.getType()).contains(no)) {
                                        String message = no + "在" + target.getType() + "表中不存在";
                                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target.nos", message, creator, createDate, analysisType, sourceProject);
                                        saveLogs.add(checkLog);
                                    }
                                }
                            }
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getPipeline())) {
                    for (Pipeline pipeline : dto.getPipeline()) {
                        if (CollUtil.isNotEmpty(pipeline.getOutput())) {
                            for (String no : pipeline.getOutput()) {
                                if (!typeToExistNosMap.get(AuthorizeType.data.name()).contains(no)) {
                                    String message = no + "在data表中不存在";
                                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "pipeline.output", message, creator, createDate, analysisType, sourceProject);
                                    saveLogs.add(checkLog);
                                }
                            }
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    public Map<String, Set<String>> collectNosFromTargetsAndPipelines(List<Analysis> analList) {
        Map<String, Set<String>> typeToExistNosMap = new HashMap<>();
        for (Analysis analysis : analList) {
            List<AnalysisTarget> targets = analysis.getTarget();
            if (CollUtil.isNotEmpty(targets)) {
                for (AnalysisTarget target : targets) {
                    if (CollUtil.isNotEmpty(target.getNos())) {
                        Set<String> nos = typeToExistNosMap.computeIfAbsent(target.getType(), k -> new HashSet<>());
                        nos.addAll(target.getNos());
                    }
                }
            }

            List<Pipeline> pipelines = analysis.getPipeline();
            if (CollUtil.isNotEmpty(pipelines)) {
                for (Pipeline pipeline : pipelines) {
                    if (CollUtil.isNotEmpty(pipeline.getOutput())) {
                        Set<String> nos = typeToExistNosMap.computeIfAbsent(AuthorizeType.data.name(), k -> new HashSet<>());
                        nos.addAll(pipeline.getOutput());
                    }
                }
            }
        }

        for (Map.Entry<String, Set<String>> entry : typeToExistNosMap.entrySet()) {
            String type = entry.getKey();
            Set<String> nos = entry.getValue();
            if (TypeInformation.typeInfoMap.containsKey(type)) {
                TypeInformation typeInformation = TypeInformation.typeInfoMap.get(type);
                List<String> existNos = dbExistNos(typeInformation.getMongoField(), nos, typeInformation.getClazz());
                typeToExistNosMap.put(type, new HashSet<>(existNos));
            }
        }

        return typeToExistNosMap;
    }

    public void checkShareDB() {
        MongoPagingIterator<Share> iterator = new MongoPagingIterator<>(mongoTemplate, Share.class, 10);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.share.name();

        int i = 0;
        while (iterator.hasNext()) {
            log.info("share 开始校验第{}页数据", i++);

            List<Share> page = iterator.next();
            Map<String, Set<String>> typeToExistNosMap = collectExistNosFromShares(page);
            for (Share dto : page) {
                String dbId = dto.getId();
                String dbNo = dto.getShareId();
                String creator = dto.getCreator();
                Date createDate = dto.getShareDate();
                // 先使用Validator校验
                Set<ConstraintViolation<Share>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Share> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }

                // 校验share的独有关系
                if (CollUtil.isNotEmpty(dto.getProjects())) {
                    for (ShareProject project : dto.getProjects()) {
                        if (!typeToExistNosMap.get(AuthorizeType.project.name()).contains(project.getProjectNo())) {
                            String message = project.getProjectNo() + "在project表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "projects.proj_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getExperiments())) {
                    for (ShareExperiment experiment : dto.getExperiments()) {
                        if (!typeToExistNosMap.get(AuthorizeType.experiment.name()).contains(experiment.getExpNo())) {
                            String message = experiment.getExpNo() + "在experiment表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "experiments.exp_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getSamples())) {
                    for (ShareSample sample : dto.getSamples()) {
                        if (!typeToExistNosMap.get(AuthorizeType.sample.name()).contains(sample.getSapNo())) {
                            String message = sample.getSapNo() + "在sample表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "samples.sap_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getRuns())) {
                    for (ShareRun run : dto.getRuns()) {
                        if (!typeToExistNosMap.get(AuthorizeType.run.name()).contains(run.getRunNo())) {
                            String message = run.getRunNo() + "在run表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "runs.run_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getDatas())) {
                    for (ShareData data : dto.getDatas()) {
                        if (!typeToExistNosMap.get(AuthorizeType.data.name()).contains(data.getDatNo())) {
                            String message = data.getDatNo() + "在data表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "datas.dat_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }

            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    private Map<String, Set<String>> collectExistNosFromShares(List<Share> shares) {
        Map<String, Set<String>> typeToExistNosMap = new HashMap<>();
        for (Share share : shares) {
            if (CollUtil.isNotEmpty(share.getProjects())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.project.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.project.name()).addAll(share.getProjects().stream().map(ShareProject::getProjectNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(share.getExperiments())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.experiment.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.experiment.name()).addAll(share.getExperiments().stream().map(ShareExperiment::getExpNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(share.getSamples())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.sample.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.sample.name()).addAll(share.getSamples().stream().map(ShareSample::getSapNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(share.getRuns())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.run.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.run.name()).addAll(share.getRuns().stream().map(ShareRun::getRunNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(share.getDatas())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.data.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.data.name()).addAll(share.getDatas().stream().map(ShareData::getDatNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(share.getAnalysis())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.analysis.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.analysis.name()).addAll(share.getAnalysis().stream().map(ShareAnalysis::getAnalNo).collect(Collectors.toSet()));
            }
        }
        for (Map.Entry<String, Set<String>> entry : typeToExistNosMap.entrySet()) {
            String type = entry.getKey();
            Set<String> nos = entry.getValue();
            if (TypeInformation.typeInfoMap.containsKey(type)) {
                TypeInformation typeInformation = TypeInformation.typeInfoMap.get(type);
                List<String> existNos = dbExistNos(typeInformation.getMongoField(), nos, typeInformation.getClazz());
                typeToExistNosMap.put(type, new HashSet<>(existNos));
            }
        }
        return typeToExistNosMap;
    }

    public void checkReviewDB() {
        MongoPagingIterator<Review> iterator = new MongoPagingIterator<>(mongoTemplate, Review.class, 50);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.review.name();

        int i = 0;
        while (iterator.hasNext()) {
            log.info("review 开始校验第{}页数据", i++);

            List<Review> page = iterator.next();
            Map<String, Set<String>> typeToExistNosMap = collectExistNosFromReview(page);
            for (Review dto : page) {
                String dbId = dto.getId();
                String dbNo = dto.getReviewId();
                String creator = dto.getCreator();
                Date createDate = dto.getReviewDate();
                // 先使用Validator校验
                Set<ConstraintViolation<Review>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Review> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }

                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }

                // 校验share的独有关系
                if (CollUtil.isNotEmpty(dto.getProjects())) {
                    for (ShareProject project : dto.getProjects()) {
                        if (!typeToExistNosMap.get(AuthorizeType.project.name()).contains(project.getProjectNo())) {
                            String message = project.getProjectNo() + "在project表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "projects.proj_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getExperiments())) {
                    for (ShareExperiment experiment : dto.getExperiments()) {
                        if (!typeToExistNosMap.get(AuthorizeType.experiment.name()).contains(experiment.getExpNo())) {
                            String message = experiment.getExpNo() + "在experiment表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "experiments.exp_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getSamples())) {
                    for (ShareSample sample : dto.getSamples()) {
                        if (!typeToExistNosMap.get(AuthorizeType.sample.name()).contains(sample.getSapNo())) {
                            String message = sample.getSapNo() + "在sample表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "samples.sap_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getRuns())) {
                    for (ShareRun run : dto.getRuns()) {
                        if (!typeToExistNosMap.get(AuthorizeType.run.name()).contains(run.getRunNo())) {
                            String message = run.getRunNo() + "在run表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "runs.run_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(dto.getDatas())) {
                    for (ReviewData data : dto.getDatas()) {
                        if (!typeToExistNosMap.get(AuthorizeType.data.name()).contains(data.getDataNo())) {
                            String message = data.getDataNo() + "在data表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "datas.dat_no", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }

            }
            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }

    private Map<String, Set<String>> collectExistNosFromReview(List<Review> reviews) {
        Map<String, Set<String>> typeToExistNosMap = new HashMap<>();
        for (Review review : reviews) {
            if (CollUtil.isNotEmpty(review.getProjects())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.project.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.project.name()).addAll(review.getProjects().stream().map(ShareProject::getProjectNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(review.getExperiments())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.experiment.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.experiment.name()).addAll(review.getExperiments().stream().map(ShareExperiment::getExpNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(review.getSamples())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.sample.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.sample.name()).addAll(review.getSamples().stream().map(ShareSample::getSapNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(review.getRuns())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.run.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.run.name()).addAll(review.getRuns().stream().map(ShareRun::getRunNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(review.getDatas())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.data.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.data.name()).addAll(review.getDatas().stream().map(ReviewData::getDataNo).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(review.getAnalysis())) {
                typeToExistNosMap.computeIfAbsent(AuthorizeType.analysis.name(), k -> new HashSet<>());
                typeToExistNosMap.get(AuthorizeType.analysis.name()).addAll(review.getAnalysis().stream().map(ShareAnalysis::getAnalNo).collect(Collectors.toSet()));
            }
        }
        for (Map.Entry<String, Set<String>> entry : typeToExistNosMap.entrySet()) {
            String type = entry.getKey();
            Set<String> nos = entry.getValue();
            if (TypeInformation.typeInfoMap.containsKey(type)) {
                TypeInformation typeInformation = TypeInformation.typeInfoMap.get(type);
                List<String> existNos = dbExistNos(typeInformation.getMongoField(), nos, typeInformation.getClazz());
                typeToExistNosMap.put(type, new HashSet<>(existNos));
            }
        }
        return typeToExistNosMap;
    }


    /**
     * 校验字段的数据类型
     *
     * @param saveLogs  需要保存的日志
     * @param dbId      数据库id
     * @param dbNo      数据库no
     * @param dbType    expSapType模板类型
     * @param tplType   expSapType模板名称
     * @param attrTpl   字段模板
     * @param attrValue 属性值
     */
    private void validateAttrDataType(Set<DbCheckLog> saveLogs, String dbId, String dbNo, String dbType, String tplType, ExpSampleType.Attributes attrTpl, String attrValue, String creator, Date createDate, List<String> tags) {
        if (StrUtil.equals(ExpSampleDataType.Date.name(), attrTpl.getDataType())) {
            try {
                DateFormatUtils.format(parseDateStr(attrValue), DatePattern.NORM_DATETIME_PATTERN);
            } catch (Exception e) {
                String message = "attributes." + attrTpl.getAttributesField() + "数据类型不正确，应该是日期类型！";
                DbCheckLog checkLog = generateCheckLog(dbId, dbType, dbNo, "attributes." + attrTpl.getAttributesField(), message, creator, createDate, tplType, tags);
                saveLogs.add(checkLog);
            }
        } else if (StrUtil.equals(Custom, attrTpl.getDataType())) {
            String attrField = attrTpl.getAttributesField();
            if (StrUtil.equals(ExpSampleTypeEnum.experiment.name(), dbType)
                    && StrUtil.equals(ExpSampleType.PLATFORM, attrField)
                    && StrUtil.equals(ExperimentTypeEnum.Microarray.getDesc(), tplType)) {
                if (!remoteDictService.existPlatform(attrValue, SecurityConstants.INNER).getData()) {
                    String message = "attributes." + attrTpl.getAttributesField() + "数据取值不在范围内！";
                    DbCheckLog checkLog = generateCheckLog(dbId, dbType, dbNo, "attributes." + attrTpl.getAttributesField(), message, creator, createDate, tplType, tags);
                    saveLogs.add(checkLog);
                }
            }
            if (StrUtil.equals(ExpSampleTypeEnum.sample.name(), dbType)
                    && StrUtil.equals(disease, attrField)
                    && StrUtil.equalsAny(tplType, SampleTypeEnum.Human.getDesc(), SampleTypeEnum.Animalia.getDesc())) {
                if (!remoteDictService.existDisease(attrValue, SecurityConstants.INNER).getData()) {
                    String message = "attributes." + attrTpl.getAttributesField() + "数据取值不在范围内！";
                    DbCheckLog checkLog = generateCheckLog(dbId, dbType, dbNo, "attributes." + attrTpl.getAttributesField(), message, creator, createDate, tplType, tags);
                    saveLogs.add(checkLog);
                }
            }
        } else if (CollUtil.isNotEmpty(attrTpl.getRangeStrList())
                && !attrTpl.isAllowCreate()
                && !attrTpl.getRangeStrList().contains(attrValue)) {
            String message = "attributes." + attrTpl.getAttributesField() + "数据取值不在范围内！";
            DbCheckLog checkLog = generateCheckLog(dbId, dbType, dbNo, "attributes." + attrTpl.getAttributesField(), message, creator, createDate, tplType, tags);
            saveLogs.add(checkLog);
        } else {
            if (StrUtil.isNotBlank(attrTpl.getValueRegex())) {
                if (!ReUtil.isMatch(attrTpl.getValueRegex(), attrValue)) {
                    String message = "attributes." + attrTpl.getAttributesField() + "数据格式不符合正则：" + attrTpl.getValueRegex();
                    DbCheckLog checkLog = generateCheckLog(dbId, dbType, dbNo, "attributes." + attrTpl.getAttributesField(), message, creator, createDate, tplType, tags);
                    saveLogs.add(checkLog);
                }
            }
        }
    }

    public void checkPublishDB() {

        MongoPagingIterator<Publish> iterator = new MongoPagingIterator<>(mongoTemplate, Publish.class, 1000);

        // 保存校验的日志
        Set<DbCheckLog> saveLogs = new HashSet<>();
        String metadataType = "publish";
        int i = 0;
        while (iterator.hasNext()) {
            log.info("publish 开始校验第{}页数据", i++);

            List<Publish> page = iterator.next();
            // 获取type对应的id在数据库是否存在
            Map<String, Set<String>> typeToExistNosMap = new HashMap<>();
            for (Publish publish : page) {
                String type = publish.getType();
                String typeId = publish.getTypeId();

                typeToExistNosMap.computeIfAbsent(type, k -> new HashSet<>()).add(typeId);
            }

            for (Map.Entry<String, Set<String>> entry : typeToExistNosMap.entrySet()) {
                String type = entry.getKey();
                Set<String> nos = entry.getValue();
                if (TypeInformation.typeInfoMap.containsKey(type)) {
                    TypeInformation typeInformation = TypeInformation.typeInfoMap.get(type);
                    List<String> existNos = dbExistNos(typeInformation.getMongoField(), nos, typeInformation.getClazz());
                    typeToExistNosMap.put(type, new HashSet<>(existNos));
                }
            }

            for (Publish dto : page) {
                if (AuditEnum.init.name().equals(dto.getAudited())) {
                    continue;
                }
                String creator = dto.getCreator();
                Date createDate = dto.getCreateDate();
                String dbId = dto.getId();
                Set<ConstraintViolation<Publish>> constraintViolations = validator.validate(dto);
                if (CollUtil.isNotEmpty(constraintViolations)) {
                    for (ConstraintViolation<Publish> violation : constraintViolations) {
                        String message = violation.getMessage();
                        String fieldName = violation.getPropertyPath().toString();

                        Field field = ReflectUtil.getField(dto.getClass(), fieldName);

                        org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);

                        if (annotation != null) {
                            if (StrUtil.isNotBlank(annotation.name())) {
                                fieldName = annotation.name();
                            }
                        }
                        DbCheckLog checkLog = generateCheckLog(dto.getId(), metadataType, dto.getTypeId(), fieldName, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }
                if (StrUtil.isNotBlank(dto.getType()) && StrUtil.isNotBlank(dto.getTypeId())) {
                    if (typeToExistNosMap.get(dto.getType()) != null && !typeToExistNosMap.get(dto.getType()).contains(dto.getTypeId())) {
                        String message = dto.getTypeId() + "在" + dto.getType() + "表中不存在!";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dto.getTypeId(), "type_id", message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    }
                }
            }

            if (CollUtil.isNotEmpty(saveLogs)) {
                dbCheckLogService.saveOrUpdateBatch(saveLogs);
                saveLogs.clear();
            }
        }
    }


    public Map<String, ExpSampleType> nameToSampleType() {
        List<ExpSampleType> list = expSampleTypeRepository.listSampleType();
        return list.stream().collect(Collectors.toMap(ExpSampleType::getName, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    public Map<String, ExpSampleType> nameToExpType() {
        List<ExpSampleType> list = expSampleTypeRepository.listExpType();
        return list.stream().collect(Collectors.toMap(ExpSampleType::getName, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    public boolean dbExistNo(String field, String no, Class clazz) {
        if (StrUtil.isBlank(no)) {
            return false;
        }
        return mongoTemplate.exists(new Query(Criteria.where(field).is(no)), clazz);
    }

    public List<String> dbExistNos(String field, Collection<String> nos, Class clazz) {
        if (CollUtil.isEmpty(nos)) {
            return new ArrayList<>();
        }
        List<String> noNilNos = nos.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        Query query = new Query(Criteria.where(field).in(noNilNos));
        query.fields().include(field);
        return mongoTemplate.findDistinct(query, field, clazz, String.class);
    }

    public DbCheckLog generateCheckLog(String dbId, String type, String typeId, String field, String message, String creator, Date createDate, String subjectType, List<String> tags) {
        DbCheckLog checkLog = new DbCheckLog();
        checkLog.setDbId(dbId);
        checkLog.setType(type);
        checkLog.setTypeId(typeId);
        checkLog.setField(field);
        checkLog.setMessage(message);
        checkLog.setCreator(creator);
        checkLog.setCreateDate(createDate);

        if (StrUtil.isNotBlank(subjectType)) {
            checkLog.setSubjectType(subjectType);
        }
        if (CollUtil.isNotEmpty(tags)) {
            checkLog.setTags(CollUtil.join(tags, "#"));
        }

        return checkLog;
    }

    /**
     * 获取表格数据，转为字符串
     */
    public static String strVal(Object obj) {
        return obj == null ? null : StrUtil.trimToNull(obj.toString());
    }

    public static Date parseDateStr(String dateStr) throws ParseException {
        // 使用严格的日期解析方法
        return DateUtils.parseDateStrictly(dateStr, null, DATE_PATTERNS);
    }


    // ===========================================================================================================================================================
    // 以下代码废弃

    public synchronized void allRelatedDataCheck() {
        if (redisService.getCacheObject(RELATE_DATA_CHECK_KEY) != null) {
            throw new ServiceException("Checking task is running, please try again later!");
        }
        redisService.setCacheObject(RELATE_DATA_CHECK_KEY, true, EXPIRE_TIME, TimeUnit.HOURS);
        ThreadUtil.execute(() -> {

            try {
                relatedDataIntegrityCheck();
            } finally {
                redisService.deleteObject(RELATE_DATA_CHECK_KEY);
            }
        });
    }

    /**
     * data数据及相关数据完整性校验
     */
    public void relatedDataIntegrityCheck() {
        MongoPagingIterator<Data> iterator = new MongoPagingIterator<>(mongoTemplate, Data.class, 1000);

        int pageNum = 0;
        while (iterator.hasNext()) {
            log.info("开始校验第{}页数据", pageNum++);

            List<Data> page = iterator.next();

            Map<String, List<DbCheckLog>> dataNoToCheckLogMap = obtainDataCheckRelateLog(page);

            // 将校验失败得data更新为false
            dataNoToCheckLogMap.forEach((k, v) -> {
                mongoTemplate.updateFirst(Query.query(Criteria.where("dat_no").is(k)), Update.update("complete", false), Data.class);
            });

        }
    }

    private Map<String, List<DbCheckLog>> obtainDataCheckRelateLog(List<Data> dataList) {
        Map<String, List<DbCheckLog>> dataNoToCheckLogMap = new LinkedHashMap<>();

        List<String> runNos = dataList.stream().map(Data::getRunNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 过滤出所有的analNos
        List<String> analNos = dataList.stream().map(Data::getAnalNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<Run> runList = mongoTemplate.find(Query.query(Criteria.where("run_no").in(runNos)), Run.class);
        List<Analysis> analysisList = mongoTemplate.find(Query.query(Criteria.where("anal_no").in(analNos)), Analysis.class);

        // 过滤出所有的expNo
        List<String> expNos = runList.stream().map(Run::getExpNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 过滤出所有的sapNo
        List<String> sapNos = runList.stream().map(Run::getSapNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<Experiment> expList = mongoTemplate.find(Query.query(Criteria.where("exp_no").in(expNos)), Experiment.class);
        List<Sample> sapList = mongoTemplate.find(Query.query(Criteria.where("sap_no").in(sapNos)), Sample.class);

        // 过滤出所有的projNo
        List<String> projNos = expList.stream().map(Experiment::getProjectNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 查询projList
        List<Project> projList = mongoTemplate.find(Query.query(Criteria.where("proj_no").in(projNos)), Project.class);

        Map<String, Project> projNoToProjMap = projList.stream().collect(Collectors.toMap(Project::getProjectNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Experiment> expNoToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Sample> sapNoToSapMap = sapList.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Run> runNoToRunMap = runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Analysis> analNoToAnalMap = analysisList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

        Map<String, ExpSampleType> expTypeMap = nameToExpType();
        Map<String, ExpSampleType> sampleTypeMap = nameToSampleType();

        Map<String, Boolean> organismMap = new HashMap<>();

        for (Data dto : dataList) {
            Set<DbCheckLog> saveLogs = new HashSet<>();

            // 校验data的数据
            Set<DbCheckLog> dataCheckErrorLogs = dataCheck(dto);
            if (CollUtil.isNotEmpty(dataCheckErrorLogs)) {
                saveLogs.addAll(dataCheckErrorLogs);
            }

            if (StrUtil.isNotEmpty(dto.getRunNo())) {
                // 校验run
                if (runNoToRunMap.containsKey(dto.getRunNo())) {
                    Run run = runNoToRunMap.get(dto.getRunNo());
                    Set<DbCheckLog> runCheckErrors = runCheck(run);
                    if (CollUtil.isNotEmpty(runCheckErrors)) {
                        saveLogs.addAll(runCheckErrors);
                    }
                    // 校验experiment
                    if (expNoToExpMap.containsKey(run.getExpNo())) {
                        Experiment exp = expNoToExpMap.get(run.getExpNo());
                        Set<DbCheckLog> expCheckErrors = expCheck(exp, expTypeMap);
                        if (CollUtil.isNotEmpty(expCheckErrors)) {
                            saveLogs.addAll(expCheckErrors);
                        }
                        // 校验project
                        if (projNoToProjMap.containsKey(exp.getProjectNo())) {
                            Project project = projNoToProjMap.get(exp.getProjectNo());
                            Set<DbCheckLog> projCheckErrors = projCheck(project);
                            if (CollUtil.isNotEmpty(projCheckErrors)) {
                                saveLogs.addAll(projCheckErrors);
                            }
                        } else {
                            String message = exp.getProjectNo() + "在Project表中不存在!";
                            DbCheckLog checkLog = generateCheckLog(exp.getId(),
                                    AuthorizeType.experiment.name(),
                                    exp.getProjectNo(),
                                    TypeInformation.typeInfoMap.get(AuthorizeType.experiment.name()).getMongoField(),
                                    message,
                                    dto.getCreator(),
                                    dto.getCreateDate(), null, null);
                            saveLogs.add(checkLog);
                        }
                    } else {
                        String message = run.getExpNo() + "在Experiment表中不存在!";
                        DbCheckLog checkLog = generateCheckLog(run.getId(),
                                AuthorizeType.run.name(), run.getRunNo(),
                                TypeInformation.typeInfoMap.get(AuthorizeType.run.name()).getMongoField(),
                                message,
                                dto.getCreator(),
                                dto.getCreateDate(), null, null);
                        saveLogs.add(checkLog);
                    }
                    // 校验sample
                    if (sapNoToSapMap.containsKey(run.getSapNo())) {
                        Sample sample = sapNoToSapMap.get(run.getSapNo());
                        Set<DbCheckLog> sampleCheckErrors = sampleCheck(sample, sampleTypeMap, organismMap);
                        if (CollUtil.isNotEmpty(sampleCheckErrors)) {
                            saveLogs.addAll(sampleCheckErrors);
                        }
                    } else {
                        String message = run.getSapNo() + "在Sample表中不存在!";
                        DbCheckLog checkLog = generateCheckLog(run.getId(),
                                AuthorizeType.run.name(), run.getRunNo(),
                                TypeInformation.typeInfoMap.get(AuthorizeType.run.name()).getMongoField(),
                                message,
                                dto.getCreator(),
                                dto.getCreateDate(), null, null);
                        saveLogs.add(checkLog);
                    }

                } else {
                    String message = dto.getRunNo() + "在Run表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dto.getId(), AuthorizeType.data.name(),
                            dto.getDatNo(),
                            TypeInformation.typeInfoMap.get(AuthorizeType.run.name()).getMongoField(),
                            message,
                            dto.getCreator(),
                            dto.getCreateDate(), null, null);
                    saveLogs.add(checkLog);
                }
            }

            if (StrUtil.isNotEmpty(dto.getAnalNo())) {
                if (analNoToAnalMap.containsKey(dto.getAnalNo())) {
                    Analysis analysis = analNoToAnalMap.get(dto.getAnalNo());

                    Map<String, Set<String>> typeToExistNosMap = collectNosFromTargetsAndPipelines(CollUtil.newArrayList(analysis));

                    Set<DbCheckLog> analysisCheckErrors = analysisCheck(analysis, typeToExistNosMap);
                    if (CollUtil.isNotEmpty(analysisCheckErrors)) {
                        saveLogs.addAll(analysisCheckErrors);
                    }
                } else {
                    String message = dto.getRunNo() + "在Analysis表中不存在!";
                    DbCheckLog checkLog = generateCheckLog(dto.getId(),
                            AuthorizeType.data.name(),
                            dto.getDatNo(),
                            TypeInformation.typeInfoMap.get(AuthorizeType.analysis.name()).getMongoField(),
                            message,
                            dto.getCreator(),
                            dto.getCreateDate(), null, null);
                    saveLogs.add(checkLog);
                }
            }

            if (CollUtil.isNotEmpty(saveLogs)) {
                dataNoToCheckLogMap.put(dto.getDatNo(), CollUtil.newArrayList(saveLogs));
            }
        }

        return dataNoToCheckLogMap;
    }

    private Set<DbCheckLog> analysisCheck(Analysis dto, Map<String, Set<String>> typeToExistNosMap) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.analysis.name();

        String dbId = dto.getId();
        String dbNo = dto.getAnalysisNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();

        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, AuthorizeType.analysis.name(), dbNo, creator, createDate);
        saveLogs.addAll(jsr303Check);

        // 校验analysis的独有关系
        if (CollUtil.isNotEmpty(dto.getTarget())) {
            for (AnalysisTarget target : dto.getTarget()) {
                if (StrUtil.isBlank(target.getType()) || CollUtil.isEmpty(target.getNos())) {
                    String message = "target的type和nos不能为空";
                    DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target", message, creator, createDate, null, null);
                    saveLogs.add(checkLog);
                } else {
                    if (!TypeInformation.typeInfoMap.containsKey(target.getType())) {
                        String message = "target的type不在取值范围内";
                        DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target.type", message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    } else {
                        for (String no : target.getNos()) {
                            if (!typeToExistNosMap.get(target.getType()).contains(no)) {
                                String message = no + "在" + target.getType() + "表中不存在";
                                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "target.nos", message, creator, createDate, null, null);
                                saveLogs.add(checkLog);
                            }
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(dto.getPipeline())) {
            for (Pipeline pipeline : dto.getPipeline()) {
                if (CollUtil.isNotEmpty(pipeline.getOutput())) {
                    for (String no : pipeline.getOutput()) {
                        if (!typeToExistNosMap.get(AuthorizeType.data.name()).contains(no)) {
                            String message = no + "在data表中不存在";
                            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "pipeline.output", message, creator, createDate, null, null);
                            saveLogs.add(checkLog);
                        }
                    }
                }
            }
        }

        return saveLogs;
    }

    private Set<DbCheckLog> sampleCheck(Sample dto, Map<String, ExpSampleType> sampleTypeMap, Map<String, Boolean> organismMap) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.sample.name();

        String dbId = dto.getId();
        String dbNo = dto.getSapNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();
        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, metadataType, dbNo, creator, createDate);
        saveLogs.addAll(jsr303Check);
        String subjectType = dto.getSubjectType();

        if (!sampleTypeMap.containsKey(subjectType)) {
            String message = "Sample Type:" + subjectType + "不在模板范围内";
            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "subject_type", message, creator, createDate, null, null);
            saveLogs.add(checkLog);
        }
        // 校验sample独有的字段
        if (StrUtil.isNotBlank(dto.getOrganism())) {
            String organism = dto.getOrganism();
            if (!organismMap.containsKey(organism)) {
                List<String> taxId = remoteDictService.getTaxIdByName(organism, SecurityConstants.INNER).getData();
//                System.out.println(organism + "：" + taxId);
                organismMap.put(organism, CollUtil.isNotEmpty(taxId));
            }
            if (!organismMap.get(organism)) {
                String message = "Sample Type:" + subjectType + "，organism:" + dto.getOrganism() + "不在合法范围内";
                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "organism", message, creator, createDate, null, null);
                saveLogs.add(checkLog);
            }
        }
        // TODO 上线开启
        // 校验attributes的内容是否符合模板中的数据
        if (sampleTypeMap.containsKey(subjectType)) {
            // sampleType对于的模板
            ExpSampleType expSampleType = sampleTypeMap.get(subjectType);

            // map的value需要转成String然后校验
            Map<String, String> attributes = dto.getAttributes();

            String type = expSampleType.getType();

            Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
            // 字典中配置的推荐属性必填个数
            Integer recommendNum = expSampleType.getRecommendNum();
            // 属性校验
            int recommendCount = 0;
            if (StrUtil.equals(BaseAttrType.required.name(), expSampleType.getProtocol()) && StrUtil.isBlank(dto.getProtocol())) {
                String message = "当type为" + type + "时protocol不能为空";
                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "protocol", message, creator, createDate, null, null);
                saveLogs.add(checkLog);
            }

            for (String attrFieldName : rangeMap.keySet()) {
                ExpSampleType.Attributes attrTpl = rangeMap.get(attrFieldName);
                // 将object转string
                String attrValue = strVal(attributes.get(attrFieldName));
                // 如果是必填项
                if (StrUtil.equals(BaseAttrType.required.name(), attrTpl.getRequired())) {
                    if (StrUtil.isBlank(attrValue)) {
                        String message = "attributes." + attrFieldName + "为必填项!";
                        DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrFieldName, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    } else {
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, null);
                    }
                } else if (StrUtil.equals(BaseAttrType.recommend.name(), attrTpl.getRequired())) {
                    if (StrUtil.isNotBlank(attrValue)) {
                        // 统计值不为空的推荐项数量
                        recommendCount++;
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, null);
                    }

                } else {
                    if (StrUtil.isNotBlank(attrValue)) {
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, subjectType, attrTpl, attrValue, creator, createDate, null);
                    }
                }
                // 如果这个模板有推荐字段数，且填实际写的字段小于推荐字段数量，则报错
                /*if (recommendNum != null && recommendCount < recommendNum) {
                    String message = "attribute推荐填写的字段数量小于" + recommendNum;
                    DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrFieldName, message, creator, createDate);
                    saveLogs.add(checkLog);
                }*/
            }
        }

        return saveLogs;
    }

    private Set<DbCheckLog> projCheck(Project dto) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.project.name();

        String dbId = dto.getId();
        String dbNo = dto.getProjectNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();
        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, metadataType, dbNo, creator, createDate);

        saveLogs.addAll(jsr303Check);

        return saveLogs;
    }

    private Set<DbCheckLog> expCheck(Experiment dto, Map<String, ExpSampleType> expTypeMap) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.experiment.name();

        String dbId = dto.getId();
        String dbNo = dto.getExpNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();

        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, metadataType, dbNo, creator, createDate);
        saveLogs.addAll(jsr303Check);
        String expType = dto.getExpType();
        if (!expTypeMap.containsKey(expType) && !StrUtil.equals(expType, "Other")) {
            String message = "exp_type:" + expType + "不在模板范围内";
            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "exp_type", message, creator, createDate, null, null);
            saveLogs.add(checkLog);
        }
        // 校验attributes的内容是否符合模板中的数据
        if (expTypeMap.containsKey(expType)) {
            ExpSampleType expSampleType = expTypeMap.get(expType);

            // 这个v需要转成String然后校验
            Map<String, Object> attributes = dto.getAttributes();

            // 模板类型
            String type = expSampleType.getType();

            Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
            // 字典中配置的推荐属性必填个数
            Integer recommendNum = expSampleType.getRecommendNum();
            // 属性校验
            int recommendCount = 0;

            // TODO 上线开启
            for (String attrField : rangeMap.keySet()) {
                ExpSampleType.Attributes attrTpl = rangeMap.get(attrField);
                // 将object转value
                String attrValue = strVal(attributes.get(attrField));
                // 如果是必填项
                if (StrUtil.equals(BaseAttrType.required.name(), attrTpl.getRequired())) {
                    if (StrUtil.isBlank(attrValue)) {
                        String message = "attributes." + attrField + "为必填项!";
                        DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrField, message, creator, createDate, null, null);
                        saveLogs.add(checkLog);
                    } else {
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, null);
                    }
                } else if (StrUtil.equals(BaseAttrType.recommend.name(), attrTpl.getRequired())) {
                    if (StrUtil.isNotBlank(attrValue)) {
                        // 统计值不为空的推荐项数量
                        recommendCount++;
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, null);
                    }

                } else {
                    if (StrUtil.isNotBlank(attrValue)) {
                        // 校验数据类型
                        validateAttrDataType(saveLogs, dbId, dbNo, type, expType, attrTpl, attrValue, creator, createDate, null);
                    }
                }
                // 如果这个模板有推荐字段数，且填实际写的字段小于推荐字段数量，则报错
                /*if (recommendNum != null && recommendCount < recommendNum) {
                    String message = "attributes填写的推荐字段数量小于推荐的数量" + recommendNum;
                    DbCheckLog checkLog = generateCheckLog(dbId, type, dbNo, "attributes." + attrField, message, creator, createDate);
                    saveLogs.add(checkLog);
                }*/
            }
        }

        return saveLogs;
    }

    private Set<DbCheckLog> runCheck(Run dto) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.run.name();

        String dbId = dto.getId();
        String dbNo = dto.getRunNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();

        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, metadataType, dbNo, creator, createDate);
        saveLogs.addAll(jsr303Check);

        return saveLogs;
    }

    private Set<DbCheckLog> dataCheck(Data dto) {
        Set<DbCheckLog> saveLogs = new HashSet<>();

        String metadataType = AuthorizeType.data.name();

        String dbId = dto.getId();
        String dbNo = dto.getDatNo();
        String creator = dto.getCreator();
        Date createDate = dto.getCreateDate();
        Set<DbCheckLog> jsr303Check = jsr303Check(dto, dbId, metadataType, dbNo, creator, createDate);

        saveLogs.addAll(jsr303Check);

        if (!CollUtil.contains(SecurityEnum.checkSecurityByShare(), dto.getSecurity())) {
            String message = "security不在取值范围内";
            DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "security", message, creator, createDate, null, null);
            saveLogs.add(checkLog);
        }
        if (StrUtil.isNotBlank(dto.getArchived())) {
            if (StrUtil.equals(ArchiveEnum.yes.name(), dto.getArchived())
                    && StrUtil.isBlank(dto.getRunNo())
                    && StrUtil.isBlank(dto.getAnalNo())) {
                String message = "archived为yes时，run_no和anal_no不能同时为空";
                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, "archived", message, creator, createDate, null, null);
                saveLogs.add(checkLog);
            }
        }
        return saveLogs;
    }


    private <T> Set<DbCheckLog> jsr303Check(T dto, String dbId, String metadataType, String dbNo, String creator, Date createDate) {
        Set<DbCheckLog> errors = new LinkedHashSet<>();
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(dto);
        if (CollUtil.isNotEmpty(constraintViolations)) {
            for (ConstraintViolation<T> violation : constraintViolations) {
                String message = violation.getMessage();
                String fieldName = violation.getPropertyPath().toString();

                Field field = ReflectUtil.getField(dto.getClass(), fieldName);
                if (field != null) {
                    org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);
                    if (annotation != null && StrUtil.isNotBlank(annotation.name())) {
                        fieldName = annotation.name();
                    }
                }
                DbCheckLog checkLog = generateCheckLog(dbId, metadataType, dbNo, fieldName, message, creator, createDate, null, null);
                errors.add(checkLog);
            }
        }
        return errors;
    }


    public Map<String, List<DbCheckLog>> relatedDataCheckByDataNos(List<String> dataNos) {
        List<Data> dataList = dataRepository.findAllByDataNoIn(dataNos);
        Map<String, List<DbCheckLog>> dataNoToCheckLogMap = obtainDataCheckRelateLog(dataList);
        return dataNoToCheckLogMap;
    }


    public List<String> findDataCheckNotPass() {
        return dataRepository.findDataCheckNotPass();
    }
}
