package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.node.TaxonomyEsDTO;
import org.biosino.common.core.utils.node.TaxonomyUtil;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDictService;
import org.biosino.es.api.external.DmsApi;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.SampleDTO;
import org.biosino.system.dto.dto.export.SampleExportDTO;
import org.biosino.system.repository.*;
import org.biosino.system.vo.metadata.PublishVO;
import org.biosino.system.vo.metadata.SampleListVO;
import org.biosino.system.vo.metadata.SampleVO;
import org.biosino.upload.api.RemoteUploadSampleService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/4/28
 */
@Service
@RequiredArgsConstructor
public class SampleService extends BaseService {
    private static final String TAXONOMY_DUPLICATED = "Taxonomy name is duplicated";

    private final SampleRepository sampleRepository;

    private final RunRepository runRepository;

    private final ExperimentRepository experimentRepository;

    private final DataRepository dataRepository;

    private final PublishRepository publishRepository;

    private final RemoteUploadSampleService remoteUploadSampleService;

    private final MessageSender messageSender;

    private final RemoteDictService remoteDictService;
    //    private final RemoteUpdateIndexService remoteUpdateIndexService;
    private final DmsApi dmsApi;

    public Page<SampleListVO> listAuditedSample(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);

        Page<Sample> page = sampleRepository.findSamplePage(queryDTO);
        // 提取所有样本编号
        List<String> sapNos = page.getContent().stream()
                .map(Sample::getSapNo)
                .collect(Collectors.toList());

        List<Run> allRuns = runRepository.findDetailBySapNoIn(sapNos);

        // 批量查询与这些样本编号关联的运行详情，并按 sapNo 分组
        Map<String, List<Run>> sapNoToRunsMap = allRuns
                .stream()
                .collect(Collectors.groupingBy(Run::getSapNo));

        // 提取所有运行的实验编号
        List<String> expNos = sapNoToRunsMap.values().stream()
                .flatMap(List::stream)
                .map(Run::getExpNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询与这些实验编号关联的实验信息
        List<Experiment> experiments = experimentRepository.findAllByExpNoIn(expNos);

        // 将实验信息映射到实验编号
        Map<String, Experiment> expNoToExperimentMap = experiments.stream()
                .collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
        // 一次性查询所有相关data
        Map<String, List<Data>> runNoToDatasMap = dataRepository
                .findDetailByRunNoIn(allRuns.stream()
                        .map(Run::getRunNo)
                        .collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Data::getRunNo));

        List<String> creators = page.getContent().stream().map(Sample::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<SampleListVO> result = page.map(x -> {
            SampleListVO vo = new SampleListVO();
            BeanUtil.copyProperties(x, vo);
            // 获取与当前样本编号关联的所有运行
            List<Run> runList = sapNoToRunsMap.getOrDefault(x.getSapNo(), Collections.emptyList());

            // 获取这些运行对应的所有实验类型
            List<String> expTypes = runList.stream()
                    .map(Run::getExpNo)
                    .distinct()
                    .map(expNo -> expNoToExperimentMap.get(expNo))
                    .filter(Objects::nonNull)
                    .map(Experiment::getExpType)
                    .distinct()
                    .collect(Collectors.toList());

            vo.setExpTypes(expTypes);

            List<Data> dataList = runList.stream()
                    .map(Run::getRunNo)
                    .flatMap(runNo -> runNoToDatasMap.getOrDefault(runNo, Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            Map<String, Long> securityMap = dataList.stream()
                    .collect(Collectors.groupingBy(Data::getSecurity, Collectors.counting()));

            for (String s : SecurityEnum.includeAllSecurity()) {
                securityMap.putIfAbsent(s, 0L);
            }

            vo.setDataCount(securityMap);

            // 设置提交人
            vo.setSubmitter(x.getSubmitter().getFirstName() + " " + x.getSubmitter().getLastName());

            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });


        return result;
    }

    public Page<Sample> listExportSample(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        Page<Sample> page = sampleRepository.findSamplePage(queryDTO);
        return page;
    }

    public SampleVO getSapInfoByNo(String sapNo) {
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        SampleVO result = new SampleVO();
        BeanUtil.copyProperties(sample, result);
        List<PublishVO> publishVo = getPublishVo(AuthorizeType.sample, sapNo);
        result.setCustomAttributes(getSampleCustomAttributes(sample));
        result.setPublish(publishVo);

        result.setOrganism(initFullOrganism(sample));
        return result;
    }

    private String initFullOrganism(final Sample sample) {
        String organism = sample.getOrganism();
        String taxId = sample.getTaxId();
        if (organism != null && taxId != null) {
            return StrUtil.format("{} [{} {}]", organism, TaxonomyUtil.TAX_ID_PRE, taxId);
        } else {
            return null;
        }
    }

    public SampleVO updateSample(SampleDTO sampleDTO) {
        String organism = sampleDTO.getOrganism();
        if (StrUtil.isNotBlank(organism)) {
            final TaxonomyEsDTO taxonomyNode = findTaxId(organism);
            if (taxonomyNode == null) {
                throw new ServiceException("Unknown Organism");
            }
            organism = taxonomyNode.getScientificName();
            sampleDTO.setOrganism(organism);
            sampleDTO.setTaxId(taxonomyNode.getTaxId());
        }

        Sample sample = sampleRepository.findTopBySapNo(sampleDTO.getSapNo()).orElseThrow(() -> new ServiceException("not found sample: " + sampleDTO.getSapNo()));
        BeanUtil.copyProperties(sampleDTO, sample);
        // 更新时间
        sample.setOperator(SecurityUtils.getUserId().toString());
        sample.setOperationDate(new Date());
        setCustomAttr(sample, sampleDTO.getCustomAttributes());
        // 更新publish
        savePublish(sampleDTO.getPublish(), AuthorizeType.sample, sample.getSapNo(), sample.getCreator());

        sampleRepository.save(sample);
        // 通知es更新索引
        updateEsData(AuthorizeType.sample.name(), sample.getSapNo());
        return getSapInfoByNo(sample.getSapNo());
    }

    /*private String findTaxId(final String organism) {
        return dmsApi.findTaxId(organism, remoteDictService);
    }*/

    private TaxonomyEsDTO findTaxId(String organism) {
        organism = StrUtil.trimToNull(organism);
        if (organism == null) {
            return null;
        }
        TaxonomyEsDTO taxonomyNode = TaxonomyUtil.parseTaxInfo(organism);
        if (taxonomyNode != null) {
            final String taxId = taxonomyNode.getTaxId();
            final List<String> taxIds = dmsApi.findTaxId(taxonomyNode.getScientificName(), remoteDictService);
            if (taxId != null) {
                if (taxIds.contains(taxId)) {
                    taxonomyNode.setTaxId(taxId);
                } else {
                    // throw new ServiceException("Taxonomy not exist");
                    taxonomyNode = null;
                }
            } else {
                final int idCount = CollUtil.size(taxIds);
                if (idCount > 1) {
                    throw new ServiceException(TAXONOMY_DUPLICATED);
                } else if (idCount == 1) {
                    taxonomyNode.setTaxId(taxIds.get(0));
                } else {
                    taxonomyNode = null;
                }
            }
        }
        return taxonomyNode;
    }


    public DeleteCheckResultVO deleteCheck(String sapNo) {
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        R<DeleteCheckResultVO> r = remoteUploadSampleService.deleteCheck(sample.getSapNo(), sample.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public DeleteCheckResultVO batchDeleteCheck(List<String> sapNoList) {
        DeleteCheckResultVO vo = new DeleteCheckResultVO();
        Set<String> sapNos = new LinkedHashSet<>();
        Set<String> runNos = new LinkedHashSet<>();
        Set<String> dataNos = new LinkedHashSet<>();
        Set<DeleteErrorMsgVO> errors = new LinkedHashSet<>();

        for (String sapNo : sapNoList) {
            DeleteCheckResultVO checkResultVO = deleteCheck(sapNo);
            sapNos.addAll(checkResultVO.getSapNos());
            runNos.addAll(checkResultVO.getRunNos());
            dataNos.addAll(checkResultVO.getDataNos());
            errors.addAll(checkResultVO.getErrors());
        }

        vo.setSapNos(new ArrayList<>(sapNos));
        vo.setRunNos(new ArrayList<>(runNos));
        vo.setDataNos(new ArrayList<>(dataNos));
        vo.setErrors(new ArrayList<>(errors));

        return vo;
    }

    public void deleteAll(String sapNo) {
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        R r = remoteUploadSampleService.deleteAll(sample.getSapNo(), sample.getCreator());
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
    }

    public void batchDeleteAll(List<String> sapNos) {
        for (String sapNo : sapNos) {
            deleteAll(sapNo);
        }
    }

    public void updateCreator(String sapNo, String creator) {
        if (StrUtil.isBlank(creator) || StrUtil.isBlank(sapNo)) {
            throw new ServiceException("Parameter cannot be empty");
        }

        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        // 查询邮件的用户
        MemberDTO data = getMemberInfoByEmail(creator);

        String newCreator = data.getId();
        if (newCreator == null) {
            throw new ServiceException(StrUtil.format("User {} not found", creator));
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(sapNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The sample cannot change creator because it is associated with other data");
        }

        // 添加修改的日志
        addChangeCreatorLog(sapNo, AuthorizeType.sample.name(), sample.getCreator(), newCreator, checkResultVO);

        // 修改sample以及关联数据的creator
        experimentRepository.updateCreatorByExpNoIn(checkResultVO.getExpNos(), newCreator);
        sampleRepository.updateCreatorBySapNoIn(checkResultVO.getSapNos(), newCreator);
        runRepository.updateCreatorByRunNoIn(checkResultVO.getRunNos(), newCreator);
        dataRepository.updateCreatorByDatNoIn(checkResultVO.getDataNos(), newCreator);

        // 修改数据相关publish的creator
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.experiment.name(), checkResultVO.getExpNos(), newCreator);
        publishRepository.updateCreatorByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos(), newCreator);

        // 通知更新索引
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(), new IndexUpdateMsg(AuthorizeType.data.name(), checkResultVO.getDataNos()));
        }
    }

    public List<String> getAuditedOrganism() {
        return sampleRepository.getAuditedOrganism();
    }

    public List<String> getAuditedSapType() {
        return sampleRepository.getAuditedSapType();
    }

    public List<Map<String, String>> getSampleCustomAttributes(Sample sample) {
        List<Map<String, String>> result = new ArrayList<>();

        Map<String, String> customAttr = sample.getCustomAttr();
        Map<String, String> customAttrDesc = sample.getCustomAttrDesc();

        if (customAttr != null && !customAttr.isEmpty()) {
            for (Map.Entry<String, String> entry : customAttr.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                Map<String, String> map = new HashMap<>();
                map.put("attr", key);
                map.put("value", value);
                if (customAttrDesc != null && customAttrDesc.containsKey(key)) {
                    map.put("attrDesc", customAttrDesc.get(key));
                }
                result.add(map);
            }
        }
        sample.setCustomAttr(customAttr);
        return result;
    }

    private void setCustomAttr(Sample sample, List<Map<String, String>> customAttributes) {
        if (customAttributes != null) {
            final Map<String, String> customAttrMap = new LinkedHashMap<>();
            final Map<String, String> customAttrDescMap = new LinkedHashMap<>();
            for (Map<String, String> item : customAttributes) {
                String attr = item.get("attr");
                String attrDesc = item.get("attrDesc");
                String value = item.get("value");
                // 排除空属性
                if (StrUtil.isNotBlank(attr) && StrUtil.isNotBlank(value)) {
                    customAttrMap.put(attr, value);
                    if (StrUtil.isNotBlank(attrDesc)) {
                        customAttrDescMap.put(attr, attrDesc);
                    }
                }
            }
            if (!customAttrMap.isEmpty()) {
                sample.setCustomAttr(customAttrMap);
            } else {
                sample.setCustomAttr(null);
            }
            if (!customAttrDescMap.isEmpty()) {
                sample.setCustomAttrDesc(customAttrDescMap);
            } else {
                sample.setCustomAttrDesc(null);
            }
        }
    }

    public List<String> getSampleNos(MetadataQueryDTO queryDTO) {
        queryDTO.setPageSize(-1);
        return sampleRepository.getSampleNos(queryDTO);
    }

    public File exportSample(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        MongoPagingIterator<Sample> iterator = sampleRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "sample.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Sample> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Sample sample : next) {
                    SampleExportDTO item = BeanUtil.copyProperties(sample, SampleExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
