package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.repository.SampleCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SampleCustomRepositoryImpl implements SampleCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Class<Sample> clz() {
        return Sample.class;
    }

    @Override
    public Sample findByNo(String sapNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sap_no").is(sapNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Sample.class);
    }

    public Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name());
    }

    @Override
    public List<Sample> findDetailBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where("sap_no").in(sapNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("sap_no")
                .include("name")
                .include("description")
                .include("organism")
                .include("subject_type");
        return mongoTemplate.find(query, clz());
    }

    @Override
    public List<Sample> findAllBySapNoIn(final List<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return new ArrayList<>();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("sap_no").in(sapNos));
        final List<Sample> list = mongoTemplate.find(new Query(new Criteria().andOperator(condition)), clz());
        // 按照输入编号的顺序排序
        return list.stream().sorted(Comparator.comparingInt(x -> sapNos.indexOf(x.getSapNo()))).collect(Collectors.toList());
    }

    @Override
    public List<Sample> findTempBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return new ArrayList<>();
        }
        Query query = new Query(Criteria.where(tempKey("sap_no")).in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public void updateToDeleteAllBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return;
        }
        Query query = new Query(Criteria.where("sap_no").in(sapNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name()).set("update_date", new Date());
        mongoTemplate.updateMulti(query, update, Sample.class);
    }

    @Override
    public void updateCreatorBySapNoIn(Collection<String> sapNos, String creator) {
        if (CollUtil.isEmpty(sapNos)) {
            return;
        }
        Query query = new Query(Criteria.where("sap_no").in(sapNos));
        Update update = new Update().set("creator", creator)
                .set("operator", SecurityUtils.getUserId().toString())
                .set("operation_date", new Date());
        mongoTemplate.updateMulti(query, update, Sample.class);
    }

    @Override
    public Page<Sample> findSamplePage(MetadataQueryDTO queryDTO) {
        Query query = getSampleQuery(queryDTO);

        // 查询数据量
        long total = mongoTemplate.count(query, Sample.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Sample> content = mongoTemplate.find(query, Sample.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Query getSampleQuery(MetadataQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        String name = queryDTO.getName();
        if (StrUtil.isNotBlank(name)) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            criteriaList.add(Criteria.where("sap_no").in(queryDTO.getNos()));
        }

        if (StrUtil.isNotEmpty(queryDTO.getSubmitterEmail())) {
            criteriaList.add(Criteria.where("submitter.email").is(queryDTO.getSubmitterEmail()));
        }

        if (StrUtil.isNotBlank(queryDTO.getSubmitterOrgName())) {
            criteriaList.add(Criteria.where("submitter.org_name").is(queryDTO.getSubmitterOrgName()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getTags())) {
            criteriaList.add(Criteria.where("source_project").in(queryDTO.getTags()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        if (CollUtil.isNotEmpty(queryDTO.getOrganisms())) {
            criteriaList.add(Criteria.where("organism").in(queryDTO.getOrganisms()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getSapTypes())) {
            criteriaList.add(Criteria.where("subject_type").in(queryDTO.getSapTypes()));
        }
        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (CollUtil.isNotEmpty(queryDTO.getAdvQueryList())) {
            // 添加高级搜索条件
            List<MetadataQueryDTO.AdvQueryDTO> advQueryDTOList = queryDTO.getAdvQueryList().stream()
                    .filter(x -> StrUtil.isNotBlank(x.getQueryField()) && StrUtil.isNotBlank(x.getInputValue()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(advQueryDTOList)) {
                Criteria criteria = null;
                for (MetadataQueryDTO.AdvQueryDTO advQueryDTO : advQueryDTOList) {
                    Criteria tempCriteria = Criteria.where(advQueryDTO.getQueryField()).is(advQueryDTO.getInputValue());
                    if (StrUtil.containsIgnoreCase(advQueryDTO.getRelation(), "NOT")) {
                        tempCriteria = Criteria.where(advQueryDTO.getQueryField()).ne(advQueryDTO.getInputValue());
                    }
                    if (StrUtil.containsIgnoreCase(advQueryDTO.getRelation(), "LIKE")) {
                        Pattern pattern = Pattern.compile("^.*" + advQueryDTO.getInputValue() + ".*$", Pattern.CASE_INSENSITIVE);
                        tempCriteria = Criteria.where(advQueryDTO.getQueryField()).regex(pattern);
                    }
                    if (criteria == null) {
                        criteria = tempCriteria;
                    } else {
                        switch (advQueryDTO.getRelation().toUpperCase()) {
                            case "AND":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "OR":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            case "AND NOT":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "AND LIKE":
                                criteria = new Criteria().andOperator(criteria, tempCriteria);
                                break;
                            case "OR NOT":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            case "OR LIKE":
                                criteria = new Criteria().orOperator(criteria, tempCriteria);
                                break;
                            default:
                                break;
                        }
                    }
                }
                criteriaList.add(criteria);
            }
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));
        return query;
    }

    @Override
    public Optional<Sample> findTopBySapNo(String sapNo) {
        if (StrUtil.isBlank(sapNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(new Criteria().orOperator(
                Criteria.where("sap_no").is(sapNo),
                Criteria.where("used_ids").in(sapNo)));

        Query query = new Query(new Criteria().andOperator(condition));
        Sample sample = mongoTemplate.findOne(query, Sample.class);

        return Optional.ofNullable(sample);
    }

    @Override
    public List<String> getAuditedOrganism() {
        Query query = new Query(baseCriteria());
        query.fields().include("organism");
        return mongoTemplate.findDistinct(query, "organism", Sample.class, String.class);
    }

    @Override
    public List<String> getAuditedSapType() {
        Query query = new Query(baseCriteria());
        query.fields().include("subject_type");
        return mongoTemplate.findDistinct(query, "subject_type", Sample.class, String.class);
    }

    @Override
    public List<String> getSampleNos(MetadataQueryDTO queryDTO) {
        Query query = getSampleQuery(queryDTO);
        return mongoTemplate.findDistinct(query, "sap_no", Sample.class, String.class);
    }

    @Override
    public Page<Sample> findPublicPageBySapNoIn(Collection<String> sapNos, Pageable pageable) {
        if (CollUtil.isEmpty(sapNos)) {
            return null;
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(baseCriteria());
        condition.add(Criteria.where("sap_no").in(sapNos));
        condition.add(Criteria.where("visible_status").is(VisibleStatusEnum.Accessible.name()));

        Query query = new Query(new Criteria().andOperator(condition));
        // 查询数据量
        long total = mongoTemplate.count(query, Sample.class);

        // 添加分页和排序
        query.with(pageable);

        // 查询query
        List<Sample> content = mongoTemplate.find(query, Sample.class);

        return new PageImpl<>(content, pageable, total);
    }

    @Override
    public MongoPagingIterator<Sample> getPagingIterator(MetadataQueryDTO queryDTO) {
        Query query = getSampleQuery(queryDTO);
        return new MongoPagingIterator<>(mongoTemplate, Sample.class, query, 5000);
    }
}
