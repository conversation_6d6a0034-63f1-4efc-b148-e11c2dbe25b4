import request from '@/utils/request';

const baseURL = '/system/fastqcTask';

// 列表
export function listFastQCTask(data) {
  return request({
    url: `${baseURL}/list`,
    method: 'post',
    data: data,
  });
}

export function getFastQCTaskInfo(no) {
  return request({
    url: `${baseURL}/getByNo/${no}`,
    method: 'get',
  });
}

export function retryFastQCTask(params) {
  return request({
    url: `${baseURL}/retry`,
    method: 'get',
    params: params,
  });
}

export function changeTaskPriority(params) {
  return request({
    url: `${baseURL}/changePriority`,
    method: 'get',
    params: params,
  });
}
