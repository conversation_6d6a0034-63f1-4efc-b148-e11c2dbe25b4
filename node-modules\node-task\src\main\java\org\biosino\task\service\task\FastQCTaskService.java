package org.biosino.task.service.task;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.Constants;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.FastQCTaskStartMsg;
import org.biosino.common.rabbitmq.msg.FastQCTaskStatusMsg;
import org.biosino.common.redis.service.RedisService;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.task.repository.DataRepository;
import org.biosino.task.repository.FastQCTaskRepository;
import org.biosino.task.repository.RunRepository;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FastQCTaskService {

    private final RabbitAdmin rabbitAdmin;

    private final MessageSender messageSender;

    private final FastQCTaskRepository fastQCTaskRepository;

    private final DataRepository dataRepository;

    private final RunRepository runRepository;

    private final static String[] SEQKIT_COMMA_FILED = {"num_seqs", "sum_len"};

    private final RedisService redisService;

    private final static String PUSH_FASTQC_TASK_KEY = "push_fastqc_task_key";

    private final static String PUSH_FASTQC_HP_TASK_KEY = "push_fastqc_hp_task_key";

    private final static Long EXPIRE_TIME = 5L;

    public synchronized void pushFastQCTaskStartMsg() {
        if (redisService.getCacheObject(PUSH_FASTQC_TASK_KEY) != null) {
            log.info("pushFastQcTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(PUSH_FASTQC_TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);

        try {
            Properties startProps = rabbitAdmin.getQueueProperties("fastqc_task_start_queue");
            // 证明队列不存在，
            if (startProps == null) {
                log.warn("fastqc_task_start_queue not exist");
                throw new ServiceException("fastqc_task_start_queue not exist");
            }
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("fastqc_task_start_queue messageCount: {}", messageCount);
                return;
            }

            int pageSize = 40;
            List<FastQCTask> taskList = fastQCTaskRepository.getReadyTasksAndPriorityLte(4, pageSize);

            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }

            List<FastQCTaskStartMsg> startMsgs = new ArrayList<>();
            for (FastQCTask fastQCTask : taskList) {
                FastQCTaskStartMsg startMsg = new FastQCTaskStartMsg();
                String fastqSuffix = getFastqSuffix(fastQCTask.getDataFileName());

                startMsg.setDataNo(fastQCTask.getDataNo());
                Date dataCreateDate = fastQCTask.getDataCreateDate();
                String baseDir = DateUtils.dateTimeFormat4Ftp(dataCreateDate);
                startMsg.setResultBaseDir(baseDir);
                startMsg.setFileType(fastqSuffix);
                startMsg.setDataFilePath(fastQCTask.getDataFilePath());
                if (fastQCTask.getPriority() >= 5) {
                    startMsg.setPriority("high");
                } else {
                    startMsg.setPriority("low");
                }
                startMsg.setPriorityInt(fastQCTask.getPriority());
                String dataFileName = StrUtil.replaceIgnoreCase(fastQCTask.getDataFileName(), fastqSuffix, "");
                startMsg.setDataFileName(dataFileName);
                startMsgs.add(startMsg);

                fastQCTask.setStatus(FastQCTaskStatusEnum.queuing.name());
                fastQCTask.setUpdateDate(new Date());
            }

            fastQCTaskRepository.saveAll(taskList);

            for (FastQCTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayPriorityMsg("fastqc_task_start_routing_key", startMsg, startMsg.getPriorityInt());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(PUSH_FASTQC_TASK_KEY);
        }
    }

    public synchronized void pushFastQCHpTaskStartMsg() {
        if (redisService.getCacheObject(PUSH_FASTQC_HP_TASK_KEY) != null) {
            log.info("pushFastQCHpTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(PUSH_FASTQC_HP_TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);

        try {
            Properties startProps = rabbitAdmin.getQueueProperties("fastqc_hp_task_start_queue");
            // 证明队列不存在，
            if (startProps == null) {
                log.warn("fastqc_hp_task_start_queue not exist");
                throw new ServiceException("fastqc_hp_task_start_queue not exist");
            }
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("fastqc_hp_task_start_queue messageCount: {}", messageCount);
                return;
            }

            int pageSize = 40;
            List<FastQCTask> taskList = fastQCTaskRepository.getReadyTasksAndPriorityGte(5, pageSize);

            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }

            List<FastQCTaskStartMsg> startMsgs = new ArrayList<>();
            for (FastQCTask fastQCTask : taskList) {
                FastQCTaskStartMsg startMsg = new FastQCTaskStartMsg();
                String fastqSuffix = getFastqSuffix(fastQCTask.getDataFileName());

                startMsg.setDataNo(fastQCTask.getDataNo());
                Date dataCreateDate = fastQCTask.getDataCreateDate();
                String baseDir = DateUtils.dateTimeFormat4Ftp(dataCreateDate);
                startMsg.setResultBaseDir(baseDir);
                startMsg.setFileType(fastqSuffix);
                startMsg.setDataFilePath(fastQCTask.getDataFilePath());
                if (fastQCTask.getPriority() >= 5) {
                    startMsg.setPriority("high");
                } else {
                    startMsg.setPriority("low");
                }
                startMsg.setPriorityInt(fastQCTask.getPriority());
                String dataFileName = StrUtil.replaceIgnoreCase(fastQCTask.getDataFileName(), fastqSuffix, "");
                startMsg.setDataFileName(dataFileName);
                startMsgs.add(startMsg);

                fastQCTask.setStatus(FastQCTaskStatusEnum.queuing.name());
                fastQCTask.setUpdateDate(new Date());
            }

            fastQCTaskRepository.saveAll(taskList);

            for (FastQCTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayPriorityMsg("fastqc_hp_task_start_routing_key", startMsg, startMsg.getPriorityInt());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(PUSH_FASTQC_HP_TASK_KEY);
        }
    }

    public void handlerTaskCreate(List<String> dataNos) {
        // 查询有哪些dataNo已经有了FastQc任务
        Map<String, FastQCTask> dataNotoFastQcTaskMap = fastQCTaskRepository.findByDataNoIn(dataNos).stream().collect(Collectors.toMap(FastQCTask::getDataNo, Function.identity(), (existingValue, newValue) -> existingValue));
        // 查询这些Data
        List<Data> dataList = dataRepository.findByDatNoIn(dataNos);
        // 查询这些Data对应的Run
        List<String> runNos = dataList.stream().map(Data::getRunNo).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, Run> runNoToRunMap = runRepository.findAllByRunNoIn(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<FastQCTask> saveFastQCTasks = new ArrayList<>();
        for (Data data : dataList) {
            FastQCTask existTask = dataNotoFastQcTaskMap.get(data.getDatNo());
            // 如果已经存在fastqc的任务，看对应的任务的expNo、sapNo是否需要补充
            if (existTask != null) {
                if (data.getRunNo() != null
                        && runNoToRunMap.containsKey(data.getRunNo())
                        && StrUtil.startWith(data.getRunNo(), "OER")) {
                    Run run = runNoToRunMap.get(data.getRunNo());
                    existTask.setExpNo(run.getExpNo());
                    existTask.setSapNo(run.getSapNo());
                    // 如果文件名不相同，那么需要更新文件名，得重新跑fastqc
                    String dataName = data.getTempData() != null ? data.getTempData().getName() : data.getName();
                    if (!StrUtil.equals(existTask.getDataFileName(), dataName)) {
                        existTask.setDataFileName(dataName);
                        existTask.setStatus(FastQCTaskStatusEnum.ready.name());
                    }
                    saveFastQCTasks.add(existTask);
                }
            } else {
                if (StrUtil.endWithAnyIgnoreCase(data.getName(), Constants.FASTQ_SUFFIX)
                        || StrUtil.endWithAnyIgnoreCase(data.getFileName(), Constants.FASTQ_SUFFIX)) {
                    FastQCTask fastQCTask = new FastQCTask();
                    fastQCTask.setPriority(5);
                    fastQCTask.setDataNo(data.getDatNo());
                    fastQCTask.setDataFileName(data.getName());
                    fastQCTask.setDataFilePath(data.getFilePath());
                    fastQCTask.setDataFileSize(data.getFileSize());
                    fastQCTask.setDataCreateDate(data.getCreateDate());
                    fastQCTask.setCreateDate(new Date());
                    fastQCTask.setStatus(FastQCTaskStatusEnum.ready.name());
                    saveFastQCTasks.add(fastQCTask);
                }
            }
        }
        for (FastQCTask saveFastQCTask : saveFastQCTasks) {
            if (saveFastQCTask.getPriority() != null && saveFastQCTask.getPriority() < 5) {
                saveFastQCTask.setPriority(5);
            }
        }

        if (CollUtil.isNotEmpty(saveFastQCTasks)) {
            fastQCTaskRepository.saveAll(saveFastQCTasks);
        }
    }

    public void handlerStatusChange(FastQCTaskStatusMsg msg) {
        FastQCTask fastQCTask = fastQCTaskRepository.findFirstByDataNo(msg.getDataNo())
                .orElseThrow(() -> new ServiceException(StrUtil.format("task {} not exist", msg.getDataNo())));

        if (StrUtil.equals(FastQCTaskStatusEnum.running.name(), msg.getStatus())) {
            fastQCTask.setStatus(FastQCTaskStatusEnum.running.name());
            fastQCTask.setUpdateDate(new Date());
            fastQCTaskRepository.save(fastQCTask);
            return;
        }

        if (StrUtil.equals(FastQCTaskStatusEnum.failed.name(), msg.getStatus())) {
            fastQCTask.setStatus(FastQCTaskStatusEnum.failed.name());
            fastQCTask.setUpdateDate(new Date());
            fastQCTask.setFailCause(msg.getFailCause());
            if (StrUtil.isNotBlank(msg.getErrorLogPath())) {
                fastQCTask.setErrorLogPath(msg.getErrorLogPath());
            }
            // 清除无关数据
            fastQCTask.setFastqcResult(null);
            fastQCTask.setSeqkitResult(null);
            fastQCTask.setUseTime(null);
            fastQCTaskRepository.save(fastQCTask);
            return;
        }

        if (StrUtil.equals(FastQCTaskStatusEnum.success.name(), msg.getStatus())) {
            handlerTaskSuccess(msg);
            return;
        }
    }

    /**
     * 测试json
     * {
     * "task_no": "FASTQC00000001",
     * "status": "success",
     * "data": {
     * "fastqc_html_file_path": "/20180125/FASTQC00000001/fastqc/test_1_fastqc.html",
     * "fastqc_report_file_path": "/20180125/FASTQC00000001/fastqc/test_1.fastq_trimming_report.txt",
     * "fastqc_zip_file_path": "/20180125/FASTQC00000001/fastqc/test_1_fastqc.zip",
     * "fastqc_version": "",
     * "seqkit_file_path": "/20180125/FASTQC00000001/seqkit/test.seqkit.stats.txt",
     * "seqkit_version": ""
     * }
     * }
     */
    private void handlerTaskSuccess(FastQCTaskStatusMsg msg) {
        FastQCTask fastQCTask = fastQCTaskRepository.findFirstByDataNo(msg.getDataNo())
                .orElseThrow(() -> new ServiceException(StrUtil.format("task {} not exist", msg.getDataNo())));

        try {
            String useTime = DateUtil.formatBetween(fastQCTask.getUpdateDate(), new Date(), BetweenFormatter.Level.MINUTE);
            fastQCTask.setUseTime(useTime);
            fastQCTask.setStatus(FastQCTaskStatusEnum.success.name());
            fastQCTask.setUpdateDate(new Date());
            // 清除无关数据
            fastQCTask.setFailCause(null);

            // 解析fastqc结果内容
            FastQCTaskStatusMsg.ResultData resultData = msg.getData();

            if (!FileUtil.file(DirConstants.FASTQC_HOME, resultData.getFastqcHtmlFilepath()).exists()) {
                throw new ServiceException("fastqc html file not exist");
            }
            // if (!FileUtil.file(DirConstants.FASTQC_HOME, resultData.getFastqcReportFilepath()).exists()) {
            //     throw new ServiceException("fastqc report file not exist");
            // }
            if (!FileUtil.file(DirConstants.FASTQC_HOME, resultData.getFastqcZipFilepath()).exists()) {
                throw new ServiceException("fastqc zip file not exist");
            }
            if (!FileUtil.file(DirConstants.FASTQC_HOME, resultData.getSeqkitFilepath()).exists()) {
                throw new ServiceException("seqkit file not exist");
            }

            FastQCTask.FastQCResult fastQCResult = new FastQCTask.FastQCResult();
            fastQCResult.setHtmlFilePath(FilenameUtils.separatorsToUnix(resultData.getFastqcHtmlFilepath()));
            // fastQCResult.setReportFilePath(FilenameUtils.separatorsToUnix(resultData.getFastqcReportFilepath()));
            fastQCResult.setZipFilePath(FilenameUtils.separatorsToUnix(resultData.getFastqcZipFilepath()));
            fastQCResult.setVersion(resultData.getFastqcVersion());

            List<String> lines = FileUtil.readUtf8Lines(FileUtil.file(DirConstants.FASTQC_HOME, resultData.getSeqkitFilepath()));
            if (lines.size() < 2) {
                throw new ServiceException("seqkit file content format is not correct");
            }

            Field[] fields = ReflectUtil.getFields(FastQCTask.SeqkitResult.class);
            HashMap<String, Field> headAliasMap = new HashMap<>();
            for (Field field : fields) {
                Alias annotation = field.getAnnotation(Alias.class);
                if (annotation != null) {
                    String value = annotation.value();
                    headAliasMap.put(value, field);
                }

            }
            List<String> heads = Arrays.stream(lines.get(0).split(" ")).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            List<String> datas = Arrays.stream(lines.get(1).split(" ")).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            if (heads.size() != datas.size()) {
                throw new ServiceException("seqkit file content format is not correct");
            }

            FastQCTask.SeqkitResult seqkitResult = new FastQCTask.SeqkitResult();

            for (int i = 0; i < heads.size(); i++) {
                String head = heads.get(i);
                String data = datas.get(i);
                if (ArrayUtil.contains(SEQKIT_COMMA_FILED, head)) {
                    data = datas.get(i).replace(",", "");
                }
                Field field = headAliasMap.get(head);
                if (field != null) {
                    ReflectUtil.setFieldValue(seqkitResult, field, data);
                }
            }

            seqkitResult.setFilePath(FilenameUtils.separatorsToUnix(resultData.getSeqkitFilepath()));
            seqkitResult.setVersion(resultData.getSeqkitVersion());

            fastQCTask.setFastqcResult(fastQCResult);
            fastQCTask.setSeqkitResult(seqkitResult);
        } catch (Exception e) {
            e.printStackTrace();
            fastQCTask.setStatus(FastQCTaskStatusEnum.failed.name());
            fastQCTask.setUpdateDate(new Date());
            fastQCTask.setFailCause(e.getMessage());
        } finally {
            fastQCTaskRepository.save(fastQCTask);
        }
        // 通知es更新data的索引
        messageSender.sendDelayMsg(RouterKeyEnum.es_index_update_key.name(),
                new IndexUpdateMsg(AuthorizeType.fastqc.name(), CollUtil.newArrayList(fastQCTask.getDataNo())));
    }

    public String getFastqSuffix(String filename) {
        for (String fastqSuffix : Constants.FASTQ_SUFFIX) {
            if (StrUtil.endWithIgnoreCase(filename, fastqSuffix)) {
                return fastqSuffix;
            }
        }
        return null;
    }
}
