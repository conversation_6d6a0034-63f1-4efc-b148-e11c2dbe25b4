package org.biosino.system.repository;

import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.ModifySourceDTO;
import org.biosino.system.dto.dto.SourceProjectMetadataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


@Repository
public interface ProjectCustomRepository {

    Project findByNo(String projectNo);

    List<Project> findAllProject();

    Project findPublicProjectByNo(String projectNo);

    List<Project> findPublicProjectByNos(Collection<String> projectNos);

    Page<Project> findProjectPage(MetadataQueryDTO queryDTO);

    Optional<Project> findTopByProjectNo(String projNo);

    void updateToDeleteAllByProjectNo(Collection<String> projNos);

    void updateCreatorByProjectNo(Collection<String> projNos, String creator);

    void batchModifySource(ModifySourceDTO modifySourceDTO, String mongofield, Class clazz);

    Page getSourceProjectMetadataPage(SourceProjectMetadataQueryDTO dto, TypeInformation typeInformation);

    List<String> getSourceProjectMetadataId(SourceProjectMetadataQueryDTO queryDTO, TypeInformation typeInformation);

    MongoPagingIterator<Project> getPagingIterator(MetadataQueryDTO queryDTO);
}
