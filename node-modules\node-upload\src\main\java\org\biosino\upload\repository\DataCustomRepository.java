package org.biosino.upload.repository;

import org.biosino.common.mongo.entity.Data;
import org.biosino.upload.dto.DataQuery;
import org.biosino.upload.dto.SelectQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataCustomRepository {
    PageImpl<Data> findDataPage(DataQuery queryVO);

    PageImpl<Data> findTempDataPage(DataQuery queryVO);

    Page<Data> getAccessableDataPage(SelectQueryDTO queryVO);

    Data getNoOpenDataByDatNo(String datNo);

    List<Data> findAllByNos(Collection<String> collection, String creator);

    List<Data> findAllPrivateByDataNoIn(Collection<String> collection, String creator);

    List<Data> findAllByDataNoIn(Collection<String> dataNos, String creator);

    boolean existsUserData(String datNo, String creator);

    List<Data> findTempDetailByAnalNo(String analNo);

    List<Data> getNoOpenDataByDatNos(Collection<String> datNos);

    Data findByNo(String datNo);

    Data findTempByNo(String datNo);

    List<Data> findTempByRunNoIn(Collection<String> runNos);

    void updateToDeleteAllByDatNoIn(Collection<String> dataNos);

    List<Data> findAllByAnalysisNo(String analNo);

    List<Data> findTempByAnalysisNo(String analNo);

    Optional<Data> findByDatNo(String datNo);

    List<Data> findAllByRunNoIn(Collection<String> runNos);

    Optional<Data> findFirstUnarchivedByMd5(String md5, String creator);

    long countUnarchivedByCreator(String memberId);

    List<Data> findAllTempByDataNoIn(Collection<String> dataNos);

    boolean existsByAnalNo(String analysisNo);

    List<Data> findByTempDataRunNoIn(Collection<String> runNos);
}
