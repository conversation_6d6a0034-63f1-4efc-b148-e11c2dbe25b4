package org.biosino.upload.controller;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.dto.DataQuery;
import org.biosino.upload.dto.FtpFileLogQueryDTO;
import org.biosino.upload.service.DataService;
import org.biosino.upload.service.FtpFileLogService;
import org.biosino.upload.vo.DataVO;
import org.biosino.upload.vo.FtpFileLogVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2023/12/27
 */
@RestController
@RequestMapping("/data")
@RequiredArgsConstructor
public class DataController extends BaseController {

    private final FtpFileLogService ftpFileLogService;

    private final DataService dataService;

    /**
     * 查询sftp未校验的文件
     */
    @RequestMapping("/list/unchecked")
    public AjaxResult listUnchecked(FtpFileLogQueryDTO query) {
        List<FtpFileLogVO> list = ftpFileLogService.listUnchecked(query);
        return AjaxResult.success(list);
    }

    /**
     * 删除FTP_HOME中文件
     */
    @RequestMapping("/ftpFile/delete")
    @Log(system = SytemEnum.APP, businessType = BusinessType.DELETE, module1 = "Submit", module2 = "Raw Data", module3 = "Unchecked / Checking Data", desc = "删除FTP_HOME中文件")
    public AjaxResult deleteFtpFile(@RequestBody String[] ids) {
        ftpFileLogService.deleteFtpFileByIds(Arrays.asList(ids));
        return AjaxResult.success();
    }

    /**
     * 查询提交过校验的列表
     */
    @RequestMapping("/list/checking")
    public TableDataInfo listChecking(FtpFileLogQueryDTO query) {
        startPage();
        TableDataInfo result = ftpFileLogService.listChecking(query);
        return result;
    }

    /**
     * 查询未归档的数据
     */
    @RequestMapping("/list/unarchived")
    public TableDataInfo listUnarchived(DataQuery query) {
        Page<DataVO> page = dataService.listUnarchivedData(query);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 导出uncheck表格中的数据
     */
    @PostMapping("/export/unchecked")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Raw Data", module3 = "Unchecked Data")
    public void exportUnchecked(HttpServletResponse response, FtpFileLogQueryDTO query) {
        ftpFileLogService.exportUnchecked(response, query);
    }

    /**
     * 导出check表格中的数据
     */
    @PostMapping("/export/checking")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Raw Data", module3 = "Checking Data")
    public void exportChecking(HttpServletResponse response, FtpFileLogQueryDTO query) {
        ftpFileLogService.exportChecking(response, query);
    }

    /**
     * 校验前预检查
     */
    @RequestMapping("/ftpFile/checkFtpFile")
    public AjaxResult checkFtpFile(@RequestBody String[] ids) {
        List<String> failCheckFileList = dataService.checkFtpFile(Arrays.asList(ids));
        return CollUtil.isEmpty(failCheckFileList) ? AjaxResult.success() : AjaxResult.success(null, CollUtil.join(failCheckFileList, "\n"));
    }

    /**
     * 校验文件md5是否一致
     */
    @RequestMapping("/ftpFile/verify")
    @Log(system = SytemEnum.APP, businessType = BusinessType.INSERT, module1 = "Submit", module2 = "Raw Data", module3 = "Data Integrity Check")
    public AjaxResult verifyFtpFile(@RequestBody String[] ids) {
        List<String> failVerifyFileList = dataService.verifyFtpFile(Arrays.asList(ids));
        return CollUtil.isEmpty(failVerifyFileList) ? AjaxResult.success() : AjaxResult.success(null, CollUtil.join(failVerifyFileList, "\n"));
    }

    /**
     * 删除unarchived的数据
     */
    @RequestMapping("/unarchived/delete")
    @Log(system = SytemEnum.APP, businessType = BusinessType.DELETE, module1 = "Submit", module2 = "Raw Data", module3 = "Unarchived Data")
    public AjaxResult deleteData(@RequestBody String[] ids) {
        dataService.deleteDataByIds(Arrays.asList(ids));
        return AjaxResult.success();
    }

    /**
     * 导出unarchived的数据
     */
    @PostMapping("/export/unarchived")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Raw Data", module3 = "Unarchived Data")
    public void exportUnarchived(HttpServletResponse response, DataQuery query) {
        dataService.exportUnarchived(response, query);
    }

    /**
     * single模式——archived analysis data表格
     */
    @GetMapping("/list/archived/analysisData")
    public TableDataInfo listArchivedAnalysisData(DataQuery query) {
        Page<DataVO> page = dataService.listArchivedAnalysisData(query);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * single模式——archived raw data表格
     */
    @GetMapping("/list/archived/rawData")
    public TableDataInfo listArchivedRawData(DataQuery query) {
        Page<DataVO> page = dataService.listArchivedRawData(query);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * single模式——Archiving Data表格
     * 查询归档中，包含 已归档未审核 和 未归档数据
     */
    @GetMapping("/list/preArchive")
    public TableDataInfo listArchivingData(DataQuery query) {
        Page<DataVO> page = dataService.listArchivingData(query);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 导出Unarchived Data中的数据，包含 已归档未审核 和 未归档数据
     */
    @PostMapping("/export/preArchive")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Archiving", module3 = "Unarchived Data")
    public void exportPreArchive(HttpServletResponse response, DataQuery query) {
        dataService.exportPreArchive(response, query);
    }

    /**
     * 导出归档到RawData的数据
     */
    @PostMapping("/export/archived/rawData")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Archiving", module3 = "Archived Raw Data")
    public void exportArchivedRawData(HttpServletResponse response, DataQuery query) {
        dataService.exportArchivedRawData(response, query);
    }

    /**
     * 导出归档后的analysis data
     */
    @PostMapping("/export/archived/analysisData")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Submit", module2 = "Archiving", module3 = "Archived Analysis Data")
    public void exportArchivedAnalysisData(HttpServletResponse response, DataQuery query) {
        dataService.exportArchivedAnalysisData(response, query);
    }

    /**
     * 删除前预检查
     */
    @GetMapping("/deleteCheck/{dataNo}")
    public AjaxResult deleteCheck(@PathVariable("dataNo") String dataNo) {
        DeleteCheckResultVO result = dataService.deleteCheck(dataNo);
        return success(result);
    }

    /**
     * 删除Data相关的内容
     */
    @RequestMapping("/deleteDataAll")
    @Log(system = SytemEnum.APP, businessType = BusinessType.DELETE, module1 = "User Center", module2 = "Data")
    public AjaxResult deleteDataAll(String dataNo, String password) {
        // 检查密码
        dataService.checkPassword(password);
        // 删除数据
        dataService.deleteDataAll(dataNo);
        return success();
    }

    /**
     * 获取rawData的统计信息
     */
    @RequestMapping("/getRawDataStat")
    public AjaxResult getRawDataStat() {
        return AjaxResult.success(dataService.getRawDataStat());
    }

    /**
     * 编辑Data Name
     */
    @Log(system = SytemEnum.APP, businessType = BusinessType.UPDATE, module1 = "Submit", module2 = "Data Upload", module3 = "Edit File Name")
    @RequestMapping("/editFileName")
    public AjaxResult editFileName(String dataNo, String name) {
        dataService.editFileName(dataNo, name);
        return AjaxResult.success();
    }

}
