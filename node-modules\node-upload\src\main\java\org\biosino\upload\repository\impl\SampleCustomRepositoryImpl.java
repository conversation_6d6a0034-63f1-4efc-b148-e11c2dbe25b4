package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.ExistSampleQueryDTO;
import org.biosino.upload.repository.SampleCustomRepository;
import org.biosino.upload.repository.common.CriteriaUtil;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SampleCustomRepositoryImpl implements SampleCustomRepository {

    private final MongoTemplate mongoTemplate;
    private static final String NO_KEY = "sap_no";

    @Override
    public Class<Sample> clz() {
        return Sample.class;
    }

    @Override
    public Sample validateSampleName(String creator, String sapNo, String projectName) {
        Query query = new Query();
        Criteria criteria = Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("name").is(projectName)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(sapNo)) {
            // 如果有sap_no，说明是编辑数据时查重，需要排除自身
            criteria.andOperator(Criteria.where(NO_KEY).ne(sapNo));
        }
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, clz());
    }

    @Override
    public Sample findByNo(String sapNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where(NO_KEY).is(sapNo).and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, clz());
    }

    @Override
    public List<Sample> findAllByCreator(String creator) {
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(creator)) {
            criteria.andOperator(Criteria.where("creator").is(creator));
        }
        query.addCriteria(criteria);
        return mongoTemplate.find(query, clz());
    }

    private static Pattern regCondition(String val) {
        return Pattern.compile("^.*" + ReUtil.escape(val) + ".*$", Pattern.CASE_INSENSITIVE);
    }

    @Override
    public Page<Sample> findAllByPage(ArchivedSelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("creator").is(queryDTO.getCreator())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());

        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = regCondition(queryDTO.getName());
            criteria.orOperator(Criteria.where(NO_KEY).regex(pattern),
                    Criteria.where("name").regex(pattern));
        }

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("name").include(NO_KEY);
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        // 统计
        long count = mongoTemplate.count(query, clz());
        // 分页
        query.with(queryDTO.getPageable());
        // 查询
        List<Sample> content = mongoTemplate.find(query, clz());
        // 精准查询并放到首位
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Criteria subCri = new Criteria();
            subCri.orOperator(Criteria.where(NO_KEY).is(queryDTO.getName()), Criteria.where("name").is(queryDTO.getName()));
            Sample sap = mongoTemplate.findOne(Query.query(subCri), Sample.class);
            if (sap != null) {
                // 将list的首位替换成exp
                content.set(0, sap);
            }
        }

        return new PageImpl<>(content, queryDTO.getPageable(), count);
    }

    @Override
    public List<String> findSampleOrganisms(String sampleType, String memberId) {
        Criteria criteria = Criteria.where("creator").is(memberId)
                .and("subject_type").is(sampleType)
                .and("audited").is(AuditEnum.audited)
                .and("temp_data").exists(false)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("organism");
        List<String> sampleList = mongoTemplate.findDistinct(query, "organism", clz(), String.class);

        if (CollUtil.isEmpty(sampleList)) {
            return null;
        }
        return sampleList;
    }

    @Override
    public List<Sample> findSampleCustomAttr(String sampleType, String memberId) {
        Criteria criteria = Criteria.where("creator").is(memberId)
                .and("subject_type").is(sampleType)
                .and("audited").is(AuditEnum.audited)
                .and("temp_data").exists(false)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("custom_attr");
        return mongoTemplate.find(query, clz());
    }

    @Override
    public boolean existsUserSampleBySapNo(String sapNo, String creator) {
        return mongoTemplate.exists(Query.query(Criteria.where(NO_KEY).is(sapNo)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), clz());
    }

    @Override
    public Map<String, Boolean> existsUserSampleBySapNos(List<String> sapNos, String creator) {
        Query query = Query.query(Criteria.where(NO_KEY).in(sapNos)
                .and("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        List<String> existSapNos = mongoTemplate.findDistinct(query, NO_KEY, Sample.class, String.class);
        Map<String, Boolean> result = new LinkedHashMap<>();
        for (String sapNo : sapNos) {
            result.put(sapNo, existSapNos.contains(sapNo));
        }
        return result;
    }

    @Override
    public boolean existAuditInitSampleBySapName(String sapName, String creator) {
        return mongoTemplate.exists(Query.query(Criteria.where("name").is(sapName)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), clz());
    }

    @Override
    public Map<String, Boolean> existAuditInitSampleBySapNames(List<String> sapNames, String creator) {
        Query query = Query.query(Criteria.where("name").in(sapNames)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        List<String> existSapNames = mongoTemplate.findDistinct(query, "name", Sample.class, String.class);
        Map<String, Boolean> result = new LinkedHashMap<>();
        for (String sapName : sapNames) {
            result.put(sapName, existSapNames.contains(sapName));
        }
        return result;
    }

    @Override
    public Sample findAuditInitSampleBySapName(String sapName, String creator) {
        return mongoTemplate.findOne(Query.query(Criteria.where("name").is(sapName)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), clz());
    }

    @Override
    public List<Sample> findAllAuditInitSampleByNames(List<String> sapNames, String creator) {
        return mongoTemplate.find(Query.query(Criteria.where("name").in(sapNames)
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), clz());
    }

    /**
     * 查询当前用户已存在、非公开的样本数据
     */
    @Override
    public PageImpl<Sample> findSample(ExistSampleQueryDTO dto) {
        List<Criteria> criteriaList = new ArrayList<>();

        Criteria criteria = Criteria.where("creator").is(dto.getCreator())
                .and("subject_type").is(dto.getSampleType())
                .and("audited").is(AuditEnum.audited)
                .and("temp_data").exists(false)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());

        criteriaList.add(criteria);
        if (StrUtil.isNotBlank(dto.getSampleNo())) {
            criteriaList.add(CriteriaUtil.noOrUsedIds(NO_KEY, dto.getSampleNo()));
        }

        if (CollUtil.isNotEmpty(dto.getSampleNos())) {
            criteriaList.add(CriteriaUtil.nosOrUsedIdsIn(NO_KEY, dto.getSampleNos()));
        }

        if (StrUtil.isNotBlank(dto.getSampleName())) {
            Pattern pattern = regCondition(dto.getSampleName());
            criteriaList.add(Criteria.where("name").regex(pattern));
        }

        if (StrUtil.isNotBlank(dto.getOrganism())) {
            criteriaList.add(Criteria.where("organism").is(dto.getOrganism()));
        }

        if (StrUtil.isNotBlank(dto.getTissue())) {
            Pattern pattern = regCondition(dto.getTissue());
            criteriaList.add(Criteria.where("tissue").regex(pattern));
        }

        String attrName = dto.getAttrName();
        if (StrUtil.isNotBlank(attrName)) {
            Pattern pattern = regCondition(dto.getAttrValue());
            if (attrName.startsWith("system_")) {
                criteriaList.add(Criteria.where("attributes." + attrName.replace("system_", "")).regex(pattern));
            } else {
                criteriaList.add(Criteria.where("custom_attr." + attrName.replace("custom_", "")).regex(pattern));
            }
        }

        if (ObjectUtil.isNotEmpty(dto.getBeginTime()) && ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(dto.getBeginTime())).lte(DateUtil.endOfDay(dto.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(dto.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(dto.getEndTime())));
        }

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        long count = mongoTemplate.count(query, clz());
        query.with(dto.getPageable());
        List<Sample> content = mongoTemplate.find(query, clz());

        return new PageImpl<>(content, dto.getPageable(), count);
    }

    @Override
    public List<Sample> findAllTempByNosAndCreatorAndType(Collection<String> nos, String creator, String sapType) {
        if (CollUtil.isEmpty(nos) || creator == null) {
            return new ArrayList<>();
        }
        Criteria criteria = tempSapNoCriteria(nos).and(tempKey("creator")).is(creator);
        if (StrUtil.isNotBlank(sapType)) {
            criteria = criteria.and(tempKey("subject_type")).is(sapType);
        }
        final List<Sample> samples = mongoTemplate.find(new Query(criteria), clz());
        final List<Sample> tempList = new ArrayList<>();
        for (Sample item : samples) {
            final Sample tempData = item.getTempData();
            if (tempData != null) {
                tempList.add(tempData);
            }
        }
        return tempList;
    }

    private Criteria tempSapNameBaseQuery(String creator) {
        return Criteria.where(tempKey("creator")).is(creator)
                .and(tempKey("ownership")).is(OwnershipEnum.self_support.getDesc())
                .and(tempKey("audited")).is(AuditEnum.init.name())
                .and(tempKey("visible_status")).in(VisibleStatusEnum.includeExistsVisibleStatus());
    }

    @Override
    public Map<String, String> findTempNameByNamesInAndNoNotIn(Collection<String> names, String creator, Collection<String> nos) {
        if (CollUtil.isEmpty(names) || creator == null) {
            return new HashMap<>();
        }
        final Criteria criteria = tempSapNameBaseQuery(creator).and(tempKey("name")).in(names);
        if (CollUtil.isNotEmpty(nos)) {
            criteria.and(tempKey(NO_KEY)).nin(nos).and(tempKey("used_ids")).nin(nos);
        }
        final Query query = new Query(criteria);
        query.fields().include(tempKey("name")).include(tempKey("sub_no"));
        final List<Sample> samples = mongoTemplate.find(query, clz());
        final Map<String, String> tempNames = new HashMap<>();
        for (Sample item : samples) {
            Sample tempData = item.getTempData();
            if (tempData != null) {
                tempNames.put(tempData.getName(), tempData.getSubNo());
            }
        }
        return tempNames;
    }

    @Override
    public Map<String, Sample> findTempNameByNamesInAndNoIn(Collection<String> names, String creator, Collection<String> nos) {
        if (CollUtil.isEmpty(names) || CollUtil.isEmpty(nos) || creator == null) {
            return new HashMap<>();
        }

        final Criteria criteria = tempSapNameBaseQuery(creator).and(tempKey("name")).in(names);
        final Query query = new Query(new Criteria().andOperator(criteria, tempSapNoCriteria(nos)));

        final List<Sample> samples = mongoTemplate.find(query, clz());
        final Map<String, Sample> tempNames = new HashMap<>();
        for (Sample item : samples) {
            Sample tempData = item.getTempData();
            if (tempData != null) {
                tempNames.put(tempData.getName(), tempData);
            }
        }
        return tempNames;
    }

    public static Criteria sapNoCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return CriteriaUtil.nosOrUsedIdsIn(NO_KEY, nos);
        } else {
            return new Criteria();
        }
    }

    @Override
    public List<Sample> findAllByNosAndCreator(Collection<String> nos, String creator) {
        if (CollUtil.isEmpty(nos)) {
            return new ArrayList<>();
        }
        final Criteria criteria = sapNoCriteria(nos).and("creator").is(creator)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        final Query query = new Query(criteria);
        final List<Sample> data = mongoTemplate.find(query, clz());
        final List<Sample> tempList = new ArrayList<>();
        for (Sample item : data) {
            final Sample tempData = item.getTempData();
            if (tempData != null) {
                tempList.add(tempData);
            } else {
                tempList.add(item);
            }
        }
        return tempList;
    }

    private Criteria tempSapNoCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return new Criteria().orOperator(
                    Criteria.where(tempKey(NO_KEY)).in(nos),
                    Criteria.where(tempKey("used_ids")).in(nos)
            );
        } else {
            return new Criteria();
        }
    }

    @Override
    public Set<String> findAllTempNoByNos(Collection<String> nos, String creator) {
        if (CollUtil.isEmpty(nos) || creator == null) {
            return new HashSet<>();
        }
        Criteria criteria = tempSapNoCriteria(nos).and(tempKey("creator")).is(creator)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include(tempKey(NO_KEY)).include(tempKey("used_ids"));
        final List<Sample> samples = mongoTemplate.find(query, clz());
        final Set<String> data = new HashSet<>();
        for (Sample item : samples) {
            final Sample tempData = item.getTempData();
            data.add(tempData.getSapNo());
            List<String> usedIds = tempData.getUsedIds();
            if (CollUtil.isNotEmpty(usedIds)) {
                for (String usedId : usedIds) {
                    if (usedId != null) {
                        data.add(usedId);
                    }
                }
            }
        }
        return data;
    }

    @Override
    public void updateTempId(Collection<String> nos) {
        if (CollUtil.isEmpty(nos)) {
            return;
        }
        Criteria criteria = sapNoCriteria(nos);
        final String idKey = tempKey("_id");
        criteria = new Criteria().andOperator(criteria, new Criteria().orOperator(
                        Criteria.where(idKey).isNull(),
                        Criteria.where(idKey).exists(false)
                )
        );
        // 使用数组，才能使$_id读取到外层id值
        String updateString = "[{\"$set\": {\"" + idKey + "\": \"$_id\"}}]";
        final String collName = mongoTemplate.getCollectionName(clz());
        // q:条件 u:更新
        mongoTemplate.executeCommand("{ update: '" + collName + "', updates: [ { q: " + criteria.getCriteriaObject().toJson() + ", u: " + updateString + ", multi: true } ] }");
    }

    private Criteria tempSapNoNotCriteria(final Collection<String> nos) {
        if (CollUtil.isNotEmpty(nos)) {
            return new Criteria().andOperator(
                    Criteria.where(tempKey(NO_KEY)).nin(nos),
                    Criteria.where(tempKey("used_ids")).nin(nos)
            );
        } else {
            return new Criteria();
        }
    }

    @Override
    public Map<String, String> findNameByIds(Collection<String> ids) {
        Query query = Query.query(Criteria.where("sap_no").in(ids));
        query.fields().include("sap_no").include("name");
        List<Sample> samples = mongoTemplate.find(query, Sample.class);
        if (CollUtil.isEmpty(samples)) {
            return null;
        }
        return samples.stream().collect(Collectors.toMap(Sample::getName, Sample::getSapNo, (existingValue, newValue) -> existingValue));
    }

    @Override
    public void deleteTempByNosAndCreator(Collection<String> oldNos, String creator, List<String> newNos) {
        if (CollUtil.isEmpty(oldNos) || creator == null) {
            return;
        }
        Criteria criteria = tempSapNoCriteria(oldNos)
                .and(tempKey("creator")).is(creator);
        criteria = new Criteria().andOperator(criteria, tempSapNoNotCriteria(newNos));

        final Query query = new Query(criteria);
        final Class<Sample> dbClz = clz();
        final List<Sample> samples = mongoTemplate.find(query, dbClz);
        final List<ObjectId> deleteIds = new ArrayList<>();
        final List<ObjectId> deleteTempDataIds = new ArrayList<>();
        for (Sample item : samples) {
            if (AuditEnum.init.name().equals(item.getAudited())) {
                deleteIds.add(new ObjectId(item.getId()));
            } else {
                final Sample tempData = item.getTempData();
                if (tempData != null) {
                    deleteTempDataIds.add(new ObjectId(item.getId()));
                }
            }
        }
        // 删除第一次提交的暂存数据
        mongoTemplate.remove(new Query(Criteria.where("_id").in(deleteIds)), dbClz);
        // 删除老数据编辑时提交的暂存数据
        mongoTemplate.updateMulti(new Query(Criteria.where("_id").in(deleteTempDataIds)), new Update().unset("tempData"), dbClz);
    }

    @Override
    public List<Sample> findTempBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return new ArrayList<>();
        }
        Query query = new Query(Criteria.where(tempKey("sap_no")).in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public void updateToDeleteAllBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return;
        }
        Query query = new Query(Criteria.where("sap_no").in(sapNos));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name());
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Sample.class);
    }

    @Override
    public Optional<Sample> findTopBySapNo(String sapNo) {
        if (StrUtil.isBlank(sapNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("sap_no").is(sapNo),
                Criteria.where("used_ids").in(sapNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Sample sap = mongoTemplate.findOne(query, Sample.class);
        return Optional.ofNullable(sap);
    }

    @Override
    public List<Sample> findAllBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("sap_no").in(sapNos),
                Criteria.where("used_ids").in(sapNos)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Sample.class);
    }
}
