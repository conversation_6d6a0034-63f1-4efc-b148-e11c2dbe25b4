package org.biosino.sftp.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.UpdateOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.ShareData;
import org.biosino.sftp.mapper.FtpFileLogMapper;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.common.core.enums.SecurityEnum.includeAllSecurity;

/**
 * <AUTHOR>
 * @date 2018/3/20
 */
@Repository
public class FtpDbManager {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private FtpFileLogMapper ftpFileLogMapper;

    public void updateFtpFileStatusByCreatorAndPathLike(String userId, String path, String status) {

        ftpFileLogMapper.updateFtpFileLogStatusByCreatorAndPathLike(status, userId, path + "%");
    }

    public void updateFtpFileStatusByCreatorAndPath(String userId, String path, String status) {

        ftpFileLogMapper.updateFtpFileLogStatusByCreatorAndPath(status, userId, path);
    }

    public void updateFtpFilePathByCreatorAndPathLike(String creator, String path, String replaceTo) {

        ftpFileLogMapper.updateFtpFileLogPathByCreatorAndPathLike(path, replaceTo, creator, path + "%");
    }

    public Collection<String> searchUserSelfRunNo(String id) {
        Set<String> result = new LinkedHashSet<>();
        if (StringUtils.isBlank(id)) {
            return result;
        }
        Aggregation agg = Aggregation.newAggregation(Aggregation.project("run_no", "creator"), Aggregation.match(Criteria.where("creator").is(id)));
        AggregationResults<Document> run_nos = mongoTemplate.aggregate(agg, "run", Document.class);
        for (Document obj : run_nos) {
            result.add(String.valueOf(obj.get("run_no")));
        }
        return result;
    }

    public Collection<String> searchUserSelfRunNoBySecurity(String id, String security) {
        Set<String> result = new LinkedHashSet<>();
        if (StringUtils.isAnyBlank(id, security)) {
            return result;
        }
        Criteria criteria = Criteria.where("creator").is(id).and("security").is(security)
                .and("run_no").exists(true)
                .and("ownership").is(OwnershipEnum.self_support.getDesc());
        Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria), Aggregation.project("run_no", "creator", "security"));
        AggregationResults<Document> run_nos = mongoTemplate.aggregate(agg, "data", Document.class);
        for (Document obj : run_nos) {
            if (!String.valueOf(obj.get("run_no")).isEmpty()) {
                result.add(String.valueOf(obj.get("run_no")));
            }
        }
        return result;
    }


    public Collection<String> searchUserSelfAnalysisNoBySecurity(String id, String security) {
        if (StringUtils.isAnyBlank(id, security)) {
            return new LinkedHashSet<>(0);
        }

        Set<String> result = new LinkedHashSet<>();

        Criteria criteria = Criteria.where("creator").is(id)
                .and("security").is(security)
                .and("anal_no").exists(true)
                .and("ownership").is(OwnershipEnum.self_support.getDesc());
        Aggregation agg = Aggregation.newAggregation(Aggregation.match(criteria), Aggregation.project("anal_no", "creator", "security"));
        AggregationResults<Document> run_nos = mongoTemplate.aggregate(agg, "data", Document.class);
        for (Document obj : run_nos) {
            if (!String.valueOf(obj.get("anal_no")).isEmpty()) {
                result.add(String.valueOf(obj.get("anal_no")));
            }

        }
        return result;
    }

    public Collection<String> searchUserPublicRunNo(String runNo, int length) {
        if (StringUtils.isBlank(runNo)) {
            return new ArrayList<>();
        }

        final BasicDBObject match = new BasicDBObject();
        BasicDBObject security = new BasicDBObject();
        // security.append("security", SecurityEnum._public.getDesc());
        security.append("visible_status", VisibleStatusEnum.Accessible.name());
        security.append("run_no", new BasicDBObject("$regex", "^" + runNo));
        match.append("$match", security);

        final BasicDBObject project = new BasicDBObject();
        project.append("$project", new BasicDBObject("run_no", new BasicDBObject("$substr", new Object[]{"$run_no", 0, runNo.length() + length})));

        final BasicDBObject group = new BasicDBObject();
        group.append("$group", new BasicDBObject("_id", "$run_no"));

        final Set<String> result = new LinkedHashSet<>();

        for (DBObject dbObject : (Iterable<DBObject>) mongoTemplate.getCollection("run").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(group);
            add(new BasicDBObject("$sort", new BasicDBObject("_id", 1)));
        }}, DBObject.class).allowDiskUse(true)) {
            result.add(String.valueOf(dbObject.get(Fields.UNDERSCORE_ID)));
        }
        return result;
    }


    public Collection<String> searchUserPublicAnalysisNo(String analysisNo, int length) {
        if (StringUtils.isBlank(analysisNo)) {
            return new ArrayList<>();
        }

        final BasicDBObject match = new BasicDBObject();
        BasicDBObject security = new BasicDBObject();
        // security.append("security", SecurityEnum._public.getDesc());
        security.append("visible_status", VisibleStatusEnum.Accessible.name());
        security.append("anal_no", new BasicDBObject("$regex", "^" + analysisNo));
        match.append("$match", security);

        final BasicDBObject project = new BasicDBObject();
        project.append("$project", new BasicDBObject("anal_no", new BasicDBObject("$substr", new Object[]{"$anal_no", 0, analysisNo.length() + length})));

        final BasicDBObject group = new BasicDBObject();
        group.append("$group", new BasicDBObject("_id", "$anal_no"));

        final Set<String> result = new LinkedHashSet<>();

        for (DBObject dbObject : (Iterable<DBObject>) mongoTemplate.getCollection("analysis").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(group);
            add(new BasicDBObject("$sort", new BasicDBObject("_id", 1)));
        }}, DBObject.class).allowDiskUse(true)) {
            result.add(String.valueOf(dbObject.get(Fields.UNDERSCORE_ID)));
        }
        return result;
    }

    public Collection<Data> getDataByRunNo(String runNo) {
        if (StringUtils.isBlank(runNo)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").is(runNo).and("security").in(includeAllSecurity());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    public Collection<Data> getDataByAnalysisNo(String analysisNo) {
        if (StringUtils.isBlank(analysisNo)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analysisNo).and("security").in(includeAllSecurity());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    public Data getData(String runNo, String fileName) {
        if (StringUtils.isBlank(runNo) || StringUtils.isBlank(fileName)) {
            return null;
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").is(runNo);
        criteria.and("file_name").is(fileName);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Data.class);
    }

    public Data getDataByAnalysisNo(String analysisNo, String fileName) {
        if (StringUtils.isBlank(analysisNo) || StringUtils.isBlank(fileName)) {
            return null;
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analysisNo);
        criteria.and("file_name").is(fileName);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Data.class);
    }

    public Run getRun(String runNo) {
        if (StringUtils.isBlank(runNo)) {
            return null;
        }
        return mongoTemplate.findOne(Query.query(Criteria.where("run_no").is(runNo)
                // .and("security").in(includeAllSecurity())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
        ), Run.class);
    }


    public Analysis getAnalysis(String analysisNo) {
        if (StringUtils.isBlank(analysisNo)) {
            return null;
        }
        return mongoTemplate.findOne(Query.query(Criteria.where("anal_no").is(analysisNo).and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Analysis.class);
    }

    public Collection<String> searchShareToUserRunNo(String username) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }
        Collection<DBObject> dbObjects = searchShareToUserDataByRunNo(username, null);

        Set<String> dataNos = new LinkedHashSet<>();
        for (DBObject object : dbObjects) {
            dataNos.add(String.valueOf(((DBObject) object.get("runs")).get("run_no")));
        }
        return dataNos;
    }


    public Collection<String> searchShareToUserAnalysisNo(String username) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }
        Collection<DBObject> dbObjects = searchShareToUserDataByAnalysisNo(username, null);

        Set<String> analysisNos = new LinkedHashSet<>();
        for (DBObject object : dbObjects) {
            analysisNos.add(String.valueOf(((DBObject) object.get("analysis")).get("anal_no")));
            System.out.println("Info in searchShareToUserAnalysisNo: analysisNo = " + String.valueOf(((DBObject) object.get("analysis")).get("anal_no")));
        }
        return analysisNos;
    }

    public Collection<String> searchShareToUserDataNo(String username, String runNo) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(runNo)) {
            return new HashSet<>();
        }

        /*Collection<DBObject> dbObjects = searchShareToUserDataByRunNo(username, runNo);
        Set<String> dataNos = new LinkedHashSet<>();
        for (DBObject object : dbObjects) {
            dataNos.add(String.valueOf(((DBObject) object.get("datas")).get("data_no")));
        }
        return dataNos;*/
        List<Share> shares = searchShareToUserDataByRunNo1(username, runNo);
        Set<String> dataNos = new LinkedHashSet<>();
        if (CollectionUtils.isNotEmpty(shares)) {
            for (Share share : shares) {
                List<ShareData> shareDatas = share.getDatas();
                if (CollectionUtils.isNotEmpty(shareDatas)) {
                    for (ShareData shareData : shareDatas) {
                        dataNos.add(shareData.getDatNo());
                    }
                }
            }
        }
        return dataNos;
    }

    /**
     * 根据分析号查询共享给用户的数据编号
     *
     * @param username   用户名
     * @param analysisNo 分析号
     * @return
     */
    public Collection<String> searchShareToUserDataNoByAnalysisNo(String username, String analysisNo) {

        Set<String> dataNos = new LinkedHashSet<>();

        if (StringUtils.isBlank(username) || StringUtils.isBlank(analysisNo)) {
            return dataNos;
        }

        List<Share> shares = searchShareToUserDataByAnalysisNo1(username, analysisNo);
        if (CollectionUtils.isEmpty(shares)) {
            return dataNos;
        }

        for (Share share : shares) {
            List<ShareData> shareDatas = share.getDatas();
            if (CollectionUtils.isEmpty(shareDatas)) {
                continue;
            }
            for (ShareData shareData : shareDatas) {
                dataNos.add(shareData.getDatNo());
            }
        }
        return dataNos;
    }

    public List<Share> searchShareToUserDataByRunNo1(String username, String runNo) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }

        Criteria criteria = Criteria.where("share_to").is(username).and("status").is(ShareStatusEnum.sharing.name());
        if (StringUtils.isNotBlank(runNo)) {
            criteria.andOperator(Criteria.where("runs.run_no").is(runNo).and("datas.run_no").is(runNo));
        }
        return mongoTemplate.find(Query.query(criteria), Share.class);
    }

    public List<Share> searchShareToUserDataByAnalysisNo1(String username, String analysisNo) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }

        Criteria criteria = Criteria.where("share_to").is(username).and("status").is(ShareStatusEnum.sharing.name());
        // 分析号不为空时，查询分析号
        if (StringUtils.isNotBlank(analysisNo)) {
            criteria.andOperator(Criteria.where("analysis.anal_no").is(analysisNo).and("datas.anal_no").is(analysisNo));
        }
        return mongoTemplate.find(Query.query(criteria), Share.class);
    }

    public Collection<DBObject> searchShareToUserDataByRunNo(String username, String runNo) {
        if (StringUtils.isBlank(username)) {
            return new HashSet<>();
        }

        BasicDBObject val = new BasicDBObject();
        val.put("share_to", username);
        val.put("status", ShareStatusEnum.sharing.name());
        if (StringUtils.isNotBlank(runNo)) {
            val.put("runs.run_no", runNo);
        }

        BasicDBObject match = new BasicDBObject();
        match.append("$match", val);

        BasicDBObject project = new BasicDBObject();
        project.put("$project", new BasicDBObject("runs", 1));

        BasicDBObject unwind = new BasicDBObject();
        unwind.put("$unwind", "$runs");

        try (MongoCursor<DBObject> aggregate = mongoTemplate.getCollection("share").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(unwind);
        }}, DBObject.class).allowDiskUse(true).iterator()) {
            final Set<DBObject> result = new HashSet<>();
            while (aggregate.hasNext()) {
                result.add(aggregate.next());
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new HashSet<>();
        }
    }

    public Collection<DBObject> searchShareToUserDataByAnalysisNo(String username, String analysisNo) {
        if (StringUtils.isBlank(username)) {
            return new HashSet<>();
        }

        BasicDBObject val = new BasicDBObject();
        val.put("share_to", username);
        val.put("status", ShareStatusEnum.sharing.name());
        if (StringUtils.isNotBlank(analysisNo)) {
            val.put("analysis.anal_no", analysisNo);
        }

        BasicDBObject match = new BasicDBObject();
        match.append("$match", val);

        BasicDBObject project = new BasicDBObject();
        project.put("$project", new BasicDBObject("analysis", 1));

        BasicDBObject unwind = new BasicDBObject();
        unwind.put("$unwind", "$analysis");

        try (MongoCursor<DBObject> aggregate = mongoTemplate.getCollection("share").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(unwind);
        }}, DBObject.class).allowDiskUse(true).iterator()) {
            final Set<DBObject> result = new HashSet<>();
            while (aggregate.hasNext()) {
                result.add(aggregate.next());
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new HashSet<>();
        }
    }

    public Collection<String> searchRunNoByDataNo(Set<String> dataNos) {
        if (CollectionUtils.isEmpty(dataNos)) {
            return new ArrayList<>();
        }

        BasicDBObject match = new BasicDBObject();
        match.put("$match", new BasicDBObject("dat_no", new BasicDBObject("$in", dataNos.toArray(new String[]{}))));

        BasicDBObject project = new BasicDBObject();
        project.put("$project", new BasicDBObject("run_no", 1));

        BasicDBObject group = new BasicDBObject();
        group.put("$group", new BasicDBObject("_id", "$run_no"));

        MongoCursor<DBObject> aggregate = mongoTemplate.getCollection("data").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(group);
        }}, DBObject.class).allowDiskUse(true).iterator();

        Set<String> result = new LinkedHashSet<>();
        while (aggregate.hasNext()) {
            String item = String.valueOf(aggregate.next().get(Fields.UNDERSCORE_ID));
            if (!item.isEmpty()) {
                result.add(item);
            }
        }
        return result;
    }

    public Collection<String> searchAnalysisNoByDataNo(Set<String> dataNos) {
        if (CollectionUtils.isEmpty(dataNos)) {
            return new ArrayList<>();
        }

        BasicDBObject match = new BasicDBObject();
        match.put("$match", new BasicDBObject("dat_no", new BasicDBObject("$in", dataNos.toArray(new String[]{}))));

        BasicDBObject project = new BasicDBObject();
        project.put("$project", new BasicDBObject("anal_no", 1));

        BasicDBObject group = new BasicDBObject();
        group.put("$group", new BasicDBObject("_id", "$anal_no"));

        MongoCursor<DBObject> aggregate = mongoTemplate.getCollection("data").aggregate(new ArrayList() {{
            add(match);
            add(project);
            add(group);
        }}, DBObject.class).allowDiskUse(true).iterator();

        Set<String> result = new LinkedHashSet<>();
        while (aggregate.hasNext()) {
            String item = String.valueOf(aggregate.next().get(Fields.UNDERSCORE_ID));
            if (!item.isEmpty()) {
                result.add(item);
            }
        }
        return result;
    }

    public Collection<Data> searchDataByNoAndUserId(String userId, String dataNo) {
        if (StringUtils.isAnyBlank(userId, dataNo)) {
            return new ArrayList<>();
        }

        Criteria criteria = Criteria.where("dat_no").in(dataNo).and("creator").is(userId);
        return mongoTemplate.find(Query.query(criteria), Data.class);
    }

    public void saveDownloadLog(DownloadLog info) {
        if (info == null) {
            return;
        }
        mongoTemplate.save(info);
    }

    public Collection<String> searchAuthorizeToUserDataNos(String userId, List<String> dataNos) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where("authorize_to").is(userId));

        if (CollUtil.isNotEmpty(dataNos)) {
            criteriaList.add(Criteria.where("data").in(dataNos));
        }

        criteriaList.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));

        // 未过期，或者 没有过期时间
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)));

        Query query = new Query(new Criteria().andOperator(criteriaList));
        List<ResourceAuthorize> list = mongoTemplate.find(query, ResourceAuthorize.class);


        // 获取list重所有的dataNo并摊平
        Set<String> resultData = new HashSet<>();
        for (ResourceAuthorize resourceAuthorize : list) {
            resultData.addAll(resourceAuthorize.getData());
        }

        // 求交集
        return CollUtil.intersectionDistinct(dataNos, resultData);
    }

    public Collection<String> searchShareToUserDataNos(String email, List<String> dataNos) {
        List<Criteria> criteriaList = new ArrayList<>();
        Criteria criteria = Criteria.where("share_to")
                .in(CollUtil.newArrayList(email))
                .and("datas.data_no").in(dataNos)
                .and("status").is(ShareStatusEnum.sharing.name());
        criteriaList.add(criteria);

        // 未过期，或者 没有过期时间
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)));

        Query query = new Query(new Criteria().andOperator(criteriaList));
        query.fields().include("datas");

        List<Share> shares = mongoTemplate.find(query, Share.class);

        // 将datas取出来，放到一个List<ShareData>
        List<ShareData> shareData = new ArrayList<>();
        for (Share share : shares) {
            shareData.addAll(share.getDatas());
        }

        // 通过交集，判断哪些是被分享过的
        Set<String> collect = shareData.stream().map(ShareData::getDatNo).collect(Collectors.toSet());
        return CollUtil.intersectionDistinct(dataNos, collect);
    }

    public Collection<ResourceAuthorize> searchAuthorizeToUserDataNo(String memberId, String dataNo) {
        if (StringUtils.isBlank(memberId)) {
            return new ArrayList<>();
        }
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("authorize_to").is(memberId));
        criteriaList.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));
        // 未过期，或者 没有过期时间
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()), Criteria.where("expire_date").exists(false)));
        if (StrUtil.isNotBlank(dataNo)) {
            criteriaList.add(Criteria.where("data").in(CollUtil.newArrayList(dataNo)));
        }
        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 只返回dataNo
        query.fields().include("typeId");

        List<ResourceAuthorize> dataList = mongoTemplate.find(query, ResourceAuthorize.class);
        return dataList;

    }

    public void incDownloadNum(String dataNo) {
        BasicDBObject query = new BasicDBObject();
        query.put("dat_no", dataNo);
        BasicDBObject update = new BasicDBObject();
        update.put("download_num", 1);
        BasicDBObject updateSet = new BasicDBObject("$inc", update);
        mongoTemplate.getCollection("data").updateOne(query, updateSet, new UpdateOptions().upsert(true));
    }

    public Data getDataByDataNo(String dataNo) {
        if (StringUtils.isBlank(dataNo)) {
            return null;
        }
        Query query = new Query();
        Criteria criteria = new Criteria().orOperator(Criteria.where("dat_no").is(dataNo),
                Criteria.where("used_ids").in(dataNo));
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Data.class);
    }

    public Collection<Data> getDataByRunNoAndCreator(String runNo, String creator) {
        if (StringUtils.isBlank(runNo)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").is(runNo).and("creator").is(creator).and("security").in(includeAllSecurity());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    public Collection<Data> getDataByAnalysisNoAndCreator(String analysisNo, String creator) {
        if (StringUtils.isBlank(analysisNo)) {
            return new ArrayList<>();
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analysisNo).and("creator").is(creator).and("security").in(includeAllSecurity());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    public boolean existShareByRunNo(String runNo, String username) {
        if (StrUtil.isBlank(runNo) || StrUtil.isBlank(username)) {
            return false;
        }
        Query query = new Query();
        Criteria criteria = Criteria.where("share_to").is(username).and("status").is(ShareStatusEnum.sharing.name());
        criteria.andOperator(Criteria.where("runs.run_no").is(runNo).and("datas.run_no").is(runNo));
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Share.class);
    }

    public boolean existShareByAnalNo(String analNo, String username) {
        if (StrUtil.isBlank(analNo) || StrUtil.isBlank(username)) {
            return false;
        }
        Query query = new Query();

        Criteria criteria = Criteria.where("share_to").is(username).and("status").is(ShareStatusEnum.sharing.name());
        criteria.andOperator(Criteria.where("analysis.anal_no").is(analNo).and("datas.anal_no").is(analNo));
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Share.class);
    }

}
