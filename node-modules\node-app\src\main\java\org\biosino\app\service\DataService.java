package org.biosino.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.*;
import org.biosino.app.vo.DataVO;
import org.biosino.app.vo.list.DataListVO;
import org.biosino.common.core.constant.HttpStatus;
import org.biosino.common.core.enums.ArchiveEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.security.utils.SecurityUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/3/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataService {

    private final DataRepository dataRepository;
    private final AnalysisRepository analysisRepository;
    private final ShareRepository shareRepository;
    private final ResourceAuthorizeRepository resourceAuthorizeRepository;
    private final RunRepository runRepository;
    private final ExperimentRepository experimentRepository;

    public List<DataVO> getRelatedRawDataList(boolean isOwner,
                                              Project project,
                                              List<Experiment> expList,
                                              List<Run> runList,
                                              List<Sample> sampleList) {

        Set<String> runNos = runList.stream().map(Run::getRunNo).collect(Collectors.toSet());
        List<Data> dataList = dataRepository.findDetailByRunNos(runNos);

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();
        // 如果用户登录且不是所有者，查询授权
        if (SecurityUtils.isMemberLogin() && !isOwner) {
            // 筛选出未公开的数据
            Set<String> notPublicDataNos = dataList.stream()
                    .filter(x -> CollUtil.contains(SecurityEnum.notPublicSecurity(), x.getSecurity())).map(Data::getDatNo).collect(Collectors.toSet());
            // 这些未公开被分享的DataNo
            sharedDataNos = shareRepository.sharedDataNo(Objects.requireNonNull(SecurityUtils.getMember()).getEmail(), notPublicDataNos);

            // 查询授权的数据
            authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(SecurityUtils.getMemberId(), notPublicDataNos);

            // 授权的DataNo
            Set<String> authorizedDataNos = new HashSet<>();

            if (CollUtil.isNotEmpty(authorizedList)) {

                for (ResourceAuthorize resourceAuthorize : authorizedList) {
                    authorizedDataNos.addAll(resourceAuthorize.getData());
                }
            }

            // 过滤掉出私有且未授权的数据
            List<String> finalSharedDataNos = sharedDataNos;
            dataList = dataList.stream().filter(x -> {
                if (StrUtil.equals(SecurityEnum._private.getDesc(), x.getSecurity())) {
                    return finalSharedDataNos.contains(x.getDatNo()) || authorizedDataNos.contains(x.getDatNo());
                }
                return true;
            }).collect(Collectors.toList());
        } else if (!SecurityUtils.isMemberLogin()) {
            // 未登录用户只能看到public + restricted的数据
            dataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        }

        Map<String, Experiment> expNoMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Run> runNoMap = runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Sample> sapNoMap = sampleList.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        Map<String, ResourceAuthorize> typeIdMap = new HashMap<>();
        for (ResourceAuthorize resourceAuthorize : authorizedList) {
            for (String item : resourceAuthorize.getData()) {
                typeIdMap.put(item, resourceAuthorize);
            }
        }

        List<DataVO> result = new ArrayList<>();
        // 开始组装数据
        for (Data data : dataList) {
            DataVO vo = new DataVO();
            // data相关属性
            vo.setDatNo(data.getDatNo());
            vo.setName(data.getName());
            vo.setSecurity(data.getSecurity());
            vo.setFileSize(data.getFileSize());
            vo.setDataType(FileTypeUtils.getDataTypeByName(data.getName()));

            vo.setReadableFileSize(FileUtil.readableFileSize(data.getFileSize()));
            vo.setCreator(data.getCreator());
            vo.setUploadTime(data.getCreateDate());
            // 根据不同情况，判断当前用户有权限访问
            if (data.getSecurity().equals(SecurityEnum._public.getDesc())) {
                vo.setAccessible(true);
            } else if (sharedDataNos.contains(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setShared(true);
            } else if (typeIdMap.containsKey(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setRequested(true);
                vo.setExpireDate(typeIdMap.get(data.getDatNo()).getExpireDate());
            } else if (data.getCreator().equals(SecurityUtils.getMemberId())) {
                vo.setAccessible(true);
            }

            // run相关属性
            String runNo = data.getRunNo();
            Run run = runNoMap.get(runNo);
            vo.setRunNo(run.getRunNo());
            vo.setRunName(run.getName());

            // project相关属性
            vo.setProjNo(project.getProjectNo());
            vo.setProjName(project.getName());
            vo.setProjDesc(project.getDescription());

            // exp相关属性
            String expNo = run.getExpNo();
            Experiment exp = expNoMap.get(expNo);
            if (exp != null) {
                vo.setExpNo(exp.getExpNo());
                vo.setExpName(exp.getName());
                vo.setExpDesc(exp.getDescription());
                vo.setExpType(exp.getExpType());
            }

            // sample相关属性
            String sapNo = run.getSapNo();
            Sample sample = sapNoMap.get(sapNo);
            if (sample != null) {
                vo.setSapNo(sample.getSapNo());
                vo.setSapName(sample.getName());
                vo.setSapDesc(sample.getDescription());
                vo.setSapType(sample.getSubjectType());
                vo.setOrganism(sample.getOrganism());
            }
            result.add(vo);
        }

        return result;
    }

    public List<DataVO> getRelatedRawDataList(boolean isOwner,
                                              String analNo) {

        List<Data> dataList = dataRepository.findDetailByAnalNoIn(CollUtil.newArrayList(analNo));

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();
        // 如果用户登录且不是所有者，查询授权
        if (SecurityUtils.isMemberLogin() && !isOwner) {
            // 筛选出未公开的数据
            Set<String> notPublicDataNos = dataList.stream()
                    .filter(x -> CollUtil.contains(SecurityEnum.notPublicSecurity(), x.getSecurity())).map(Data::getDatNo).collect(Collectors.toSet());
            // 这些未公开的中被分享的DataNo
            sharedDataNos = shareRepository.sharedDataNo(SecurityUtils.getMember().getEmail(), notPublicDataNos);

            // 查询授权的数据
            authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(SecurityUtils.getMemberId(), notPublicDataNos);

            // 授权的DataNo
            Set<String> authorizedDataNos = new HashSet<>();

            if (CollUtil.isNotEmpty(authorizedList)) {

                for (ResourceAuthorize resourceAuthorize : authorizedList) {
                    authorizedDataNos.addAll(resourceAuthorize.getData());
                }
            }

            // 过滤掉出私有且未授权的数据
            List<String> finalSharedDataNos = sharedDataNos;
            dataList = dataList.stream().filter(x -> {
                if (StrUtil.equals(SecurityEnum._private.getDesc(), x.getSecurity())) {
                    return finalSharedDataNos.contains(x.getDatNo()) || authorizedDataNos.contains(x.getDatNo());
                }
                return true;
            }).collect(Collectors.toList());
        } else if (!SecurityUtils.isMemberLogin()) {
            // 未登录用户只能看到public + restricted的数据
            dataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        }

        Map<String, ResourceAuthorize> typeIdMap = authorizedList.stream().collect(Collectors.toMap(ResourceAuthorize::getTypeId, Function.identity(), (existingValue, newValue) -> existingValue));

        List<DataVO> result = new ArrayList<>();
        // 开始组装数据
        for (Data data : dataList) {
            DataVO vo = new DataVO();
            // data相关属性
            vo.setDatNo(data.getDatNo());
            vo.setName(data.getName());
            vo.setSecurity(data.getSecurity());
            vo.setFileSize(data.getFileSize());
            vo.setDataType(FileTypeUtils.getDataTypeByName(data.getName()));
            vo.setReadableFileSize(FileUtil.readableFileSize(data.getFileSize()));
            vo.setCreator(data.getCreator());
            vo.setUploadTime(data.getCreateDate());
            // 根据不同情况，判断当前用户有权限访问
            if (data.getSecurity().equals(SecurityEnum._public.getDesc())) {
                vo.setAccessible(true);
            } else if (sharedDataNos.contains(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setShared(true);
            } else if (typeIdMap.containsKey(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setRequested(true);
                vo.setExpireDate(typeIdMap.get(data.getDatNo()).getExpireDate());
            } else if (data.getCreator().equals(SecurityUtils.getMemberId())) {
                vo.setAccessible(true);
            }

            result.add(vo);
        }
        return result;
    }

    private String getFileType(String fileType, String fileName) {
        if (StrUtil.isBlank(fileType) && StrUtil.isNotBlank(fileName)) {
            String fileTail = fileName.substring(fileName.lastIndexOf(".") + 1);
            if (StrUtil.isBlank(fileTail)) {
                fileTail = "unknown";
            }
            return fileTail;
        }
        return fileType;
    }

    public List<DataVO> getDataByNo(boolean isOwner, Collection<String> dataNo) {

        List<Data> dataList = dataRepository.findByDataNos(dataNo);

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();
        // 如果用户登录且不是所有者，查询授权
        if (SecurityUtils.isMemberLogin() && !isOwner) {
            // 筛选出未公开的数据
            Set<String> notPublicDataNos = dataList.stream()
                    .filter(x -> CollUtil.contains(SecurityEnum.notPublicSecurity(), x.getSecurity())).map(Data::getDatNo).collect(Collectors.toSet());
            // 这些未公开的中被分享的DataNo
            sharedDataNos = shareRepository.sharedDataNo(Objects.requireNonNull(SecurityUtils.getMember()).getEmail(), notPublicDataNos);

            // 查询授权的数据
            authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(SecurityUtils.getMemberId(), notPublicDataNos);

            // 授权的DataNo
            Set<String> authorizedDataNos = new HashSet<>();

            if (CollUtil.isNotEmpty(authorizedList)) {

                for (ResourceAuthorize resourceAuthorize : authorizedList) {
                    authorizedDataNos.addAll(resourceAuthorize.getData());
                }
            }

            // 过滤掉出私有且未授权的数据
            List<String> finalSharedDataNos = sharedDataNos;
            dataList = dataList.stream().filter(x -> {
                if (StrUtil.equals(SecurityEnum._private.getDesc(), x.getSecurity())) {
                    return finalSharedDataNos.contains(x.getDatNo()) || authorizedDataNos.contains(x.getDatNo());
                }
                return true;
            }).collect(Collectors.toList());
        } else if (!SecurityUtils.isMemberLogin()) {
            // 未登录用户只能看到public + restricted的数据
            dataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        }

        Map<String, ResourceAuthorize> typeIdMap = new HashMap<>();
        for (ResourceAuthorize resourceAuthorize : authorizedList) {
            for (String item : resourceAuthorize.getData()) {
                typeIdMap.put(item, resourceAuthorize);
            }
        }

        List<DataVO> result = new ArrayList<>();
        // 开始组装数据
        for (Data data : dataList) {
            DataVO vo = new DataVO();
            // data相关属性
            vo.setDatNo(data.getDatNo());
            vo.setName(data.getName());
            vo.setSecurity(data.getSecurity());
            vo.setMd5(data.getMd5());
            vo.setFileSize(data.getFileSize());
            vo.setDataType(FileTypeUtils.getDataTypeByName(data.getName()));
            vo.setReadableFileSize(FileUtil.readableFileSize(data.getFileSize()));
            vo.setCreator(data.getCreator());
            vo.setUploadTime(data.getCreateDate());
            // 根据不同情况，判断当前用户有权限访问
            if (data.getSecurity().equals(SecurityEnum._public.getDesc())) {
                vo.setAccessible(true);
            } else if (sharedDataNos.contains(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setShared(true);
            } else if (typeIdMap.containsKey(data.getDatNo())) {
                vo.setAccessible(true);
                vo.setRequested(true);
                vo.setExpireDate(typeIdMap.get(data.getDatNo()).getExpireDate());
            } else if (data.getCreator().equals(SecurityUtils.getMemberId())) {
                vo.setAccessible(true);
            }

            result.add(vo);
        }
        return result;
    }

    public List<DataVO> getRelatedAnalysisDataList(boolean isOwner, String no, String targetType) {
        List<Analysis> analysisList = analysisRepository.findDetailByTargetIn(no, targetType);
        Set<String> analNos = analysisList.stream().map(Analysis::getAnalysisNo).collect(Collectors.toSet());
        List<Data> dataList = dataRepository.findDetailByAnalNoIn(analNos);

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();
        // 如果用户登录且不是所有者，查询授权
        if (SecurityUtils.isMemberLogin() && !isOwner) {
            // 筛选出未公开的数据
            Set<String> notPublicDataNos = dataList.stream()
                    .filter(x -> CollUtil.contains(SecurityEnum.notPublicSecurity(), x.getSecurity())).map(Data::getDatNo).collect(Collectors.toSet());
            // 这些未公开的中被分享的DataNo
            sharedDataNos = shareRepository.sharedDataNo(Objects.requireNonNull(SecurityUtils.getMember()).getEmail(), notPublicDataNos);

            // 查询授权的数据
            authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(SecurityUtils.getMemberId(), notPublicDataNos);

            // 授权的DataNo
            Set<String> authorizedDataNos = new HashSet<>();

            if (CollUtil.isNotEmpty(authorizedList)) {

                for (ResourceAuthorize resourceAuthorize : authorizedList) {
                    authorizedDataNos.addAll(resourceAuthorize.getData());
                }
            }

            // 过滤掉出私有且未授权的数据
            List<String> finalSharedDataNos = sharedDataNos;
            dataList = dataList.stream().filter(x -> {
                if (StrUtil.equals(SecurityEnum._private.getDesc(), x.getSecurity())) {
                    return finalSharedDataNos.contains(x.getDatNo()) || authorizedDataNos.contains(x.getDatNo());
                }
                return true;
            }).collect(Collectors.toList());
        } else if (!SecurityUtils.isMemberLogin()) {
            // 未登录用户只能看到public + restricted的数据
            dataList = dataList.stream().filter(x -> CollUtil.contains(SecurityEnum.includeSecurity(), x.getSecurity())).collect(Collectors.toList());
        }

        Map<String, List<Data>> analNoToDataListMap = dataList.stream().collect(Collectors.groupingBy(Data::getAnalNo));

        Map<String, Analysis> analNoMap = analysisList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // Map<String, ResourceAuthorize> typeIdMap = authorizedList.stream().collect(Collectors.toMap(ResourceAuthorize::getTypeId, Function.identity(),(existingValue, newValue) -> existingValue));

        List<DataVO> result = new ArrayList<>();

        for (Map.Entry<String, Analysis> entry : analNoMap.entrySet()) {
            String analNo = entry.getKey();
            Analysis analysis = entry.getValue();
            List<Data> datas = analNoToDataListMap.get(analNo);
            if (CollUtil.isEmpty(datas)) {
                continue;
            }
            DataVO vo = new DataVO();

            vo.setAnalNo(analNo);
            vo.setAnalName(analysis.getName());
            vo.setAnalType(analysis.getAnalysisType());
            if (analysis.getAnalysisType().equalsIgnoreCase("other") && StrUtil.isNotBlank(analysis.getCustomAnalysisType())) {
                vo.setAnalType("Other (" + analysis.getCustomAnalysisType() + ")");
            }
            List<DataVO> dataInfos = new ArrayList<>();

            for (Data data : datas) {
                DataVO item = new DataVO();

                item.setDatNo(data.getDatNo());
                item.setName(data.getName());
                item.setSecurity(data.getSecurity());
                item.setReadableFileSize(FileUtil.readableFileSize(data.getFileSize()));
                item.setUploadTime(data.getCreateDate());
                dataInfos.add(item);
            }
            vo.setAnalysisRelatedDatas(dataInfos);

            vo.setSubmissionDate(analysis.getCreateDate());
            result.add(vo);
        }
        return result;
    }

    public Page<DataListVO> listUnarchived(UserCenterListSearchDTO queryDTO) {
        if (!SecurityUtils.isMemberLogin()) {
            throw new ServiceException("User is not logged in!", HttpStatus.UNAUTHORIZED);
        }
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setArchived(ArchiveEnum.no.name());

        Page<Data> page = dataRepository.findDataPage(queryDTO);
        Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));

            return vo;
        });
        return result;
    }

    public Page<DataListVO> listRawData(UserCenterListSearchDTO queryDTO) {
        if (!SecurityUtils.isMemberLogin()) {
            throw new ServiceException("User is not logged in!", HttpStatus.UNAUTHORIZED);
        }
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistRun(true);

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        /* Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
            String runNo = x.getRunNo();
            Optional<Run> runOptional = runRepository.findFirstByRunNo(runNo);
            if (!runOptional.isPresent()) {
                return vo;
            }
            Run run = runOptional.orElseThrow(() -> new ServiceException("Run (" + runNo + ") not found!"));
            vo.setRunNo(run.getRunNo());
            vo.setExpNo(run.getExpNo());
            vo.setSapNo(run.getSapNo());
            Optional<Experiment> experimentOptional = experimentRepository.findTopByExpNo(run.getExpNo());
            if (!experimentOptional.isPresent()) {
                return vo;
            }
            Experiment experiment = experimentOptional.orElseThrow(() -> new ServiceException("Experiment (" + run.getExpNo() + ") not found!"));
            vo.setProjNo(experiment.getProjectNo());
            return vo;
        }); */

        List<DataListVO> dataListVOS = createDataListVOS(page.getContent());

        return new PageImpl<>(dataListVOS, page.getPageable(), page.getTotalElements());
    }

    public Page<DataListVO> listAnalysisData(UserCenterListSearchDTO queryDTO) {
        if (!SecurityUtils.isMemberLogin()) {
            throw new ServiceException("User is not logged in!", HttpStatus.UNAUTHORIZED);
        }
        queryDTO.setCreator(SecurityUtils.getMemberId());
        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistAnalysis(true);

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        /* Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
            Optional<Analysis> analysisOptional = analysisRepository.findTopByAnalysisNo(x.getAnalNo());
            if (!analysisOptional.isPresent()) {
                return vo;
            }
            Analysis analysis = analysisOptional.orElseThrow(() -> new ServiceException("Analysis not found!"));
            vo.setAnalNo(analysis.getAnalysisNo());
            vo.setAnalName(analysis.getName());
            vo.setAnalType(analysis.getAnalysisType());

            return vo;
        }); */

        List<DataListVO> dataListVOS = createDataListVOS(page.getContent());

        return new PageImpl<>(dataListVOS, page.getPageable(), page.getTotalElements());

    }

    public List<DataListVO> createDataListVOS(List<Data> list) {
        List<DataListVO> result = new ArrayList<>();

        // 缓存数据
        Map<String, Experiment> expRecordMap = new HashMap<>();
        Map<String, Run> runRecordMap = new HashMap<>();
        Map<String, Analysis> analRecordMap = new HashMap<>();

        for (Data x : list) {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
            if (StrUtil.isBlank(x.getRunNo()) && StrUtil.isBlank(x.getAnalNo())) {
                continue;
            }

            if (StrUtil.isNotBlank(x.getRunNo())) {
                Run run;
                if (runRecordMap.containsKey(x.getRunNo())) {
                    run = runRecordMap.get(x.getRunNo());
                } else {
                    Optional<Run> optional = runRepository.findFirstByRunNo(x.getRunNo());
                    if (!optional.isPresent()) {
                        continue;
                    }
                    run = optional.orElseThrow(() -> new ServiceException("Run not found"));
                    runRecordMap.put(x.getRunNo(), run);
                }
                vo.setRunNo(run.getRunNo());
                vo.setExpNo(run.getExpNo());
                vo.setSapNo(run.getSapNo());

                Experiment experiment;
                if (expRecordMap.containsKey(run.getExpNo())) {
                    experiment = expRecordMap.get(run.getExpNo());
                } else {
                    Optional<Experiment> optional = experimentRepository.findTopByExpNo(run.getExpNo());
                    if (!optional.isPresent()) {
                        continue;
                    }
                    experiment = optional.orElseThrow(() -> new ServiceException("Experiment not found"));
                    expRecordMap.put(run.getExpNo(), experiment);
                }
                vo.setProjNo(experiment.getProjectNo());
            }

            if (StrUtil.isNotBlank(x.getAnalNo())) {
                Analysis analysis;
                if (analRecordMap.containsKey(x.getAnalNo())) {
                    analysis = analRecordMap.get(x.getAnalNo());
                } else {
                    Optional<Analysis> optional = analysisRepository.findTopByAnalysisNo(x.getAnalNo());
                    if (!optional.isPresent()) {
                        continue;
                    }
                    analysis = optional.orElseThrow(() -> new ServiceException("analysis not found"));
                    analRecordMap.put(x.getAnalNo(), analysis);
                }
                vo.setAnalNo(analysis.getAnalysisNo());
                vo.setAnalName(analysis.getName());
                vo.setAnalType(analysis.getAnalysisType());
            }
            result.add(vo);
        }

        return result;
    }
}
