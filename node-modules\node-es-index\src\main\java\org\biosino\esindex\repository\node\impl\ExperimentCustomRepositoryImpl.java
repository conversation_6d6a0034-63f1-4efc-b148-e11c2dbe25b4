package org.biosino.esindex.repository.node.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.esindex.repository.node.ExperimentCustomRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ExperimentCustomRepositoryImpl implements ExperimentCustomRepository {
    private final MongoTemplate mongoTemplate;

    private Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").is(VisibleStatusEnum.Accessible.name())
                .and("audited").is(AuditEnum.audited.name());
    }


    @Override
    public List<String> findNosByTypes(Collection<String> types, Pageable pageable) {
        if (CollUtil.isEmpty(types)) {
            return new ArrayList<>();
        }
        final Query query = new Query(baseCriteria().and("exp_type").in(types));
        query.with(pageable);
        query.fields().include("exp_no").exclude("_id");
        final List<Experiment> experiments = mongoTemplate.find(query, Experiment.class);
        if (CollUtil.isNotEmpty(experiments)) {
            return experiments.stream().map(Experiment::getExpNo).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }


    @Override
    public List<Experiment> findAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("exp_no").in(expNos),
                Criteria.where("used_ids").in(expNos)
        ));
        List<Experiment> list = mongoTemplate.find(new Query(new Criteria().andOperator(condition)), Experiment.class);
        return list;
    }
}
