package org.biosino.esindex.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.CacheConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.es.entity.NodeEs;
import org.biosino.common.es.entity.NodeRelatedEs;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.biosino.common.es.mapper.NodeEsMapper;
import org.biosino.common.es.mapper.NodeRelatedEsMapper;
import org.biosino.common.es.mapper.TaxonomyMapper;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.redis.service.RedisService;
import org.biosino.esindex.config.ThreadPoolConfig;
import org.biosino.esindex.dto.mapper.NodeEsDTOMapper;
import org.biosino.esindex.dto.stat.NodeEsStatDTO;
import org.biosino.esindex.index.iteration.IterateAnalysisData;
import org.biosino.esindex.index.iteration.IterateExperimentData;
import org.biosino.esindex.index.iteration.IterateProjectData;
import org.biosino.esindex.index.iteration.IterateSampleData;
import org.biosino.esindex.index.pojo2doc.*;
import org.biosino.esindex.index.wrapper.Search;
import org.biosino.esindex.repository.node.AnalysisRepository;
import org.biosino.esindex.repository.node.ExperimentRepository;
import org.biosino.esindex.repository.node.FastQCTaskRepository;
import org.biosino.esindex.repository.node.ProjectRepository;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.elasticsearch.action.admin.indices.flush.FlushRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.biosino.common.es.entity.NodeEs.NODE_ES_NAME;
import static org.biosino.common.es.entity.NodeRelatedEs.NODE_RELATED_ES_NAME;
import static org.biosino.esindex.index.pojo2doc.ConvertPojoAbstract.addCollIntoSet;
import static org.biosino.esindex.index.pojo2doc.ConvertPojoAbstract.getFirst;

/**
 * es数据同步服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndexScheduledService {
    private final NodeEsMapper nodeEsMapper;
    private final NodeRelatedEsMapper nodeRelatedEsMapper;
    private final ProjectRepository projectRepository;
    private final ExperimentRepository experimentRepository;
    private final AnalysisRepository analysisRepository;
    private final RestHighLevelClient restHighLevelClient;
    private final RedisService redisService;

    private final CreateTaxonomyService createTaxonomyService;
    private final CreatePlatformService createPlatformService;
    private final CreateDiseaseService createDiseaseService;

    private final FastQCTaskRepository fastQCTaskRepository;

    private final TaxonomyMapper taxonomyMapper;

    @Autowired
    @Qualifier(value = ThreadPoolConfig.THREAD_POOL_NAME)
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final Map<String, Object> taskRunningMap = new ConcurrentHashMap<>();
    private static final String CREATE_INDEX_TASK = "create_index_task";
    private static final String CREATE_RELATED_INDEX_TASK = "create_related_index_task";
    private static final String INDEX_INSERT_SIZE = "index_insert_size";
    private static final String RELATED_INDEX_INSERT_SIZE = "related_index_insert_size";
//    private static final String REDIS_RELATED_KEY = "related_es_";
//    private static final String ADD_INDEX_PREFIX = "index_import_";

    /**
     * 创建es索引，导入数据
     */
    public void createIndex() {
        final String runningKey = CREATE_INDEX_TASK;
        if (taskRunningMap.containsKey(runningKey)) {
            throw new ServiceException("ES同步任务正在运行中");
        }
        taskRunningMap.put(runningKey, System.currentTimeMillis());
        threadPoolTaskExecutor.execute(() -> createIndexTask(runningKey));
    }

    private List<String> prjIds() {
        final LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper.eq(NodeEs::getType, NodeEsTypeEnum.project.name());
        wrapper.select(NodeEs::getId);
        List<String> ids = nodeEsMapper.selectList(wrapper).stream().map(NodeEs::getId).collect(Collectors.toList());
        if (ids.size() > 3) {
            for (int i = 0; i < 3; i++) {
                ids.remove(ids.size() - 1);
            }
        }
        return ids;
    }

    /**
     * 创建es索引，导入数据
     */
    public synchronized void createIndexTask(String runningKey) {
        try {
            log.info("开始ES同步任务");
//            createIndexIfNotExist();
            createNewIndex();
            // 同步项目数据
            Search search = new Search();
            search.setOwnership(OwnershipEnum.self_support.getDesc());

//            final IterateProjectData iterateProjectData = new IterateProjectData(search, mongoTemplate, Boolean.TRUE.equals(isIncremental) ? prjIds() : null);
            final IterateProjectData iterateProjectData = new IterateProjectData(search, mongoTemplate, null);
            final ConvertPojo2ProjectData convertPojo2ProjectData = new ConvertPojo2ProjectData();
            final ConvertPojo2ExperimentData convertPojo2ExperimentData = new ConvertPojo2ExperimentData();
            final ConvertPojo2SampleData convertPojo2SampleData = new ConvertPojo2SampleData();
            log.info("开始同步项目信息");
            final Map<String, List<NodeEs>> sampleExpNosMap = new HashMap<>();
            while (true) {
                final Collection<Project> projects = iterateProjectData.next();
                final int prjSize = CollUtil.size(projects);
                if (prjSize == 0) {
                    refresh();
                    break;
                }
                final List<NodeEs> nodeEsList = new ArrayList<>();
                final Set<String> deleteIds = new HashSet<>();
                int i = 0;
                String firstPrjNos = "";
                String lastPrjNos = "";
                for (Project project : projects) {
                    importProjectEs(project, nodeEsList, deleteIds,
                            convertPojo2ProjectData, convertPojo2ExperimentData, convertPojo2SampleData, sampleExpNosMap);
                    if (i == 0) {
                        firstPrjNos = project.getProjectNo();
                    }
                    if (++i == prjSize) {
                        lastPrjNos = project.getProjectNo();
                    }
                }
                if (CollUtil.isNotEmpty(deleteIds)) {
                    nodeEsMapper.deleteBatchIds(deleteIds);
                }
                if (CollUtil.isNotEmpty(nodeEsList)) {
                    insertBatch(nodeEsList, deleteIds);
                    sampleExpNosMap.clear();
                }

                log.info("已同步项目相关数据量: {}，firstPrjNos:{}, lastPrjNos:{}", nodeEsList.size(), firstPrjNos, lastPrjNos);
            }
            log.info("ES 同步项目任务完毕，耗时：{}", timeToken((Long) taskRunningMap.get(runningKey)));

            search = new Search();
            search.setOwnership(OwnershipEnum.self_support.getDesc());
            // 查询并同步Analysis
            final IterateAnalysisData iterateAnalysisData = new IterateAnalysisData(search, mongoTemplate);
            final ConvertPojo2AnalysisData convertPojo2AnalysisData = new ConvertPojo2AnalysisData();
            log.info("开始同步Analysis");
            final Set<String> targetNos = new HashSet<>();
            while (true) {
                final Collection<Analysis> analyses = iterateAnalysisData.next();
                if (CollUtil.isEmpty(analyses)) {
                    refresh();
                    break;
                }
                final List<NodeEs> nodeEsList = new ArrayList<>();
                final Set<String> deleteIds = new HashSet<>();
                for (Analysis analysis : analyses) {
                    importAnalysisEs(analysis, nodeEsList, deleteIds, convertPojo2AnalysisData, targetNos, convertPojo2ProjectData, convertPojo2ExperimentData, convertPojo2SampleData);
                }

                if (CollUtil.isNotEmpty(deleteIds)) {
                    nodeEsMapper.deleteBatchIds(deleteIds);
                }
                if (CollUtil.isNotEmpty(nodeEsList)) {
                    insertBatch(nodeEsList, deleteIds);
                }
                log.info("已同步Analysis数量: {}", nodeEsList.size());
            }

            log.info("ES Analysis同步任务完毕，耗时：{}", timeToken((Long) taskRunningMap.get(runningKey)));

            log.warn("ES同步任务完毕，耗时：{}", timeToken((Long) taskRunningMap.get(runningKey)));
        } catch (Exception e) {
            log.error("ES同步任务异常", e);
            throw new ServiceException(e.getMessage());
        } finally {
            taskRunningMap.remove(runningKey);
        }
    }

    /**
     * 批量新增, 分页新增
     *
     * @param nodeEsList
     * @param deleteIds
     */
    private void insertBatch(final List<NodeEs> nodeEsList, Set<String> deleteIds) {
        if (CollUtil.isNotEmpty(deleteIds)) {
            // 清理被删除的关联id
            final Set<String> deleteUsedIds = new HashSet<>();
            for (NodeEs es : nodeEsList) {
                String typeId = es.getTypeId();
                if (deleteIds.contains(typeId)) {
                    deleteUsedIds.addAll(es.getUsedIds());
                }
                removeAll(es.getRelaTypeIds(), deleteIds);
            }
            if (CollUtil.isNotEmpty(deleteUsedIds)) {
                for (NodeEs es : nodeEsList) {
                    removeAll(es.getRelaUsedIds(), deleteUsedIds);
                }
            }
        }

        final int total = nodeEsList.size();
        final int pageSize = 1000;
        final int totalPage = PageUtil.totalPage(total, pageSize);
        for (int page = 0; page < totalPage; page++) {
//            ThreadUtil.sleep(200);
            final List<NodeEs> tempData = new ArrayList<>();
            for (int j = 0; j < pageSize; j++) {
                final int i = page * pageSize + j;
                if (i < total) {
                    NodeEs nodeEs = nodeEsList.get(i);
                    tempData.add(nodeEs);
                } else {
                    break;
                }
            }
            nodeEsMapper.insertBatch(tempData);
            nodeEsMapper.refresh();

            Integer insertSize = (Integer) taskRunningMap.get(INDEX_INSERT_SIZE);
            if (insertSize == null) {
                insertSize = 0;
            }
            insertSize += tempData.size();
            taskRunningMap.put(INDEX_INSERT_SIZE, insertSize);
            if (insertSize >= 3000) {
                try {
                    // 调用refresh和flush，防止服务器内存压力过大
                    refresh();
                } finally {
                    taskRunningMap.put(INDEX_INSERT_SIZE, 0);
                }
            }
        }
    }

    private <T> void removeAll(Set<T> set, Collection<T> col) {
        if (set != null) {
            set.removeAll(col);
        }
    }

    private synchronized void refresh() {
        try {
            nodeEsMapper.refresh();
            restHighLevelClient.indices().flush(new FlushRequest(NodeEs.NODE_ES_NAME), RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error(NodeEs.NODE_ES_NAME + " ES refresh and flush error", e);
        }
    }

    private String timeToken(long startTime) {
        return timeToken(startTime, BetweenFormatter.Level.MINUTE);
    }

    private String timeToken(long startTime, BetweenFormatter.Level level) {
        return DateUtil.formatBetween(System.currentTimeMillis() - startTime, level);
    }

    /**
     * 单个分析及其关联数据(data数据)同步
     */
    private void importAnalysisEs(Analysis analysis, final List<NodeEs> nodeEsList, Set<String> deleteIds,
                                  final ConvertPojo2AnalysisData convertPojo2AnalysisData, final Set<String> targetNos, final ConvertPojo2ProjectData convertPojo2ProjectData,
                                  final ConvertPojo2ExperimentData convertPojo2ExperimentData,
                                  final ConvertPojo2SampleData convertPojo2SampleData) {
        final NodeEs anaNodeEs = convertPojo2AnalysisData.toDocument(analysis);
        if (anaNodeEs == null || anaNodeEs.getId() == null) {
            return;
        }
        boolean deleted = IterateAnalysisData.isDeleted(analysis);
        if (deleted) {
            deleteIds.add(anaNodeEs.getId());
            return;
        }

        // 补充target关联的公开数据
        final List<AnalysisTarget> target = analysis.getTarget();
        if (CollUtil.isNotEmpty(target)) {
            for (AnalysisTarget analysisTarget : target) {
                final String type = analysisTarget.getType();
                final List<String> nos = analysisTarget.getNos();
                if (type != null && CollUtil.isNotEmpty(nos)) {
                    switch (type) {
                        case "project":
                            for (final String no : nos) {
                                if (no == null || targetNos.contains(no)) {
                                    continue;
                                }
                                targetNos.add(no);

                                final Long count = nodeEsMapper.selectCount(EsWrappers.lambdaQuery(NodeEs.class).eq(NodeEs::getId, no));
                                if (count == null || count == 0) {
                                    final Project one = mongoTemplate.findOne(visibleQuery("proj_no", no), Project.class);
                                    if (one != null) {
                                        final NodeEs nodeEs = convertPojo2ProjectData.toDocument(one);
                                        if (nodeEs != null) {
                                            nodeEsList.add(nodeEs);
                                        }
                                    }
                                }
                            }
                            break;
                        case "experiment":
                            for (final String no : nos) {
                                if (no == null || targetNos.contains(no)) {
                                    continue;
                                }
                                targetNos.add(no);

                                final Long count = nodeEsMapper.selectCount(EsWrappers.lambdaQuery(NodeEs.class).eq(NodeEs::getId, no));
                                if (count == null || count == 0) {
                                    final Experiment one = mongoTemplate.findOne(visibleQuery("exp_no", no), Experiment.class);
                                    if (one != null) {
                                        final NodeEs nodeEs = convertPojo2ExperimentData.toDocument(one, null, null);
                                        if (nodeEs != null) {
                                            nodeEsList.add(nodeEs);
                                        }
                                    }
                                }
                            }
                            break;
                        case "sample":
                            for (final String no : nos) {
                                if (no == null || targetNos.contains(no)) {
                                    continue;
                                }
                                targetNos.add(no);

                                final Long count = nodeEsMapper.selectCount(EsWrappers.lambdaQuery(NodeEs.class).eq(NodeEs::getId, no));
                                if (count == null || count == 0) {
                                    final Sample one = mongoTemplate.findOne(visibleQuery("sap_no", no), Sample.class);
                                    if (one != null) {
                                        final NodeEs nodeEs = convertPojo2SampleData.toDocument(one, null, null, null, null);
                                        if (nodeEs != null) {
                                            nodeEsList.add(nodeEs);
                                        }
                                    }
                                }
                            }
                            break;
                    }
                }
            }
        }
        nodeEsList.add(anaNodeEs);
    }

    private Query visibleQuery(String field, String no) {
        return new Query(Criteria.where(field).is(no).and("visible_status").is(VisibleStatusEnum.Accessible.name()));
    }

    /**
     * 单个项目及其关联数据(实验、样本、data数据)同步
     */
    private void importProjectEs(final Project project, final List<NodeEs> nodeEsListAll, final Set<String> deleteIds,
                                 final ConvertPojo2ProjectData convertPojo2ProjectData,
                                 final ConvertPojo2ExperimentData convertPojo2ExperimentData,
                                 final ConvertPojo2SampleData convertPojo2SampleData,
                                 final Map<String, List<NodeEs>> sampleExpNosMap
    ) {
        final NodeEs prjNodeEs = convertPojo2ProjectData.toDocument(project);
        if (prjNodeEs == null) {
            return;
        }
        final NodeEs initPrj = NodeEsDTOMapper.INSTANCE.copy(prjNodeEs);
        final String prjId = prjNodeEs.getId();
        if (prjId == null) {
            return;
        }

        // 查询实验
        Search search = new Search();
        search.setOwnership(OwnershipEnum.self_support.getDesc());
        search.setProjectNo(prjId);
        final IterateExperimentData iterateExperimentData = new IterateExperimentData(search, mongoTemplate);
        final Collection<Experiment> experiments = iterateExperimentData.next();
        final Map<String, NodeEs> sampleMap = new LinkedHashMap<>();
        final NodeEsStatDTO nodeEsStatDTO = new NodeEsStatDTO();

        final List<NodeEs> nodeEsList = new ArrayList<>();
        if (CollUtil.isNotEmpty(experiments)) {
            // 统计信息
            for (Experiment experiment : experiments) {
                NodeEs expNodeEs = convertPojo2ExperimentData.toDocument(experiment, prjNodeEs, initPrj);
                if (expNodeEs == null) {
                    continue;
                }
                final String expId = expNodeEs.getId();
                if (expId == null) {
                    continue;
                }
                final NodeEs initExp = NodeEsDTOMapper.INSTANCE.copy(expNodeEs);

                // 查询run, 关联出sample
                search = new Search();
                search.setOwnership(OwnershipEnum.self_support.getDesc());
                search.setExpNo(expId);

                final boolean expDeleted = IterateExperimentData.isDeleted(experiment);
                final IterateSampleData iterateSample = new IterateSampleData(search, mongoTemplate, expNodeEs, nodeEsStatDTO, prjNodeEs);

                final Collection<Sample> samples = iterateSample.next();
                // 在IterateSampleData中，补充了实验的security和dataType
                expNodeEs = iterateSample.getExpNodeEs();

                if (CollUtil.isNotEmpty(samples)) {
                    for (Sample sample : samples) {
                        final NodeEs sampleNodeEs = convertPojo2SampleData.toDocument(sample, expNodeEs, prjNodeEs, initPrj, initExp);
                        if (sampleNodeEs == null || sampleNodeEs.getId() == null) {
                            continue;
                        }

                        final String sapId = sampleNodeEs.getId();
                        if (IterateSampleData.isDeleted(sample)) {
                            deleteIds.add(sapId);
                            continue;
                        }
                        // 使用map存放sample信息，防止关联样本重复
                        sampleMap.put(sapId, sampleNodeEs);
                        // 存放关联实验
                        List<NodeEs> nodeEsListOfSap = sampleExpNosMap.get(sapId);
                        if (nodeEsListOfSap == null) {
                            nodeEsListOfSap = new ArrayList<>();
                        }
                        nodeEsListOfSap.add(initExp);
                        sampleExpNosMap.put(sapId, nodeEsListOfSap);
                    }
                }

                if (expDeleted) {
                    deleteIds.add(expId);
                    continue;
                }

                // 添加项目信息
                LinkedHashSet<String> coll = addCollIntoSet(prjNodeEs.getRelaFileType(), expNodeEs.getRelaFileType());
                prjNodeEs.setFileType(getFirst(coll));
                prjNodeEs.setRelaFileType(coll);

                prjNodeEs.setSecurity(addCollIntoSet(prjNodeEs.getSecurity(), expNodeEs.getSecurity()));

                // 添加统计信息
//                expNodeEs.setNumOfSample(nodeEsStatDTO.findSampleCountByExp(expId));
//                expNodeEs.setNumOfFile(nodeEsStatDTO.findFileCountByExp(expId));
                nodeEsList.add(expNodeEs);
            }
        }
        // 添加样本es数据
        if (CollUtil.isNotEmpty(sampleMap)) {
            // 样本被多个项目使用时，合并相关实验信息
            final List<NodeEs> existedSample = existSample(sampleMap.keySet());
            if (CollUtil.isNotEmpty(existedSample)) {
                for (final NodeEs nodeEsSaved : existedSample) {
                    final String id = nodeEsSaved.getId();
                    final NodeEs newNodeEs = sampleMap.get(id);
                    // 查出来的数据没有visibleStatus
                    ConvertPojoAbstract.mergeExpInfo(nodeEsSaved, newNodeEs, false);

                    sampleMap.put(id, newNodeEs);
                }
            }

            final Collection<NodeEs> values = sampleMap.values();
            for (NodeEs nodeEs : values) {
                // 添加统计信息
                final String id = nodeEs.getId();

                // 合并exp信息到sample
                List<NodeEs> expList = sampleExpNosMap.get(id);
                if (CollUtil.isNotEmpty(expList)) {
                    for (NodeEs expNodeEs : expList) {
                        ConvertPojoAbstract.mergeExpInfo(expNodeEs, nodeEs);
                    }
                }
            }

            nodeEsList.addAll(values);
        }

        final boolean prjDeleted = IterateProjectData.isDeleted(project);

        boolean withPrj = true;
        if (prjDeleted) {
            deleteIds.add(prjId);
            withPrj = false;
        }

        final LinkedHashSet<String> relaExpType = prjNodeEs.getRelaExpType();
        final int expSize = CollUtil.size(relaExpType);
        if (expSize == 0) {
            // 不存在组学的项目，需要关联查询analysis的target包含该项目，且公开的数据
            final Optional<Analysis> analysisOptional = analysisRepository.findTopByTargetNo(prjId, AuthorizeType.project);
            if (!analysisOptional.isPresent()) {
                // target不包含该项目，则忽略此项目
                deleteIds.add(prjId);
                withPrj = false;
            }
        }

        if (withPrj) {
            final boolean isMultiOmics = expSize > 1;
            prjNodeEs.setMultiOmics(isMultiOmics);

            ConvertPojoAbstract.initAccess(prjNodeEs);
            nodeEsList.add(prjNodeEs);
        }

        nodeEsListAll.addAll(nodeEsList);
    }

    private int add(Integer n1, Integer n2) {
        return ((n1 == null) ? 0 : n1) + ((n2 == null) ? 0 : n2);
    }

    private List<NodeEs> existSample(Collection<String> ids) {
//        nodeEsMapper.refresh();
//        final int total = nodeEsList.size();
//        final int pageSize = 1000;
//        final int totalPage = PageUtil.totalPage(total, pageSize);
        final LambdaEsQueryWrapper<NodeEs> wrapper2 = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper2.in(NodeEs::getId, ids);
        return nodeEsMapper.pageQuery(wrapper2, 1, CollUtil.size(ids)).getList();
    }

    /**
     * 删库重新录入
     */
    private void createNewIndex() {
        final Boolean exists = nodeEsMapper.existsIndex(NODE_ES_NAME);
        if (Boolean.TRUE.equals(exists)) {
            nodeEsMapper.deleteIndex(NODE_ES_NAME);
            ThreadUtil.sleep(6000);
        }
        nodeEsMapper.createIndex();
        ThreadUtil.sleep(2000);
    }

    /**
     * 创建es
     */
    private void createIndexIfNotExist() {
        final Boolean exists = nodeEsMapper.existsIndex(NODE_ES_NAME);
        if (Boolean.FALSE.equals(exists)) {
            nodeEsMapper.createIndex();
        }
    }

    public void createProjectIndex(final String projectNo) {
        if (StrUtil.isBlank(projectNo)) {
            throw new ServiceException("项目编号不能为空");
        }

        final long startTime = System.currentTimeMillis();

        createIndexIfNotExist();

        // 删除所有相关项目及所属数据
        LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper.eq(NodeEs::getTypeId, projectNo).eq(NodeEs::getType, NodeEsTypeEnum.project.name())
                .select(NodeEs::getRelaTypeIds).limit(1);
        final NodeEs es = nodeEsMapper.selectOne(wrapper);

        final Set<String> prjIds = new HashSet<>();
        if (es != null) {
            final Set<String> allIds = new HashSet<>();

            final Set<String> sampleIds = new HashSet<>();
            Set<String> relaTypeIds = es.getRelaTypeIds();
            if (CollUtil.isNotEmpty(relaTypeIds)) {
                allIds.addAll(relaTypeIds);

                for (String typeId : relaTypeIds) {
                    if (typeId.startsWith(SequenceType.SAMPLE.getPrefix())) {
                        sampleIds.add(typeId);
                    }
                }
            }

            if (CollUtil.isNotEmpty(sampleIds)) {
                wrapper = EsWrappers.lambdaQuery(NodeEs.class);
                wrapper.in(NodeEs::getTypeId, sampleIds).eq(NodeEs::getType, NodeEsTypeEnum.sample.name())
                        .select(NodeEs::getRelaTypeIds);
                final List<NodeEs> samples = nodeEsMapper.selectList(wrapper);
                if (CollUtil.isNotEmpty(samples)) {
                    for (NodeEs sample : samples) {
                        relaTypeIds = sample.getRelaTypeIds();
                        if (CollUtil.isNotEmpty(relaTypeIds)) {
                            for (String typeId : relaTypeIds) {
                                if (typeId.startsWith(SequenceType.PROJECT.getPrefix())) {
                                    prjIds.add(typeId);
                                }
                            }
                        }
                    }
                }
            }

            prjIds.remove(projectNo);
            if (!prjIds.isEmpty()) {
                wrapper = EsWrappers.lambdaQuery(NodeEs.class);
                wrapper.in(NodeEs::getTypeId, prjIds).eq(NodeEs::getType, NodeEsTypeEnum.project.name())
                        .select(NodeEs::getRelaTypeIds);
                final List<NodeEs> projects = nodeEsMapper.selectList(wrapper);
                if (CollUtil.isNotEmpty(projects)) {
                    for (NodeEs prj : projects) {
                        relaTypeIds = prj.getRelaTypeIds();
                        allIds.addAll(relaTypeIds);
                    }
                }
            }

            if (CollUtil.isNotEmpty(allIds)) {
                nodeEsMapper.delete(EsWrappers.lambdaQuery(NodeEs.class).in(NodeEs::getTypeId, allIds));
                refresh();
            }
        }
        prjIds.add(projectNo);

        final List<Project> projects = projectRepository.findAllByProjectNoIn(prjIds);
        if (CollUtil.isEmpty(projects)) {
            throw new ServiceException("项目不存在");
        }
//        final ExecutorService executor = ThreadPoolUtil.executor();

        for (Project project : projects) {
            importProjectToEs(project, startTime);
        }
    }

    private void importProjectToEs(Project project, final long startTime) {
        final String currPrjNo = project.getProjectNo();
//        try {
//            interruptProjectTask(currPrjNo);
//            SINGLE_PRJ_THREAD.put(currPrjNo, Thread.currentThread());

        IterateProjectData.handelMore(project, mongoTemplate);

        final ConvertPojo2ProjectData convertPojo2ProjectData = new ConvertPojo2ProjectData();
        final ConvertPojo2ExperimentData convertPojo2ExperimentData = new ConvertPojo2ExperimentData();
        final ConvertPojo2SampleData convertPojo2SampleData = new ConvertPojo2SampleData();

        final List<NodeEs> nodeEsList = new ArrayList<>();
        final Set<String> deleteIds = new HashSet<>();

        final Map<String, List<NodeEs>> sampleExpNosMap = new HashMap<>();
        importProjectEs(project, nodeEsList, deleteIds,
                convertPojo2ProjectData, convertPojo2ExperimentData, convertPojo2SampleData, sampleExpNosMap);

        if (CollUtil.isNotEmpty(deleteIds)) {
            nodeEsMapper.deleteBatchIds(deleteIds);
        }
        if (CollUtil.isNotEmpty(nodeEsList)) {
            insertBatch(nodeEsList, deleteIds);
        }
        refresh();
        log.warn("已同步项目: {}, 耗时：{}", currPrjNo, timeToken(startTime, BetweenFormatter.Level.SECOND));
        /*} finally {
            SINGLE_PRJ_THREAD.remove(currPrjNo);
        }*/
    }

    private synchronized void interruptProjectTask(final String projectNo) {
        /*final Thread thread = SINGLE_PRJ_THREAD.get(projectNo);
        if (thread != null) {
            try {
                ThreadUtil.interrupt(thread, true);
            } finally {
                SINGLE_PRJ_THREAD.remove(projectNo);
            }
        }*/
    }

    public void createAnalysisIndex(String analysisNo) {
        if (StrUtil.isBlank(analysisNo)) {
            throw new ServiceException("Analysis编号不能为空");
        }
        final long startTime = System.currentTimeMillis();
        createIndexIfNotExist();
        Analysis analysis = analysisRepository.findTopByAnalysisNo(analysisNo).orElseThrow(() -> new ServiceException("Analysis不存在"));
        IterateAnalysisData.handelMore(analysis, mongoTemplate);
        final ConvertPojo2AnalysisData convertPojo2AnalysisData = new ConvertPojo2AnalysisData();

        final List<NodeEs> nodeEsList = new ArrayList<>();
        final Set<String> targetNos = new HashSet<>();
        final Set<String> deleteIds = new HashSet<>();

        final ConvertPojo2ProjectData convertPojo2ProjectData = new ConvertPojo2ProjectData();
        final ConvertPojo2ExperimentData convertPojo2ExperimentData = new ConvertPojo2ExperimentData();
        final ConvertPojo2SampleData convertPojo2SampleData = new ConvertPojo2SampleData();

        importAnalysisEs(analysis, nodeEsList, deleteIds, convertPojo2AnalysisData, targetNos, convertPojo2ProjectData, convertPojo2ExperimentData, convertPojo2SampleData);
        if (CollUtil.isNotEmpty(deleteIds)) {
            nodeEsMapper.deleteBatchIds(deleteIds);
        }
        if (CollUtil.isNotEmpty(nodeEsList)) {
            insertBatch(nodeEsList, deleteIds);
        }
        refresh();
        log.warn("已同步Analysis: {}, 耗时：{}", analysisNo, timeToken(startTime, BetweenFormatter.Level.SECOND));
    }

    /**
     * 创建关联数据ES索引数据
     */
    public void createRelatedNodeEs() {
        final String runningKey = CREATE_RELATED_INDEX_TASK;
        if (taskRunningMap.containsKey(runningKey)) {
            throw new ServiceException("关联数据ES索引同步任务正在运行中");
        }
        taskRunningMap.put(runningKey, System.currentTimeMillis());
        threadPoolTaskExecutor.execute(() -> createRelatedNodeEsTask(runningKey));
    }

    private void createRelatedIndexIfNotExist() {
        final Boolean exists = nodeRelatedEsMapper.existsIndex(NODE_RELATED_ES_NAME);
        if (Boolean.FALSE.equals(exists)) {
            nodeRelatedEsMapper.createIndex();
        }
    }

    private synchronized void createRelatedNodeEsTask(final String runningKey) {
        try {
            log.info("开始关联数据ES同步任务");
            deleteDataEsCache();
//            createRelatedIndexIfNotExist();
            final Boolean exists = nodeRelatedEsMapper.existsIndex(NODE_RELATED_ES_NAME);
            if (Boolean.TRUE.equals(exists)) {
                nodeRelatedEsMapper.deleteIndex(NODE_RELATED_ES_NAME);
                ThreadUtil.sleep(6000);
            }
            nodeRelatedEsMapper.createIndex();
            ThreadUtil.sleep(2000);

//            final Set<String> redisKeys = new HashSet<>();
//            final Date lastUpdateDate = lastUpdateIndexQuery();
//            final Criteria criteria = new Criteria();
            /*if (lastUpdateDate != null) {
                criteria.orOperator(
                        new Criteria().orOperator(Criteria.where("updateDate").isNull(), Criteria.where("updateDate").exists(false))
                                .and("submission_date").gte(lastUpdateDate),
                        Criteria.where("updateDate").gte(lastUpdateDate)
                );
            }*/
//            final Set<String> allHasQcDataNos = new HashSet<>();

            final Query query = new Query(baseCriteria());
            final int pageSize = 8000;
            for (int i = 0; i < NodeRelatedEs.NODE_RELATED_MAX_COUNT; i++) {
                query.skip((long) i * pageSize).limit(pageSize);
                final List<Data> dataList = mongoTemplate.find(query, Data.class);
                if (CollUtil.isEmpty(dataList)) {
                    break;
                }
                log.info("开始关联数据ES同步任务第{}页", i + 1);
                final List<NodeRelatedEs> nodeEsList = new ArrayList<>();
                final Set<String> deleteIds = new HashSet<>();
                final Set<String> runNos = new HashSet<>();
                final Set<String> analNos = new HashSet<>();
                final Set<String> dataNos = new HashSet<>();
                for (Data data : dataList) {
                    String runNo = data.getRunNo();
                    if (StrUtil.isNotBlank(runNo)) {
                        dataNos.add(data.getDatNo());
                        runNos.add(runNo);
                    } else {
                        String analNo = data.getAnalNo();
                        if (StrUtil.isNotBlank(analNo)) {
                            analNos.add(analNo);
                        }
                    }
                }
                Map<String, Run> runNoMap = null;
                Map<String, Analysis> analNoMap = null;
                if (CollUtil.isNotEmpty(runNos)) {
                    if (CollUtil.isNotEmpty(runNos)) {
                        final Query runQuery = new Query(baseCriteria().and("run_no").in(runNos));
                        runNoMap = mongoTemplate.find(runQuery, Run.class).stream()
                                .collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
                    }


                    if (CollUtil.isNotEmpty(analNos)) {
                        final Query analQuery = new Query(baseCriteria().and("anal_no").in(analNos));
                        analNoMap = mongoTemplate.find(analQuery, Analysis.class).stream()
                                .collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));
                    }

                }
                final List<String> hasQcDataNos = fastQCTaskRepository.findAllByStatusAndDataNoIn(FastQCTaskStatusEnum.success.name(), dataNos).stream().map(FastQCTask::getDataNo).collect(Collectors.toList());
//                allHasQcDataNos.addAll(hasQcDataNos);

                dataNos.clear();
                for (Data data : dataList) {
                    importDataRelatedEs(data, nodeEsList, deleteIds, runNoMap, analNoMap, hasQcDataNos);
                }
                if (CollUtil.isNotEmpty(deleteIds)) {
                    deleteDataIndex(deleteIds);
                }
                final int size = CollUtil.size(nodeEsList);
                if (size > 0) {
//                    redisKeys.addAll(nodeEsList.stream().map(x -> redisKey(x.getId())).collect(Collectors.toList()));
                    insertRelatedEsBatch(nodeEsList);
                    refreshRelatedEs();
                    log.info("同步关联数据ES数据量：{}, 最后dataNo: {}", size, nodeEsList.get(size - 1));
                }
            }
//            deleteCachedByNos(redisKeys);
            log.warn("关联数据ES同步任务完毕，耗时：{}", timeToken((Long) taskRunningMap.get(runningKey)));

            // 将不存在data的数据同步到node_related_es索引库中
            insertNoDataItems();
        } catch (Exception e) {
            log.error("关联数据ES同步任务异常", e);
            throw new ServiceException(e.getMessage());
        } finally {
            deleteDataEsCache();
            taskRunningMap.remove(runningKey);
        }
    }

    /**
     * 将不存在data的数据同步到node_related_es索引库中
     */
    public void insertNoDataItems() {
        nodeRelatedEsMapper.refresh(NODE_RELATED_ES_NAME);

        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        wrapper.exists(NodeRelatedEs::getExpNo);
        wrapper.select(NodeRelatedEs::getExpNo);

        final Set<String> expNos = new HashSet<>();
//        final Set<String> anaNos = new HashSet<>();
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 30000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            for (NodeRelatedEs es : list) {
                if (es.getExpNo() != null) {
                    expNos.add(es.getExpNo());
                }
                /*if (es.getAnalNo() != null) {
                    anaNos.add(es.getAnalNo());
                }*/
            }
        }

        log.warn("当前索引库中的组学个数：{}", expNos.size());


        if (CollUtil.isNotEmpty(expNos)) {
            final List<Experiment> list = mongoTemplate.find(new Query(baseCriteria().and("exp_no").nin(expNos)
                    .and("audited").is(AuditEnum.audited.name())
                    .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Experiment.class);

            final int size = CollUtil.size(list);
            if (size > 0) {
                log.warn("不存在data数据的组学个数：{}", size);

                final List<NodeRelatedEs> newData = new ArrayList<>();
                for (Experiment exp : list) {
                    final NodeRelatedEs es = new NodeRelatedEs();
                    es.setExpNo(exp.getExpNo());

                    final String visibleStatus = exp.getVisibleStatus();
                    es.setExpVisible(visibleStatus);
                    es.setExpName(exp.getName());
                    es.setExpType(exp.getExpType());

                    final String projectNo = exp.getProjectNo();
                    if (projectNo == null) {
                        continue;
                    }
                    es.setProjNo(projectNo);

                    NodeRelatedEs cachedRelatedEs = findCachedByNo(projectNo);
                    if (cachedRelatedEs == null) {
                        final Project project = mongoTemplate.findOne(new Query(baseCriteria().and("proj_no").is(projectNo)), Project.class);
                        if (project != null) {
                            cachedRelatedEs = new NodeRelatedEs();
                            cachedRelatedEs.setProjNo(project.getProjectNo());
                            cachedRelatedEs.setProjVisible(project.getVisibleStatus());
                            cachedRelatedEs.setProjName(project.getName());
                            // cachedRelatedEs.setProjDesc(project.getDescription());

                            setUpdateDateTime(cachedRelatedEs, project.getUpdateDate());
                            putCachedByNo(projectNo, cachedRelatedEs);
                        } else {
                            continue;
                        }
                    }

                    es.setProjName(cachedRelatedEs.getProjName());
                    es.setProjVisible(cachedRelatedEs.getProjVisible());
                    setUpdateDateTime(es, cachedRelatedEs.getUpdateDate());
                    setUpdateDateTime(es, exp.getUpdateDate());

                    es.setSecurity(VisibleStatusEnum.Unaccessible.name().equals(visibleStatus) ? SecurityEnum._private.getDesc() : SecurityEnum._restricted.getDesc());
                    es.setCreator(exp.getCreator());
                    long fileSize = 0L;
                    es.setFileSize(fileSize);
                    es.setReadableFileSize(FileUtil.readableFileSize(fileSize));

                    final Date createDate = exp.getCreateDate();
                    es.setUploadTime(createDate);
                    final Date updateDate = exp.getUpdateDate();
                    if (updateDate != null) {
                        es.setSubmissionDate(updateDate);
                    } else {
                        es.setSubmissionDate(createDate);
                    }
                    es.setPublicDate(exp.getPublicDate());

                    /*if (allHasQcDataNos != null) {
                        es.setFastqcFinished(allHasQcDataNos.contains(datNo));
                    }*/

                    newData.add(es);
                }

                if (CollUtil.isNotEmpty(newData)) {
                    insertRelatedEsBatch(newData);
                }
                log.warn("新入库的不存在data数据的组学个数：{}", newData.size());
            }
        }

        /*if (CollUtil.isNotEmpty(anaNos)) {
            final List<Analysis> list = mongoTemplate.find(new Query(baseCriteria().and("anal_no").nin(anaNos)
                    .and("audited").is(AuditEnum.audited.name())
                    .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())), Analysis.class);

            final int size = CollUtil.size(list);

            log.warn("不存在data数据的分析个数：{}", size);
        }*/

    }

    private <T> List<List<T>> subPage(final List<T> list) {
        final int totalNoSize = CollUtil.size(list);
        if (totalNoSize == 0) {
            return new ArrayList<>();
        }
        // Terms Query request has exceeded the allowed maximum of [65536]
        // in查询条数最多不能超过65536
        final int pageSize = 50000;
        int totalPage = PageUtil.totalPage(totalNoSize, pageSize);
        final List<List<T>> listGroup = new ArrayList<>();
        for (int page = 0; page < totalPage; page++) {
            listGroup.add(new ArrayList<>(list.subList(page * pageSize, Math.min((page + 1) * pageSize, totalNoSize))));
        }
        return listGroup;
    }

    private synchronized void insertRelatedEsBatch(final List<NodeRelatedEs> nodeEsList) {
        // 获取所有data no
        final Set<String> allDataNos = new HashSet<>();
        for (NodeRelatedEs es : nodeEsList) {
            final String datNo = es.getDatNo();
            if (datNo != null) {
                allDataNos.add(datNo);
            }
        }

        // 根据data no集合，查询已存在的es数据的id值
        final Map<String, String> dataNoAndIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(allDataNos)) {
            nodeRelatedEsMapper.refresh(NODE_RELATED_ES_NAME);
            final List<List<String>> listGroup = subPage(new ArrayList<>(allDataNos));
            for (List<String> nos : listGroup) {
                final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
                wrapper.select(NodeRelatedEs::getId, NodeRelatedEs::getDatNo);
                wrapper.in(NodeRelatedEs::getDatNo, nos);

                final List<NodeRelatedEs> nodeRelatedEs = nodeRelatedEsMapper.selectList(wrapper);
                if (CollUtil.isNotEmpty(nodeRelatedEs)) {
                    for (NodeRelatedEs es : nodeRelatedEs) {
                        dataNoAndIdMap.put(es.getDatNo(), es.getId());
                    }
                }
            }
        }

        // 将已存在的es索引的id值设置到nodeEsList中，以便insertBatch执行更新操作
        for (NodeRelatedEs es : nodeEsList) {
            final String datNo = es.getDatNo();
            String id = null;
            if (datNo != null) {
                id = dataNoAndIdMap.get(datNo);
            }
            if (StrUtil.isBlank(id)) {
                id = IdUtil.fastSimpleUUID();
            }
            es.setId(id);
        }

        final int total = nodeEsList.size();
        final int pageSize = 5000;
        final int totalPage = PageUtil.totalPage(total, pageSize);
        for (int page = 0; page < totalPage; page++) {
//            ThreadUtil.sleep(200);
            final List<NodeRelatedEs> tempData = new ArrayList<>();
            final int start = page * pageSize;
            for (int j = 0; j < pageSize; j++) {
                final int i = start + j;
                if (i < total) {
                    NodeRelatedEs nodeEs = nodeEsList.get(i);
                    tempData.add(nodeEs);
                } else {
                    break;
                }
            }
            nodeRelatedEsMapper.insertBatch(tempData);
            Integer insertSize = (Integer) taskRunningMap.get(RELATED_INDEX_INSERT_SIZE);
            if (insertSize == null) {
                insertSize = 0;
            }
            insertSize += tempData.size();
            taskRunningMap.put(RELATED_INDEX_INSERT_SIZE, insertSize);
            if (insertSize >= 10000) {
                try {
                    // 调用refresh和flush，防止服务器内存压力过大
                    refreshRelatedEs();
                } finally {
                    taskRunningMap.put(RELATED_INDEX_INSERT_SIZE, 0);
                }
            }
        }
    }

    private void refreshRelatedEs() {
        try {
            nodeRelatedEsMapper.refresh(NODE_RELATED_ES_NAME);
            restHighLevelClient.indices().flush(new FlushRequest(NODE_RELATED_ES_NAME), RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error(NODE_RELATED_ES_NAME + " ES refresh and flush error", e);
        }
    }

    private void importDataRelatedEs(final Data data, final List<NodeRelatedEs> nodeEsList, final Set<String> deleteIds, final List<String> hasQcDataNos) {
        importDataRelatedEs(data, nodeEsList, deleteIds, null, null, hasQcDataNos);
    }

    private void importDataRelatedEs(final Data data, final List<NodeRelatedEs> nodeEsList, final Set<String> deleteIds, final Map<String, Run> runNoMap,
                                     final Map<String, Analysis> analNoMap, final List<String> hasQcDataNos) {
        final String security = data.getSecurity();
        final String datNo = data.getDatNo();
        if (StrUtil.isBlank(datNo)) {
            return;
        }

        final List<String> secList = SecurityEnum.includeAllSecurity();
        if (StrUtil.isBlank(security) || !secList.contains(security) || !"yes".equals(data.getArchived())) {
            deleteIds.add(datNo);
        } else {
            final NodeRelatedEs relatedEs = new NodeRelatedEs();
            final String runNo = data.getRunNo();
            if (StrUtil.isNotBlank(runNo)) {
                // 存在run
                Run run;
                if (CollUtil.isNotEmpty(runNoMap)) {
                    run = runNoMap.get(runNo);
                } else {
                    run = mongoTemplate.findOne(new Query(baseCriteria().and("run_no").is(runNo)), Run.class);
                }
                if (run != null) {
                    relatedEs.setHasData(true);
                    relatedEs.setRunNo(run.getRunNo());
                    relatedEs.setRunName(run.getName());
                    relatedEs.setExpNo(run.getExpNo());
                    relatedEs.setSapNo(run.getSapNo());

                    findByNo(relatedEs, relatedEs.getSapNo(), AuthorizeType.sample);
                    findByNo(relatedEs, relatedEs.getExpNo(), AuthorizeType.experiment);
                    // 必须在AuthorizeType.experiment运行完毕之后运行，否则没有projNo
                    findByNo(relatedEs, relatedEs.getProjNo(), AuthorizeType.project);
                }
            } else {
                // 存在analysis
                final String analNo = data.getAnalNo();
                if (StrUtil.isBlank(analNo)) {
                    return;
                }
                Analysis analysis;
                if (CollUtil.isNotEmpty(analNoMap)) {
                    analysis = analNoMap.get(analNo);
                } else {
                    analysis = mongoTemplate.findOne(new Query(baseCriteria().and("anal_no").is(analNo)), Analysis.class);
                }
                if (analysis != null) {
                    relatedEs.setHasData(true);
                    relatedEs.setAnalNo(analysis.getAnalysisNo());
                    relatedEs.setAnalVisible(analysis.getVisibleStatus());
                    relatedEs.setAnalName(analysis.getName());
                    relatedEs.setAnalType(analysis.getAnalysisType());
                    relatedEs.setCustomAnalysisType(analysis.getCustomAnalysisType());

                    setUpdateDateTime(relatedEs, analysis.getUpdateDate());
                }
            }

            if (Boolean.TRUE.equals(relatedEs.getHasData())) {
                // es id改为自动生成
                // relatedEs.setId(datNo);
                relatedEs.setCreator(data.getCreator());
                relatedEs.setDatNo(datNo);
                relatedEs.setName(data.getName());
                relatedEs.setSecurity(data.getSecurity());
                relatedEs.setMd5(data.getMd5());

                Long fileSize = data.getFileSize();
                fileSize = fileSize == null ? 0L : fileSize;
                relatedEs.setFileSize(fileSize);
                relatedEs.setReadableFileSize(FileUtil.readableFileSize(fileSize));

                relatedEs.setDataType(getFileType(data.getDataType(), data.getFileName()));

                final Date createDate = data.getCreateDate();
                relatedEs.setUploadTime(createDate);
                final Date updateDate = data.getUpdateDate();
                if (updateDate != null) {
                    relatedEs.setSubmissionDate(updateDate);
                } else {
                    relatedEs.setSubmissionDate(createDate);
                }

                relatedEs.setPublicDate(data.getPublicDate());

                if (hasQcDataNos != null) {
                    relatedEs.setFastqcFinished(hasQcDataNos.contains(datNo));
                }

                setUpdateDateTime(relatedEs, data.getUpdateDate());
                nodeEsList.add(relatedEs);
            }
        }
    }

    public static String getFileType(String fileType, String fileName) {
        if (StrUtil.isBlank(fileType) && StrUtil.isNotBlank(fileName)) {
            return StrUtil.trimToNull(FilenameUtils.getExtension(fileName));
        }
        return StrUtil.trimToNull(fileType);
    }

    private void findByNo(final NodeRelatedEs relatedEs, final String no, final AuthorizeType type) {
        if (StrUtil.isBlank(no)) {
            return;
        }
//        boolean hasData = false;
        NodeRelatedEs cachedRelatedEs;
        switch (type) {
            case sample:
                cachedRelatedEs = findCachedByNo(no);
                if (cachedRelatedEs == null) {
                    final Sample sample = mongoTemplate.findOne(new Query(baseCriteria().and("sap_no").is(no)), Sample.class);
                    if (sample != null) {
                        cachedRelatedEs = new NodeRelatedEs();
                        cachedRelatedEs.setSapNo(sample.getSapNo());
                        cachedRelatedEs.setSapVisible(sample.getVisibleStatus());
                        cachedRelatedEs.setSapName(sample.getName());
                        cachedRelatedEs.setSapType(sample.getSubjectType());
                        cachedRelatedEs.setOrganism(sample.getOrganism());
//                        cachedRelatedEs.setSapDesc(sample.getDescription());

                        setUpdateDateTime(cachedRelatedEs, sample.getUpdateDate());
                        putCachedByNo(no, cachedRelatedEs);
                    } else {
                        break;
                    }
                }
                relatedEs.setSapName(cachedRelatedEs.getSapName());
                relatedEs.setSapType(cachedRelatedEs.getSapType());
                relatedEs.setOrganism(cachedRelatedEs.getOrganism());
                relatedEs.setSapVisible(cachedRelatedEs.getSapVisible());
//                relatedEs.setSapDesc(cachedRelatedEs.getSapDesc());

                setUpdateDateTime(relatedEs, cachedRelatedEs.getUpdateDate());
                break;
            case experiment:
                cachedRelatedEs = findCachedByNo(no);
                if (cachedRelatedEs == null) {
                    final Experiment exp = mongoTemplate.findOne(new Query(baseCriteria().and("exp_no").is(no)), Experiment.class);
                    if (exp != null) {
                        cachedRelatedEs = new NodeRelatedEs();
                        cachedRelatedEs.setExpNo(exp.getExpNo());
                        cachedRelatedEs.setExpVisible(exp.getVisibleStatus());
                        cachedRelatedEs.setExpName(exp.getName());
                        cachedRelatedEs.setExpType(exp.getExpType());
//                        cachedRelatedEs.setExpDesc(exp.getDescription());
                        cachedRelatedEs.setProjNo(exp.getProjectNo());

                        setUpdateDateTime(cachedRelatedEs, exp.getUpdateDate());
                        putCachedByNo(no, cachedRelatedEs);
                    } else {
                        break;
                    }
                }
                relatedEs.setExpName(cachedRelatedEs.getExpName());
                relatedEs.setExpType(cachedRelatedEs.getExpType());
                relatedEs.setExpVisible(cachedRelatedEs.getExpVisible());
//                relatedEs.setExpDesc(cachedRelatedEs.getExpDesc());
                relatedEs.setProjNo(cachedRelatedEs.getProjNo());

                setUpdateDateTime(relatedEs, cachedRelatedEs.getUpdateDate());
                break;
            case project:
                cachedRelatedEs = findCachedByNo(no);
                if (cachedRelatedEs == null) {
                    final Project project = mongoTemplate.findOne(new Query(baseCriteria().and("proj_no").is(no)), Project.class);
                    if (project != null) {
                        cachedRelatedEs = new NodeRelatedEs();
                        cachedRelatedEs.setProjNo(project.getProjectNo());
                        cachedRelatedEs.setProjVisible(project.getVisibleStatus());
                        cachedRelatedEs.setProjName(project.getName());
//                        cachedRelatedEs.setProjDesc(project.getDescription());

                        setUpdateDateTime(cachedRelatedEs, project.getUpdateDate());
                        putCachedByNo(no, cachedRelatedEs);
                    } else {
                        break;
                    }
                }
                relatedEs.setProjNo(cachedRelatedEs.getProjNo());
                relatedEs.setProjName(cachedRelatedEs.getProjName());
                relatedEs.setProjVisible(cachedRelatedEs.getProjVisible());
//                relatedEs.setProjDesc(cachedRelatedEs.getProjDesc());

                setUpdateDateTime(relatedEs, cachedRelatedEs.getUpdateDate());
                break;
        }
    }

    private static void setUpdateDateTime(final NodeRelatedEs relatedEs, final Date date) {
        if (relatedEs != null) {
            relatedEs.setUpdateDate(latestDateTime(relatedEs.getUpdateDate(), date));
        }
    }

    @Nullable
    public static Date latestDateTime(final Date date1, final Date date2) {
        if (date1 != null && date2 != null) {
            return date1.after(date2) ? date1 : date2;
        }
        return date1 != null ? date1 : date2;
    }

    private <T> T findCachedByNo(String no) {
        return redisService.getCacheObject(redisKey(no));
    }

    private <T> void putCachedByNo(String no, T data) {
        redisService.setCacheObject(redisKey(no), data, 8L * 60, TimeUnit.MINUTES);
    }

    private String redisKey(String no) {
        return CacheConstants.NODE_RELATED_ES_KEY + no;
    }

    private <T> void deleteCachedByNos(final Set<String> redisKeys) {
        if (CollUtil.isNotEmpty(redisKeys)) {
            redisService.deleteObject(redisKeys);
        }
    }

    /**
     * 根据类型查询最后跟新时间
     *
     * @return
     */
    private Date lastUpdateIndexQuery() {
        nodeRelatedEsMapper.refresh();
        final LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.orderByDesc(NodeRelatedEs::getSubmissionDate);
        final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(queryWrapper, 1, 1);
        final List<NodeRelatedEs> list = pageInfo.getList();
        return CollUtil.isEmpty(list) ? null : list.get(0).getSubmissionDate();
    }

    public void createRelatedDataNodeEs(final String... dataNos) {
        deleteDataEsCache();
        if (ArrayUtil.isEmpty(dataNos)) {
            return;
        }
        final HashSet<String> dataNoSet = CollUtil.newHashSet(dataNos);
        for (String dataNo : dataNos) {
            if (dataNo != null) {
                dataNoSet.add(dataNo);
            }
        }
        if (CollUtil.isEmpty(dataNoSet)) {
            return;
        }

        createRelatedIndexIfNotExist();
        final long startTime = System.currentTimeMillis();
        final List<Data> dataList = mongoTemplate.find(Query.query(baseCriteria().and("dat_no").in(dataNoSet)), Data.class);
        if (CollUtil.isEmpty(dataList)) {
            throw new ServiceException("数据不存在");
        }
        final List<NodeRelatedEs> nodeEsList = new ArrayList<>();
        final Set<String> deleteIds = new HashSet<>();
        final List<String> hasQcDataNos = fastQCTaskRepository.findAllByStatusAndDataNoIn(FastQCTaskStatusEnum.success.name(), dataNoSet).stream().map(FastQCTask::getDataNo).collect(Collectors.toList());
        for (Data data : dataList) {
            importDataRelatedEs(data, nodeEsList, deleteIds, hasQcDataNos);
        }

        if (CollUtil.isNotEmpty(deleteIds)) {
            deleteDataIndex(deleteIds);
        }
        final int size = CollUtil.size(nodeEsList);
        if (size > 0) {
            insertRelatedEsBatch(nodeEsList);
            deleteCachedByNos(nodeEsList.stream().map(x -> redisKey(x.getDatNo())).collect(Collectors.toSet()));
        }
        refreshRelatedEs();
        log.warn("已同步Data关联数据个数: {}, 耗时：{}", size, timeToken(startTime, BetweenFormatter.Level.SECOND));
    }

    private Criteria baseCriteria() {
        return Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc());
    }

    public void createRelatedDataNodeEsByNos(Set<String> dataNos) {
        deleteDataEsCache();
        if (CollUtil.isEmpty(dataNos)) {
            return;
        }
        createRelatedDataNodeEs(dataNos.toArray(new String[0]));
    }

    public void deleteDataEsCache() {
        Collection<String> keys = redisService.keys(CacheConstants.NODE_RELATED_ES_KEY + "*");
        if (CollUtil.isNotEmpty(keys)) {
            redisService.deleteObject(keys);
        }
    }

    public void createOtherNodeEs() {
        final String runningKey = "createOtherNodeEsKey";
        if (taskRunningMap.containsKey(runningKey)) {
            throw new ServiceException("Taxonomy等ES索引同步任务正在运行中");
        }
        taskRunningMap.put(runningKey, System.currentTimeMillis());
        threadPoolTaskExecutor.execute(() -> doCreateOtherNodeEs(runningKey));
    }

    private void doCreateOtherNodeEs(final String runningKey) {
        try {
            log.info("开始同步Taxonomy等数据");
            createTaxonomyService.createTaxonomy();
            createPlatformService.createMicroArrayPlatform();
            createDiseaseService.createDisease();
            log.warn("Taxonomy等ES同步任务完毕，耗时：{}", timeToken((Long) taskRunningMap.get(runningKey)));
        } finally {
            taskRunningMap.remove(runningKey);
        }
    }

    public void updateProjectIndex(String typeId) {
        Project project = projectRepository.findTopByProjectNo(typeId).orElseThrow(() -> new ServiceException("Project not found"));
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getProjNo, typeId));
        if (CollUtil.isEmpty(esList)) {
            return;
        }
        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setProjNo(project.getProjectNo());
            nodeRelatedEs.setProjVisible(project.getVisibleStatus());
            nodeRelatedEs.setProjName(project.getName());
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);
    }

    public void updateExperimentIndex(String typeId) {
        Experiment exp = mongoTemplate.findOne(new Query(baseCriteria().and("exp_no").is(typeId)), Experiment.class);
        if (exp == null) {
            return;
        }
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getExpNo, typeId));
        if (CollUtil.isEmpty(esList)) {
            return;
        }
        // 如果 编辑Experiment的project前后发生了变化，则需要更新相应project的索引
        String projNo = esList.get(0).getProjNo();
        if (!StrUtil.equals(projNo, exp.getProjectNo())) {
            // 重建原project索引
            createProjectIndex(projNo);
        }

        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setProjNo(exp.getProjectNo());
            nodeRelatedEs.setExpNo(exp.getExpNo());
            nodeRelatedEs.setExpVisible(exp.getVisibleStatus());
            nodeRelatedEs.setExpName(exp.getName());
            nodeRelatedEs.setExpType(exp.getExpType());
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);

    }

    public void updateExperimentsIndex(Collection<String> expNos) {
        List<Experiment> expList = experimentRepository.findAllByExpNoIn(expNos);
        List<String> projNos = expList.stream().map(Experiment::getProjectNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        for (String projNo : projNos) {
            createProjectIndex(projNo);
        }
    }

    public void updateSampleIndex(String typeId) {
        Sample sample = mongoTemplate.findOne(new Query(baseCriteria().and("sap_no").is(typeId)), Sample.class);
        if (sample == null) {
            return;
        }
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getSapNo, typeId));
        if (CollUtil.isEmpty(esList)) {
            return;
        }
        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setSapNo(sample.getSapNo());
            nodeRelatedEs.setSapVisible(sample.getVisibleStatus());
            nodeRelatedEs.setSapName(sample.getName());
            nodeRelatedEs.setSapType(sample.getSubjectType());
            nodeRelatedEs.setOrganism(sample.getOrganism());
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);
    }

    public void updateSamplesIndex(Collection<String> sapNos) {
        // 通过sap找到run
        List<Run> runList = mongoTemplate.find(new Query(baseCriteria().and("sap_no").in(sapNos)), Run.class);
        List<String> expNos = runList.stream().map(Run::getExpNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<Experiment> expList = experimentRepository.findAllByExpNoIn(expNos);
        List<String> projNos = expList.stream().map(Experiment::getProjectNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        for (String projNo : projNos) {
            createProjectIndex(projNo);
        }
    }

    public void updateAnalysisIndex(String typeId) {
        Analysis analysis = mongoTemplate.findOne(new Query(baseCriteria().and("anal_no").is(typeId)), Analysis.class);
        if (analysis == null) {
            return;
        }
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getSapNo, typeId));
        if (CollUtil.isEmpty(esList)) {
            return;
        }
        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setAnalNo(analysis.getAnalysisNo());
            nodeRelatedEs.setAnalVisible(analysis.getVisibleStatus());
            nodeRelatedEs.setAnalName(analysis.getName());
            nodeRelatedEs.setAnalType(analysis.getAnalysisType());
            nodeRelatedEs.setCustomAnalysisType(analysis.getCustomAnalysisType());
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);
    }

    public void updateDataIndex(String typeId) {
        Data data = mongoTemplate.findOne(new Query(baseCriteria().and("dat_no").is(typeId)), Data.class);
        if (data == null) {
            return;
        }
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getDatNo, typeId));

        if (CollUtil.isEmpty(esList)) {
            return;
        }
        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setCreator(data.getCreator());
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);
    }

    public void updateDataFastqcStatus(String typeId) {
        FastQCTask task = mongoTemplate.findOne(new Query(Criteria.where("data_no").is(typeId)), FastQCTask.class);
        if (task == null) {
            return;
        }
        List<NodeRelatedEs> esList = nodeRelatedEsMapper.selectList(EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .eq(NodeRelatedEs::getDatNo, typeId));
        if (CollUtil.isEmpty(esList)) {
            return;
        }
        for (NodeRelatedEs nodeRelatedEs : esList) {
            nodeRelatedEs.setFastqcFinished(StrUtil.equals(task.getStatus(), FastQCTaskStatusEnum.success.name()));
        }
        nodeRelatedEsMapper.updateBatchByIds(esList);

    }


    public void deleteDataIndex(Collection<String> typeIds) {
        if (CollUtil.isEmpty(typeIds)) {
            return;
        }
        nodeRelatedEsMapper.delete(EsWrappers.lambdaQuery(NodeRelatedEs.class).in(NodeRelatedEs::getDatNo, typeIds));
    }

    /*public void saveNewTaxonomy(final TaxonomyNodeDTO dto) {
        try {
            threadPoolTaskExecutor.execute(() -> doSaveNewTaxonomy(dto));
        } catch (Exception e) {
            log.warn("保存Taxonomy数据多线程任务出错: {}", e.getMessage());
        }
    }*/

    /**
     * 保存从dms获取的Taxonomy数据
     */
    /*private synchronized void doSaveNewTaxonomy(final TaxonomyNodeDTO dto) {
        if (dto == null) {
            return;
        }
        final String taxId = dto.getTaxId();
        if (taxId == null) {
            return;
        }
        final boolean exists = mongoTemplate.exists(new Query(Criteria.where("tax_id").is(taxId)), TaxonomyNode.class);
        if (!exists) {
            final TaxonomyNode taxonomyNode = new TaxonomyNode();
            taxonomyNode.setTaxId(taxId);
            taxonomyNode.setScientificName(dto.getScientificName());
            mongoTemplate.save(taxonomyNode);
        }

        final Long count = taxonomyMapper.selectCount(EsWrappers.lambdaQuery(TaxonomyEs.class).eq(TaxonomyEs::getTaxId, taxId), false);
        if (count == null || count == 0) {
            final TaxonomyEs taxonomyEs = new TaxonomyEs();
            taxonomyEs.setTaxId(taxId);
            taxonomyEs.setScientificName(dto.getScientificName());
            taxonomyMapper.insert(taxonomyEs);
            taxonomyMapper.refresh();
        }
    }*/
    public void deleteRelatedEsByNo(AuthorizeType authorizeType, String no) {
        if (authorizeType == null || StrUtil.isBlank(no)) {
            return;
        }
        deleteDataEsCache();

        switch (authorizeType) {
            case project:
                nodeRelatedEsMapper.delete(EsWrappers.lambdaQuery(NodeRelatedEs.class).eq(NodeRelatedEs::getProjNo, no));
                break;
            case analysis:
                nodeRelatedEsMapper.delete(EsWrappers.lambdaQuery(NodeRelatedEs.class).eq(NodeRelatedEs::getAnalNo, no));
                break;
        }
    }

}
