import request from '@/utils/request';

const baseURL = '/app/project';

// 获取project的基本信息
export function getProjectGeneralInfo(projNo) {
  return request({
    url: `${baseURL}/getGeneralInfo/${projNo}`,
    method: 'get',
  });
}

// 获取project的基本统计数据
export function getStatInfo(projNo) {
  return request({
    url: `${baseURL}/getStatInfo/${projNo}`,
    method: 'get',
  });
}

// 获取DataList
export function getDataList(projNo) {
  return request({
    url: `${baseURL}/getDataList/${projNo}`,
    method: 'get',
  });
}

// 获取Project有关系的Analysis
export function getRelatedAnalysis(projNo) {
  return request({
    url: `${baseURL}/getRelatedAnalysis/${projNo}`,
    method: 'get',
  });
}

// 获取project的AuthorInfo
export function getAuthorInfo(projNo) {
  return request({
    url: `${baseURL}/getAuthorInfo/${projNo}`,
    method: 'get',
  });
}

// 获取project的组学、样本表格标题信息
export function getExpAndSampleTable(projNo) {
  return request({
    url: `${baseURL}/getExpAndSampleTable/${projNo}`,
    method: 'get',
  });
}

// 获取project的组学、样本表格数据
export function getExpAndSampleList(params) {
  return request({
    url: `${baseURL}/getExpAndSampleList`,
    method: 'get',
    params: params,
  });
}

// 获取统计详情
export function getStatDetail(projNo) {
  return request({
    url: `${baseURL}/getStatDetail/${projNo}`,
    method: 'get',
  });
}

// 获取用户的Project列表
export function listProject(params) {
  return request({
    url: `${baseURL}/listProject`,
    method: 'get',
    params: params,
  });
}

// 检查project是否存在，或者被有权限访问
export function checkProjectPermission(no) {
  return request({
    url: `${baseURL}/checkProjectPermission/${no}`,
    method: 'get',
  });
}

export function getProjectExpTypes(no) {
  return request({
    url: `${baseURL}/getProjectExpTypes/${no}`,
    method: 'get',
  });
}

export function getProjectSubjectTypes(no) {
  return request({
    url: `${baseURL}/getProjectSubjectTypes/${no}`,
    method: 'get',
  });
}

export function getProjectDataSecurity(no) {
  return request({
    url: `${baseURL}/getProjectDataSecurity/${no}`,
    method: 'get',
  });
}

export function getProjectExpInfo(data) {
  return request({
    url: `${baseURL}/getProjectExpInfo`,
    method: 'post',
    data: data,
  });
}
