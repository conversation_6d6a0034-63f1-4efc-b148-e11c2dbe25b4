package org.biosino.member.controller;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import org.biosino.common.core.domain.R;
import org.biosino.member.service.BmdcRegistService;
import org.biosino.system.api.dto.MemberDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 请求用户数据接口
 *
 * <AUTHOR>
 * <p>
 * 使用样例
 * @Autowired private RemoteMemberService memberService;
 * R<Member> nodeadminUser = memberService.getMemberInfoByEmail("<EMAIL>", "NodeadminUser", SecurityConstants.INNER);
 */
@RestController
public class BmdcRegistController {
    private static final Logger log = LoggerFactory.getLogger(BmdcRegistController.class);

    @Autowired
    private BmdcRegistService bmdcRegistService;

    // 临时解决上游bmdc接口慢的问题，后期要改造bmdc加上缓存
    private final TimedCache<String, MemberDTO> getOneMemberByMemberIdMap = CacheUtil.newTimedCache(1000 * 60 * 50);

    /**
     * 根据用户ID查询用户详细信息
     *
     * @param memberId         用户ID
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getOneMemberByMemberId/{memberId}/{currentUserEmail}")
    public R<MemberDTO> getOneMemberByMemberId(@PathVariable String memberId, @PathVariable String currentUserEmail) {
        try {
            MemberDTO member;
            if (getOneMemberByMemberIdMap.containsKey(memberId)) {
                member = getOneMemberByMemberIdMap.get(memberId);
            } else {
                member = bmdcRegistService.getOneMemberByMemberId(memberId, currentUserEmail);
                if (member != null) {
                    getOneMemberByMemberIdMap.put(memberId, member);
                }
            }
            return R.ok(member);
        } catch (Exception e) {
            log.error("查询getOneMemberByMemberId失败, 用户ID：{}，ERROR:{}", memberId, e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    // 临时解决上游bmdc接口慢的问题，后期要改造bmdc加上缓存
    private final TimedCache<String, MemberDTO> getOneMemberByBioIdMap = CacheUtil.newTimedCache(1000 * 60 * 50);

    /**
     * 根据用户BioID查询用户详细信息
     *
     * @param bioId            用户BioID
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getOneMemberByBioId/{bioId}/{currentUserEmail}")
    public R<MemberDTO> getOneMemberByBioId(@PathVariable String bioId, @PathVariable String currentUserEmail) {
        try {
            MemberDTO member;
            if (getOneMemberByBioIdMap.containsKey(bioId)) {
                member = getOneMemberByBioIdMap.get(bioId);
            } else {
                member = bmdcRegistService.getOneMemberByBioId(bioId, currentUserEmail);
                if (member != null) {
                    getOneMemberByBioIdMap.put(bioId, member);
                }
            }
            return R.ok(member);
        } catch (Exception e) {
            log.error("查询getOneMemberByBioId失败, 用户BioID：{}，ERROR:{}", bioId, e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    // 临时解决上游bmdc接口慢的问题，后期要改造bmdc加上缓存
    private final TimedCache<String, MemberDTO> getMemberInfoByEmailMap = CacheUtil.newTimedCache(1000 * 60 * 5);

    /**
     * 根据用户邮箱查询用户详细信息
     *
     * @param memberEmail      用户邮箱
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getMemberInfoByEmail/{memberEmail}/{currentUserEmail}")
    public R<MemberDTO> getMemberInfoByEmail(@PathVariable String memberEmail, @PathVariable String currentUserEmail) {
        try {
            MemberDTO member;
            if (getMemberInfoByEmailMap.containsKey(memberEmail)) {
                member = getMemberInfoByEmailMap.get(memberEmail);
            } else {
                member = bmdcRegistService.getMemberInfoByEmail(memberEmail, currentUserEmail);
                if (member != null) {
                    getMemberInfoByEmailMap.put(memberEmail, member);
                }
            }
            return R.ok(member);
        } catch (Exception e) {
            log.error("查询getMemberInfoByEmail失败, 用户邮箱：{}，ERROR:{}", memberEmail, e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据邮箱关键字模糊查询用户列表
     *
     * @param email            邮箱查询关键字
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getMemberListByMemberEmailLike/{email}/{currentUserEmail}")
    public R<List<MemberDTO>> getMemberListByMemberEmailLike(@PathVariable String email, @PathVariable String currentUserEmail) {
        try {
            List<MemberDTO> members = bmdcRegistService.getMemberListByMemberEmailLike(email, currentUserEmail);
            return R.ok(members);
        } catch (Exception e) {
            log.error("查询getMemberListByMemberEmailLike失败, 用户邮箱：{}，ERROR:{}", email, e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询所有NODE用户邮箱列表
     *
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getAllMemberEmailList/{currentUserEmail}")
    public R<List<String>> getAllMemberEmailList(@PathVariable String currentUserEmail) {
        try {
            List<String> eamilList = bmdcRegistService.getAllMemberEmailList(currentUserEmail);
            return R.ok(eamilList);
        } catch (Exception e) {
            log.error("查询getAllMemberEmailList失败：{}", e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据时间范围查询node用户数量
     *
     * @param startDate        开始时间
     * @param endDate          结束时间
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getNodeMemberByCreateDateRange/{currentUserEmail}")
    public R<Long> getNodeMemberByCreateDateRange(Date startDate, Date endDate, @PathVariable String currentUserEmail) {
        try {
            Long memberTotal = bmdcRegistService.getNodeMemberByCreateDateRange(startDate, endDate, currentUserEmail);
            return R.ok(memberTotal);
        } catch (Exception e) {
            log.error("查询getNodeMemberByCreateDateRange失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询Node中Email
     *
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    @GetMapping("/getNodeMemberEmailList/{currentUserEmail}")
    public R<List<String>> getNodeMemberEmailList(@PathVariable String currentUserEmail) {
        try {
            List<String> emailList = bmdcRegistService.getNodeMemberEmailList(currentUserEmail);
            return R.ok(emailList);
        } catch (Exception e) {
            log.error("查询getNodeMemberEmailList失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询node所有用户的简要信息
     */
    @GetMapping("/getNodeMemberInfoList/{currentUserEmail}")
    public R<List<MemberDTO>> getNodeMemberInfoList(@PathVariable String currentUserEmail) {
        try {
            List<MemberDTO> memberList = bmdcRegistService.getNodeMemberInfoList(currentUserEmail);
            return R.ok(memberList);
        } catch (Exception e) {
            log.error("查询getNodeMemberInfoList失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取指定用户的id to email的map
     */
    @PostMapping("/getMemberIdToEmailMapByMemberIds/{currentUserEmail}")
    public R<Map<String, String>> getMemberIdToEmailMapByMemberIds(@RequestBody Collection<String> memberIds, @PathVariable String currentUserEmail) {
        try {
            Map<String, String> map = bmdcRegistService.getMemberIdToEmailMapByMemberIds(memberIds, currentUserEmail);
            return R.ok(map);
        } catch (Exception e) {
            log.error("查询getMemberIdToEmailMap失败", e);
            return R.fail(e.getMessage());
        }
    }
}
