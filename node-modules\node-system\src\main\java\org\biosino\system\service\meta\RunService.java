package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.dto.dto.export.RunExportDTO;
import org.biosino.system.repository.DataRepository;
import org.biosino.system.repository.RunRepository;
import org.biosino.system.vo.metadata.RunListVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/12/2
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RunService extends BaseService {

    private final RunRepository runRepository;

    private final DataRepository dataRepository;

    public Page<RunListVO> listAuditedRun(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        Page<Run> page = runRepository.findRunPage(queryDTO);
        List<String> runNos = page.getContent().stream().map(Run::getRunNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDatasMap = dataRepository.findDetailByRunNoIn(runNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        List<String> creators = page.getContent().stream().map(Run::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<RunListVO> result = page.map(x -> {
            RunListVO vo = new RunListVO();
            BeanUtil.copyProperties(x, vo);
            List<Data> dataList = runNoToDatasMap.getOrDefault(x.getRunNo(), new ArrayList<>());
            vo.setDataNum(dataList.size());

            Map<String, Long> securityMap = dataList.stream()
                    .collect(Collectors.groupingBy(Data::getSecurity, Collectors.counting()));

            for (String s : SecurityEnum.includeAllSecurity()) {
                securityMap.putIfAbsent(s, 0L);
            }

            vo.setDataCount(securityMap);

            if (x.getSubmitter() != null) {
                vo.setSubmitter(x.getSubmitter().getFirstName() + " " + x.getSubmitter().getLastName());
            }

            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });

        return result;
    }

    public Page<Run> listRun(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        Page<Run> page = runRepository.findRunPage(queryDTO);
        return page;
    }

    public File exportRun(MetadataQueryDTO queryDTO) {
        // 将email转为memberId
        setMetaQueryDTOCreator(queryDTO);
        MongoPagingIterator<Run> iterator = runRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "run.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Run> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Run run : next) {
                    RunExportDTO item = BeanUtil.copyProperties(run, RunExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
