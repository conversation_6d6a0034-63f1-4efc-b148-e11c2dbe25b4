package org.biosino.api.repository;

import org.biosino.common.mongo.entity.Publish;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface PublishRepository extends MongoRepository<Publish, String> {

    List<Publish> findByTypeAndTypeIdInAndAuditedAndDeleted(String type, Collection<String> typeIds, String audited, Boolean deleted);
}
