@import './base.css';

.container-fluid {
    width: 100%;
    padding: 0 10px;
    margin: 0 auto;
    max-width: 80%;
}

.card {
    padding: 15px 20px;
    background-color: #ffffff;
    border-radius: 14px;
    box-shadow: 0 1px 20px 0 rgba(118, 109, 224, 0.1);
}

.card-container {
    padding: 15px 0 !important;
}

.d-flex {
    display: flex !important;
}

.flex-1 {
    flex: 1;
}

.flex-2 {
    flex: 2;
}

.flex-column {
    flex-direction: column
}

.flex-wrap {
    flex-wrap: wrap
}

.justify-center {
    justify-content: center;
}
.justify-space-around{
    justify-content: space-around;
}
.justify-end {
    justify-content: flex-end;
}

.align-items-center {
    align-items: center
}

.align-flex-start {
    align-items: flex-start !important;
}

.justify-space-between {
    justify-content: space-between;
}

.self-flex-start {
    align-self: flex-start
}

.page-content {
    min-height: calc(100vh - 100px);
}

.el-breadcrumb {
    margin-left: 24px;
}

.el-breadcrumb .el-breadcrumb__inner.is-link, .el-breadcrumb__inner a {
    color: #FE7F2B !important;
}

.text-main-color {
    color: #333333;
}

.text-secondary-color {
    color: #666666;
}

.text-other-color {
    color: #999999;
}

.text-align-right {
    text-align: right;
}

.text-align-center {
    text-align: center;
}

.pos-relative {
    position: relative;
}
.pos-absolute {
    position: absolute;
}
.gap-12 {
    gap: 12px;
}

.gap-30 {
    gap: 30px;

}

.row-gap-10 {
    row-gap: 10px;
}
.row-gap-15 {
    row-gap: 15px;
}
.row-gap-20 {
    row-gap: 20px;
}
.w-25 {
    width: 25%;
}

.w-30 {
    width: 30% !important;
}

.w-35 {
    width: 35% !important;
}

.w-40 {
    width: 40% !important;
}

.w-60 {
    width: 60% !important;
}

.w-50 {
    width: 50%;
}

.w-70 {
    width: 70%;
}

.w-75 {
    width: 75%;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100%;
}

.text-primary {
    color: #3A78E8 !important;
}

.text-warning {
    color: #FE7F2B!important;
}

.bg-round-warning {
    background-color: #FE7F2B;
}

.text-danger {
    color: red;
}

.text-data {
    color: #ec8282;
}

.text-data:hover {
    color: #e30707;
}

.text-success, .text-success:hover {
    color: #07BBB3!important;
}

.a-text, .a-text:hover, .a-text:visited {
    color: #3A78E8;
}

.a-text:hover, .a-text:visited {
    text-decoration: underline;
}

.border-0 {
    border: none;
}
.font-17{
    font-size: 17px;
}
.ml-03{
    margin-left: .3rem;
}
.radius-14 {
    border-radius: 14px !important;
}
.radius-20 {
    border-radius: 20px !important;
}
.radius-12 {
    border-radius: 12px!important;
}

.radius-8 {
    border-radius: 8px;
}

.float-right {
    float: right;
}

.font-600 {
    font-weight: 600;
}
.font-700 {
    font-weight: 700;
}
.font-20 {
    font-size: 20px;
}

.font-24 {
    font-size: 24px;
}
.font-28 {
    font-size: 28px;
}
.font-30 {
    font-size: 30px;
}
.font-16 {
    font-size: 16px;
}

.font-18 {
    font-size: 18px;
}

.font-14 {
    font-size: 14px;
}

.font-12 {
    font-size: 12px;
}

.mr-1 {
    margin-right: 1rem !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.mr-05 {
    margin-right: .5rem;
}
.mr-07 {
    margin-right: .7rem;
}
.mr-03 {
    margin-right: .3rem;
}

.mr-2 {
    margin-right: 2rem;
}

.ml-1 {
    margin-left: 1rem;
}

.ml-05 {
    margin-left: .5rem;
}

.ml-2 {
    margin-left: 2rem !important;
}

.ml-3 {
    margin-left: 3rem;
}

.mr-3 {
    margin-right: 3rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-05 {
    margin-top: .5rem!important;
}
.mt-03{
    margin-top: .3rem!important;
}
.mb-03{
    margin-bottom: .3rem!important;
}
.mt-1 {
    margin-top: 1rem !important;
}

.mt-1-5 {
    margin-top: 1.5rem !important;
}

.mt-2 {
    margin-top: 2rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 1rem!important;
}

.mb-05 {
    margin-bottom: .5rem !important;
}

.mb-2 {
    margin-bottom: 2rem !important;
}

.pt-0 {
    padding-top: 0 !important;
}
.pt-20 {
    padding-top: 20px !important;
}
.pl-10 {
    padding-left: 10px !important;
}

.pl-15 {
    padding-left: 15px;
}

.pl-20 {
    padding-left: 20px;
}

.plr-20 {
    padding: 0 20px;
}

.plr-40 {
    padding: 0 40px;
}

.plr-60 {
    padding: 0 60px;
}

.plr-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.p-20 {
    padding: 20px;
}

.p-15 {
    padding: 15px;
}

.pr-20 {
    padding-right: 20px;
}

.bg-gray {
    background-color: #F9F9F9;
}

.bg-success {
    background-color: rgb(7, 188, 180);
}

.bg-complete {
    background-color: #D6E5FF;
}

.bg-warning {
    background-color: #FFF7F2;
}

.bg-white {
    background-color: #ffffff;
}

.before-circle {
    position: relative;
    padding: 2px 0 2px 15px;
    color: #666666;
}

.before-circle:before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: "";
    height: 5px;
    width: 5px;
    border-radius: 50%;
    background: #FE7F2B;
}

/* 气泡*/
.bubble-right {
    position: relative;
    box-sizing: border-box;
    padding: 4px 24px;
    border-radius: 12px;
    background-color: #EFF2F5;
    cursor: pointer;
    transition: all .5s ease-in-out;
}

.bubble-right:hover {
    background-color: #e6edfa;
}

.bubble-right.active {
    border: 1px solid #3A78E8;
    background-color: #D5E4FE;
    box-shadow: 0 3px 10px -2px rgba(226, 236, 232, 0.5);
}

.bubble-right.active:before,
.bubble-right.active:after {
    position: absolute;
    content: '';
    border: 8px solid;
    /*transform: translateY(50%)*/
}

.bubble-right.active:before {
    right: -16px;
    top: 30%;
    /* border-color:transparent transparent transparent #00f; */
    border-color: transparent transparent transparent #3A78E8;

}

.bubble-right.active:after {
    border-color: transparent transparent transparent #D5E4FE;
    right: -14px;
    top: 30%;
}


.category-title {
    background-color: #D6E5FF;
    border-left: 3px solid #3A78E8;
    padding: 4px 0 4px 20px;
    font-size: 16px;
}

.el-table__header th {
    background-color: #f2f2f2;
    color: #333333;
    font-weight: 600;
}

.btn {
    padding: 12px 34px !important;
}

.btn-shadow {
    box-shadow: 0 8px 28px rgba(58, 120, 232, 0.2);
}

.btn-round {
    border: 1px solid #3A78E8;
    color: #3A78E8;
}

/*.<el-button round>Round</el-button>*/
.btn-round-primary, .btn-round-warning {
    background-color: #DDE9FE;
    color: #3B79E8;
    border: 1px solid #3B79E8;
    padding: 12px 28px !important;
    transition: all 0.4s ease;
}

.btn-round-primary:hover {
    background-color: #3B79E8;
    color: #ffffff;
}

.btn-round-warning {
    background-color: #FEEEE4;
    color: #FE7F2B;
    border: 1px solid #FE7F2B;
    padding: 12px 28px !important;
    transition: all 0.4s ease;
}

.main-btn-waring {
    background-color: #FFEBDE !important;
    color: #FE7F2B !important;
    border: 1px solid #FE7F2B !important;
}

.main-btn-waring:hover {
    background-color: #ffe0cc !important;
}

.btn-round-warning:hover {
    background-color: #FE7F2B;
    color: #ffffff;
}

.btn-round-primary span, .btn-round-warning span {
    font-weight: 600;
}

.el-input__wrapper {
    border-radius: 12px!important;
}

.submit-page {
    padding: 20px 0 25px 0;
}

.page {
    padding: 20px 0 25px 0;
}

.el-form .el-select-v2__wrapper {
    border-radius: 12px;
}

.el-form .el-select__wrapper {
    border-radius: 12px;
}

.el-radio__input.is-checked .el-radio__inner {
    border-color: #FE7F2B !important;
    background-color: #FE7F2B !important;
}

.el-radio__label {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
}

.el-radio__input.is-checked + .el-radio__label {
    color: #333333 !important;
}

.el-radio-group .el-radio__label {
    font-weight: 600;
    color: #666666;
}

.el-radio-group .el-radio__input.is-checked + .el-radio__label {
    color: #666666 !important;
}


.btn-prev, .btn-next {
    color: black;
    border-radius: 50% !important;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.4);
    background-color: #fff !important;
}

.el-pager li {
    background-color: transparent;
}

.el-pager li.is-active {
    background-color: #3A78E8;
    border-radius: 8px;
}

/*动画*/
.fade-enter-active,
.fade-leave-active {
    transition: opacity 2s ease-in;
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.preview-dialog .el-dialog__body {
    padding-top: 0 !important;
}

.preview-dialog .el-dialog__title {
    color: #333333;
    font-weight: 700;
    font-size: 20px;
}

.preview-title {
    color: #666666;
}

.preview {
    flex-wrap: wrap;
}

.preview > div {
    display: flex;
    width: 50%;
    padding: 8px 8px 8px 15px;
}

.preview > div:nth-child(odd) {
    /*margin-right: 1rem;*/
}

.preview > div:nth-child(4n+1),
.preview > div:nth-child(4n+2) {
    background-color: #FBFBFB;
}

.preview .title {
    display: inline-block;
    min-width: 150px;
    text-align: right;
    color: #666666;
    font-weight: 600;
    margin-right: 40px;
}

.preview .content {
    white-space: normal; /* 允许内容自动换行 */
    word-break: break-all; /* 在单词内部换行 */
}

.bg-primary {
    background-color: #D6E5FF;
}

.el-table__header .cell {
    font-weight: 600;
}

.tooltip {
    position: absolute;
    background-color: #ffffff;
    color: #333333;
}

.hoveredTH {
    position: relative;
}

.hoveredTH:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    left: unset;
    border-left: 6px solid transparent;
    border-right: none;
    border-top: 6px solid red;
}

.hover-comment {
    position: absolute;
    z-index: 999;
    background: #FFFFE1;
    color: #333333;
    border-radius: 8px;
    font-size: 14px;
    padding: 4px 8px;
    border: 1px solid #999999;
    max-width: 200px;
}

.cursor-pointer:hover {
    cursor: pointer;
}

.handsontableEditor {
    min-height: 130px !important;
    box-shadow: 0 0 12px rgba(0, 0, 0, .12);
}

.pointer-events {
    pointer-events: none;
}

.el-tabs--card > .el-tabs__header {
    border-bottom: none !important;
}

.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: none !important;
}

.el-tabs--card .el-tabs__item {
    border: none;
    border-radius: 4px;
    background-color: #edf4fe;
}

.el-tabs--card .el-tabs__item.is-active {
    background-color: #3a78e8 !important;
    color: #fff !important;
    border-radius: 4px !important;
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner, .el-checkbox__input.is-checked .el-checkbox__inner {
    border-color: #FE7F2B !important;
    background-color: #FE7F2B !important;
}

.animation-enter-from,
.animation-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

.animation-enter-to,
.animation-leave-from {
    opacity: 1;
}

.animation-enter-active {
    transition: all 0.7s ease;
}

.animation-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.6, 0.6, 1);
}

.go-back:hover {
    background-color: #EBF2FD;
}

.line-before {
    position: relative;
    padding-left: 14px;
}

.line-before::before {
    content: '';
    background: #3a78e8;
    width: 3px;
    height: 16px;
    border-radius: 4px;
    position: absolute;
    left: 3px;
    top: 4px;
}

.el-descriptions__cell.is-bordered-label {
    color: #333333 !important;
    font-weight: 600 !important;
    width: 25%;
    background-color: #ECF2FC !important;
    border-color: #dddddd !important;
}

.el-descriptions__cell.is-bordered-content {
    width: 25%;
    border-color: #dddddd !important;
}

.tag-proj {
    background-color: #BB577A !important;
    color: #ffffff !important;
    width: 50px;
}

.tag-samp {
    background-color: #7287ba !important;
    color: #ffffff !important;
    width: 50px;
}

.tag-expr {
    background-color: #07BCB4 !important;
    color: #ffffff !important;
    width: 50px;
}

.tag-anal {
    background-color: #cb9b64 !important;
    color: #ffffff !important;
    width: 50px;
}


.tag-project {
    border: none !important;
    background-color: #FFEBDE !important;
    color: #FE822F !important
}

.tag-project .el-tag__content {
    font-weight: 600;
}

.intable-tooltip {
    width: 140px !important;
}

.card-shadow {
    box-shadow: 0 0 12px rgba(0, 0, 0, .12);
}

.select-item .items-selected {
    min-width: 210px;
}

.select-item .tag {
    gap: 10px;
}


.el-form .el-select__wrapper {
    border-radius: 12px;
}

.complete-bg {
    border-left-color: #07bcb4 !important;
    background-color: #c4f1f0;
}

.rejected-bg {
    border-left-color: #ff8181 !important;
    background-color: #ffe2e2;
}

.reviewing-bg {
    border-left-color: #fe7f2b !important;
    background-color: #ffebde;
}

.waiting-bg {
    border-left-color: #fe7f2b !important;
    background-color: #ffebde;
}

.deleted-bg {
    border-left-color: #999999 !important;
    background-color: #e4e5e5;
}

.editing-bg {
    border-left-color: #3A78E8 !important;
    background-color: #D6E5FF;
}

.complete-linear-bg {
    background: linear-gradient(to right, #07bcb4, #91e7e1);
}

.rejected-linear-bg {
    background: linear-gradient(to right, #ff8181, #ffe2e2);
}

.reviewing-linear-bg {
    background: linear-gradient(to right, #fe7f2b, #ecc8b1);
}

.waiting-linear-bg {
    background: linear-gradient(to right, #fe7f2b, #ecc8b1);
}

.deleted-linear-bg {
    background: linear-gradient(to right, #696969, #969696);
}

.editing-linear-bg {
    background: linear-gradient(to right, #3A78E8, #9eb9e7);
}

.detail-submitter {
    row-gap: 6px;
}

.detail-submitter .label {
    display: inline-block;
    width: 110px;
    margin-bottom: 0.3rem;
}

.detail-submitter .content {
    display: inline-block;
    width: 100%;
    height: 32px;
    padding: 4px 4px 4px 8px;
}

.sub-detail {
    padding: 20px 20px;
    margin-top: 70px;
}

.sub-detail .submit-card {
    position: relative;
    top: -70px;
}

.sub-detail .submit-info {
    border-left: 4px solid;
    row-gap: 8px;
    background-color: transparent !important;
}

.sub-detail .submit-card .el-col:nth-child(even) {
    text-align: right;
}

.sub-detail .submit-card .sub-state {
    display: inline-block;
    text-align: center;
    padding: 0 4px;
    border-radius: 4px;
    transform: skewX(170deg);
    color: #ffffff;
}

.rowHeader {
    background-color: #EDF3FD !important;
}

.rowHeader .colHeader {
    font-weight: 600 !important;
}

.htDimmed {
    overflow: hidden !important;
    white-space: nowrap !important;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;

}

.submission-detail {
    position: absolute;
    z-index: 999;
    background: #000000;
    color: #ffffff;
    border-radius: 4px;
    font-size: 14px;
    padding: 4px 8px;
    max-width: 300px;
}

.el-dialog__title {
    font-weight: 600;
}

.drop-down-title {
    font-weight: 600;
    padding: 2px 15px;
    background-color: #d6e5ff;
    border-radius: 14px;
    border: 1px solid #3a78e8;
    color: #3a78e8;
    transition: all 0.3s ease-in;
}

.drop-down-title:hover {
    background-color: #bad1f8;
}

.mg-divider {
    margin: 10px 0;
}

.id-list {
    background-color: #fff;
    border-radius: 6px;
    padding: 4px 4px 4px 40px;
    position: relative;
    font-size: 12px;
    display: inline-block;
    color: #666;
    box-shadow: 2px 2px #ccc;
}

.id-list > span:first-child {
    position: absolute;
    height: 100%;
    width: 20px;
    text-align: center;
    color: #fff;
    top: 0;
    left: 12px;
    line-height: 28px;
    font-size: 16px;
}

.id-list .btn-project {
    background-color: #00A8FF;
}

.id-list .btn-experiment {
    background-color: #AC6BEC;
}

.id-list .btn-sample {
    background-color: #F29824;
}

.id-list .btn-run {
    background-color: #46C35F;
}

.id-list .btn-data {
    background-color: #FF7777;
}

.round-dialog {
    border-radius: 12px !important;
}

.p-10-15 {
    padding: 10px 15px;
}
.p-10 {
    padding: 10px;
}
.sort {
    background-color: #f5f8fe;
    border-radius: 4px;
    padding: 8px;
}

.sort .el-button {
    margin: 0;
    border: 1px solid #95cdff;
    border-radius: 0;
}

.sort .el-button:nth-of-type(1) {
    border-radius: 12px 0 0 12px;
}

.sort .el-button:last-child {
    border-radius: 0 12px 12px 0;
}

.sort .el-button.active {
    background-color: #ebf2fd;
    color: #3a78e8;
}
.el-button{
    border-radius: 10px!important;
}
.el-button.is-circle{
    border-radius: 50%!important;
}
.align-items-baseline{
    align-items: baseline;
}
