package org.biosino.task.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.VipMapSyncTask;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.task.repository.AnalysisRepository;
import org.biosino.task.repository.DataRepository;
import org.biosino.task.repository.VipmapSyncTaskRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/7/11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VipmapTaskService {

    private final RemoteNotificationService remoteNotificationService;

    private final RemoteMemberService remoteMemberService;

    private final DataRepository dataRepository;

    private final AnalysisRepository analysisRepository;

    private final VipmapSyncTaskRepository vipmapSyncTaskRepository;

    private final MessageSender messageSender;

    @Value("#{${vipmap.analysis-mapping}}")
    private Map<String, String> vipmapAnalysisTypeMap;

    public void handlerTaskProcess(VipMapSyncTask syncTask) {
        Date createDate = syncTask.getCreateDate();
        R<MemberDTO> r = remoteMemberService.getOneMemberByMemberId(syncTask.getCreator(), "FtpUser", SecurityConstants.INNER);
        MemberDTO member = r.getData();
        try {
            // 先拷贝数据，创建Data
            List<SyncFile> syncFiles = syncTask.getSyncFiles();
            ArrayList<Data> dataList = new ArrayList<>();
            for (SyncFile syncFile : syncFiles) {
                try {
                    // 创建data
                    Data data = new Data();
                    // 获取原始文件对象
                    File sourceFile = FileUtil.file(vipmapAnalysisTypeMap.get(syncTask.getAnalysisType()), syncTask.getTaskId(), syncFile.getFilePath());
                    String extension = FilenameUtils.getExtension(syncFile.getFilePath());
                    String newDataPath = MyFileUtils.getNewDataPath(createDate, "." + extension);

                    data.setId(IdUtil.objectId());
                    data.setName(sourceFile.getName());
                    data.setMd5(SecureUtil.md5(sourceFile));
                    data.setFileName(sourceFile.getName());
                    data.setFilePath(MyFileUtils.changeToLinuxSeparator(newDataPath));
                    data.setUploadType(UploadType.transfer.name());
                    data.setSecurity(SecurityEnum._private.getDesc());
                    data.setCreator(syncTask.getCreator());
                    data.setArchived(ArchiveEnum.yes.name());
                    data.setOwnership(OwnershipEnum.self_support.getDesc());
                    data.setFileSize(sourceFile.length());
                    data.setDataType(FileTypeUtils.getDataTypeByName(sourceFile.getName()));
                    data.setCreateDate(createDate);

                    // 拷贝文件
                    // if (FileUtil.isWindows()) {
                    FileUtil.copy(sourceFile, FileUtil.file(DirConstants.DATA_HOME, newDataPath), true);
                    // } else {
                    //     RsyncUtils.copyFile(sourceFile.getAbsolutePath(), FileUtil.file(DirConstants.DATA_HOME, newDataPath).getAbsolutePath());
                    // }

                    // 保存data
                    Data save = dataRepository.save(data);
                    dataList.add(save);

                    syncFile.setDataNo(save.getDatNo());
                    syncFile.setStatus("success");

                } catch (Exception e) {
                    String message = e.getMessage();
                    syncFile.setStatus("fail");
                    syncFile.setFailCause(message);
                }
            }
            // 设置基本信息
            Analysis analysis = new Analysis();
            analysis.setName(syncTask.getTaskId());
            analysis.setAnalysisType("Other");
            analysis.setCustomAnalysisType(StrUtil.format("{} ({})", "ViPMAP", syncTask.getAnalysisType()));

            // 设置target和customTarget
            List<String> runNos = syncFiles.stream().map(SyncFile::getRunNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

            if (CollUtil.isNotEmpty(runNos)) {
                AnalysisTarget target = new AnalysisTarget();
                target.setType(AuthorizeType.run.name());
                target.setNos(runNos);

                analysis.setTarget(CollUtil.newArrayList(target));
            }
            List<CustomTarget> customTargets = syncFiles.stream().filter(x -> StrUtil.isNotBlank(x.getOtherTargetName()) && StrUtil.isNotBlank(x.getOtherTargetLink()))
                    .map(x -> new CustomTarget(x.getOtherTargetName(), x.getOtherTargetLink())).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(customTargets)) {
                analysis.setCustomTarget(customTargets);
            }

            // 设置pipeline
            Pipeline pipeline = new Pipeline();
            pipeline.setIndex(1);
            pipeline.setLink("https://www.biosino.org/vipmap");
            pipeline.setProgram(syncTask.getAnalysisType());
            pipeline.setVersion("1.0.0");
            pipeline.setOutput(syncFiles.stream().map(SyncFile::getDataNo).collect(Collectors.toList()));

            analysis.setPipeline(CollUtil.newArrayList(pipeline));

            // 设置其他信息
            analysis.setCreateDate(createDate);
            analysis.setUpdateDate(createDate);
            analysis.setCreator(syncTask.getCreator());
            analysis.setOwnership(OwnershipEnum.self_support.getDesc());
            analysis.setSubmitter(BeanUtil.copyProperties(member, Submitter.class));
            analysis.setHitNum(0L);
            analysis.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
            analysis.setAudited(AuditEnum.audited.name());

            Analysis save = analysisRepository.save(analysis);

            for (Data data : dataList) {
                data.setAnalNo(save.getAnalysisNo());
            }
            dataRepository.saveAll(dataList);

            syncTask.setAnalysisNo(save.getAnalysisNo());

            // 通知创建索引
            messageSender.sendDelayMsg(RouterKeyEnum.es_index_key.name(),
                    new IndexUpdateMsg(AuthorizeType.data.name(), dataList.stream().map(Data::getDatNo).collect(Collectors.toList())));

            try {
                Map<String, Object> params = new HashMap<>();
                params.put("taskId", syncTask.getTaskId());
                params.put("platform", "ViPMAP");
                params.put("link", WebConstants.WEB_DOMAIN + "/analysis/detail/" + save.getAnalysisNo());
                SendEmailDTO dto = new SendEmailDTO();
                dto.setToEmail(member.getEmail());
                dto.setParams(params);
                dto.setMailTemplate(MailTemplate.Sync_Success);
                remoteNotificationService.sendEmail(dto, SecurityConstants.INNER);
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", syncTask.getTaskId());
            params.put("platform", "ViPMAP");
            params.put("email", member.getEmail());
            params.put("msg", e.getMessage());
            SendEmailDTO dto = new SendEmailDTO();
            dto.setToEmail(WebConstants.SUPPORT_EMAIL);
            dto.setParams(params);
            dto.setMailTemplate(MailTemplate.Sync_Fail);
            remoteNotificationService.sendEmail(dto, SecurityConstants.INNER);
        } finally {
            vipmapSyncTaskRepository.save(syncTask);
        }
    }
}
