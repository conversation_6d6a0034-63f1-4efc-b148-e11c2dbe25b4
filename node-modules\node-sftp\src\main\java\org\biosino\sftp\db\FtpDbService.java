package org.biosino.sftp.db;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.enums.FtpFileLogStatus;
import org.biosino.common.mongo.entity.*;
import org.biosino.sftp.mapper.FtpFileLogMapper;
import org.biosino.sftp.service.FtpFileLogService;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FtpDbService {

    private final FtpDbManager ftpDbManager;
    private final FtpFileLogMapper ftpFileLogMapper;
    private final FtpFileLogService ftpFileLogService;

    public void saveOrUpdateFtpFile(final FtpFileLog ftpFile) {
        if (ftpFile == null) {
            return;
        }
        ftpFileLogService.saveOrUpdate(ftpFile);
    }

    public void updateMd5Info(String md5FileContent, String id) {
        ftpFileLogMapper.updateMd5Info(md5FileContent, id);
    }

    public FtpFileLog getFtpFileByCreatorAndPath(String userId, String path) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path)) {
            return null;
        }
        return ftpFileLogMapper.findFirstByCreatorAndPath(userId, path);
    }

    public FtpFileLog getExistedFtpFileByCreatorAndPath(String userId, String path) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path)) {
            return null;
        }
        List<FtpFileLog> result = ftpFileLogMapper.findAllByCreatorAndPathAndStatusIn(userId, path, FtpFileLogStatus.includeExsistedStatus());
        if (result != null && result.size() > 0) {
            return result.get(0);
        }
        return null;
    }

    public FtpFileLog getFtpFileByCreatorAndPathAndStatus(String userId, String path, String status) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path)) {
            return null;
        }
        if (StringUtils.isBlank(status)) {
            return ftpFileLogMapper.findFirstByCreatorAndPath(userId, path);
        } else {
            return ftpFileLogMapper.findFirstByCreatorAndPathAndStatus(userId, path, status);
        }

    }

    public void updateFtpFileStatusByCreatorAndPath(String userId, String path, String status) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path) || StringUtils.isBlank(status)) {
            return;
        }
        ftpDbManager.updateFtpFileStatusByCreatorAndPath(userId, path, status);
    }

    public void updateFtpFileStatusById(String status, String id) {
        if (StringUtils.isBlank(status) || StringUtils.isBlank(id)) {
            return;
        }
        ftpFileLogMapper.updateFtpFileStatusById(status, id);
    }

    public List<FtpFileLog> getFtpFileByCreatorAndPathLike(String userId, String path) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path)) {
            return null;
        }
        return ftpFileLogMapper.findByCreatorAndPathStartingWith(userId, path);
    }

    public void updateFtpFileStatusByCreatorAndPathLike(String userId, String path, String status) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(path) || StringUtils.isBlank(status)) {
            return;
        }
        ftpDbManager.updateFtpFileStatusByCreatorAndPathLike(userId, path, status);
    }

    public void updateFtpFilePathByCreatorAndPathLike(String creator, String path, String replaceTo) {
        if (StringUtils.isBlank(creator) || StringUtils.isBlank(path) || replaceTo == null) {
            return;
        }
        ftpDbManager.updateFtpFilePathByCreatorAndPathLike(creator, path, replaceTo);
    }

    public Collection<String> searchUserSelfRunNo(String id) {
        if (StringUtils.isBlank(id)) {
            return new LinkedHashSet<>();
        }
        return ftpDbManager.searchUserSelfRunNo(id);
    }

    public Collection<String> searchUserSelfRunNoBySecurity(String id, String security) {
        if (StringUtils.isAnyBlank(id, security)) {
            return new LinkedHashSet<>();
        }
        return ftpDbManager.searchUserSelfRunNoBySecurity(id, security);
    }

    public Collection<String> searchUserSelfAnalysisNoBySecurity(String id, String security) {
        if (StringUtils.isAnyBlank(id, security)) {
            return new LinkedHashSet<>();
        }
        return ftpDbManager.searchUserSelfAnalysisNoBySecurity(id, security);
    }

    public Collection<String> searchUserPublicRunNo(String runNo, int length) {
        if (StringUtils.isBlank(runNo)) {
            return new HashSet<>();
        }
        return ftpDbManager.searchUserPublicRunNo(runNo, length);
    }

    public Collection<String> searchUserPublicAnalysisNo(String analysisNo, int length) {
        if (StringUtils.isBlank(analysisNo)) {
            return new HashSet<>();
        }
        return ftpDbManager.searchUserPublicAnalysisNo(analysisNo, length);
    }

    public Collection<Data> getDataByRunNo(String runNo) {
        if (StringUtils.isBlank(runNo)) {
            return new HashSet<>();
        }
        return ftpDbManager.getDataByRunNo(runNo);
    }

    public Collection<Data> getDataByRunNoAndCreator(String runNo, String creator) {
        if (StringUtils.isBlank(runNo) || StringUtils.isBlank(creator)) {
            return new HashSet<>();
        }
        return ftpDbManager.getDataByRunNoAndCreator(runNo, creator);
    }

    public Collection<Data> getDataByAnalysisNo(String analysisNo) {
        if (StringUtils.isBlank(analysisNo)) {
            return new HashSet<>();
        }
        return ftpDbManager.getDataByAnalysisNo(analysisNo);
    }

    public Collection<Data> getDataByAnalysisNoAndCreator(String analysisNo, String creator) {
        if (StringUtils.isBlank(analysisNo) || StringUtils.isBlank(creator)) {
            return new HashSet<>();
        }
        return ftpDbManager.getDataByAnalysisNoAndCreator(analysisNo, creator);
    }

    public Data getData(String runNo, String fileName) {
        if (StringUtils.isBlank(runNo) || StringUtils.isBlank(fileName)) {
            return null;
        }
        return ftpDbManager.getData(runNo, fileName);
    }

    public Data getDataByAnalysisNo(String analysisNo, String fileName) {
        if (StringUtils.isBlank(analysisNo) || StringUtils.isBlank(fileName)) {
            return null;
        }
        return ftpDbManager.getDataByAnalysisNo(analysisNo, fileName);
    }

    public Run getRun(String runNo) {
        if (StringUtils.isBlank(runNo)) {
            return null;
        }
        return ftpDbManager.getRun(runNo);
    }

    public Analysis getAnalysis(String analysisNo) {
        if (StringUtils.isBlank(analysisNo)) {
            return null;
        }
        return ftpDbManager.getAnalysis(analysisNo);
    }

    public Collection<String> searchShareToUserDataNo(String username, String runNo) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(runNo)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchShareToUserDataNo(username, runNo);
    }

    public Collection<String> searchShareToUserDataNoByAnalysisNo(String username, String analysisNo) {
        if (StringUtils.isBlank(username) || StringUtils.isBlank(analysisNo)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchShareToUserDataNoByAnalysisNo(username, analysisNo);
    }

    public Collection<String> searchShareToUserRunNo(String username) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchShareToUserRunNo(username);
    }

    public Collection<String> searchShareToUserAnalysisNo(String username) {
        if (StringUtils.isBlank(username)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchShareToUserAnalysisNo(username);
    }

    public Collection<String> searchAuthorizeToUserRunNo(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new LinkedHashSet<>();
        }

        Set<String> collect = searchAuthorizeToUserDataNo(userId).stream().map(ResourceAuthorize::getTypeId).collect(Collectors.toSet());

        return ftpDbManager.searchRunNoByDataNo(collect);
    }

    public Collection<String> searchAuthorizeToUserAnalysisNo(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new LinkedHashSet<>();
        }

        Set<String> collect = searchAuthorizeToUserDataNo(userId).stream().map(ResourceAuthorize::getTypeId).collect(Collectors.toSet());
        return ftpDbManager.searchAnalysisNoByDataNo(collect);
    }

    public Collection<Data> searchUserSelfDataNo(String userId, String dataNo) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(dataNo)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchDataByNoAndUserId(userId, dataNo);
    }

    public Collection<ResourceAuthorize> searchAuthorizeToUserDataNo(String userId) {
        if (StringUtils.isBlank(userId)) {
            return new ArrayList<>();
        }
        return searchAuthorizeToUserDataNo(userId, null);
    }

    /**
     * 查询用户授权访问数据的信息
     *
     * @param userId 用户id
     * @param dataNo 数据编号
     * @return
     */
    public Collection<ResourceAuthorize> searchAuthorizeToUserDataNo(String userId, String dataNo) {
        return ftpDbManager.searchAuthorizeToUserDataNo(userId, dataNo);
    }

    /**
     * 保存用户下载的现象
     *
     * @param info
     */
    public void insertDownloadLog(DownloadLog info) {
        if (info == null) {
            return;
        }
        ftpDbManager.saveDownloadLog(info);
    }

    public Collection<String> searchAuthorizeToUserDataNos(String userId, List<String> datNos) {
        if (StringUtils.isBlank(userId) || CollUtil.isEmpty(datNos)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchAuthorizeToUserDataNos(userId, datNos);
    }

    public Collection<String> searchShareToUserDataNos(String username, List<String> dataNos) {
        if (StringUtils.isBlank(username) || CollUtil.isEmpty(dataNos)) {
            return new ArrayList<>();
        }
        return ftpDbManager.searchShareToUserDataNos(username, dataNos);
    }

    public void incDownloadNum(String datNo) {
        if (StringUtils.isBlank(datNo)) {
            return;
        }
        ftpDbManager.incDownloadNum(datNo);
    }

    public Data getDataByDataNo(String dataNo) {
        if (StringUtils.isBlank(dataNo)) {
            return null;
        }
        return ftpDbManager.getDataByDataNo(dataNo);
    }

    public boolean existShareByRunNo(String runNo, String username) {
        if (StringUtils.isBlank(runNo) || StringUtils.isBlank(username)) {
            return false;
        }
        return ftpDbManager.existShareByRunNo(runNo, username);
    }

    public boolean existShareByAnalNo(String analNo, String username) {
        if (StringUtils.isBlank(analNo) || StringUtils.isBlank(username)) {
            return false;
        }
        return ftpDbManager.existShareByAnalNo(analNo, username);
    }
}
