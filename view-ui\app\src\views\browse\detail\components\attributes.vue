<template>
  <div>
    <div class="card mt-1">
      <h3>Attributes</h3>
      <el-divider class="mt-05"></el-divider>
      <div v-for="(item, index) in data" :key="index">
        <el-descriptions :column="pagerCount" border class="mt-1">
          >
          <el-descriptions-item
            v-for="(it, idx) in item"
            :key="'des-' + idx"
            label-class-name="label-style"
            class-name="content-style"
          >
            <template #label>
              {{ $text(it.name) }}
              <el-tooltip
                v-if="!isStrBlank(it.desc)"
                effect="light"
                :content="it.desc"
                placement="top"
                :teleported="false"
              >
                <el-icon
                  size="14"
                  color="#3A78E8"
                  class="pos-relative"
                  style="top: 2px"
                >
                  <question-filled />
                </el-icon>
              </el-tooltip>
            </template>
            <!--            {{ $text(it.value) }}-->
            <span v-html="it.value"></span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div v-if="data.length === 0" class="card mt-1">
      <h3>Attributes</h3>
      <el-divider class="mt-05"></el-divider>
      <el-empty :image-size="100" description="No Data" />
    </div>
  </div>
</template>

<script setup>
  import { defineProps, toRefs } from 'vue';
  import { isStrBlank } from '@/utils';
  import { QuestionFilled } from '@element-plus/icons-vue';

  const props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 移动端页码按钮的数量端默认值5
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 1 : 2,
    },
  });

  let { data } = toRefs(props);
</script>

<style lang="scss">
  .label-style {
    width: 15% !important;
    word-break: break-all;
  }

  .content-style {
    word-break: break-all;
  }
</style>

<style lang="scss" scoped>
  :deep(.el-descriptions__cell.is-bordered-label) {
    width: 15%;
  }
</style>
