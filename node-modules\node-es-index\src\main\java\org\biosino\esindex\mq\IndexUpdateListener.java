package org.biosino.esindex.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.biosino.esindex.service.IndexScheduledService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndexUpdateListener {

    private final IndexScheduledService indexScheduledService;

    @RabbitHandler
    @RabbitListener(queues = "es_index_queue")
    public void updateIndex(@Payload IndexUpdateMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.debug("es_index_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            final String type = msg.getType();
            final List<String> typeIds = msg.getTypeIds();
            final boolean deleteRelatedEs = Boolean.TRUE.equals(msg.getDeleteRelatedEs());

            if (CollUtil.isEmpty(typeIds) || StrUtil.isBlank(type)) {
                throw new ServiceException("es_index update error, Type or type Ids cannot be empty");
            }

            final Set<String> typeIdsSet = new LinkedHashSet<>(typeIds);
            for (String typeId : typeIdsSet) {
                if (AuthorizeType.project.name().equals(type)) {
                    indexScheduledService.createProjectIndex(typeId);
                    if (deleteRelatedEs) {
                        indexScheduledService.deleteRelatedEsByNo(AuthorizeType.project, typeId);
                    }
                } else if (AuthorizeType.analysis.name().equals(type)) {
                    indexScheduledService.createAnalysisIndex(typeId);
                    if (deleteRelatedEs) {
                        indexScheduledService.deleteRelatedEsByNo(AuthorizeType.analysis, typeId);
                    }
                }

            }
            if (AuthorizeType.data.name().equals(type)) {
                indexScheduledService.createRelatedDataNodeEsByNos(typeIdsSet);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    @RabbitHandler
    @RabbitListener(queues = "es_index_update_queue")
    public void updateTypeIndex(@Payload IndexUpdateMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.debug("es_index_update_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            String type = msg.getType();
            List<String> typeIds = msg.getTypeIds();

            if (CollUtil.isEmpty(typeIds) || StrUtil.isBlank(type)) {
                throw new ServiceException("es_index update error, Type or type Ids cannot be empty");
            }

            final Set<String> typeIdsSet = new LinkedHashSet<>(typeIds);
            if (AuthorizeType.project.name().equals(type)) {
                typeIdsSet.forEach(indexScheduledService::createProjectIndex);
            }
            if (AuthorizeType.analysis.name().equals(type)) {
                typeIdsSet.forEach(indexScheduledService::createAnalysisIndex);
            }
            if (AuthorizeType.experiment.name().equals(type)) {
                indexScheduledService.updateExperimentsIndex(typeIdsSet);
            }
            if (AuthorizeType.sample.name().equals(type)) {
                indexScheduledService.updateSamplesIndex(typeIdsSet);
            }
            if (AuthorizeType.data.name().equals(type)) {
                typeIdsSet.forEach(indexScheduledService::updateDataIndex);
            }
            if (AuthorizeType.fastqc.name().equals(type)) {
                typeIdsSet.forEach(indexScheduledService::updateDataFastqcStatus);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

}
