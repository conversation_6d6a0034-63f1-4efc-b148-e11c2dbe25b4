package org.biosino.upload.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/23
 */
@Data
public class ArchiveImportDTO {
    private Integer rowIndex;

    @JsonProperty("experiment_id")
    @JSONField(name = "experiment_id")
    private String expNo;

    @JsonProperty("experiment_name")
    @JSONField(name = "experiment_name")
    private String expName;

    @JsonProperty("sample_id")
    @JSONField(name = "sample_id")
    private String sapNo;

    @JsonProperty("sample_name")
    @JSONField(name = "sample_name")
    private String sapName;

    @JsonProperty("run_id")
    @JSONField(name = "run_id")
    private String runNo;

    @JsonProperty("run_name")
    @JSONField(name = "run_name")
    private String runName;

    @JsonProperty("run_description")
    @JSONField(name = "run_description")
    private String runDesc;

    @JsonProperty("data_id")
    @JSO<PERSON>ield(name = "data_id")
    private String dataNos;

    @JsonProperty("file_name")
    @JSONField(name = "file_name")
    private String fileName;

    @JsonProperty("data_remark")
    @JSONField(name = "data_remark")
    private String dataRemark;

}
