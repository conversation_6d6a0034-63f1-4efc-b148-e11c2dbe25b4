package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.Publish;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.repository.PublishCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.TEMP_DATA_FIELD;
import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class PublishCustomRepositoryImpl implements PublishCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Publish> findByTypeId(String type, String typeId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("type").is(type).and("type_id").is(typeId).and("deleted").is(false);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public List<Publish> findExistTempByTypeId(String type, String typeId) {
        Query query = new Query();
        Criteria criteria = Criteria.where(tempKey("type")).is(type).and(tempKey("type_id")).is(typeId).and(tempKey("deleted")).is(false);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public List<Publish> findTempBySubNo(String subNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where(tempKey("sub_no")).is(subNo);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public List<Publish> findTempByTypeId(String type, String typeId) {
        Query query = new Query();
        Criteria criteria = Criteria.where(tempKey("type")).is(type).and(tempKey("type_id")).is(typeId).and(TEMP_DATA_FIELD).exists(true);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public List<Publish> findAllByDOI(String doi) {
        Query query = Query.query(Criteria.where("DOI").is(doi).and("deleted").is(false));
        return mongoTemplate.find(query, Publish.class);
    }

    @Override
    public void updateToDeleteByTypeAndTypeId(String type, Collection<String> typeIds) {
        if (CollUtil.isEmpty(typeIds)) {
            return;
        }
        Query query = new Query(Criteria.where("type").is(type).and("type_id").in(typeIds));
        Update update = new Update().set("deleted", true);
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Publish.class);
    }
}
