package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.upload.repository.ResourceAuthorizeCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@RequiredArgsConstructor
public class ResourceAuthorizeCustomRepositoryImpl implements ResourceAuthorizeCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<String> searchAuthorizeDataList(String memberId) {
        if (StringUtils.isBlank(memberId)) {
            return new ArrayList<>();
        }
        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where("authorize_to").is(memberId));

        criteriaList.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));

        // 未过期，或者 没有过期时间
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()), Criteria.where("expire_date").exists(false)));

        Query query = new Query(new Criteria().andOperator(criteriaList));

        query.fields().include("data");

        List<ResourceAuthorize> dataList = mongoTemplate.find(query, ResourceAuthorize.class);

        if (CollUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        List<String> resultData = new ArrayList<>();

        for (ResourceAuthorize resourceAuthorize : dataList) {
            resultData.addAll(resourceAuthorize.getData());
        }

        return resultData;
    }

    @Override
    public boolean existAccessableDataNo(String dataNo, String memberId) {
        if (StrUtil.isBlank(dataNo) || StrUtil.isBlank(memberId)) {
            return false;
        }

        List<Criteria> conditions = new ArrayList<>();

        conditions.add(Criteria.where("authorize_to").is(memberId));

        conditions.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));

        conditions.add(new Criteria().orOperator(
                Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)));

        conditions.add(Criteria.where("data").in(dataNo));

        Query query = new Query(new Criteria().andOperator(conditions));

        return mongoTemplate.exists(query, ResourceAuthorize.class);
    }

    @Override
    public Set<String> getAccessableDataNos(Collection<String> dataNos, String memberId) {
        if (CollUtil.isEmpty(dataNos) || StrUtil.isBlank(memberId)) {
            return new HashSet<>();
        }

        List<Criteria> conditions = new ArrayList<>();

        conditions.add(Criteria.where("authorize_to").is(memberId));

        conditions.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));

        conditions.add(new Criteria().orOperator(
                Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)));

        conditions.add(Criteria.where("data").in(dataNos));

        Query query = new Query(new Criteria().andOperator(conditions));
        List<ResourceAuthorize> dataList = mongoTemplate.find(query, ResourceAuthorize.class);
        Set<String> list = dataList.stream()
                .flatMap(x -> x.getData().stream()).collect(Collectors.toSet());
        if (CollUtil.isEmpty(list)) {
            return new HashSet<>();
        }
        Set<String> result = CollUtil.intersectionDistinct(dataNos, list);
        return result;
    }

    @Override
    public List<ResourceAuthorize> findByDataNoInAndCreator(Collection<String> dataNos, String memberId) {
        if (CollUtil.isEmpty(dataNos) || StrUtil.isBlank(memberId)) {
            return new ArrayList<>();
        }

        List<Criteria> conditions = new ArrayList<>();

        conditions.add(Criteria.where("owner").is(memberId));
        conditions.add(Criteria.where("data").in(dataNos));
        conditions.add(Criteria.where("status").in(RequestStatusEnum.request.getDesc(), RequestStatusEnum.authorized.getDesc()));
        conditions.add(new Criteria().orOperator(
                Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)));

        Query query = new Query(new Criteria().andOperator(conditions));

        return mongoTemplate.find(query, ResourceAuthorize.class);

    }
}
