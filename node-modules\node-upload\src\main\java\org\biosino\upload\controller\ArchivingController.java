package org.biosino.upload.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.*;
import org.biosino.upload.repository.RunRepository;
import org.biosino.upload.service.ArchiveService;
import org.biosino.upload.vo.ErrorMsgVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static org.biosino.common.core.web.domain.AjaxResult.success;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/1/15
 */
@RestController
@RequestMapping("/archiving")
@RequiredArgsConstructor
public class ArchivingController {

    private final ArchiveService archiveService;
    private final RunRepository runRepository;

    /**
     * single -> raw data归档
     */
    @PostMapping("/rawData/saveArchive")
    @Log(system = SytemEnum.APP, businessType = BusinessType.INSERT, module1 = "Submit", module2 = "Archiving", module3 = "single", desc = "raw data")
    public AjaxResult rawDataArchive(@Validated @RequestBody RawDataArchiveDTO archiveDTO) {
        archiveService.saveArchivingRawData(archiveDTO);
        runRepository.deletedUselessRun(SecurityUtils.getMemberId());
        return success();
    }

    /**
     * single -> analysis归档
     */
    @PostMapping("/analysisData/saveArchive")
    @Log(system = SytemEnum.APP, businessType = BusinessType.INSERT, module1 = "Submit", module2 = "Archiving", module3 = "single", desc = "analysis data")
    public AjaxResult analysisDataArchive(@Validated @RequestBody AnalysisDataArchiveDTO archiveDTO) {
        archiveService.saveArchivingAnalysisData(archiveDTO);
        runRepository.deletedUselessRun(SecurityUtils.getMemberId());
        return success();
    }

    /**
     * 批量归档 -> raw data归档
     */
    @PostMapping("/rawData/batchSaveArchive")
    @Log(system = SytemEnum.APP, businessType = BusinessType.INSERT, module1 = "Submit", module2 = "Archiving", module3 = "Multiple", desc = "raw data")
    public AjaxResult batchRawDataArchive(@Validated @RequestBody ArchivingBatchDTO archiveDTO) {
        List<ErrorMsgVO> resultVo = archiveService.batchSaveArchivingRawData(archiveDTO);
        runRepository.deletedUselessRun(SecurityUtils.getMemberId());
        return success(resultVo);
    }

    /**
     * 批量归档 -> analysis归档
     */
    @PostMapping("/analysisData/batchSave")
    @Log(system = SytemEnum.APP, businessType = BusinessType.INSERT, module1 = "Submit", module2 = "Archiving", module3 = "Multiple", desc = "analysis data")
    public AjaxResult batchSaveAnalDataArchive(@Validated @RequestBody ArchivingBatchDTO archiveDTO) {
        List<ErrorMsgVO> resultVo = archiveService.batchSaveAnalDataArchive(archiveDTO);
        runRepository.deletedUselessRun(SecurityUtils.getMemberId());
        return success(resultVo);
    }

    /**
     * 批量归档 -> Analysis Data归档回显
     */
    @GetMapping("/analysisData/getMultiArchiveBySubNo/{subNo}")
    public AjaxResult getMultiAnalArchiveBySubNo(@PathVariable String subNo) {
        List<ArchiveAnalysisImportDTO> list = archiveService.getMultiAnalArchiveBySubNo(subNo, true, false);
        return success(list);
    }

    /**
     * 批量归档 -> Raw Data归档回显
     */
    @GetMapping("/getMultiRawDataArchiveBySubNo/{subNo}")
    public AjaxResult getMultiRawDataArchiveBySubNo(@PathVariable String subNo) {
        List<Map<String, Object>> resultVo = archiveService.getMultiRawDataArchiveBySubNo(subNo, true, false);
        return success(resultVo);
    }

    /**
     * 删除Rawdata归档
     */
    @DeleteMapping("/delete")
    @Log(system = SytemEnum.APP, businessType = BusinessType.DELETE, module1 = "Submit", module2 = "Archiving")
    public AjaxResult delete(String subNo, String type, Boolean single) {
        archiveService.delete(subNo, type, single);
        return success();
    }

    /**
     * 取消归档
     */
    @PostMapping("/cancel")
    @Log(system = SytemEnum.APP, businessType = BusinessType.UPDATE, module1 = "Submit", module2 = "Archiving")
    public AjaxResult cancel(@RequestBody @Validated ArchivingCancelDTO dto) {
        archiveService.cancel(dto);
        return success();
    }

}
