<template>
  <div class="app-container">
    <div v-loading="loadingFlag" class="card list">
      <el-form v-show="showSearch" :model="queryParams" :inline="true">
        <el-form-item label="Attributes Name">
          <el-input
            v-model="queryParams.attributesName"
            clearable
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="Input Type">
          <el-select
            v-model="queryParams.dataType"
            style="width: 200px"
            placeholder="Select"
            clearable
          >
            <el-option
              v-for="(item, index) in attrTypeListRef"
              :key="`in_tp_${index}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Status">
          <el-select
            v-model="queryParams.status"
            style="width: 200px"
            placeholder="Select"
            clearable
          >
            <el-option
              v-for="(item, index) in statusListRef"
              :key="`sea_status_${index}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Create Time">
          <el-date-picker
            v-model="queryParams.auditTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 250px"
          ></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="searchData"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetSearchForm">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="async () => await handleAdd()"
            >Add
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Upload" @click="handleImport"
            >Import
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >Export
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="Close" @click="handleClose"
            >Close
          </el-button>
        </el-col>
        <el-col :span="5" :offset="5">
          <strong class="font-600 font-16">{{ attrParentType }}</strong>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="refreshTable"
        ></right-toolbar>
      </el-row>

      <el-table
        :data="attrData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        height="76vh"
      >
        <el-table-column
          prop="attributesName"
          label="Attributes Name"
          sortable
          min-width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="attributesField"
          label="Attributes Field"
          sortable
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="description"
          label="Description"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column
          prop="dataType"
          label="Input Type"
          sortable
          width="120"
        >
          <template #default="scope">
            <span v-text="getInputTypeLabel(scope.row.dataType)"></span>
          </template>
        </el-table-column>

        <el-table-column
          prop="required"
          label="Required"
          sortable
          width="120"
        />
        <el-table-column
          v-if="sampleFlag"
          prop="group"
          label="Group"
          sortable
          width="100"
        />
        <el-table-column prop="sort" label="Sort" sortable width="80" />
        <el-table-column prop="status" label="Status" width="70">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="enable"
              inactive-value="disable"
              @change="val => doChangeStatus(scope.row, val)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="Create Time"
          sortable
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="Update Time"
          sortable
          width="160"
        >
        </el-table-column>
        <el-table-column
          label="Operate"
          align="center"
          fixed="right"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="Edit">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="async () => await handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Delete">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="delAttr(scope.row.id)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!--<pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
      />-->
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="980px" class="exp-dialog">
      <el-form
        ref="expRef"
        v-loading="loadingFlag"
        :model="form"
        label-width="150px"
        :inline="true"
        :rules="rules"
      >
        <el-form-item label="Attribute Name" prop="attributesName">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">Attribute Name</span>
              <el-tooltip
                placement="top"
                content="Can only contain numbers, letters, spaces, '(', ')', '/', '-' and '_'"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.attributesName" />
        </el-form-item>

        <el-form-item label="Attribute Field" prop="attributesField">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">Attribute Field</span>
              <el-tooltip
                placement="top"
                content="Can only contain numbers, letters, spaces, '(', ')', '/', '-' and '_'"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.attributesField" />
        </el-form-item>

        <el-form-item label="Description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            style="width: 705px"
          />
        </el-form-item>
        <el-form-item>
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">Value Format</span>
              <el-tooltip
                placement="top"
                content="The value range requirements filled in the field work together with the Value Regex field. If the Value Regex verification fails, the user will be prompted with this information."
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.valueFormat" />
        </el-form-item>
        <el-form-item>
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">Value Regex</span>
              <el-tooltip
                placement="top"
                content="Custom verification rules for fields, fill in the format as regular expressions"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.valueRegex" />
        </el-form-item>

        <el-form-item v-if="sampleFlag" required prop="group">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">Group</span>
              <el-tooltip
                placement="top"
                content="The group to which the field belongs, the same group will be displayed together."
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input-number
            v-model="form.group"
            controls-position="right"
            :min="1"
          />
        </el-form-item>

        <el-form-item label="Sort" required prop="sort">
          <el-input-number v-model="form.sort" :min="1" />
        </el-form-item>

        <el-form-item label="Status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="(item, index) in statusListRef"
              :key="`dia-stat-${index}`"
              :value="item.value"
              :label="item.label"
            ></el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="Required">
          <el-radio-group v-model="form.required">
            <el-radio
              v-for="(item, index) in requiredListRef"
              :key="`dia-req-${index}`"
              :value="item.value"
              :label="item.label"
            ></el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="Input Type" class="mb-0">
          <el-radio-group v-model="form.dataType" @change="inputTypeChange">
            <el-radio
              v-for="(item, index) in attrTypeListRef"
              :key="`dia-datTp-${index}`"
              :value="item.value"
              :label="item.label"
            >
              <el-tooltip
                v-if="item.remark"
                :content="item.remark"
                placement="top"
              >
                <span class="font-600">{{ item.label }}</span>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="
            form.dataType === 'Select' ||
            form.dataType === 'Select2' ||
            form.dataType === 'Custom'
          "
          label="  "
          prop="desc"
        >
          <div
            v-if="
              form.dataType === 'Select' ||
              form.dataType === 'Select2' ||
              form.dataType === 'Custom'
            "
          >
            <el-checkbox
              v-model="form.allowCreate"
              label="Allow custom input"
              size="large"
            />
          </div>
          <el-input
            v-if="form.dataType === 'Select'"
            v-model="form.selectStr"
            type="textarea"
            rows="5"
            style="width: 620px"
            placeholder="Please enter the value range of the data, one line represents one option"
          />
          <json-editor-vue
            v-if="form.dataType === 'Select2'"
            v-model="form.jsonObj"
            class="editor"
            current-mode="text"
            :mode-list="modeList"
            :options="options"
            @blur="remarkValidate"
            @validation-error="validationError"
          />
          <el-input
            v-if="form.dataType === 'Custom'"
            v-model="form.dataSource"
            style="width: 620px"
            placeholder="Data sources"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-loading="loadingFlag" class="text-center">
          <el-button type="primary" @click="saveForm">Save</el-button>
          <el-button @click="open = false">Cancel</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 属性导入对话框 -->
    <el-dialog
      v-model="upload.open"
      :title="upload.title"
      width="450px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx"
        :headers="upload.headers"
        :action="`${upload.url}?type=${standType}&standId=${standId}&deleteOld=${upload.deleteOld}`"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          Drag the file here, or <em>click upload</em>
        </div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.deleteOld">
                <strong class="text-danger">Delete all</strong>
                existing data
              </el-checkbox>
            </div>
            <span>Only allow importing xlsx format files.</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >Download template
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">Upload</el-button>
          <el-button @click="upload.open = false">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  import JsonEditorVue from 'json-editor-vue3';
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    reactive,
    ref,
    toRaw,
    toRefs,
  } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    attrDetail,
    delAttrStandMg,
    saveAttrStandMg,
    updateAttrStatus,
  } from '@/api/standard/attrMg';
  import { isArrEmpty, trimStr } from '@/utils';

  const { proxy } = getCurrentInstance();
  const loadingFlag = ref(false);

  const route = useRoute();
  const { params } = route;
  const standId = ref(params.standId);
  const standType = ref(params.standType);
  const attrParentType = ref(route.query.type);
  const attrTypeListRef = ref([]);
  const statusListRef = ref([]);
  const requiredListRef = ref([]);

  const sampleFlag = computed(() => standType.value === 'sample');
  const router = useRouter();

  /*** 属性导入参数 */
  const upload = reactive({
    // 是否显示弹出层（属性导入）
    open: false,
    // 弹出层标题（属性导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否删除已经存在的属性数据
    deleteOld: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + `/system/stdMg/importAttrData`,
  });

  // sample类型中group必填
  const numValidate = (rule, value, callback) => {
    if (!sampleFlag.value) {
      callback();
    } else {
      if (value === '') {
        callback(new Error('Please input group'));
      } else {
        callback();
      }
    }
  };

  // Select2 json初始值
  const initSelect2Json = [
    {
      parent_name: 'demo1',
      value_array: ['v1', 'v2'],
    },
    {
      parent_name: 'demo2',
      value_array: ['v3', 'v4', 'v5'],
    },
  ];

  // 导入模块
  const data = reactive({
    form: {
      id: null,
      attributesName: '',
      attributesField: '',
      description: '',
      valueFormat: '',
      valueRegex: '',
      group: null,
      sort: 1,
      status: 'disable',
      required: 'optional',
      dataType: 'Input',
      allowCreate: false,
      selectStr: null,
      jsonObj: proxy.$_.cloneDeep(initSelect2Json),
      dataSource: null,
    },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      attributesName: null,
      dataType: null,
      status: null,
      auditTime: [],
    },
    rules: {
      attributesName: [
        {
          pattern: /^(?!\s+$)[-()/\w ]{1,60}$/,
          required: true,
          message: 'Illegal format',
          trigger: 'blur',
        },
      ],
      attributesField: [
        {
          pattern: /^(?!\s+$)[-()/\w ]{1,60}$/,
          required: true,
          message: 'Illegal format',
          trigger: 'blur',
        },
      ],
      group: [
        {
          validator: numValidate,
          message: 'Illegal group',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  const queryParamsInit = proxy.$_.cloneDeep(toRaw(data.queryParams));
  // 表单初始数据
  const formInit = reactive(proxy.$_.cloneDeep(toRaw(data.form)));
  const editFormInit = ref({});

  const showSearch = ref(true);

  const attrDataOriginal = ref([]);
  const attrData = ref([]);

  const title = ref('Genomic');
  const open = ref(false);
  const addFlag = ref(true);

  const jsonError = ref(true);

  const options = ref({
    search: false,
    history: false,
    enableSort: false,
    enableTransform: false,
  });
  // json编辑器可选模式
  const modeList = ref(['code', 'text']);

  const remarkValidate = () => {};

  // json 校验是否通过
  function validationError(editor, errors) {
    jsonError.value = !isArrEmpty(errors);
  }

  /**打开文件上传弹窗 */
  function handleImport() {
    upload.title = 'Batch import';
    upload.open = true;
    upload.deleteOld = false;
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    loadingFlag.value = true;
    upload.isUploading = true;
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    loadingFlag.value = false;
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs['uploadRef'].handleRemove(file);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      'Import result',
      { dangerouslyUseHTMLString: true },
    );
    initList();
  };

  /** 文件上传失败处理 */
  const handleFileError = (response, file, fileList) => {
    loadingFlag.value = false;
    proxy.$refs['uploadRef'].clearFiles();
    let msg = trimStr(response.msg);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        (msg ? msg : 'upload error') +
        '</div>',
      'Import result',
      { dangerouslyUseHTMLString: true },
    );
  };

  // 表格检索
  function searchData() {
    // 属性创建时间范围
    let createTimeStart = null;
    let createTimeEnd = null;
    if (!isArrEmpty(queryParams.value.auditTime)) {
      createTimeStart = new Date(queryParams.value.auditTime[0] + ' 00:00:00');
      createTimeEnd = new Date(queryParams.value.auditTime[1] + ' 23:59:59');
    }
    // 属性名称
    const attributesName = trimStr(
      queryParams.value.attributesName,
    ).toLowerCase();
    // 属性类型
    const dataType = trimStr(queryParams.value.dataType);
    // 属性状态
    const status = trimStr(queryParams.value.status);

    attrData.value = attrDataOriginal.value.filter(item => {
      // 匹配创建时间
      let matchCreateTime = true;
      if (createTimeStart && createTimeEnd) {
        const createTime = new Date(item.createTime);
        matchCreateTime =
          createTime >= createTimeStart && createTime <= createTimeEnd;
      }
      // 匹配名称
      let matchName = true;
      if (attributesName) {
        matchName =
          item.attributesName &&
          item.attributesName.toLowerCase().includes(attributesName);
      }
      // 匹配属性类型
      let matchInputType = true;
      if (dataType) {
        matchInputType = item.dataType === dataType;
      }
      // 匹配属性状态
      let matchStatus = true;
      if (status) {
        matchStatus = item.status === status;
      }
      return matchCreateTime && matchName && matchInputType && matchStatus;
    });
  }

  // 表格检索条件重置
  function resetSearchForm() {
    queryParams.value = proxy.$_.cloneDeep(queryParamsInit);
  }

  // 单行数据状态变更
  function doChangeStatus(row, status) {
    const param = {
      id: row.id,
      disable: status === 'disable',
      standId: standId.value,
    };
    updateAttrStatus(param)
      .then(res => {
        // console.log(res);
      })
      .catch(() => {
        if (param.disable) {
          row.status = 'enable';
        } else {
          row.status = 'disable';
        }
      });
  }

  /** 重置操作表单 */
  async function reset() {
    await nextTick(() => {
      proxy.resetForm('expRef');
      form.value = proxy.$_.cloneDeep(toRaw(formInit));
    });
  }

  /** 新增按钮操作 */
  async function handleAdd() {
    addFlag.value = true;
    await reset();
    title.value = 'Add attribute';
    open.value = true;
  }

  /** 修改按钮操作 */
  async function handleUpdate(row) {
    row = proxy.$_.cloneDeep(toRaw(row));
    addFlag.value = false;
    await reset();
    // 设置编辑弹窗回显数据
    for (let key in form.value) {
      form.value[key] = row[key];
    }
    if (row.dataType === 'Select2') {
      form.value.jsonObj = proxy.$_.cloneDeep(row.valueRange);
    } else {
      form.value.jsonObj = proxy.$_.cloneDeep(initSelect2Json);
    }
    if (row.dataType === 'Select') {
      form.value.selectStr = row.valueRange
        ? toRaw(row.valueRange).join('\n')
        : null;
    }
    editFormInit.value = proxy.$_.cloneDeep(toRaw(form.value));
    title.value = 'Update attribute';
    open.value = true;
  }

  function inputTypeChange(val) {
    let item = addFlag.value ? formInit : editFormInit.value;
    item = proxy.$_.cloneDeep(toRaw(item));
    form.value.allowCreate = item.allowCreate;
    if (val !== 'Select') {
      form.value.selectStr = item.selectStr;
    }
    if (val !== 'Select2') {
      form.value.jsonObj = item.jsonObj;
    }
    if (val !== 'Custom') {
      form.value.dataSource = item.dataSource;
    }
  }

  /** 删除属性 */
  function delAttr(id) {
    proxy.$modal.confirm('Are you sure to delete this attribute').then(() => {
      loadingFlag.value = true;
      delAttrStandMg(standId.value, id)
        .then(res => {
          loadingFlag.value = false;
          if (res.code === 200) {
            proxy.$modal.msgSuccess(`Delete success`);
            initList();
          }
        })
        .catch(() => {
          loadingFlag.value = false;
        });
    });
  }

  /** 保存属性 */
  function saveForm() {
    proxy.$refs['expRef'].validate(valid => {
      if (valid) {
        const param = {
          ...form.value,
          type: standType.value,
          standId: standId.value,
        };
        const dataType = param.dataType;
        if (dataType === 'Select2') {
          if (jsonError.value) {
            proxy.$modal.alertError(`Invalid JSON`);
            return false;
          }
          const jsonObj = toRaw(param.jsonObj);
          if (!(jsonObj instanceof Array)) {
            proxy.$modal.alertError(`JSON data must be a Array`);
            return false;
          }
          if (isArrEmpty(jsonObj)) {
            proxy.$modal.alertError(`JSON data cannot be empty`);
            return false;
          }
          param.jsonStr = JSON.stringify(jsonObj);
        } else if (dataType === 'Select') {
          const selectStr = trimStr(param.selectStr);
          if (!selectStr) {
            proxy.$modal.alertError(`Select value range cannot be empty`);
            return false;
          }
        }
        // console.log(param);
        loadingFlag.value = true;
        saveAttrStandMg(param)
          .then(res => {
            loadingFlag.value = false;
            if (res.code === 200) {
              proxy.$modal.msgSuccess(`${param.id ? 'Update' : 'Add'} success`);
              open.value = false;
              initList();
            }
          })
          .catch(() => {
            loadingFlag.value = false;
          });
      } else {
        proxy.$modal.msgError(`Please correct the form data`);
      }
    });
  }

  /** 返回按钮操作 */
  function handleClose() {
    router.go(-1);
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download(
      'system/stdMg/importTemplate',
      {},
      `${attrParentType.value}_Attributes_Template.xlsx`,
    );
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs['uploadRef'].submit();
  }

  /** 导出按钮操作 */
  function handleExport() {
    let param = {
      standId: standId.value,
    };
    proxy.download(
      'system/stdMg/exportAttr',
      param,
      `${attrParentType.value}_Attributes_${new Date().getTime()}.xlsx`,
    );
  }

  /** 根据类型值，获取枚举label */
  function getInputTypeLabel(val) {
    let attrTypeList = attrTypeListRef.value;
    let len = attrTypeList.length;
    let label = '';
    if (len > 0) {
      for (let i = 0; i < len; i++) {
        if (attrTypeList[i].value === val) {
          label = attrTypeList[i].label;
          break;
        }
      }
    }
    return label;
  }

  // 刷新表格，并实现前端筛选
  async function refreshTable() {
    await initList();
    searchData();
  }

  /** 查询列表数据 */
  function initList() {
    return new Promise((resolve, reject) => {
      loadingFlag.value = true;
      attrDataOriginal.value = [];
      attrData.value = [];
      attrDetail(standType.value, standId.value)
        .then(res => {
          if (res.data) {
            const { attrTypeList, expSampleType, statusList, requiredList } =
              res.data;
            attrTypeListRef.value = attrTypeList;
            statusListRef.value = statusList;
            requiredListRef.value = requiredList;
            let attributes = expSampleType.attributes;
            if (attributes) {
              attrDataOriginal.value = attributes;
              attrData.value = attributes;
            }
          }
          resolve();
        })
        .catch(() => {
          reject();
        })
        .finally(() => {
          loadingFlag.value = false;
        });
    });
  }

  onMounted(() => {
    // console.log(route);
    initList();
  });
</script>

<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-radio {
      margin-right: 20px;
    }

    .el-radio-group .el-radio__label {
      color: #848484 !important;
      font-size: 14px !important;
    }

    .el-textarea__inner {
      border-radius: 12px;
    }

    .el-input,
    .el-input-number {
      width: 270px;
    }
  }

  .editor {
    width: 620px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }

  .jsoneditor-menu a.jsoneditor-poweredBy {
    display: none !important;
  }
</style>
