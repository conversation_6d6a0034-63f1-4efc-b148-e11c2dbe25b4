package org.biosino.api.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class AnalysisVO {

    private String analysisNo;

    private String name;

    private String description;

    private String analysisType;

    private String customAnalysisType;

    private List<Pipeline> pipeline;

    private List<AnalysisTarget> target;

    private List<CustomTarget> customTarget;

    private Submitter submitter;

    private String creator;

    private Date createDate;

    private Date updateDate;

    private List<String> usedIds;

    private List<String> sourceProject;

    private String visibleStatus;

    private List<PublishVO> references;
}
