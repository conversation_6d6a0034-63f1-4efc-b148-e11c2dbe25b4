package org.biosino.job.repository;

import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ExpSampleTypeCustomRepository {

    default Class<ExpSampleType> getClz() {
        return ExpSampleType.class;
    }

    Set<String> findAllNameByType(ExpSampleTypeEnum type);

    List<ExpSampleType> findByType(String type);

}
