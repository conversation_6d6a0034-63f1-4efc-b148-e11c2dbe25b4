package org.biosino.esindex.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.es.entity.NodeEs;
import org.biosino.common.es.entity.NodeRelatedEs;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.biosino.common.es.mapper.NodeEsMapper;
import org.biosino.common.es.mapper.NodeRelatedEsMapper;
import org.biosino.common.mongo.dto.TreeItemDTO;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.mongo.entity.ExpSampleTypeGroup;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.es.api.dto.*;
import org.biosino.es.api.vo.BrowseStatResVO;
import org.biosino.es.api.vo.detail.ExpSapSearchVO;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.esindex.dto.mapper.NodeEsDTOMapper;
import org.biosino.esindex.dto.mapper.TreeItemDTOMapper;
import org.biosino.esindex.repository.node.*;
import org.dromara.easyes.common.params.SFunction;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NodeEsSearchService {
    private final NodeEsMapper nodeEsMapper;
    private final NodeRelatedEsMapper nodeRelatedEsMapper;

    private final ExpSampleTypeGroupRepository expSampleTypeGroupRepository;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    private final ExperimentRepository experimentRepository;

    private final DataService dataService;

    private final SampleRepository sampleRepository;

    private final FastQCTaskRepository fastQCTaskRepository;

    private static final String EXPERIMENT_TYPE = "experiment";
    private static final String SAMPLE_TYPE = "sample";

    /**
     * 根据项目编号模糊检索所有项目编号
     */
    public List<String> searchPrjId(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return null;
        }

        final LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper.eq(NodeEs::getType, NodeEsTypeEnum.project.name());
        wrapper.like(NodeEs::getTypeId, keyword);
        wrapper.select(NodeEs::getTypeId);

        final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(wrapper, 1, 30);
        final List<String> ids = new ArrayList<>();
        if (pageInfo.getTotal() > 0) {
            final List<NodeEs> list = pageInfo.getList();
            for (NodeEs nodeEs : list) {
                ids.add(nodeEs.getTypeId());
            }
        }
        return ids;
    }

    public Map<String, StatDTO> statByPrj(final Set<String> prjNos) {
        if (CollUtil.isEmpty(prjNos)) {
            return null;
        }
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        wrapper.in(NodeRelatedEs::getProjNo, prjNos)
                .in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
        wrapper.orderByAsc(NodeRelatedEs::getDatNo);
//        final SearchSourceBuilder searchSourceBuilder = nodeRelatedEsMapper.getSearchSourceBuilder(wrapper);
//        searchSourceBuilder.size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
//        searchSourceBuilder.aggregation(initAggregation(entry.getKey(), entry.getValue().getFieldFunc()));


        final Map<String, Set<String>> expNosMap = new HashMap<>();
        final Map<String, Set<String>> expTypesMap = new HashMap<>();

        final Map<String, Set<String>> sapNosMap = new HashMap<>();
        final Map<String, Set<String>> sapTypesMap = new HashMap<>();

        final Map<String, Long> dataCountMap = new HashMap<>();
        final Map<String, Long> dataSizeMap = new HashMap<>();

        final Map<String, StatDTO> result = new HashMap<>();
        for (int i = 0; i < NodeRelatedEs.NODE_RELATED_MAX_COUNT; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 3000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            final int size = CollUtil.size(list);
            if (size == 0) {
                break;
            }
            for (NodeRelatedEs es : list) {
                final String projNo = es.getProjNo();
                result.put(projNo, new StatDTO());

                addToMapSet(projNo, expNosMap, es.getExpNo());
                addToMapSet(projNo, expTypesMap, es.getExpType());

                addToMapSet(projNo, sapNosMap, es.getSapNo());
                addToMapSet(projNo, sapTypesMap, es.getSapType());

                addToMapCount(projNo, dataCountMap, 1L);
                addToMapCount(projNo, dataSizeMap, es.getFileSize());
            }
        }

        for (Map.Entry<String, StatDTO> entry : result.entrySet()) {
            final StatDTO dto = entry.getValue();
            final String key = entry.getKey();
            dto.setExpNos(expNosMap.get(key));
            dto.setExpCount(CollUtil.size(expNosMap.get(key)));
            dto.setExpTypes(expTypesMap.get(key));
            dto.setSapNos(sapNosMap.get(key));
            dto.setSapCount(CollUtil.size(sapNosMap.get(key)));
            dto.setSapTypes(sapTypesMap.get(key));
            dto.setDataSize(dataSizeMap.getOrDefault(key, 0L));
            dto.setDataCount(dataCountMap.getOrDefault(key, 0L));
        }
        return result;
    }

    public Map<String, StatDTO> statByBiomeCurated() {
        Map<String, List<String>> biomeTypeToSapNosMap = sampleRepository.getBiomeTypeToSapNosMap();
        final Map<String, StatDTO> result = new LinkedHashMap<>();
        for (Map.Entry<String, List<String>> entry : biomeTypeToSapNosMap.entrySet()) {
            String type = entry.getKey();
            List<String> sapNos = entry.getValue();
            if (CollUtil.isEmpty(sapNos)) {
                continue;
            }
            LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
            wrapper.in(NodeRelatedEs::getSapNo, sapNos);
            wrapper.orderByAsc(NodeRelatedEs::getDatNo);
            StatDTO statDTO = new StatDTO();
            HashSet<String> sapNoSets = new HashSet<>();
            long dataCount = 0;
            long datasize = 0;
            long basesSize = 0;
            for (int i = 0; i < NodeRelatedEs.NODE_RELATED_MAX_COUNT; i++) {
                EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 3000);
                List<NodeRelatedEs> list = pageInfo.getList();
                int size = CollUtil.size(list);
                if (size == 0) {
                    break;
                }
                for (NodeRelatedEs nodeRelatedEs : list) {
                    dataCount++;
                    datasize += nodeRelatedEs.getFileSize();
                    sapNoSets.add(nodeRelatedEs.getSapNo());
                }

                List<String> dataNos = list.stream().map(NodeRelatedEs::getDatNo).collect(Collectors.toList());
                List<FastQCTask> fastQCTaskList = fastQCTaskRepository.findAllByStatusAndDataNoIn(FastQCTaskStatusEnum.success.name(), dataNos);

                for (FastQCTask fastQCTask : fastQCTaskList) {
                    if (fastQCTask.getSeqkitResult() != null && fastQCTask.getSeqkitResult().getSumLen() != null) {
                        basesSize += fastQCTask.getSeqkitResult().getSumLen();
                    }
                }

            }

            statDTO.setSapNos(sapNoSets);
            statDTO.setSapCount(CollUtil.size(sapNoSets));
            statDTO.setDataCount(dataCount);
            statDTO.setDataSize(datasize);
            statDTO.setBasesSize(basesSize);
            result.put(type, statDTO);

        }
        return result;
    }

    private void addToMapSet(final String key, final Map<String, Set<String>> map, String val) {
        if (val != null) {
            Set<String> set = map.get(key);
            if (set == null) {
                set = new HashSet<>();
            }
            set.add(val);
            map.put(key, set);
        }
    }

    private void addToMapCount(final String key, final Map<String, Long> map, final Long count) {
        if (count != null) {
            Long c = map.get(key);
            if (c == null) {
                c = 0L;
            }
            map.put(key, c + count);
        }
    }

    /**
     * 查询多组学项目数据
     */
    public FdMultipleResDTO searchMultipleOmics(final MultipleOmicsQueryVO searchVO) {
        // 初始化左侧多组学数据
        final Set<TreeItemDTO> multipleOmics = findMultipleOmics();

        final Set<String> expTypes = new HashSet<>();
        final Set<String> checkedIds = new LinkedHashSet<>();
        if (!multipleOmics.isEmpty()) {
            final List<String> leftStatQueries = searchVO.getLeftStatQueries();
            if (CollUtil.isNotEmpty(leftStatQueries)) {
                // 左侧复选框筛选
                final Map<String, String> nameIdMap = new HashMap<>();
                initTreeNameIdMap(multipleOmics, nameIdMap);

                for (String name : leftStatQueries) {
                    if (nameIdMap.containsKey(name)) {
                        expTypes.add(name);
                        checkedIds.add(nameIdMap.get(name));
                    }
                }
            } else {
                // 默认选中第一个子节点
                /*TreeItemDTO first = multipleOmics.iterator().next();
                final List<TreeItemDTO> data = first.getData();
                if (CollUtil.isNotEmpty(data)) {
                    first = data.get(0);
                }
                expTypes.add(first.getFieldName());
                checkedIds.add(first.getId());*/
            }
        }

        final FdMultipleResDTO result = new FdMultipleResDTO();
        result.setEsTreeItems(TreeItemDTOMapper.INSTANCE.copyListToEs(new ArrayList<>(multipleOmics)));
        if (!expTypes.isEmpty()) {
            final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
            queryWrapper.eq(NodeEs::getType, NodeEsTypeEnum.project.name());
            queryWrapper.eq(NodeEs::getMultiOmics, true);
            for (String expType : expTypes) {
                // 树复选框之前为and关系
                queryWrapper.in(NodeEs::getRelaExpType, expType);
            }
            // 添加查询条件
            final String typeId = searchVO.getProjID();
            if (typeId != null) {
                queryWrapper.like(NodeEs::getTypeId, ReUtil.escape(typeId));
            }
            final String name = searchVO.getProjName();
            if (name != null) {
                queryWrapper.like(NodeEs::getName, ReUtil.escape(name));
            }
            final String expType = searchVO.getExpType();
            if (expType != null) {
                queryWrapper.like(NodeEs::getRelaExpType, ReUtil.escape(expType));
            }

            final Pageable pageable = searchVO.initPageInfo();
            // Easy es分页页码从1开始
            final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(queryWrapper, pageable.getPageNumber() + 1, pageable.getPageSize());

            final List<NodeEs> list = pageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                final EsPageInfo<FeatureDataPrjDTO> info = EsPageInfo.of(NodeEsDTOMapper.INSTANCE.copyListToMulPrj(list));
                info.setTotal(pageInfo.getTotal());
                result.setPageInfo(info);
            }
            result.setDefaultCheckedKeys(new ArrayList<>(checkedIds));
        }
        return result;
    }

    private void initTreeNameIdMap(final Collection<TreeItemDTO> multipleOmics, final Map<String, String> nameIdMap) {
        if (CollUtil.isNotEmpty(multipleOmics)) {
            for (TreeItemDTO multipleOmic : multipleOmics) {
                final String fieldName = multipleOmic.getFieldName();
                if (fieldName != null) {
                    nameIdMap.put(fieldName, multipleOmic.getId());
                }
                final List<TreeItemDTO> children = multipleOmic.getData();
                if (CollUtil.isNotEmpty(children)) {
                    initTreeNameIdMap(children, nameIdMap);
                }
            }
        }
    }

    public Set<TreeItemDTO> findMultipleOmics() {
        return findMultipleOmics(false);
    }

    public Set<TreeItemDTO> findMultipleOmics(final boolean forSample) {
        // Yes
        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (forSample) {
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery(fieldName(NodeEs::getType), NodeEsTypeEnum.sample.name()))
                    .filter(QueryBuilders.scriptQuery(multExpScript()))
            );
        } else {
            searchSourceBuilder.query(QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery(fieldName(NodeEs::getType), NodeEsTypeEnum.project.name()))
                    .must(QueryBuilders.termQuery(fieldName(NodeEs::getMultiOmics), true))
            );
        }

        searchSourceBuilder.aggregation(initAggregation("multiOmicsYes", NodeEs::getRelaExpType));
        searchSourceBuilder.size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
        final SearchResponse searchResponse = nodeEsMapper.search(EsWrappers.lambdaQuery(NodeEs.class).setSearchSourceBuilder(searchSourceBuilder));
        // 聚合结果
        final Aggregations aggregations = searchResponse.getAggregations();
        final Map<String, Integer> statMap = parseTerms(aggregations.asList());

        final List<ExpSampleTypeGroup> expGroups = expSampleTypeGroupRepository.findAllByTypeOrderBySortAsc(EXPERIMENT_TYPE);
        final Set<String> names = new LinkedHashSet<>();
        final Set<String> ids = new LinkedHashSet<>();
        final Set<TreeItemDTO> yesData = new LinkedHashSet<>();
//        final List<String> allExperiment = ExpSampleTokenUtils.getAllExperimentName();
        if (CollUtil.isNotEmpty(expGroups)) {
            for (ExpSampleTypeGroup expGroup : expGroups) {
                final TreeItemDTO groupInfo = expGroup.getGroupInfo();
                allMultipleOmics(names, ids, groupInfo, statMap, null);
                if (groupInfo.getNumber() != null && groupInfo.getNumber() == 0) {
                    continue;
                }
                yesData.add(groupInfo);
            }
        }
        final List<ExpSampleType> notSaved = expSampleTypeRepository.findAllByTypeAndNameNotInOrderBySortAsc(EXPERIMENT_TYPE, names);
        if (CollUtil.isNotEmpty(notSaved)) {
            int startId = ids.size();
            for (ExpSampleType expSampleType : notSaved) {
                Integer count = statMap.get(expSampleType.getName().toLowerCase());
                count = count == null ? 0 : count;
                if (count == 0) {
                    continue;
                }
                TreeItemDTO treeItemDTO = new TreeItemDTO(groupItemId(++startId, EXPERIMENT_TYPE), expSampleType.getName());
                treeItemDTO.setNumber(count);
                yesData.add(treeItemDTO);
            }
        }

        return yesData;
    }

    public Set<TreeItemDTO> findMultipleSample() {
        final SearchSourceBuilder yesBuilder = new SearchSourceBuilder();
        yesBuilder.query(QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(fieldName(NodeEs::getType), NodeEsTypeEnum.project.name()))
                .filter(QueryBuilders.scriptQuery(multSapScript()))
        );
        yesBuilder.aggregation(initAggregation("multiSampleYes", NodeEs::getRelaSampleType));
        yesBuilder.size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
        final SearchResponse searchResponse = nodeEsMapper.search(EsWrappers.lambdaQuery(NodeEs.class).setSearchSourceBuilder(yesBuilder));
        // 聚合结果
        final Aggregations aggregations = searchResponse.getAggregations();
        final Map<String, Integer> statMap = parseTerms(aggregations.asList());

        final List<ExpSampleType> expSampleTypeList = expSampleTypeRepository.findAllByTypeWithoutAttr(SAMPLE_TYPE);
        final Map<String, TreeItemDTO> lvl1Map = new LinkedHashMap<>();
        final Map<String, List<TreeItemDTO>> childMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(expSampleTypeList)) {
            int startId = 0;
            for (final ExpSampleType expSampleType : expSampleTypeList) {
                final String name = expSampleType.getName();
                final Integer count = statMap.get(name.toLowerCase());
                final TreeItemDTO treeItemDTO = new TreeItemDTO(groupItemId(++startId, SAMPLE_TYPE), name);
                treeItemDTO.setNumber(count == null ? 0 : count);

                final String parentName = expSampleType.getParentName();
                if (StrUtil.isBlank(parentName)) {
                    lvl1Map.put(name, treeItemDTO);
                } else {
                    List<TreeItemDTO> treeItemDTOS = childMap.get(parentName);
                    if (treeItemDTOS == null) {
                        treeItemDTOS = new ArrayList<>();
                    }
                    treeItemDTOS.add(treeItemDTO);
                    childMap.put(parentName, treeItemDTOS);
                }
            }
        }

        final Set<TreeItemDTO> result = new LinkedHashSet<>();
        for (Map.Entry<String, TreeItemDTO> entry : lvl1Map.entrySet()) {
            final String name = entry.getKey();
            final TreeItemDTO itemDTO = entry.getValue();
            final List<TreeItemDTO> childDTOS = childMap.get(name);
            if (CollUtil.isEmpty(childDTOS) && itemDTO.getNumber() > 0) {
                result.add(itemDTO);
            } else {
                if (CollUtil.isNotEmpty(childDTOS)) {
                    final List<TreeItemDTO> collect = childDTOS.stream().filter(child -> child.getNumber() > 0).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        itemDTO.setData(collect);
                        result.add(itemDTO);
                    }
                }
            }
        }
        return result;
    }

    private void allMultipleOmics(final Set<String> names, final Set<String> ids, final TreeItemDTO groupInfo, Map<String, Integer> statMap, Iterator<TreeItemDTO> parIterator) {
        List<TreeItemDTO> children = groupInfo.getData();
        if (CollUtil.isEmpty(children)) {
            ids.add(groupInfo.getId());
            final String name = groupInfo.getName();
            names.add(name);
            Integer count = statMap.get(name.toLowerCase());
            count = count == null ? 0 : count;
            groupInfo.setNumber(count);
            if (parIterator != null && count == 0) {
                parIterator.remove();
            }
        } else {
            Iterator<TreeItemDTO> iterator = children.iterator();
            while (iterator.hasNext()) {
                TreeItemDTO child = iterator.next();
                allMultipleOmics(names, ids, child, statMap, iterator);
            }
        }
    }

    private String groupItemId(int id, String type) {
        final String idStr = StrUtil.padPre(String.valueOf(id), 4, "0");
        if (EXPERIMENT_TYPE.equals(type)) {
            return SequenceType.EXPERIMENT.getPrefix() + idStr;
        } else if (SAMPLE_TYPE.equals(type)) {
            return SequenceType.SAMPLE.getPrefix() + idStr;
        } else {
            return "GRP_" + idStr;
        }
    }

    private Map<String, Integer> parseTerms(final List<Aggregation> aggregations) {
        final Map<String, Integer> map = new HashMap<>();
        if (CollUtil.isNotEmpty(aggregations)) {
            final ParsedTerms terms = (ParsedTerms) aggregations.get(0);
            final List<? extends Terms.Bucket> buckets = terms.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                map.put(bucket.getKeyAsString().toLowerCase(), (int) bucket.getDocCount());
            }
        }
        return map;
    }

    public static TermsAggregationBuilder initAggregation(String key, SFunction<NodeEs, ?> column) {
        // 配置字段分组结果最大个数，若不配置，则默认值为10个
        return AggregationBuilders.terms(key).field(fieldName(column)).size(100);
    }

    public static String fieldName(SFunction<NodeEs, ?> column) {
        return FieldUtils.getFieldName(column);
    }

    /**
     * 根据项目编号集合，查询多组学项目数据
     */
    public List<FeatureDataPrjDTO> searchMultOmicByPrjNos(List<String> prjNos) {
        if (CollUtil.isEmpty(prjNos)) {
            return new ArrayList<>();
        }
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        queryWrapper.in(NodeEs::getTypeId, prjNos)
                .eq(NodeEs::getType, NodeEsTypeEnum.project.name())
                .eq(NodeEs::getMultiOmics, true);
        return NodeEsDTOMapper.INSTANCE.copyListToMulPrj(nodeEsMapper.selectList(queryWrapper));
    }


    /**
     * 查询多样本项目数据
     */
    public FdMultipleResDTO searchMultipleSample(final MultipleSampleQueryVO searchVO) {
        // 初始化左侧多样本数据
        final Set<TreeItemDTO> treeDataSet = findMultipleSample();

        final Set<String> sapTypes = new HashSet<>();
        final Set<String> checkedIds = new LinkedHashSet<>();
        if (!treeDataSet.isEmpty()) {
            final List<String> leftStatQueries = searchVO.getLeftStatQueries();
            if (CollUtil.isNotEmpty(leftStatQueries)) {
                // 左侧复选框筛选
                final Map<String, String> nameIdMap = new HashMap<>();
                initTreeNameIdMap(treeDataSet, nameIdMap);

                for (String name : leftStatQueries) {
                    if (nameIdMap.containsKey(name)) {
                        sapTypes.add(name);
                        checkedIds.add(nameIdMap.get(name));
                    }
                }
            }
        }

        final FdMultipleResDTO result = new FdMultipleResDTO();
        result.setEsTreeItems(TreeItemDTOMapper.INSTANCE.copyListToEs(new ArrayList<>(treeDataSet)));
        if (!sapTypes.isEmpty()) {
            final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
            queryWrapper.eq(NodeEs::getType, NodeEsTypeEnum.project.name());
            for (String sapType : sapTypes) {
                // 树复选框之前为and关系
                queryWrapper.in(NodeEs::getRelaSampleType, sapType);
            }
            // 添加查询条件
            final String typeId = searchVO.getProjID();
            if (typeId != null) {
                queryWrapper.like(NodeEs::getTypeId, ReUtil.escape(typeId));
            }
            final String name = searchVO.getProjName();
            if (name != null) {
                queryWrapper.like(NodeEs::getName, ReUtil.escape(name));
            }
            final String sapType = searchVO.getSapType();
            if (sapType != null) {
                queryWrapper.like(NodeEs::getRelaSampleType, ReUtil.escape(sapType));
            }

            final Pageable pageable = searchVO.initPageInfo();
            // Easy es分页页码从1开始
            final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(addMultSapFilter(queryWrapper, pageable.getPageSize()), pageable.getPageNumber() + 1, pageable.getPageSize());

            final List<NodeEs> list = pageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                final EsPageInfo<FeatureDataPrjDTO> info = EsPageInfo.of(NodeEsDTOMapper.INSTANCE.copyListToMulPrj(list));
                info.setTotal(pageInfo.getTotal());
                result.setPageInfo(info);
            }
            result.setDefaultCheckedKeys(new ArrayList<>(checkedIds));
        }
        return result;
    }

    /**
     * 根据项目编号集合，查询多样本项目数据
     */
    public List<FeatureDataPrjDTO> searchMultSampleByPrjNos(List<String> prjNos) {
        if (CollUtil.isEmpty(prjNos)) {
            return new ArrayList<>();
        }

        // 使用混合查询,查询多样本
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        queryWrapper.in(NodeEs::getTypeId, prjNos)
                .eq(NodeEs::getType, NodeEsTypeEnum.project.name());

        return NodeEsDTOMapper.INSTANCE.copyListToMulPrj(nodeEsMapper.selectList(addMultSapFilter(queryWrapper, prjNos.size())));
    }

    /**
     * 使用混合查询，添加多样本筛选添加脚本
     */
    private LambdaEsQueryWrapper<NodeEs> addMultSapFilter(final LambdaEsQueryWrapper<NodeEs> originalWrapper, int size) {
        final QueryBuilder queryBuilder = nodeEsMapper.getSearchSourceBuilder(originalWrapper).query();

        // 添加filter脚本，判断是否为多样本
        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(
                QueryBuilders.boolQuery().must(queryBuilder).filter(QueryBuilders.scriptQuery(multSapScript()))
        ).size(size);

        final LambdaEsQueryWrapper<NodeEs> finalWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        finalWrapper.setSearchSourceBuilder(searchSourceBuilder);
        return finalWrapper;
    }

    /**
     * 多样本筛选脚本
     */
    private static Script multSapScript() {
        return new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG,
                "doc['relaSampleType'].length > 1", Collections.emptyMap());
    }

    /**
     * 查询单样本多组学样本数据
     */
    public FdMultipleResDTO searchSingleSap(final SingleSapQueryVO searchVO) {
        // 初始化左侧多组学数据
        final Set<TreeItemDTO> multipleOmics = findMultipleOmics(true);

        final Set<String> expTypes = new HashSet<>();
        final Set<String> checkedIds = new LinkedHashSet<>();
        if (!multipleOmics.isEmpty()) {
            final List<String> leftStatQueries = searchVO.getLeftStatQueries();
            if (CollUtil.isNotEmpty(leftStatQueries)) {
                // 左侧复选框筛选
                final Map<String, String> nameIdMap = new HashMap<>();
                initTreeNameIdMap(multipleOmics, nameIdMap);

                for (String name : leftStatQueries) {
                    if (nameIdMap.containsKey(name)) {
                        expTypes.add(name);
                        checkedIds.add(nameIdMap.get(name));
                    }
                }
            }
        }

        final FdMultipleResDTO result = new FdMultipleResDTO();
        result.setEsTreeItems(TreeItemDTOMapper.INSTANCE.copyListToEs(new ArrayList<>(multipleOmics)));
        if (!expTypes.isEmpty()) {
            final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
            queryWrapper.eq(NodeEs::getType, NodeEsTypeEnum.sample.name());
            for (String expType : expTypes) {
                // 树复选框之前为and关系
                queryWrapper.in(NodeEs::getRelaExpType, expType);
            }
            // 添加查询条件
            final String typeId = searchVO.getSapID();
            if (typeId != null) {
                queryWrapper.like(NodeEs::getTypeId, ReUtil.escape(typeId));
            }

            final String sapType = searchVO.getSapType();
            if (sapType != null) {
                queryWrapper.like(NodeEs::getSampleType, ReUtil.escape(sapType));
            }

            final String name = searchVO.getSapName();
            if (name != null) {
                queryWrapper.like(NodeEs::getName, ReUtil.escape(name));
            }
            final String expType = searchVO.getExpType();
            if (expType != null) {
                queryWrapper.like(NodeEs::getRelaExpType, ReUtil.escape(expType));
            }

            final Pageable pageable = searchVO.initPageInfo();
            // Easy es分页页码从1开始
            final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(addMultExpFilter(queryWrapper, pageable.getPageSize()), pageable.getPageNumber() + 1, pageable.getPageSize());

            final List<NodeEs> list = pageInfo.getList();
            if (CollUtil.isNotEmpty(list)) {
                final EsPageInfo<FeatureDataPrjDTO> info = EsPageInfo.of(NodeEsDTOMapper.INSTANCE.copyListToMulPrj(list));
                info.setTotal(pageInfo.getTotal());
                result.setPageInfo(info);
            }
            result.setDefaultCheckedKeys(new ArrayList<>(checkedIds));
        }
        return result;
    }

    /**
     * 根据项目编号集合，查询多组学项目数据
     */
    public List<FeatureDataPrjDTO> searchSingleSapcByNos(List<String> nos) {
        if (CollUtil.isEmpty(nos)) {
            return new ArrayList<>();
        }
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        queryWrapper.in(NodeEs::getTypeId, nos)
                .eq(NodeEs::getType, NodeEsTypeEnum.sample.name());
        final List<NodeEs> nodeEs = nodeEsMapper.selectList(addMultExpFilter(queryWrapper, nos.size()));
        return NodeEsDTOMapper.INSTANCE.copyListToMulPrj(nodeEs);
    }

    /**
     * 使用混合查询，添加多组学筛选添加脚本
     */
    private LambdaEsQueryWrapper<NodeEs> addMultExpFilter(final LambdaEsQueryWrapper<NodeEs> originalWrapper, int size) {
        final QueryBuilder queryBuilder = nodeEsMapper.getSearchSourceBuilder(originalWrapper).query();

        // 添加filter脚本，判断是否为多样本
        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(
                QueryBuilders.boolQuery().must(queryBuilder).filter(QueryBuilders.scriptQuery(multExpScript()))
        ).size(size);

        final LambdaEsQueryWrapper<NodeEs> finalWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        finalWrapper.setSearchSourceBuilder(searchSourceBuilder);
        return finalWrapper;
    }

    /**
     * 多组学筛选脚本
     */
    private static Script multExpScript() {
        return new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG,
                "doc['relaExpType'].length > 1", Collections.emptyMap());
    }


    /**
     * 特殊数据集 首页组学统计数据
     */
    public Map<String, StatDTO> expStatInfo() {
        final List<ExpSampleTypeGroup> expGroups = expSampleTypeGroupRepository.findAllByTypeOrderBySortAsc(EXPERIMENT_TYPE);
        final Map<String, StatDTO> result = new HashMap<>();
        if (CollUtil.isNotEmpty(expGroups)) {
            for (ExpSampleTypeGroup expGroup : expGroups) {
                final TreeItemDTO groupInfo = expGroup.getGroupInfo();
                if (groupInfo != null) {
                    // exp类型名称
                    final String fieldName = groupInfo.getFieldName();
                    if ("Electron microscopy".equalsIgnoreCase(fieldName)) {
                        continue;
                    }
                    final List<TreeItemDTO> children = groupInfo.getData();
                    final List<String> types = new ArrayList<>();
                    if (CollUtil.isNotEmpty(children)) {
                        for (TreeItemDTO item : children) {
                            types.add(item.getFieldName());
                        }
                    } else {
                        types.add(fieldName);
                    }
                    final Sort idSort = Sort.by("_id").ascending();
                    int typeTotalCount = 0;
                    long typeTotalSize = 0;
                    for (int i = 0; i < Integer.MAX_VALUE; i++) {
                        final List<String> expNos = experimentRepository.findNosByTypes(types, PageRequest.of(i, 3000, idSort));
                        final int size = CollUtil.size(expNos);
                        if (size == 0) {
                            break;
                        }
                        typeTotalCount += size;

                        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
                        wrapper.in(NodeRelatedEs::getExpNo, expNos);
                        wrapper.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
                        // 文件大小求和
                        final SFunction<NodeRelatedEs, ?> fileSizeFiled = NodeRelatedEs::getFileSize;
                        wrapper.sum(fileSizeFiled);
                        final SearchResponse response = nodeRelatedEsMapper.search(wrapper);
                        final Aggregation aggregation = response.getAggregations().asMap().get(FieldUtils.getFieldName(fileSizeFiled) + "Sum");
                        if (aggregation instanceof ParsedSum) {
                            final ParsedSum parsedSum = (ParsedSum) aggregation;
                            typeTotalSize += ((long) parsedSum.getValue());
                        }
                    }

                    final StatDTO dto = new StatDTO();
                    dto.setExpCount(typeTotalCount);
                    dto.setDataSize(typeTotalSize);
                    dto.setDataSizeStr(FileUtil.readableFileSize(typeTotalSize));
                    result.put(fieldName, dto);
                }
            }
        }
        return result;
    }

    /**
     * 根据项目编号，查询样本和对应组学类型
     */
    public FdSampleDTO findSapNoAndExpTypesByPrjNos(final FdQueryDTO search) {
        final Set<String> prjNos = search.getPrjNos();
        final FdSampleDTO fdSampleDTO = new FdSampleDTO();
        if (CollUtil.isEmpty(prjNos)) {
            return fdSampleDTO;
        }
        final LambdaEsQueryWrapper<NodeEs> wrapper = EsWrappers.lambdaQuery(NodeEs.class);
        wrapper.in(NodeEs::getRelaTypeIds, prjNos).eq(NodeEs::getType, NodeEsTypeEnum.sample.name());
        wrapper.select(NodeEs::getTypeId, NodeEs::getRelaExpType);

        final Pageable pageable = search.initPageInfo();
        // 添加排序条件
        final Sort sort = pageable.getSort();
        if (!sort.isEmpty()) {
            final Sort.Order order = sort.iterator().next();
            final String property = order.getProperty();
            wrapper.orderBy(true, order.isAscending(), property);
        }

        final EsPageInfo<NodeEs> pageInfo = nodeEsMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, pageable.getPageSize());

        final LinkedHashMap<String, Set<String>> map = new LinkedHashMap<>();
        final List<NodeEs> list = pageInfo.getList();
        if (CollUtil.isNotEmpty(list)) {
            for (NodeEs es : list) {
                final String id = es.getTypeId();
                Set<String> set = map.get(id);
                if (set == null) {
                    set = new LinkedHashSet<>();
                }
                final LinkedHashSet<String> relaExpType = es.getRelaExpType();
                if (CollUtil.isNotEmpty(relaExpType)) {
                    set.addAll(relaExpType);
                }
                map.put(id, set);
            }
        }
        fdSampleDTO.setTotal(pageInfo.getTotal());
        fdSampleDTO.setSapExpTypes(map);
        return fdSampleDTO;
    }

    public List<FieldCountDTO> countByTypeAndField(final ExpSapSearchVO searchVO) {
        final String projNo = searchVO.getProjNo();
        final String currMemberId = searchVO.getCurrMemberId();

        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = new LambdaEsQueryWrapper<>();
        // 根据登录信息查询对应权限
        dataService.addAuthWrapper(currMemberId, wrapper, AuthorizeType.project, projNo, searchVO.getCurrMemberEmail());
        final SearchSourceBuilder searchSourceBuilder = nodeRelatedEsMapper.getSearchSourceBuilder(wrapper);

        final String field = searchVO.getField();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .must(searchSourceBuilder.query())
                .must(QueryBuilders.termQuery("projNo", projNo))
                .must(QueryBuilders.existsQuery(field))
        );

//        wrapper.eq(NodeRelatedEs::getProjNo, projNo);
//        wrapper.exists(field);

        final int aggSize = 100;

        // 聚合统计时size设置为0
        searchSourceBuilder.size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
        searchSourceBuilder.aggregation(AggregationBuilders.terms(field + "Agg").field(field).size(aggSize));
        wrapper.setSearchSourceBuilder(searchSourceBuilder);

        final SearchResponse searchResponse = nodeRelatedEsMapper.search(wrapper);
        final Aggregations aggregations = searchResponse.getAggregations();
        final List<FieldCountDTO> result = new ArrayList<>();
        if (aggregations == null) {
            return result;
        }
        final List<Aggregation> list = aggregations.asList();
        for (final Aggregation aggregation : list) {
            final ParsedTerms terms = (ParsedTerms) aggregation;
            final List<? extends Terms.Bucket> buckets = terms.getBuckets();
            for (final Terms.Bucket bucket : buckets) {
                final FieldCountDTO dto = new FieldCountDTO();
                dto.setFieldName(bucket.getKeyAsString());
                dto.setCount(bucket.getDocCount());
                result.add(dto);
            }
        }
        return result;
    }

    public List<FieldCountDTO> countByTypeAndField4Browse(String id, String type, String field) {
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = new LambdaEsQueryWrapper<>();
        final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        final int aggSize = 100;
        final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("type", type))
                .must(QueryBuilders.termsQuery("relaTypeIds", id));
        /*searchId = StrUtil.trimToNull(searchId);
        if (searchId != null) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("typeId", BaseEsConstants.WILDCARD_SIGN + ReUtil.escape(searchId) + BaseEsConstants.WILDCARD_SIGN));
        }*/
        // 聚合统计时size设置为0
        searchSourceBuilder.query(boolQueryBuilder).size(0).trackTotalHitsUpTo(Integer.MAX_VALUE);
        searchSourceBuilder.aggregation(AggregationBuilders.terms(field + "Agg").field(field).size(aggSize));
        queryWrapper.setSearchSourceBuilder(searchSourceBuilder);

        final SearchResponse searchResponse = nodeEsMapper.search(queryWrapper);
        final Aggregations aggregations = searchResponse.getAggregations();
        final List<FieldCountDTO> result = new ArrayList<>();
        if (aggregations == null) {
            return result;
        }
        final List<Aggregation> list = aggregations.asList();
        for (final Aggregation aggregation : list) {
            final ParsedTerms terms = (ParsedTerms) aggregation;
            final List<? extends Terms.Bucket> buckets = terms.getBuckets();
            for (final Terms.Bucket bucket : buckets) {
                final FieldCountDTO dto = new FieldCountDTO();
                dto.setFieldName(bucket.getKeyAsString());
                dto.setCount(bucket.getDocCount());
                result.add(dto);
            }
        }
        return result;
    }

    public ProjSapNosDTO searchAllSapNoByPrjId(final String prjNo) {

        final ProjSapNosDTO nosDTO = new ProjSapNosDTO();
        if (StrUtil.isBlank(prjNo)) {
            return nosDTO;
        }

        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        wrapper.eq(NodeRelatedEs::getProjNo, prjNo)
                .exists(NodeRelatedEs::getSapNo)
                .in(NodeRelatedEs::getSecurity, SecurityEnum.includeAllSecurity());
        wrapper.select(NodeRelatedEs::getSapNo, NodeRelatedEs::getSecurity);

        Set<String> privateNos = nosDTO.getPrivateNos();
        if (privateNos == null) {
            privateNos = new HashSet<>();
        }

        Set<String> publicOrRestrictedNos = nosDTO.getPublicOrRestrictedNos();
        if (publicOrRestrictedNos == null) {
            publicOrRestrictedNos = new HashSet<>();
        }

        for (int i = 0; i < NodeRelatedEs.NODE_RELATED_MAX_COUNT; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 6000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            for (NodeRelatedEs es : list) {
                if (SecurityEnum._private.getDesc().equals(es.getSecurity())) {
                    privateNos.add(es.getSapNo());
                } else {
                    publicOrRestrictedNos.add(es.getSapNo());
                }
            }
        }

        nosDTO.setPrivateNos(privateNos);
        nosDTO.setPublicOrRestrictedNos(publicOrRestrictedNos);
        return nosDTO;
    }

    public Map<String, NodeEsTypeDTO> searchTypeData(final List<String> typeIds, final String type) {
        final Map<String, NodeEsTypeDTO> data = new LinkedHashMap<>();
        if (CollUtil.isEmpty(typeIds)) {
            return data;
        }
        final Optional<NodeEsTypeEnum> typeOpt = NodeEsTypeEnum.findByName(type);
        if (!typeOpt.isPresent()) {
            return data;
        }

        final NodeEsTypeEnum nodeEsTypeEnum = typeOpt.get();
        final LambdaEsQueryWrapper<NodeEs> queryWrapper = EsWrappers.lambdaQuery(NodeEs.class);
        queryWrapper.eq(NodeEs::getType, nodeEsTypeEnum.name()).in(NodeEs::getTypeId, typeIds);
        queryWrapper.select(NodeEs::getName, NodeEs::getTypeId, NodeEs::getRelaExpType, NodeEs::getRelaSampleType, NodeEs::getRelaAnalysisType);

        final List<NodeEs> nodeEs = nodeEsMapper.selectList(queryWrapper);

        if (CollUtil.isNotEmpty(nodeEs)) {
            for (final NodeEs item : nodeEs) {
                final NodeEsTypeDTO typeDTO = new NodeEsTypeDTO();
                typeDTO.setName(item.getName());

                final Map<String, LinkedHashSet<String>> dto = new HashMap<>();
                dto.put("relaExpType", item.getRelaExpType());
                dto.put("relaSampleType", item.getRelaSampleType());
                dto.put("relaAnalysisType", item.getRelaAnalysisType());
                typeDTO.setRelaTypeMap(dto);

                data.put(item.getTypeId(), typeDTO);
            }
        }
        return data;
    }

    private List<List<String>> subPage(final List<String> nos) {
        final int totalNoSize = CollUtil.size(nos);
        if (totalNoSize == 0) {
            return new ArrayList<>();
        }
        // Terms Query request has exceeded the allowed maximum of [65536]
        // in查询条数最多不能超过65536
        final int pageSize = 60000;
        int totalPage = PageUtil.totalPage(totalNoSize, pageSize);
        final List<List<String>> noList = new ArrayList<>();
        for (int page = 0; page < totalPage; page++) {
            noList.add(new ArrayList<>(nos.subList(page * pageSize, Math.min((page + 1) * pageSize, totalNoSize))));
        }
        return noList;
    }

    public long findAllSizeByNosAndType(final FileSizeSearchDTO searchDTO) {
        final List<String> nos = new ArrayList<>(searchDTO.getNos());
        searchDTO.getNos().clear();
        final String type = searchDTO.getType();
        final int totalNoSize = CollUtil.size(nos);
        if (totalNoSize == 0 || type == null) {
            return 0L;
        }

        final Optional<AuthorizeType> typeOptional = AuthorizeType.findByName(type);
        if (!typeOptional.isPresent()) {
            return 0L;
        }

        // in查询条数最多不能超过65536
        final List<List<String>> noList = subPage(nos);
        nos.clear();

        long totalSize = 0;
        final AuthorizeType authorizeType = typeOptional.get();
        for (List<String> noOfPage : noList) {
            final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
            switch (authorizeType) {
                case experiment:
                    wrapper.in(NodeRelatedEs::getExpNo, noOfPage);
                    break;
                case sample:
                    wrapper.in(NodeRelatedEs::getSapNo, noOfPage);
                    break;
                default:
                    throw new ServiceException("Type not found");
            }
            // 统计文件大小集合
            wrapper.sum(NodeRelatedEs::getFileSize);

            final SearchResponse searchResponse = nodeRelatedEsMapper.search(wrapper);
            final List<Aggregation> list = searchResponse.getAggregations().asList();

            if (CollUtil.isNotEmpty(list)) {
                for (Aggregation aggregation : list) {
                    if (aggregation instanceof ParsedSum) {
                        final ParsedSum sum = (ParsedSum) aggregation;
                        totalSize += ((long) sum.getValue());
                    }
                }
            }
        }

        return totalSize;
    }

    /**
     * 获取样本-实验组合统计数据
     */
    public SampleExpStatDTO statExpOfSap(final String sapType, final String expType) {
        final SampleExpStatDTO dto = new SampleExpStatDTO();
        if (StrUtil.isBlank(sapType) || StrUtil.isBlank(expType)) {
            return dto;
        }
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        wrapper.eq(NodeRelatedEs::getSapType, sapType);
        wrapper.eq(NodeRelatedEs::getExpType, expType);
        wrapper.exists(NodeRelatedEs::getExpNo).exists(NodeRelatedEs::getDatNo).exists(NodeRelatedEs::getFileSize);

        wrapper.select(NodeRelatedEs::getExpNo, NodeRelatedEs::getFileSize, NodeRelatedEs::getSecurity);
        /*if (StrUtil.isNotBlank(security)) {
            wrapper.eq(NodeRelatedEs::getSecurity, security);
            wrapper.select(NodeRelatedEs::getExpNo, NodeRelatedEs::getFileSize, NodeRelatedEs::getSecurity);
        } else {
            wrapper.select(NodeRelatedEs::getExpNo, NodeRelatedEs::getFileSize);
        }*/

        final Set<String> setNos = new HashSet<>();
        long expTotalFileSize = 0;
        long expPrivateFileSize = 0;
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 8000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            for (NodeRelatedEs es : list) {
                final String expNo = es.getExpNo();
                setNos.add(expNo);

                long fileSize = longVal(es.getFileSize());

                expTotalFileSize += fileSize;

                final String security = es.getSecurity();
                if (SecurityEnum._private.getDesc().equals(security)) {
                    expPrivateFileSize += fileSize;
                }
            }
        }

        dto.setExpTotal(CollUtil.size(setNos));
        dto.setExpTotalFileSize(expTotalFileSize);
        dto.setExpPrivateFileSize(expPrivateFileSize);
        return dto;
    }

    private long longVal(Long val) {
        return val == null ? 0L : val;
    }

    /**
     * 获取多组学项目统计数据
     */
    public MultiExpStatDTO statMultiExp(final boolean searchSample, final String type) {
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);

        final SFunction<NodeRelatedEs, String> noField = searchSample ? (NodeRelatedEs::getSapNo) : (NodeRelatedEs::getProjNo);
        wrapper.exists(noField)
                .exists(NodeRelatedEs::getExpType)
                .exists(NodeRelatedEs::getDatNo)
                .exists(NodeRelatedEs::getFileSize);

        if (StrUtil.isNotBlank(type)) {
            if (searchSample) {
                wrapper.eq(NodeRelatedEs::getSapType, type);
            }
        }

        wrapper.select(noField, NodeRelatedEs::getProjVisible, NodeRelatedEs::getExpType, NodeRelatedEs::getFileSize);


        final Map<String, Object> statMap = new HashMap<>();

        final int expTypeIndex = 0;
        final int fileSizeIndex = 1;
        final int projVisibleIndex = 2;
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 60000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }

            for (NodeRelatedEs es : list) {
                final String no = noField.apply(es);
                List prjList = (List) statMap.get(no);
                if (prjList == null) {
                    prjList = new ArrayList();
                    prjList.add(new HashSet<String>());
                    prjList.add(0L);
                    prjList.add(null);
                }

                final String expType = es.getExpType();
                final long fileSize = longVal(es.getFileSize());
                prjList.set(expTypeIndex, CollUtil.addAll(((Set<String>) prjList.get(expTypeIndex)), expType));
                prjList.set(fileSizeIndex, ((Long) prjList.get(fileSizeIndex)) + fileSize);

                final String projVisible = es.getProjVisible();
                prjList.set(projVisibleIndex, projVisible);

                statMap.put(no, prjList);
            }
        }

        final MultiExpStatDTO dto = new MultiExpStatDTO();
        final Map<String, Integer> expMultStatMap = new HashMap<>();
        final Map<String, Integer> expSingleStatMap = new HashMap<>();
        for (Object val : statMap.values()) {
            final List value = (List) val;
            final Set<String> set = (Set<String>) value.get(expTypeIndex);
            final int expTypeSize = CollUtil.size(set);
            if (expTypeSize > 1) {
                // 多组学
                dto.setMulti(dto.getMulti() + 1);

                for (String expType : set) {
                    Integer count = expMultStatMap.get(expType);
                    if (count == null) {
                        count = 0;
                    }
                    expMultStatMap.put(expType, count + 1);
                }

                final long fileSize = longVal((Long) value.get(fileSizeIndex));
                dto.setMultiFileSize(dto.getMultiFileSize() + fileSize);

                if (VisibleStatusEnum.Accessible.name().equals(value.get(projVisibleIndex))) {
                    dto.setAccessibleMulti(dto.getAccessibleMulti() + 1);
                    dto.setAccessibleMultiFileSize(dto.getAccessibleMultiFileSize() + fileSize);
                }
            } else {
                // 单组学
                dto.setSingle(dto.getSingle() + 1);

                for (String expType : set) {
                    Integer count = expSingleStatMap.get(expType);
                    if (count == null) {
                        count = 0;
                    }
                    expSingleStatMap.put(expType, count + 1);
                }
            }
        }

        dto.setExpMultStatMap(expMultStatMap);
        dto.setExpSingleStatMap(expSingleStatMap);

        return dto;
    }

    public Map<String, BrowseStatResVO> findBrowseStatNum(BrowseStatEsDTO dto) {
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);

        final Set<String> prjNoSet = dto.getPrjNoSet();
        final Set<String> expNoSet = dto.getExpNoSet();
        final Set<String> sapNoSet = dto.getSapNoSet();
        final Set<String> anaNoSet = dto.getAnaNoSet();

        wrapper.not().eq(NodeRelatedEs::getSecurity, SecurityEnum._private.getDesc()).and(i -> {
            if (CollUtil.isNotEmpty(prjNoSet)) {
                i.or().in(NodeRelatedEs::getProjNo, prjNoSet);
            }

            if (CollUtil.isNotEmpty(expNoSet)) {
                i.or().in(NodeRelatedEs::getExpNo, expNoSet);
            }

            if (CollUtil.isNotEmpty(sapNoSet)) {
                i.or().in(NodeRelatedEs::getSapNo, sapNoSet);
            }

            if (CollUtil.isNotEmpty(anaNoSet)) {
                i.or().in(NodeRelatedEs::getAnalNo, anaNoSet);
            }
        });

        final String acc = VisibleStatusEnum.Accessible.name();

        Map<String, BrowseStatEsDTO> map = new HashMap<>();
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final EsPageInfo<NodeRelatedEs> pageInfo = nodeRelatedEsMapper.pageQuery(wrapper, i + 1, 20000);
            final List<NodeRelatedEs> list = pageInfo.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            for (NodeRelatedEs es : list) {
                final String projNo = es.getProjNo();

                final String expNo = es.getExpNo();
                final String expType = es.getExpType();
                final boolean expVisible = acc.equals(es.getExpVisible());

                final String sapNo = es.getSapNo();
                final String sapType = es.getSapType();
                final boolean sapVisible = acc.equals(es.getSapVisible());

                final String analNo = es.getAnalNo();

                final String datNo = es.getDatNo();

                if (prjNoSet.contains(projNo)) {
                    BrowseStatEsDTO dtoStat = map.get(projNo);

                    if (dtoStat == null) {
                        dtoStat = new BrowseStatEsDTO();
                    }
                    if (expVisible) {
                        addIntoSet(dtoStat.getExpNoSet(), expNo);
                        addIntoSet(dtoStat.getExpTypes(), expType);
                    }
                    if (sapVisible) {
                        addIntoSet(dtoStat.getSapNoSet(), sapNo);
                        addIntoSet(dtoStat.getSapTypes(), sapType);
                    }
                    addIntoSet(dtoStat.getDataNoSet(), datNo);

                    map.put(projNo, dtoStat);
                }

                if (expNoSet.contains(expNo)) {
                    BrowseStatEsDTO dtoStat = map.get(expNo);

                    if (dtoStat == null) {
                        dtoStat = new BrowseStatEsDTO();
                    }
                    if (sapVisible) {
                        addIntoSet(dtoStat.getSapNoSet(), sapNo);
                        addIntoSet(dtoStat.getSapTypes(), sapType);
                    }
                    addIntoSet(dtoStat.getDataNoSet(), datNo);

                    map.put(expNo, dtoStat);
                }

                if (sapNoSet.contains(sapNo)) {
                    BrowseStatEsDTO dtoStat = map.get(sapNo);

                    if (dtoStat == null) {
                        dtoStat = new BrowseStatEsDTO();
                    }
                    if (expVisible) {
                        addIntoSet(dtoStat.getExpNoSet(), expNo);
                        addIntoSet(dtoStat.getExpTypes(), expType);
                    }
                    addIntoSet(dtoStat.getDataNoSet(), datNo);

                    map.put(sapNo, dtoStat);
                }

                if (anaNoSet.contains(analNo)) {
                    BrowseStatEsDTO dtoStat = map.get(analNo);

                    if (dtoStat == null) {
                        dtoStat = new BrowseStatEsDTO();
                    }
                    addIntoSet(dtoStat.getDataNoSet(), datNo);

                    map.put(analNo, dtoStat);
                }
            }
        }

        final Map<String, BrowseStatResVO> result = new HashMap<>();
        for (Map.Entry<String, BrowseStatEsDTO> entry : map.entrySet()) {
            final BrowseStatEsDTO value = entry.getValue();

            final BrowseStatResVO vo = new BrowseStatResVO();
            vo.setExpNum(value.getExpNoSet().size());
            vo.setExpTypes(value.getExpTypes());

            vo.setSapNum(value.getSapNoSet().size());
            vo.setSapTypes(value.getSapTypes());

            vo.setFileNum(value.getDataNoSet().size());

            result.put(entry.getKey(), vo);
        }
        return result;
    }

    private <T> void addIntoSet(Set<T> set, T val) {
        if (val != null) {
            set.add(val);
        }
    }

}
