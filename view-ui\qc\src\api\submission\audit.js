import request from '@/utils/request';

// 查询列表
export function listSubmission(query) {
  return request({
    url: '/qc/submission/list',
    method: 'get',
    params: query,
  });
}

// 查看提交详情
export function getDetailData(subNo) {
  return request({
    url: `/upload/metadata/submission/detail/${subNo}`,
    timeout: 0,
    method: 'get',
  });
}

export function retryFastQCTask(params) {
  return request({
    url: `/system/fastqcTask/retry`,
    method: 'get',
    params: params,
  });
}

export function retrySamToolTask(params) {
  return request({
    url: `/system/samToolTask/retry`,
    method: 'get',
    params: params,
  });
}

export function getDataQcDetail(params) {
  return request({
    url: `/qc/submission/getDataQCInfoList`,
    timeout: 0,
    method: 'get',
    params: params,
  });
}

export function getSamToolQcDetail(params) {
  return request({
    url: `/qc/submission/getSamToolQcList`,
    timeout: 0,
    method: 'get',
    params: params,
  });
}

export function getQcStatusStat(subNo) {
  return request({
    url: `/qc/submission/getQcStatusStat?subNo=${subNo}`,
    timeout: 0,
    method: 'get',
  });
}

export function getSamToolQcStatusStat(subNo) {
  return request({
    url: `/qc/submission/getSamToolQcStatusStat?subNo=${subNo}`,
    timeout: 0,
    method: 'get',
  });
}

// 开始审核
export function startReview(subNo) {
  return request({
    url: `/qc/submission/startReview/${subNo}`,
    timeout: 0,
    method: 'post',
  });
}

// 审核通过
export function pass(subNo) {
  return request({
    url: `/qc/submission/pass/${subNo}`,
    timeout: 0,
    method: 'post',
  });
}

// 驳回提交
export function rejectSubmission(data) {
  return request({
    url: `/qc/submission/reject`,
    method: 'post',
    data: data,
  });
}

// 日志列表
export function listLog(query) {
  return request({
    url: `/qc/log/list`,
    method: 'get',
    params: query,
  });
}
