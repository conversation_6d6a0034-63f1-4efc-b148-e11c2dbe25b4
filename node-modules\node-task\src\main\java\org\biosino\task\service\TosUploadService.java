package org.biosino.task.service;

import cn.hutool.core.util.StrUtil;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;
import com.volcengine.tos.model.object.UploadFileV2Input;
import com.volcengine.tos.model.object.UploadFileV2Output;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * TOS 上传服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TosUploadService {

    @Value("${tos.enabled:false}")
    private Boolean tosEnabled;

    @Value("${tos.endpoint:}")
    private String tosEndpoint;

    @Value("${tos.region:}")
    private String tosRegion;

    @Value("${tos.access-key:}")
    private String tosAccessKey;

    @Value("${tos.secret-key:}")
    private String tosSecretKey;

    @Value("${tos.bucket-name:}")
    private String tosBucketName;

    @Value("${tos.task-num:5}")
    private Integer tosTaskNum;

    @Value("${tos.part-size:20971520}")
    private Long tosPartSize;

    @Value("${tos.enable-checkpoint:true}")
    private Boolean tosEnableCheckpoint;

    /**
     * 检查 TOS 配置是否有效
     */
    public boolean isValidTosConfig() {
        return tosEnabled
                && StrUtil.isNotBlank(tosEndpoint)
                && StrUtil.isNotBlank(tosRegion)
                && StrUtil.isNotBlank(tosAccessKey)
                && StrUtil.isNotBlank(tosSecretKey)
                && StrUtil.isNotBlank(tosBucketName);
    }

    /**
     * 上传文件到 TOS
     *
     * @param sourceFile 源文件
     * @param objectKey  对象键（TOS 中的文件路径）
     * @return 上传是否成功
     */
    public boolean uploadFile(File sourceFile, String objectKey) {
        if (!isValidTosConfig()) {
            log.warn("TOS upload is disabled or configuration is invalid");
            return false;
        }

        TOSV2 tos = null;
        try {
            // 创建 TOS 客户端，使用简化的构建方式
            tos = new TOSV2ClientBuilder().build(tosRegion, tosEndpoint, tosAccessKey, tosSecretKey);

            // 构建上传请求，支持分片上传、并发上传和断点续传
            UploadFileV2Input input = new UploadFileV2Input()
                    .setBucket(tosBucketName)
                    .setKey(objectKey)
                    .setFilePath(sourceFile.getAbsolutePath())
                    .setEnableCheckpoint(tosEnableCheckpoint)
                    .setPartSize(tosPartSize)
                    .setTaskNum(tosTaskNum);

            // 执行上传
            UploadFileV2Output output = tos.uploadFile(input);

            log.info("TOS upload successful: bucket={}, key={}, etag={}, crc64={}",
                    tosBucketName, objectKey, output.getEtag(), output.getHashCrc64ecma());

            return true;

        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            log.error("TOS upload failed - Client Exception: bucket={}, key={}, message={}",
                    tosBucketName, objectKey, e.getMessage());
            if (e.getCause() != null) {
                log.error("TOS upload client exception cause:", e.getCause());
            }
            return false;
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            log.error("TOS upload failed - Server Exception: bucket={}, key={}, statusCode={}, code={}, message={}, requestID={}",
                    tosBucketName, objectKey, e.getStatusCode(), e.getCode(), e.getMessage(), e.getRequestID());
            return false;
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            log.error("TOS upload failed - Unexpected Exception: bucket={}, key={}, message={}",
                    tosBucketName, objectKey, t.getMessage(), t);
            return false;
        }
        // 注意：TOSV2 客户端不需要显式关闭
    }


}
