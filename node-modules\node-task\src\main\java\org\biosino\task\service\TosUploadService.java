package org.biosino.task.service;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TOSClientConfiguration;
import com.volcengine.tos.auth.StaticCredentials;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * TOS 上传服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TosUploadService {

    /**
     * 上传文件到 TOS
     *
     * @param sourceFile 源文件
     * @param endpoint   TOS 端点
     * @param region     TOS 区域
     * @param accessKey  访问密钥 ID
     * @param secretKey  访问密钥
     * @param bucketName 存储桶名称
     * @return 上传是否成功
     */
    public boolean uploadFile(File sourceFile, String endpoint, String region,
                             String accessKey, String secretKey, String bucketName) {
        TOSV2 tos = null;
        FileInputStream fileInputStream = null;

        try {
            // 创建 TOS 客户端配置
            TOSClientConfiguration config = TOSClientConfiguration.builder()
                    .region(region)
                    .endpoint(endpoint)
                    .credentials(new StaticCredentials(accessKey, secretKey))
                    .build();

            // 创建 TOS 客户端
            tos = new TOSV2ClientBuilder().build(config);

            // 创建文件输入流
            fileInputStream = new FileInputStream(sourceFile);

            // 生成对象键（TOS 中的文件路径）
            String objectKey = generateObjectKey(sourceFile);

            // 构建上传请求
            PutObjectInput putObjectInput = PutObjectInput.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .content(fileInputStream)
                    .contentLength(sourceFile.length())
                    .build();

            // 执行上传
            PutObjectOutput putObjectOutput = tos.putObject(putObjectInput);

            log.info("TOS upload successful: bucket={}, key={}, etag={}",
                    bucketName, objectKey, putObjectOutput.getEtag());

            return true;

        } catch (Exception e) {
            log.error("TOS upload failed: bucket={}, key={}, error={}",
                    bucketName, generateObjectKey(sourceFile), e.getMessage(), e);
            return false;
        } finally {
            // 关闭资源
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Failed to close file input stream", e);
                }
            }
            // TOS 客户端不需要显式关闭
        }
    }

    /**
     * 生成对象键（TOS 中的文件路径）
     *
     * @param sourceFile 源文件
     * @return 对象键
     */
    public String generateObjectKey(File sourceFile) {
        // 使用文件名作为对象键，可以根据需要调整路径结构
        return "uploads/" + sourceFile.getName();
    }
}
