package org.biosino.task.service;

import cn.hutool.core.util.StrUtil;
import com.volcengine.tos.TOSClientConfiguration;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.auth.StaticCredentials;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * TOS 上传服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TosUploadService {

    @Value("${tos.enabled:false}")
    private Boolean tosEnabled;

    @Value("${tos.endpoint:}")
    private String tosEndpoint;

    @Value("${tos.region:}")
    private String tosRegion;

    @Value("${tos.access-key:}")
    private String tosAccessKey;

    @Value("${tos.secret-key:}")
    private String tosSecretKey;

    @Value("${tos.bucket-name:}")
    private String tosBucketName;

    /**
     * 检查 TOS 配置是否有效
     */
    public boolean isValidTosConfig() {
        return tosEnabled
                && StrUtil.isNotBlank(tosEndpoint)
                && StrUtil.isNotBlank(tosRegion)
                && StrUtil.isNotBlank(tosAccessKey)
                && StrUtil.isNotBlank(tosSecretKey)
                && StrUtil.isNotBlank(tosBucketName);
    }

    /**
     * 上传文件到 TOS
     *
     * @param sourceFile 源文件
     * @return 上传是否成功
     */
    public boolean uploadFile(File sourceFile,String objectKey) {
        if (!isValidTosConfig()) {
            log.warn("TOS upload is disabled or configuration is invalid");
            return false;
        }
        TOSV2 tos = null;
        FileInputStream fileInputStream = null;

        try {
            // 创建 TOS 客户端配置
            TOSClientConfiguration config = TOSClientConfiguration.builder()
                    .region(tosRegion)
                    .endpoint(tosEndpoint)
                    .credentials(new StaticCredentials(tosAccessKey, tosSecretKey))
                    .build();

            // 创建 TOS 客户端
            tos = new TOSV2ClientBuilder().build(config);

            // 创建文件输入流
            fileInputStream = new FileInputStream(sourceFile);


            // 构建上传请求
            PutObjectInput putObjectInput = PutObjectInput.builder()
                    .bucket(tosBucketName)
                    .key(objectKey)
                    .content(fileInputStream)
                    .contentLength(sourceFile.length())
                    .build();

            // 执行上传
            PutObjectOutput putObjectOutput = tos.putObject(putObjectInput);

            log.info("TOS upload successful: bucket={}, key={}, etag={}",
                    tosBucketName, objectKey, putObjectOutput.getEtag());

            return true;

        } catch (Exception e) {
            log.error("TOS upload failed: bucket={}, key={}, error={}",
                    tosBucketName, objectKey, e.getMessage(), e);
            return false;
        } finally {
            // 关闭资源
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Failed to close file input stream", e);
                }
            }
        }
    }


}
