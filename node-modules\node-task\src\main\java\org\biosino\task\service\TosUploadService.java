package org.biosino.task.service;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.auth.StaticCredentials;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.task.config.TosProperties;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * TOS 上传服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TosUploadService {

    private final TosProperties tosProperties;

    /**
     * 上传文件到 TOS
     *
     * @param sourceFile 源文件
     * @param objectKey  对象键（TOS 中的文件路径）
     * @return 上传是否成功
     */
    public boolean uploadFile(File sourceFile, String objectKey) {
        if (!tosProperties.getEnabled()) {
            log.warn("TOS upload is disabled");
            return false;
        }

        TOSV2 tos = null;
        FileInputStream fileInputStream = null;
        
        try {
            // 创建 TOS 客户端
            tos = new TOSV2ClientBuilder()
                    .region(tosProperties.getRegion())
                    .endpoint(tosProperties.getEndpoint())
                    .credentials(new StaticCredentials(tosProperties.getAccessKey(), tosProperties.getSecretKey()))
                    .build();

            // 创建文件输入流
            fileInputStream = new FileInputStream(sourceFile);

            // 构建上传请求
            PutObjectInput putObjectInput = PutObjectInput.builder()
                    .bucket(tosProperties.getBucketName())
                    .key(objectKey)
                    .content(fileInputStream)
                    .contentLength(sourceFile.length())
                    .build();

            // 执行上传
            PutObjectOutput putObjectOutput = tos.putObject(putObjectInput);
            
            log.info("TOS upload successful: bucket={}, key={}, etag={}", 
                    tosProperties.getBucketName(), objectKey, putObjectOutput.getEtag());
            
            return true;

        } catch (Exception e) {
            log.error("TOS upload failed: bucket={}, key={}, error={}", 
                    tosProperties.getBucketName(), objectKey, e.getMessage(), e);
            return false;
        } finally {
            // 关闭资源
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Failed to close file input stream", e);
                }
            }
            if (tos != null) {
                try {
                    tos.close();
                } catch (IOException e) {
                    log.error("Failed to close TOS client", e);
                }
            }
        }
    }

    /**
     * 生成对象键（TOS 中的文件路径）
     *
     * @param sourceFile 源文件
     * @return 对象键
     */
    public String generateObjectKey(File sourceFile) {
        // 使用文件名作为对象键，可以根据需要调整路径结构
        return "uploads/" + sourceFile.getName();
    }
}
