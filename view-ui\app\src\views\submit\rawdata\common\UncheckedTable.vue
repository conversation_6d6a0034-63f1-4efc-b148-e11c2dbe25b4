<template>
  <div class="uncheckedTable">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        placeholder="Search for [File Name]"
        class="w-50"
        clearable
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">Period</span>
      <el-date-picker
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="To"
        start-placeholder="Start date"
        end-placeholder="End date"
      />
      <el-button class="radius-12 ml-1 mr-1" type="primary" @click="getDataList"
        >Search
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      row-key="id"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="fileName" label="File Name" sortable>
        <template #default="scope">
          <svg
            v-if="scope.row.isDir"
            xmlns="http://www.w3.org/2000/svg"
            width="15"
            height="16"
            viewBox="0 0 18 16"
            fill="none"
          >
            <path
              id="路径 379"
              fill-rule="evenodd"
              style="fill: #3a78e8"
              opacity="1"
              d="M7.61,1.26h8.59c1.02,0 1.8,0.85 1.8,1.86v11.03c0,1.01 -0.82,1.85 -1.8,1.85h-14.4c-0.98,0 -1.8,-0.84 -1.8,-1.85v-12.3c0,-1.01 0.82,-1.85 1.8,-1.85h3.56c0.12,0 0.29,0.04 0.41,0.13zM1.5957,2.1036v1.73c0,0.25 0.16,0.42 0.41,0.42h13.91c0.24,0 0.41,-0.17 0.41,-0.42v-0.47c0,-0.25 -0.17,-0.42 -0.41,-0.42h-8.55c-0.12,0 -0.29,-0.04 -0.41,-0.12l-1.84,-1.14h-3.07c-0.2,0 -0.41,0.21 -0.45,0.42z"
            ></path>
          </svg>
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="16"
            viewBox="0 0 14 16"
            fill="none"
          >
            <path
              id="路径 1315"
              fill-rule="evenodd"
              style="fill: #999999"
              opacity="1"
              d="M2.18,0h7.13v2.13c0,0.23 0.04,0.47 0.12,0.71c0.07,0.24 0.19,0.47 0.35,0.68c0.16,0.21 0.37,0.39 0.62,0.52c0.25,0.14 0.54,0.21 0.87,0.21h2.73v9.62c0,0.33 -0.07,0.62 -0.21,0.88c-0.15,0.27 -0.34,0.49 -0.57,0.67c-0.24,0.18 -0.5,0.32 -0.79,0.43c-0.28,0.1 -0.56,0.15 -0.84,0.15h-9.18c-0.24,0 -0.5,-0.06 -0.78,-0.19c-0.28,-0.12 -0.54,-0.29 -0.78,-0.49c-0.24,-0.21 -0.45,-0.44 -0.61,-0.7c-0.16,-0.26 -0.24,-0.54 -0.24,-0.82v-11.6c0,-0.24 0.06,-0.49 0.18,-0.74c0.12,-0.26 0.28,-0.49 0.48,-0.71c0.21,-0.22 0.44,-0.39 0.7,-0.54c0.26,-0.14 0.54,-0.21 0.82,-0.21zM11.2202,0.2c0.13,0.08 0.31,0.2 0.51,0.35c0.21,0.14 0.46,0.34 0.78,0.59c0.33,0.29 0.6,0.52 0.8,0.71c0.2,0.19 0.35,0.35 0.46,0.48c0.1,0.13 0.17,0.24 0.19,0.33c0.03,0.09 0.04,0.17 0.04,0.24v0.27h-2.76c-0.13,0 -0.24,-0.04 -0.33,-0.13c-0.09,-0.08 -0.17,-0.18 -0.23,-0.29c-0.07,-0.12 -0.11,-0.23 -0.14,-0.35c-0.03,-0.12 -0.05,-0.22 -0.05,-0.28v-2.12h0.06c0.1,0 0.2,0.01 0.3,0.04c0.11,0.03 0.23,0.09 0.37,0.16zM2.674,6.6989c-0.23,0.21 -0.34,0.46 -0.34,0.75c0,0.3 0.11,0.55 0.34,0.76c0.22,0.2 0.49,0.3 0.82,0.3h7c0.32,0 0.59,-0.1 0.82,-0.3c0.22,-0.21 0.33,-0.46 0.33,-0.76c0,-0.29 -0.11,-0.54 -0.33,-0.75c-0.23,-0.21 -0.5,-0.32 -0.82,-0.32h-7c-0.33,0 -0.6,0.11 -0.82,0.32zM10.494,10.6445h-7c-0.33,0 -0.6,0.1 -0.82,0.31c-0.23,0.2 -0.34,0.45 -0.34,0.75c0,0.29 0.11,0.55 0.34,0.76c0.22,0.21 0.49,0.31 0.82,0.31h7c0.32,0 0.59,-0.1 0.82,-0.31c0.22,-0.21 0.33,-0.47 0.33,-0.76c0,-0.3 -0.11,-0.55 -0.33,-0.75c-0.23,-0.21 -0.5,-0.31 -0.82,-0.31z"
            ></path>
          </svg>
          <span class="ml-05">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="Upload Date"
        width="160"
        sortable
      />
      <el-table-column
        prop="uploadType"
        label="Upload Type"
        width="130"
        sortable
      />
      <el-table-column prop="size" label="Size" width="100" sortable />
      <el-table-column prop="md5File" label="MD5 File" width="200" sortable>
        <template #default="scope">
          <div v-if="!scope.row.isDir" class="d-flex align-items-center">
            <el-icon
              v-if="scope.row.md5FileStatus === 'Provided'"
              :color="iconColor(scope.row.md5FileStatus)"
            >
              <CircleCheckFilled></CircleCheckFilled>
            </el-icon>
            <el-icon
              v-if="scope.row.md5FileStatus === 'Not Provided'"
              :color="iconColor(scope.row.md5FileStatus)"
            >
              <RemoveFilled />
            </el-icon>
            <el-icon
              v-if="scope.row.md5FileStatus === 'Invalid Format'"
              :color="iconColor(scope.row.md5FileStatus)"
            >
              <CircleCloseFilled />
            </el-icon>
            <span
              :style="{
                color: iconColor(scope.row.md5FileStatus),
              }"
              >{{
                scope.row.md5FileStatus === 'Invalid Format'
                  ? 'Md5 Invalid Format'
                  : scope.row.md5FileStatus
              }}</span
            >
            <el-tooltip
              v-if="scope.row.md5FileStatus !== 'Provided'"
              content="You need provide the MD5 file for your files.<br>
1) Be sure the MD5 file name matches the name of that file. e.g. sample1.fastq.gz and sample1.fastq.gz.md5.<br>
2) Be sure the MD5 file contains only the 32-digit MD5 value for a single file. eg. 9ef6aeca276489b64bd777d0ca2b1e9a sample1.fastq.gz<br>
3) The md5 file and its corresponding raw data file should be placed in the same file folder."
              raw-content
            >
              <el-icon color="#FE7F2B">
                <QuestionFilled></QuestionFilled>
              </el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-divider />
    <div class="text-align-right">
      <el-button class="btn-round-primary" round @click="showDialog">
        <el-icon>
          <Check />
        </el-icon>
        <span class="integrity">Data Integrity Check</span>
      </el-button>
      <el-button
        class="btn-round-primary"
        :icon="Download"
        round
        @click="handleExport"
        >Export
      </el-button>
      <el-button
        class="btn-round-warning"
        round
        :icon="Delete"
        @click="handleDelete"
        >Delete
      </el-button>
    </div>
  </div>
  <el-dialog
    v-model="showCheckDialog"
    title="Data Integrity Check"
    width="40%"
    class="integrity-check"
  >
    <div class="integrity-body mb-1">
      <div class="d-flex align-items-center">
        <el-icon color="#8A6D3B" size="large">
          <WarnTriangleFilled />
        </el-icon>
        <span class="note font-600 font-18">Note:</span>
      </div>
      <div>
        1: You need provide the MD5 file for your files. Be sure it contains
        only the 32-digit MD5 value for a single file, and that its name matches
        the name of that file. e.g.
        <strong> sample1.fastq.gz</strong>
        and <strong>sample1.fastq.gz.md5</strong>
      </div>
      <div>
        2. In Linux system,<strong> md5sum</strong> command can be used to
        calculate MD5. For more information, please contact
        <a href="mailto:<EMAIL>" class="text-primary"
          ><EMAIL></a
        >
      </div>
      <div class="mt-05">
        3: After the integrity check is completed, the data is automatically
        moved to the
        <strong>"Unarchived Data"</strong>
        of <strong>"My Data"</strong>. You can view the data through data
        integrity checking in the "Unarchived Data" list.
      </div>
      <div class="mt-05">
        4: Please ensure that file paths and names do not include special
        characters such as spaces, ampersands (&), percent signs (%), asterisks
        (*), or Greek letters. It is recommended to use a combination of
        uppercase letters (A-Z), lowercase letters (a-z), numbers (0-9),
        underscores (_), and hyphens (-) to construct your file names.
      </div>
      <div class="mt-05">
        5: Integrity check takes a while, please be patient.
      </div>
    </div>
    The following files will undergo data integrity verification. Do you want to
    continue?
    <div class="text-warning">
      Files that do not provide an MD5 file will be filtered out
    </div>
    <div v-for="(it, index) in uncheckSelected" :key="index">
      <el-tag class="mt-05">{{ it.relativePath }}</el-tag>
    </div>
    <el-divider class="mb-05"></el-divider>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          class="btn-primary"
          round
          @click="ftpFileBatchVerify"
          >Confirm
        </el-button>
        <el-button class="btn btn-round" round @click="showCheckDialog = false"
          >Cancel</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { Delete, Download, QuestionFilled } from '@element-plus/icons-vue';
  import {
    checkFtpFile,
    deleteFtpFile,
    listUnchecked,
    verifyFtpFile,
  } from '@/api/metadata/data';

  const { proxy } = getCurrentInstance();
  defineExpose({
    getDataList,
  });

  const iconColor = status => {
    if (status === 'Provided') {
      return '#3A78E8';
    } else if (status === 'Invalid Format') {
      return '#FF8989';
    } else if (status === 'Not Provided') {
      return '#999999';
    } else return '#999999';
  };

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    queryParams: {
      name: '',
    },
    dateRange: [],
    selectedRows: [],
    loading: false,
    showCheckDialog: false,
  });

  // 解构
  const {
    tableData,
    queryParams,
    dateRange,
    selectedRows,
    showCheckDialog,
    loading,
  } = toRefs(data);

  /* onMounted */
  onMounted(() => {
    getDataList();
  });

  function getDataList() {
    loading.value = true;
    listUnchecked(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.data;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** handleSelectionChange */
  function handleSelectionChange(selection) {
    // 过滤出不是dir的文件的path
    selectedRows.value = selection;
  }

  let uncheckSelected = reactive([]);

  /* 批量校验sftp里面的文件 */
  function showDialog() {
    // 过滤出不是dir且有md5的文件的path
    uncheckSelected = selectedRows.value.filter(
      item => !item.isDir && item.md5FileStatus === 'Provided',
    );
    if (uncheckSelected.length === 0) {
      proxy.$modal.alertError(
        'No valid file selected. Only files that have provided MD5 file can be checked.',
      );
      return;
    }
    let ids = uncheckSelected.map(it => it.id);
    checkFtpFile(ids).then(response => {
      if (response.data) {
        proxy.$modal.alertError(`${response.data}`, true);
      } else {
        showCheckDialog.value = true;
      }
    });
  }

  function ftpFileBatchVerify() {
    proxy.$modal.loading('Request Processing!');
    verifyFtpFile(uncheckSelected.map(it => it.id))
      .then(response => {
        if (response.data) {
          proxy.$modal.alertError(`${response.data}`, true);
        } else {
          getDataList();
          showCheckDialog.value = false;
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /* 批量删除文件 */
  function handleDelete() {
    if (selectedRows.value.length === 0) {
      proxy.$modal.alertError('Please check the files that need to be checked');
      return;
    }
    let uncheckSelected = selectedRows.value.filter(item => !item.isDir);
    proxy.$modal
      .confirm(
        `The following files will be deleted. Do you want to continue?<br>
                ${uncheckSelected.map(x => x.relativePath).join('<br>')}`,
      )
      .then(() => {
        return deleteFtpFile(uncheckSelected.map(it => it.id));
      })
      .then(() => {
        getDataList();
      });
  }

  /* 批量导出 */
  function handleExport() {
    let b = selectedRows.value.length === 0;
    let content = '';
    if (b) {
      content = 'All unchecked table data will be exported';
    } else {
      content = 'Selected unchecked table data will be exported';
    }

    const downloadParams = b
      ? { ...queryParams }
      : { ids: selectedRows.value.map(it => it.id) };

    proxy.$modal.confirm(content).then(() => {
      proxy.download(
        '/upload/data/export/unchecked',
        downloadParams,
        `unchecked_${new Date().getTime()}.xlsx`,
      );
    });
  }
</script>

<style scoped lang="scss">
  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }
</style>
