package org.biosino.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TOS 配置属性
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tos")
public class TosProperties {

    /**
     * TOS 开关
     */
    private Boolean enabled = false;

    /**
     * TOS 服务端点
     */
    private String endpoint;

    /**
     * TOS 区域
     */
    private String region;

    /**
     * TOS 访问密钥 ID
     */
    private String accessKey;

    /**
     * TOS 访问密钥
     */
    private String secretKey;

    /**
     * TOS 存储桶名称
     */
    private String bucketName;
}
