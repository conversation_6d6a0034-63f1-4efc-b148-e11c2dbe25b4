package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 标签数据统计
 * 以一个标签{source oragnization，source project，source leader}为例
 *
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
@Document("statistics_tag")
public class StatisticsTag {

    @Id
    private String id;

    @Excel(name = "Month")
    private String month;

    @Excel(name = "Tag")
    private String tag;

    @Excel(name = "Project", cellType = Excel.ColumnType.NUMERIC)
    private Long proj = 0L;

    @Excel(name = "Project Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long projAccessible = 0L;

    @Excel(name = "Experiment", cellType = Excel.ColumnType.NUMERIC)
    private Long exp = 0L;

    @Excel(name = "Experiment Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long expAccessible = 0L;

    @Excel(name = "Sample", cellType = Excel.ColumnType.NUMERIC)
    private Long sap = 0L;

    @Excel(name = "Sample Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long sapAccessible = 0L;

    @Excel(name = "Run", cellType = Excel.ColumnType.NUMERIC)
    private Long run = 0L;

    @Excel(name = "Run Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long runAccessible = 0L;

    @Excel(name = "Analysis", cellType = Excel.ColumnType.NUMERIC)
    private Long anal = 0L;

    @Excel(name = "Analysis Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long analAccessible = 0L;

    @Excel(name = "FastQC Data File Num", cellType = Excel.ColumnType.NUMERIC)
    private Long fastqcDataFileNum = 0L;

    @Excel(name = "FastQC Data Sum_Len SUM", cellType = Excel.ColumnType.NUMERIC)
    private Double fastqcDataSumLenSum = 0.0;

    @Excel(name = "FastQC Data Q20 Average", cellType = Excel.ColumnType.NUMERIC)
    private Double fastqcDataQ20Avg = 0.0;

    @Excel(name = "FastQC Data Q30 Average", cellType = Excel.ColumnType.NUMERIC)
    private Double fastqcDataQ30Avg = 0.0;

    @Excel(name = "Public FastQC Data File Num", cellType = Excel.ColumnType.NUMERIC)
    private Long publicFastqcDataFileNum = 0L;

    @Excel(name = "Public FastQC Data Sum_Len SUM", cellType = Excel.ColumnType.NUMERIC)
    private Double publicFastqcDataSumLenSum = 0.0;

    @Excel(name = "Public FastQC Data Q20 Average", cellType = Excel.ColumnType.NUMERIC)
    private Double publicFastqcDataQ20Avg = 0.0;

    @Excel(name = "Public FastQC Data Q30 Average", cellType = Excel.ColumnType.NUMERIC)
    private Double publicFastqcDataQ30Avg = 0.0;

    @Excel(name = "Data File Num", cellType = Excel.ColumnType.NUMERIC)
    private Long dataFileNum = 0L;

    private Long dataFileSize = 0L;

    @Excel(name = "Data Public File Num", cellType = Excel.ColumnType.NUMERIC)
    private Long dataPublicFileNum = 0L;

    private Long dataPublicFileSize = 0L;
}
