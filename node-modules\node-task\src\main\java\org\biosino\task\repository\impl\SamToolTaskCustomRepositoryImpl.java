package org.biosino.task.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.task.repository.SamToolTaskCustomRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@RequiredArgsConstructor
public class SamToolTaskCustomRepositoryImpl implements SamToolTaskCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<SamToolTask> getReadyTasksAndPriorityGte(int priority, int pageSize) {
        Query query = Query.query(Criteria.where("status").is(FastQCTaskStatusEnum.ready.name())
                .and("priority").gte(priority));
        query.with(Sort.by(Sort.Direction.DESC, "priority", "create_date")).limit(pageSize);
        return mongoTemplate.find(query, SamToolTask.class);
    }

    @Override
    public List<SamToolTask> getReadyTasksAndPriorityLte(int priority, int pageSize) {
        Query query = Query.query(Criteria.where("status").is(FastQCTaskStatusEnum.ready.name())
                .and("priority").lte(priority));
        query.with(Sort.by(Sort.Direction.DESC, "priority", "create_date")).limit(pageSize);
        return mongoTemplate.find(query, SamToolTask.class);
    }
}
