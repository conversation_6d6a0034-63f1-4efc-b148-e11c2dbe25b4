package org.biosino.common.es.entity;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.biosino.common.es.entity.base.BaseEsInfo;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashSet;

/**
 * node全文检索索引
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@IndexName(value = NodeEs.NODE_ES_NAME)
@Settings(maxResultWindow = 300 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW)
public class NodeEs extends BaseEsInfo implements Serializable {
    public static final String NODE_ES_NAME = "node_es";

    /**
     * es主键，使用数据库存储的编号，如OEP00000078、OEX00000119
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /**
     * 数据类型
     *
     * @see NodeEsTypeEnum
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.NOT_EMPTY, ignoreCase = true)
    private String type;


    /**
     * 数据编号（id不可用于wildcard检索），使用数据库存储的编号，如OEP00000078、OEX00000119
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.NOT_EMPTY, ignoreCase = true)
    private String typeId;

    /**
     * 数据库存储的编号，如OEP00000078、OEX00000119
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.NOT_EMPTY, ignoreCase = true)
    private LinkedHashSet<String> relaTypeIds;

    /**
     * 名称
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String name;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.NOT_EMPTY, ignoreCase = true)
    private LinkedHashSet<String> relaNames;

    /**
     * 公共描述信息
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String description;

    /**
     * 更新时间
     */
    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date modifiedDate;

    /**
     * 创建时间
     */
    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date createDate;

    /**
     * 提交者id，node cas单点登录返回的id
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String creator;

    @IndexField(exist = false, fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String visibleStatus;

    /**
     * 曾经用过的id
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> usedIds;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaUsedIds;

    /**
     * 是否公开
     * Public 存在_public的data
     * Controlled 存在_restricted的data
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaAccess;

    @IndexField(exist = false, fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> security;

    // ============== 项目 start ==============
    /**
     * 是否为多组学
     */
    @IndexField(fieldType = FieldType.BOOLEAN, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private Boolean multiOmics;
    // ============== 项目 end ==============


    // ============== 实验 start ==============
    /**
     * 方案
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String protocol;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaProtocol;

    /**
     * 实验类型
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String expType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaExpType;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String libraryStrategy;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaLibraryStrategy;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String libraryLayout;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaLibraryLayout;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String librarySelection;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaLibrarySelection;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String libraryName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaLibraryName;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String platform;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaPlatform;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String matePair;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaMatePair;
    // ============== 实验 end ==============

    // ============== 数据 start ==============
    /**
     * 文件类型
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String fileType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaFileType;
    // ============== 数据 end ==============

    // ============== 样本 start ==============
    /**
     * 样本类型
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String sampleType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSampleType;

    /**
     * 样本生物体
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String organism;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaOrganism;

    /**
     * 样品组织
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String tissue;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaTissue;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subjectId;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSubjectId;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String biomaterialProvider;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaBiomaterialProvider;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String disease;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaDisease;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String disPhenotype;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaDisPhenotype;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String mutationType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaMutationType;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String sampleLoc;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSampleLoc;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String gender;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaGender;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String extractedMolType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaExtractedMolType;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String devStage;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaDevStage;


    /**
     * attributes.biome
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String biome;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaBiome;
    /**
     * attributes.env_biome
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String envBiome;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaEnvBiome;
    /**
     * attributes.env_material
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String envMaterial;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaEnvMaterial;
    /**
     * attributes.env_feature
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String envFeature;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaEnvFeature;
    // ============== 样本 end ==============


    // ============== 分析 start ==============
    /**
     * 分析类型
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String analysisType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaAnalysisType;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String pipelineProgram;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaPipelineProgram;
    // ============== 分析 end ==============

    /**
     * fastQc 结果
     */
    @IndexField(fieldType = FieldType.DOUBLE, strategy = FieldStrategy.IGNORED)
    private LinkedHashSet<Double> relaNumSeqs;

    @IndexField(fieldType = FieldType.DOUBLE, strategy = FieldStrategy.IGNORED)
    private LinkedHashSet<Double> relaSumLen;

    @IndexField(fieldType = FieldType.DOUBLE, strategy = FieldStrategy.IGNORED)
    private LinkedHashSet<Double> relaQ20;

    @IndexField(fieldType = FieldType.DOUBLE, strategy = FieldStrategy.IGNORED)
    private LinkedHashSet<Double> relaQ30;


//    @IndexField(fieldType = FieldType.INTEGER, strategy = FieldStrategy.IGNORED)
//    private Integer numOfExp;
//    @IndexField(fieldType = FieldType.INTEGER, strategy = FieldStrategy.IGNORED)
//    private Integer numOfSample;
//    @IndexField(fieldType = FieldType.INTEGER, strategy = FieldStrategy.IGNORED)
//    private Integer numOfFile;

}
