package org.biosino.common.mongo.entity;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Data
@Document(collection = "fastqc_task")
public class FastQCTask {
    @Id
    private String id;

    @Field("data_no")
    private String dataNo;

    /**
     * data的安全等级
     */
    @Transient
    private String dataSecurity;

    private String status;

    @Field("data_file_name")
    private String dataFileName;

    @Field("data_file_path")
    private String dataFilePath;

    @Field("data_file_size")
    private Long dataFileSize;

    @Field("exp_no")
    private String expNo;

    @Field("sap_no")
    private String sapNo;

    @Field("data_create_date")
    private Date dataCreateDate;

    @Field("create_date")
    private Date createDate;

    @Field("update_date")
    private Date updateDate;

    @Field
    private Integer priority;

    @Field("use_time")
    private String useTime;

    @Field("fail_cause")
    private String failCause;

    @Field("error_log_path")
    private String errorLogPath;

    @Field("fastqc_result")
    private FastQCResult fastqcResult;

    @Field("seqkit_result")
    private SeqkitResult seqkitResult;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FastQCResult {
        @Field("html_file_path")
        private String htmlFilePath;
        @Field("report_file_path")
        private String reportFilePath;
        @Field("zip_file_path")
        private String zipFilePath;
        @Field("version")
        private String version;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SeqkitResult {
        @Field("format")
        @Alias("format")
        private String format;
        @Field("type")
        @Alias("type")
        private String type;
        @Field("num_seqs")
        @Alias("num_seqs")
        private Double numSeqs;
        @Field("sum_len")
        @Alias("sum_len")
        private Double sumLen;
        @Field("min_len")
        @Alias("min_len")
        private Double minLen;
        @Field("avg_len")
        @Alias("avg_len")
        private Double avgLen;
        @Field("max_len")
        @Alias("max_len")
        private Double maxLen;
        @Field("q1")
        @Alias("Q1")
        private Double q1;
        @Field("q2")
        @Alias("Q2")
        private Double q2;
        @Field("q3")
        @Alias("Q3")
        private Double q3;
        @Field("sum_gap")
        @Alias("sum_gap")
        private Double sumGap;
        @Field("n50")
        @Alias("N50")
        private Double n50;
        @Field("n50_num")
        @Alias("N50_num")
        private Double n50Num;
        @Field("q20")
        @Alias("Q20(%)")
        private Double q20;
        @Field("q30")
        @Alias("Q30(%)")
        private Double q30;
        @Field("avg_qual")
        @Alias("AvgQual")
        private Double avgQual;
        @Field("gc")
        @Alias("GC(%)")
        private Double gc;

        @Field("file_path")
        private String filePath;
        private String version;
    }
}
