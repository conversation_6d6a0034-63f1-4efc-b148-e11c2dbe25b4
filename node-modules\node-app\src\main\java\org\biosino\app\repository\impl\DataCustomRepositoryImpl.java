package org.biosino.app.repository.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.DataCustomRepository;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.common.mongo.entity.Share;
import org.biosino.system.api.model.Member;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Li
 * @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Data findByNo(String datNo) {
        return mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(datNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity())), Data.class);
    }

    @Override
    public List<Data> findDetailByRunNos(Collection<String> runNos) {
        Query query = Query.query(Criteria.where("run_no").in(runNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no")
                .include("name")
                .include("run_no")
                .include("data_type")
                .include("file_name")
                .include("security")
                .include("file_size")
                .include("creator")
                .include("submission_date");
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findDetailByAnalNoIn(Collection<String> analNos) {
        Query query = Query.query(Criteria.where("anal_no").in(analNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no")
                .include("name")
                .include("anal_no")
                .include("data_type")
                .include("security")
                .include("file_size")
                .include("update_date")
                .include("creator")
                .include("submission_date");
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findByDataNos(Collection<String> dataNos) {
        Query query = Query.query(Criteria.where("dat_no").in(dataNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("security").in(SecurityEnum.includeAllSecurity()));
        query.fields().include("dat_no")
                .include("name")
                .include("anal_no")
                .include("run_no")
                .include("data_type")
                .include("security")
                .include("file_size")
                .include("md5")
                .include("creator")
                .include("update_date")
                .include("temp_data.sub_no")
                .include("complete")
                .include("submission_date");
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public BrowseStatDTO findBrowseDataNoByRunNoIn(Collection<String> runNos) {
        final BrowseStatDTO result = new BrowseStatDTO();
        findBrowseDataNoByRunNo(mongoTemplate, runNos, result);
        return result;
    }

    public static void findBrowseDataNoByRunNo(final MongoTemplate mongoTemplate, final Collection<String> runNos, final BrowseStatDTO result) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("run_no").in(runNos).and("security").in(SecurityEnum.includeSecurity()));
        query.fields().include("dat_no");
        result.setDataNos(new LinkedHashSet<>(mongoTemplate.findDistinct(query, "dat_no", Data.class, String.class)));
    }

    public static void findBrowseDataNoByAnalNo(final MongoTemplate mongoTemplate, final Collection<String> analNos, final BrowseStatDTO result) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("anal_no").in(analNos).and("security").in(SecurityEnum.includeSecurity()));
        query.fields().include("dat_no");
        result.setDataNos(new LinkedHashSet<>(mongoTemplate.findDistinct(query, "dat_no", Data.class, String.class)));
    }

    @Override
    public List<Data> findByRunNosAndSecurity(List<String> runNos, String security) {
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").in(runNos).and("security").is(security).and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public List<Data> findByAnalNos(List<String> analysisNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").in(analysisNo).and("security").in(SecurityEnum.includeAllSecurity()).and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public Page<Data> findDataPage(UserCenterListSearchDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        criteriaList.add(Criteria.where("archived").is(queryDTO.getArchived()));
        criteriaList.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where("dat_no").regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        if (queryDTO.isExistAnalysis()) {
            criteriaList.add(Criteria.where("anal_no").exists(true));
        }
        if (queryDTO.isExistRun()) {
            criteriaList.add(Criteria.where("run_no").exists(true));
        }
        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, Data.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Data> content = mongoTemplate.find(query, Data.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public Optional<Data> findByDatNo(String datNo) {
        if (StrUtil.isBlank(datNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(new Criteria().orOperator(
                Criteria.where("dat_no").is(datNo),
                Criteria.where("used_ids").in(datNo)
        ));

        Query query = new Query(new Criteria().andOperator(condition));

        Data data = mongoTemplate.findOne(query, Data.class);

        return Optional.ofNullable(data);
    }

    @Override
    public void updateDataHumanRecordNo(String id, String recordOption, String humanRecordNo) {
        Query query = Query.query(Criteria.where("id").is(id));

        Update update = new Update();
        update.set("human_record_option", recordOption);
        if (StrUtil.isBlank(humanRecordNo)) {
            update.unset("human_record_no");
        } else {
            update.set("human_record_no", humanRecordNo.trim());
        }

        mongoTemplate.updateFirst(query, update, Data.class);
    }

    @Override
    public boolean checkAccessPermission(String dataNo, Member member) {
        Data data = findByDatNo(dataNo).orElseThrow(() -> new ServiceException("data not exist!"));

        // 判断是否为公开数据
        if (StrUtil.equals(data.getSecurity(), SecurityEnum._public.getDesc())) {
            return true;
        }

        if (member == null) {
            return false;
        }

        // 判断是否为公开数据 or 创建者
        if (StrUtil.equals(data.getCreator(), member.getId())) {
            return true;
        }
        // 判断当前用户是否有Share
        boolean existShare = mongoTemplate.exists(new Query(
                Criteria.where("share_to").in(member.getEmail())
                        .and("datas.data_no").in(dataNo)
                        .and("status").is(ShareStatusEnum.sharing.name())), Share.class);

        if (existShare) {
            return true;
        }

        // 判断当前用户是否有ResourceAuthorize
        boolean existResourceAuthorize = mongoTemplate.exists(
                new Query(
                        Criteria.where("data").in(dataNo)
                                .and("status").is(RequestStatusEnum.authorized.getDesc())
                                .and("expire_date").gt(new Date())
                                .and("authorize_to").is(member.getId())
                ),
                ResourceAuthorize.class
        );
        if (existResourceAuthorize) {
            return true;
        }

        return false;
    }
}
