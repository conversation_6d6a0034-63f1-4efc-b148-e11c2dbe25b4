package org.biosino.app.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.app.service.BrowseService;
import org.biosino.app.vo.search.EsSelectQueryVO;
import org.biosino.app.vo.search.NodeEsQueryVO;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;

/**
 * 检索列表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/browse")
public class BrowseController extends BaseController {
    private final BrowseService browseService;

    /**
     * Browse界面查询接口
     */
    @PostMapping("/search")
    public AjaxResult search(@RequestBody NodeEsQueryVO searchVO) {
        return success(browseService.search(searchVO));
    }


    /**
     * 自动补全--查询下拉框数据
     */
    @PostMapping("/searchSelectData")
    public AjaxResult searchSelectData(@RequestBody EsSelectQueryVO queryVO) {
        return success(browseService.searchSelectData(queryVO));
    }

    /**
     * 查询experiment的Icon信息
     */
    @GetMapping("/getExpIconInfo")
    public AjaxResult getExpIconInfo() {
        return success(browseService.getExpIconInfo());
    }

}
