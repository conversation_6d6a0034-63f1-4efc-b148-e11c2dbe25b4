package org.biosino.esindex.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.es.entity.NodeRelatedEs;
import org.biosino.common.es.mapper.NodeRelatedEsMapper;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.es.api.dto.DataSearchDTO;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.dto.SecurityDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.biosino.esindex.dto.mapper.NodeRelatedEsDTOMapper;
import org.biosino.esindex.repository.node.DataRepository;
import org.biosino.esindex.repository.node.ProjectRepository;
import org.biosino.esindex.repository.node.ResourceAuthorizeRepository;
import org.biosino.esindex.repository.node.ShareRepository;
import org.dromara.easyes.common.params.SFunction;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataService {
    private final NodeRelatedEsMapper dataMapper;
    private final ProjectRepository projectRepository;

    private final DataRepository dataRepository;

    private final ShareRepository shareRepository;
    private final ResourceAuthorizeRepository resourceAuthorizeRepository;

    public List<RelatedDataDTO> findAllByTypeAndNo(final DataListSearchVO searchVO) {
        final String type = searchVO.getType();
        final String typeNo = searchVO.getTypeNo();
        final String security = searchVO.getSecurity();

        Optional<AuthorizeType> name = AuthorizeType.findByName(type);
        if (!name.isPresent()) {
            return null;
        }

        AuthorizeType authorizeType = name.get();

        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = new LambdaEsQueryWrapper<>();

        if (searchVO.isMustHasData()) {
            wrapper.exists(NodeRelatedEs::getDatNo);
        }

        wrapper.eq(StrUtil.isNotBlank(security), NodeRelatedEs::getSecurity, security);

        initRelatedEsQuery(wrapper, authorizeType, typeNo);

        List<NodeRelatedEs> nodeRelatedEs = dataMapper.selectList(wrapper);

        if (CollUtil.isEmpty(nodeRelatedEs)) {
            return null;
        }

        return initDtoResult(nodeRelatedEs);
    }

    private LambdaEsQueryWrapper<NodeRelatedEs> createNewEsQuery(final AuthorizeType authorizeType, final String typeNo) {
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        initRelatedEsQuery(wrapper, authorizeType, typeNo);
        return wrapper;
    }

    private void initRelatedEsQuery(LambdaEsQueryWrapper<NodeRelatedEs> wrapper, final AuthorizeType authorizeType, final String typeNo) {
        if (authorizeType == null) {
            return;
        }
        switch (authorizeType) {
            case project:
                wrapper.eq(NodeRelatedEs::getProjNo, typeNo);
                break;
            case experiment:
                wrapper.eq(NodeRelatedEs::getExpNo, typeNo);
                break;
            case sample:
                wrapper.eq(NodeRelatedEs::getSapNo, typeNo);
                break;
            case run:
                wrapper.eq(NodeRelatedEs::getRunNo, typeNo);
                break;
            case data:
                wrapper.eq(NodeRelatedEs::getDatNo, typeNo);
                break;
            case analysis:
                wrapper.eq(NodeRelatedEs::getAnalNo, typeNo);
                break;
        }
    }

    public EsPageInfo<RelatedDataDTO> findPageData(final DataListSearchVO searchVO) {
        final Pageable pageable = searchVO.initPageInfo();
        final String currMemberId = searchVO.getCurrMemberId();

        final Optional<AuthorizeType> typeOptional = AuthorizeType.findByName(searchVO.getType());
        final String typeNo = searchVO.getTypeNo();

        if (!typeOptional.isPresent() || StrUtil.isBlank(typeNo)) {
            return EsPageInfo.of(new ArrayList<>());
        }
        final AuthorizeType authorizeType = typeOptional.get();
        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = createNewEsQuery(authorizeType, typeNo);

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();

        final Set<String> dataNos = searchVO.getDataNos();
        if (CollUtil.isNotEmpty(dataNos)) {
            wrapper.in(NodeRelatedEs::getDatNo, dataNos);
        } else {
            List list = addAuthWrapper(currMemberId, wrapper, authorizeType, typeNo, searchVO.getCurrMemberEmail());
            authorizedList = (List<ResourceAuthorize>) list.get(0);
            sharedDataNos = (List<String>) list.get(1);
            /*if (currMemberId != null) {
                // 已登录
                NodeRelatedEs es = dataMapper.selectOne(createNewEsQuery(authorizeType, typeNo).exists(NodeRelatedEs::getCreator).limit(1));
                final String creator = es.getCreator();

                if (!currMemberId.equals(creator)) {
                    // 不是拥有者
                    final LambdaEsQueryWrapper<NodeRelatedEs> notPublicQuery = createNewEsQuery(authorizeType, typeNo);
                    notPublicQuery.in(NodeRelatedEs::getSecurity, SecurityEnum.notPublicSecurity());
                    notPublicQuery.select(NodeRelatedEs::getDatNo);
                    final Set<String> notPublicDataNos = dataMapper.selectList(notPublicQuery)
                            .stream().map(NodeRelatedEs::getDatNo).collect(Collectors.toSet());

                    final String currMemberEmail = searchVO.getCurrMemberEmail();

                    if (currMemberEmail != null) {
                        sharedDataNos = shareRepository.sharedDataNo(currMemberEmail, notPublicDataNos);
                    }

                    authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(currMemberId, notPublicDataNos);
                    // 授权的DataNo
                    Set<String> authorizedDataNos = new HashSet<>();
                    if (CollUtil.isNotEmpty(authorizedList)) {
                        for (ResourceAuthorize resourceAuthorize : authorizedList) {
                            authorizedDataNos.addAll(resourceAuthorize.getData());
                        }
                    }

                    final List<String> finalSharedDataNos = sharedDataNos;
                    if (CollUtil.isNotEmpty(authorizedDataNos) || CollUtil.isNotEmpty(finalSharedDataNos)) {
                        wrapper.and(t -> t.or(f -> f.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity()))
                                .or(i -> i.eq(NodeRelatedEs::getSecurity, SecurityEnum._private.getDesc())
                                        .and(s -> s.in(NodeRelatedEs::getDatNo, finalSharedDataNos)
                                                .or().in(NodeRelatedEs::getDatNo, authorizedDataNos))
                                )

                        );
                    } else {
                        wrapper.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
                    }
                }
            } else {
                // 未登录
                wrapper.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
            }*/
        }

        // 添加fastqcTask条件
        if (searchVO.getFastqcFinished() != null) {
            wrapper.eq(NodeRelatedEs::getFastqcFinished, searchVO.getFastqcFinished());
        }

        // data no可能为空，此字段判断是否添加data no必须存在的条件
        final boolean mustHasData = searchVO.isMustHasData();
        if (mustHasData) {
            wrapper.exists(NodeRelatedEs::getDatNo);
        }

        // 添加排序条件
        final Sort sort = pageable.getSort();
        if (!sort.isEmpty()) {
            final Sort.Order order = sort.iterator().next();
            final String property = order.getProperty();
            if ("readableFileSize".equals(property)) {
                wrapper.orderBy(true, order.isAscending(), "fileSize");
            } else {
                wrapper.orderBy(true, order.isAscending(), property);
            }
        }

        int size = pageable.getPageSize();
        if (searchVO.isFindAll()) {
            size = 100000;
        }

        // easy ES页码从1开始
        final EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, size);
        final List<RelatedDataDTO> relatedDataDTOS = initDtoResult(pageInfo.getList());

        final Map<String, ResourceAuthorize> typeIdMap = new HashMap<>();
        for (ResourceAuthorize resourceAuthorize : authorizedList) {
            for (String item : resourceAuthorize.getData()) {
                typeIdMap.put(item, resourceAuthorize);
            }
        }
        for (RelatedDataDTO data : relatedDataDTOS) {
            // 根据不同情况，判断当前用户有权限访问
            if (data.getSecurity().equals(SecurityEnum._public.getDesc())) {
                data.setAccessible(true);
            } else if (sharedDataNos.contains(data.getDatNo())) {
                data.setAccessible(true);
                data.setShared(true);
            } else if (typeIdMap.containsKey(data.getDatNo())) {
                data.setAccessible(true);
                data.setRequested(true);
                data.setExpireDate(typeIdMap.get(data.getDatNo()).getExpireDate());
            } else if (StrUtil.equals(data.getCreator(), currMemberId)) {
                data.setAccessible(true);
            }
        }
        final EsPageInfo<RelatedDataDTO> info = EsPageInfo.of(relatedDataDTOS);
        info.setTotal(pageInfo.getTotal());
        return info;
    }

    public String generateDataExcel(final DataListSearchVO searchVO) {
        BigExcelWriter writer = null;
        try {
            final File excelFile = new File(MyFileUtils.getTempDirInDay(), IdUtil.fastSimpleUUID() + ".xlsx");
            writer = ExcelUtil.getBigWriter(excelFile);

            searchVO.noLimitPageSize(10000);
            final boolean findFilePath = searchVO.isFindFilePath();
            boolean isEmpty = true;

            // 所有标题
            final LinkedHashSet<String> allColName = new LinkedHashSet<>();

            for (int i = 0; i < Integer.MAX_VALUE; i++) {
                searchVO.setPageNum(i + 1);
                final EsPageInfo<RelatedDataDTO> data = findPageData(searchVO);
                if (data == null) {
                    break;
                }
                final List<RelatedDataDTO> list = data.getList();
                if (CollUtil.isEmpty(list)) {
                    break;
                }
                isEmpty = false;

                final List<LinkedHashMap<String, String>> rows = new ArrayList<>();

                final Set<String> datNos = new HashSet<>();

                final String dataId = "data_id";
                for (RelatedDataDTO relatedDataDTO : list) {
                    final String datNo = relatedDataDTO.getDatNo();
                    if (findFilePath) {
                        datNos.add(datNo);
                    }

                    final LinkedHashMap<String, String> row = new LinkedHashMap<>();
                    row.put("experiment_id", relatedDataDTO.getExpNo());
                    row.put("experiment_name", relatedDataDTO.getExpName());
                    row.put("experiment_type", relatedDataDTO.getExpType());
                    row.put("sample_id", relatedDataDTO.getSapNo());
                    row.put("sample_name", relatedDataDTO.getSapName());
                    row.put("sample_type", relatedDataDTO.getSapType());
                    row.put("run_id", relatedDataDTO.getRunNo());
                    row.put("run_name", relatedDataDTO.getRunName());
                    row.put(dataId, datNo);
                    row.put("data_name", relatedDataDTO.getName());
                    row.put("data_type", relatedDataDTO.getDataType());
                    row.put("security", relatedDataDTO.getSecurity());
                    row.put("md5", relatedDataDTO.getMd5());
                    row.put("data_size", relatedDataDTO.getReadableFileSize());
                    rows.add(row);
                }

                // 补充文件路径
                if (findFilePath) {
                    final Map<String, String> noAndFilePathMap = dataRepository.findNoAndFilePath(datNos);
                    for (LinkedHashMap<String, String> row : rows) {
                        final String path = noAndFilePathMap.get(row.get(dataId));
                        row.put("data_path", path == null ? StrUtil.EMPTY : path);
                    }
                }

                allColName.addAll(rows.get(0).keySet());
                // 写出当前页数据到Excel
                writer.write(rows, true);
            }

            if (isEmpty) {
                throw new ServiceException("No Data");
            }

            // 设置列宽
            int colIndex = 0;
            for (String s : allColName) {
                writer.setColumnWidth(colIndex++, s.length() + 2);
            }

            writer.close();
            return excelFile.getAbsolutePath();
        } finally {
            IoUtil.close(writer);
        }
    }

    public List addAuthWrapper(String currMemberId, final LambdaEsQueryWrapper<NodeRelatedEs> wrapper, final AuthorizeType authorizeType,
                               final String typeNo, final String currMemberEmail) {

        List<ResourceAuthorize> authorizedList = new ArrayList<>();
        List<String> sharedDataNos = new ArrayList<>();

        if (currMemberId != null) {
            // 已登录
            final NodeRelatedEs es = dataMapper.selectOne(createNewEsQuery(authorizeType, typeNo).exists(NodeRelatedEs::getCreator).limit(1));
            String creator;
            if (es != null) {
                creator = es.getCreator();
            } else {
                creator = projectRepository.findCreatorByTypeAndNo(authorizeType, typeNo);
            }

            if (!currMemberId.equals(creator)) {
                // 不是拥有者
                final LambdaEsQueryWrapper<NodeRelatedEs> notPublicQuery = createNewEsQuery(authorizeType, typeNo);
                notPublicQuery.in(NodeRelatedEs::getSecurity, SecurityEnum.notPublicSecurity());
                notPublicQuery.select(NodeRelatedEs::getDatNo);
                final Set<String> notPublicDataNos = dataMapper.selectList(notPublicQuery)
                        .stream().map(NodeRelatedEs::getDatNo).collect(Collectors.toSet());


                if (currMemberEmail != null) {
                    sharedDataNos = shareRepository.sharedDataNo(currMemberEmail, notPublicDataNos);
                }

                authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(currMemberId, notPublicDataNos);
                // 授权的DataNo
                Set<String> authorizedDataNos = new HashSet<>();
                if (CollUtil.isNotEmpty(authorizedList)) {
                    for (ResourceAuthorize resourceAuthorize : authorizedList) {
                        authorizedDataNos.addAll(resourceAuthorize.getData());
                    }
                }

                final List<String> finalSharedDataNos = sharedDataNos;
                if (CollUtil.isNotEmpty(authorizedDataNos) || CollUtil.isNotEmpty(finalSharedDataNos)) {
                    wrapper.and(t -> t.or(f -> f.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity()))
                            .or(i -> i.eq(NodeRelatedEs::getSecurity, SecurityEnum._private.getDesc())
                                    .and(s -> s.in(NodeRelatedEs::getDatNo, finalSharedDataNos)
                                            .or().in(NodeRelatedEs::getDatNo, authorizedDataNos))
                            )

                    );
                } else {
                    wrapper.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
                }
            }
        } else {
            // 未登录
            wrapper.in(NodeRelatedEs::getSecurity, SecurityEnum.includeSecurity());
        }

        final List dataList = new ArrayList<>();
        dataList.add(authorizedList);
        dataList.add(sharedDataNos);
        return dataList;
    }


    public Long countDataByPage(DataListSearchVO searchVO) {
        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        String currMemberId = searchVO.getCurrMemberId();

        wrapper.exists(NodeRelatedEs::getDatNo);
        wrapper.eq(StrUtil.isNotBlank(currMemberId), NodeRelatedEs::getCreator, currMemberId);

        wrapper.between(searchVO.getStartTime() != null && searchVO.getEndTime() != null,
                NodeRelatedEs::getUploadTime, searchVO.getStartTime(), searchVO.getEndTime());

        return dataMapper.selectCount(wrapper);
    }

    public List<RelatedDataDTO> statisticDataByPage(DataListSearchVO searchVO) {
        Pageable pageable = searchVO.initPageInfo();
        String currMemberId = searchVO.getCurrMemberId();

        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);

        // 为了加快查询速度，节省内存，只返回统计需要的字段
        wrapper.select(NodeRelatedEs::getProjNo, NodeRelatedEs::getExpNo, NodeRelatedEs::getExpType,
                NodeRelatedEs::getSapNo, NodeRelatedEs::getSapType, NodeRelatedEs::getRunNo, NodeRelatedEs::getAnalNo,
                NodeRelatedEs::getDatNo, NodeRelatedEs::getDataType, NodeRelatedEs::getFileSize, NodeRelatedEs::getSecurity, NodeRelatedEs::getUploadTime);

        wrapper.exists(NodeRelatedEs::getDatNo);
        wrapper.eq(StrUtil.isNotBlank(currMemberId), NodeRelatedEs::getCreator, currMemberId);

        wrapper.between(searchVO.getStartTime() != null && searchVO.getEndTime() != null,
                NodeRelatedEs::getUploadTime, searchVO.getStartTime(), searchVO.getEndTime());

        EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, pageable.getPageSize());
        List<RelatedDataDTO> relatedDataDTOS = initDtoResult(pageInfo.getList());
        return relatedDataDTOS;
    }

    public EsPageInfo<RelatedDataDTO> findPublicPageData(DataListSearchVO searchVO) {
        String searchName = searchVO.getSearchName();
        Pageable pageable = searchVO.initPageInfo();

        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        // 查询公开的数据
        wrapper.exists(NodeRelatedEs::getDatNo);
        wrapper.eq(NodeRelatedEs::getSecurity, SecurityEnum._public.getDesc());
        if (StrUtil.equals(searchVO.getType(), AuthorizeType.project.name())) {
            // 根据type查询Project不为空的
            wrapper.exists(NodeRelatedEs::getProjNo);
            if (StrUtil.isNotBlank(searchName)) {
                wrapper.and(a -> a.like(NodeRelatedEs::getProjNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getProjName, searchName)
                        .or()
                        .like(NodeRelatedEs::getExpNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getExpName, searchName)
                        .or()
                        .like(NodeRelatedEs::getSapNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getSapName, searchName)
                        .or()
                        .like(NodeRelatedEs::getRunNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getRunName, searchName)
                        .or()
                        .like(NodeRelatedEs::getDatNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getName, searchName));
            }
        } else {
            // 根据type查询Anal不为空的
            wrapper.exists(NodeRelatedEs::getAnalNo);
            if (StrUtil.isNotBlank(searchName)) {
                wrapper.and(a -> a.like(NodeRelatedEs::getAnalNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getAnalName, searchName)
                        .or()
                        .like(NodeRelatedEs::getDatNo, searchName)
                        .or()
                        .like(NodeRelatedEs::getName, searchName));
            }
        }
        // 添加排序条件
        final Sort sort = pageable.getSort();
        if (!sort.isEmpty()) {
            final Sort.Order order = sort.iterator().next();
            final String property = order.getProperty();

            wrapper.orderBy(true, order.isAscending(), property);
        }
        EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, pageable.getPageSize());
        List<RelatedDataDTO> relatedDataDTOS = initDtoResult(pageInfo.getList());

        EsPageInfo<RelatedDataDTO> info = EsPageInfo.of(relatedDataDTOS);
        info.setTotal(pageInfo.getTotal());
        return info;
    }

    /*private LambdaEsQueryWrapper<NodeRelatedEs> copyWrapper(final LambdaEsQueryWrapper<NodeRelatedEs> originalWrapper) {
        return EsWrappers.lambdaQuery(NodeRelatedEs.class).setSearchSourceBuilder(copyQuery(originalWrapper));
    }*/

    private SearchSourceBuilder copyQuery(final LambdaEsQueryWrapper<NodeRelatedEs> originalWrapper) {
        return dataMapper.getSearchSourceBuilder(originalWrapper);
    }

    private List<RelatedDataDTO> initDtoResult(List<NodeRelatedEs> list) {
        List<RelatedDataDTO> items = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (NodeRelatedEs es : list) {
                items.add(NodeRelatedEsDTOMapper.INSTANCE.copyToDTO(es));
            }
        }
        return items;
    }

    public List<RelatedDataDTO> findAllByDataNos(DataSearchDTO dto) {

        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = new LambdaEsQueryWrapper<>();

        wrapper.exists(NodeRelatedEs::getDatNo);
        wrapper.eq(StrUtil.isNotBlank(dto.getCreator()), NodeRelatedEs::getCreator, dto.getCreator());
        wrapper.in(CollUtil.isNotEmpty(dto.getDataNos()), NodeRelatedEs::getDatNo, dto.getDataNos());

        List<NodeRelatedEs> nodeRelatedEs = dataMapper.selectList(wrapper);
        return initDtoResult(nodeRelatedEs);
    }

    public EsPageInfo<RelatedDataDTO> findDataShareData(DataShareSearchVO searchVO) {
        final Pageable pageable = searchVO.initPageInfo();

        final String type = searchVO.getType();
        final String typeNo = searchVO.getTypeNo();

        AuthorizeType authorizeType = null;

        if (StrUtil.isNotBlank(type)) {

            final Optional<AuthorizeType> typeOptional = AuthorizeType.findByName(searchVO.getType());

            if (!typeOptional.isPresent() || StrUtil.isBlank(typeNo)) {
                return EsPageInfo.of(new ArrayList<>());
            }
            authorizeType = typeOptional.get();
        }

        final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = createNewEsQuery(authorizeType, typeNo);

        wrapper.exists(NodeRelatedEs::getDatNo);
        // 过滤条件
        wrapper.eq(StrUtil.isNotBlank(searchVO.getCreator()), NodeRelatedEs::getCreator, searchVO.getCreator())
                .eq(StrUtil.isNotBlank(searchVO.getSecurity()), NodeRelatedEs::getSecurity, searchVO.getSecurity())
                .like(StrUtil.isNotBlank(searchVO.getExpNo()), NodeRelatedEs::getExpNo, searchVO.getExpNo(), 1.0F)
                .like(StrUtil.isNotBlank(searchVO.getSapNo()), NodeRelatedEs::getSapNo, searchVO.getSapNo(), 1.0F)
                .eq(StrUtil.isNotBlank(searchVO.getRunNo()), NodeRelatedEs::getRunNo, searchVO.getRunNo())
                .eq(StrUtil.isNotBlank(searchVO.getDatNo()), NodeRelatedEs::getDatNo, searchVO.getDatNo())
                .eq(StrUtil.isNotBlank(searchVO.getAnalNo()), NodeRelatedEs::getAnalNo, searchVO.getAnalNo())
                .eq(StrUtil.isNotBlank(searchVO.getSapName()), NodeRelatedEs::getSapName, searchVO.getSapName())
                .eq(StrUtil.isNotBlank(searchVO.getSapType()), NodeRelatedEs::getSapType, searchVO.getSapType())
                .eq(StrUtil.isNotBlank(searchVO.getOrganism()), NodeRelatedEs::getOrganism, searchVO.getOrganism())
                .in(CollUtil.isNotEmpty(searchVO.getDataNos()), NodeRelatedEs::getDatNo, searchVO.getDataNos());

        // 添加排序条件
        final Sort sort = pageable.getSort();
        if (!sort.isEmpty()) {
            final Sort.Order order = sort.iterator().next();
            final String property = order.getProperty();
            wrapper.orderBy(true, order.isAscending(), property);
        }

        final EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, pageable.getPageSize());
        final List<RelatedDataDTO> relatedDataDTOS = initDtoResult(pageInfo.getList());

        final EsPageInfo<RelatedDataDTO> info = EsPageInfo.of(relatedDataDTOS);
        info.setTotal(pageInfo.getTotal());
        return info;
    }

    public Set<String> findSelectData(DataShareSearchVO searchVO) {

        final Optional<AuthorizeType> typeOptional = AuthorizeType.findByName(searchVO.getType());
        final String typeNo = searchVO.getTypeNo();

        if (!typeOptional.isPresent() || StrUtil.isBlank(typeNo)) {
            return new HashSet<>();
        }

        final AuthorizeType authorizeType = typeOptional.get();

        final String field = searchVO.getField();
        if (StrUtil.isBlank(field)) {
            return new LinkedHashSet<>();
        }
        try {
            String keyword = StrUtil.trimToNull(searchVO.getKeyword());
            if (StrUtil.isBlank(keyword)) {
                keyword = "";
            }

            final SFunction<NodeRelatedEs, ?> fieldFunc = initFieldFunc(field);

            // 查询指定字段值
            final LambdaEsQueryWrapper<NodeRelatedEs> wrapper = createNewEsQuery(authorizeType, typeNo);
            wrapper.exists(NodeRelatedEs::getDatNo);
            wrapper.eq(StrUtil.isNotBlank(searchVO.getCreator()), NodeRelatedEs::getCreator, searchVO.getCreator())
                    .eq(StrUtil.isNotBlank(searchVO.getSecurity()), NodeRelatedEs::getSecurity, searchVO.getSecurity())
                    .eq(StrUtil.isNotBlank(searchVO.getExpNo()), NodeRelatedEs::getExpNo, searchVO.getExpNo())
                    .eq(StrUtil.isNotBlank(searchVO.getSapNo()), NodeRelatedEs::getSapNo, searchVO.getSapNo())
                    .eq(StrUtil.isNotBlank(searchVO.getRunNo()), NodeRelatedEs::getRunNo, searchVO.getRunNo())
                    .eq(StrUtil.isNotBlank(searchVO.getDatNo()), NodeRelatedEs::getDatNo, searchVO.getDatNo())
                    .eq(StrUtil.isNotBlank(searchVO.getAnalNo()), NodeRelatedEs::getAnalNo, searchVO.getAnalNo())
                    .eq(StrUtil.isNotBlank(searchVO.getSapName()), NodeRelatedEs::getSapName, searchVO.getSapName())
                    .eq(StrUtil.isNotBlank(searchVO.getSapType()), NodeRelatedEs::getSapType, searchVO.getSapType())
                    .eq(StrUtil.isNotBlank(searchVO.getOrganism()), NodeRelatedEs::getOrganism, searchVO.getOrganism())
                    .in(CollUtil.isNotEmpty(searchVO.getDataNos()), NodeRelatedEs::getDatNo, searchVO.getDataNos());

            wrapper.like(fieldFunc, ReUtil.escape(keyword));
            // distinct底层使用的es collapse实现，注意数组字段无法使用collapse
            wrapper.distinct(fieldFunc);
            wrapper.select(fieldFunc);

            final EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, 1, 50);
            final List<NodeRelatedEs> list = pageInfo.getList();

            if (CollUtil.isNotEmpty(list)) {
                final Set<String> data = new LinkedHashSet<>();

                for (NodeRelatedEs es : list) {
                    Object valObj = fieldFunc.apply(es);
                    if (valObj instanceof Collection) {

                        final Collection jsonArray = (Collection) valObj;

                        if (CollUtil.isNotEmpty(jsonArray)) {
                            for (final Object o : jsonArray) {
                                final String valStr = o.toString();
                                if (StrUtil.containsIgnoreCase(valStr, keyword)) {
                                    data.add(valStr);
                                }
                            }
                        }
                    } else {
                        data.add(valObj.toString());
                    }
                }
                return data;
            } else {
                return new LinkedHashSet<>();
            }
        } catch (Exception e) {
            log.info("自动补全输入框查询出错", e);
            return new LinkedHashSet<>();
        }
    }

    /**
     * 高级查询获取字段函数
     *
     * @param field 前端传入的字段名称
     */
    public static SFunction<NodeRelatedEs, ?> initFieldFunc(final String field) {
        switch (field) {
            case "expNo":
                return NodeRelatedEs::getExpNo;
            case "expName":
                return NodeRelatedEs::getExpName;
            case "sapNo":
                return NodeRelatedEs::getSapNo;
            case "sapName":
                return NodeRelatedEs::getSapName;
            case "runNo":
                return NodeRelatedEs::getRunNo;
            case "analNo":
                return NodeRelatedEs::getAnalNo;
            case "datNo":
                return NodeRelatedEs::getDatNo;
            case "organism":
                return NodeRelatedEs::getOrganism;
            case "sapType":
                return NodeRelatedEs::getSapType;
            default:
                throw new ServiceException("Unknown field");
        }
    }

    public Boolean existPublicByDataNos(SecurityDTO dto) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(dto.getFiled(), dto.getTypeNo()))
                .must(QueryBuilders.existsQuery("datNo"))
                // data是Restricted、Public
                .mustNot(QueryBuilders.termQuery("security", SecurityEnum._private.getDesc()))
                .mustNot(QueryBuilders.termsQuery("datNo", dto.getDataNos())));
        Long count = dataMapper.selectCount(EsWrappers.lambdaQuery(NodeRelatedEs.class).setSearchSourceBuilder(searchSourceBuilder));
        return count != null && count != 0;
    }

    public EsPageInfo<RelatedDataDTO> findRelatedDataPage(RelatedEsSearchVO searchVO) {
        Pageable pageable = searchVO.initPageInfo();
        LambdaEsQueryWrapper<NodeRelatedEs> wrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);

        wrapper.in(CollUtil.isNotEmpty(searchVO.getProjNos()), NodeRelatedEs::getProjNo, searchVO.getProjNos());
        wrapper.in(CollUtil.isNotEmpty(searchVO.getExpNos()), NodeRelatedEs::getExpNo, searchVO.getExpNos());
        wrapper.in(CollUtil.isNotEmpty(searchVO.getSapNos()), NodeRelatedEs::getSapNo, searchVO.getSapNos());
        wrapper.like(StrUtil.isNotBlank(searchVO.getExpType()), NodeRelatedEs::getExpType, searchVO.getExpType());
        wrapper.like(StrUtil.isNotBlank(searchVO.getSapType()), NodeRelatedEs::getSapType, searchVO.getSapType());
        wrapper.like(StrUtil.isNotBlank(searchVO.getOrganism()), NodeRelatedEs::getOrganism, searchVO.getOrganism());
        wrapper.in(CollUtil.isNotEmpty(searchVO.getSecurity()), NodeRelatedEs::getSecurity, searchVO.getSecurity());
        wrapper.like(StrUtil.isNotBlank(searchVO.getDataType()), NodeRelatedEs::getDataType, searchVO.getDataType());

        // 对外api 额外添加的查询条件
        wrapper.in(CollUtil.isNotEmpty(searchVO.getRunNos()), NodeRelatedEs::getRunNo, searchVO.getRunNos());
        wrapper.in(CollUtil.isNotEmpty(searchVO.getDataNos()), NodeRelatedEs::getDatNo, searchVO.getDataNos());
        wrapper.in(CollUtil.isNotEmpty(searchVO.getAnalNos()), NodeRelatedEs::getAnalNo, searchVO.getAnalNos());
        wrapper.eq(StrUtil.isNotBlank(searchVO.getCreator()), NodeRelatedEs::getCreator, searchVO.getCreator());
        if (searchVO.getBeginTime() != null) {
            wrapper.ge(NodeRelatedEs::getUpdateDate, DateUtil.format(DateUtil.beginOfDay(searchVO.getBeginTime()), DatePattern.NORM_DATETIME_PATTERN));
        }

        if (searchVO.getEndTime() != null) {
            wrapper.le(NodeRelatedEs::getUpdateDate, DateUtil.format(DateUtil.endOfDay(searchVO.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
        }


        // 添加排序条件
        final Sort sort = pageable.getSort();
        if (!sort.isEmpty()) {
            final Sort.Order order = sort.iterator().next();
            final String property = order.getProperty();

            wrapper.orderBy(true, order.isAscending(), property);
        }
        EsPageInfo<NodeRelatedEs> pageInfo = dataMapper.pageQuery(wrapper, pageable.getPageNumber() + 1, pageable.getPageSize());
        List<RelatedDataDTO> relatedDataDTOS = initDtoResult(pageInfo.getList());

        EsPageInfo<RelatedDataDTO> info = EsPageInfo.of(relatedDataDTOS);
        info.setTotal(pageInfo.getTotal());
        return info;
    }
}

