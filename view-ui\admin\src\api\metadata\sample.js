import request from '@/utils/request';

const baseURL = '/system/metadata/sample';

// 查询用户拥有的Sample列表
export function listSample(params) {
  return request({
    url: `${baseURL}/listSample`,
    method: 'post',
    data: params,
  });
}

// 根据expNo查询用户的Sample信息
export function getSapInfo(sapNo) {
  return request({
    url: `${baseURL}/getByNo/${sapNo}`,
    method: 'get',
  });
}

// 保存编辑
export function editSap(data) {
  return request({
    url: `${baseURL}/edit`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// Sample删除预检查
export function sapDeleteCheck(sapNo) {
  return request({
    url: `${baseURL}/deleteCheck/${sapNo}`,
    method: 'get',
  });
}

export function sapBatchDeleteCheck(sapNos) {
  return request({
    url: `${baseURL}/batchDeleteCheck?sapNos=${sapNos.join(',')}`,
    method: 'get',
  });
}

export function batchDeleteSapAll(sapNos) {
  return request({
    url: `${baseURL}/batchDeleteAll?sapNos=${sapNos.join(',')}`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
  });
}

// 删除Sample所有以及相关联的
export function deleteSapAll(params) {
  return request({
    url: `${baseURL}/deleteAll`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 获取Sample的ExpType
export function getAuditedSapType() {
  return request({
    url: `${baseURL}/getAuditedSapType`,
    method: 'get',
  });
}

// 修改Sample及关联的数据的creator
export function updateSapCreator(params) {
  return request({
    url: `${baseURL}/updateCreator`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}
