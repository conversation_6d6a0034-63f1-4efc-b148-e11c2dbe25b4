<template>
  <el-dialog
    v-model="showDialog"
    :title="`${baseType} Attributes (${currType})`"
    width="1000"
    class="dialog radius-14"
    @close="() => {}"
  >
    <el-row class="exist-form mb-1">
      <el-col :span="12" class="font-bold">
        Attribute Name
        <el-select
          v-model="selectName"
          class="radius-8"
          style="width: 250px"
          filterable
          clearable
          @change="nameChange"
        >
          <el-option
            v-for="item in attrList"
            :key="item.id"
            :label="item.attributesField"
            :value="item.attributesField"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="12">
        <fill-tip :recommend="true"></fill-tip>
      </el-col>
    </el-row>
    <el-table
      :data="filterAttrList"
      stripe
      border
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      height="650"
    >
      <el-table-column
        prop="attributesField"
        label="Attribute Name"
        min-width="70"
      >
        <template #default="scope">
          <span v-if="scope.row.required === 'required'" class="text-danger"
            >*</span
          >
          <el-icon
            v-if="scope.row.required === 'recommend'"
            size="12"
            color="#3A78E8"
          >
            <InfoFilled />
          </el-icon>
          {{ scope.row.attributesField }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="Description">
        <template #default="scope">
          <span v-html="scope.row.description"></span>
        </template>
      </el-table-column>
      <el-table-column prop="valueFormat" label="Value Format" min-width="120">
        <template #default="scope">
          <el-select
            v-if="
              scope.row.attributesField === 'organism' ||
              scope.row.dataSource === 'Taxonomy'
            "
            v-model="taxonomy"
            clearable
            filterable
            remote
            reserve-keyword
            :fit-input-width="false"
            placeholder="Please input search taxonomy"
            :remote-method="taxonomyQuerySearch"
            :loading="taxonomyLoading"
          >
            <el-option
              v-for="taxonomyItem in taxonomyOptions"
              :key="`taxonomyOption-${taxonomyItem.label} [${taxonomyItem.value}]`"
              :label="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
              :value="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
              :title="`Lineage: ${trimStr(taxonomyItem.title)}`"
            />

            <!--<el-option
              v-for="item in taxonomyOptions"
              :key="'taxonomyOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />-->
          </el-select>
          <el-select
            v-else-if="
              scope.row.attributesField === 'platform' &&
              currType === 'Microarray'
            "
            v-model="platform"
            filterable
            remote
            reserve-keyword
            placeholder="Please Input Search"
            :remote-method="platformQuerySearch"
            :loading="platformLoading"
          >
            <el-option
              v-for="item in platformOptions"
              :key="'platformOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.attributesField === 'disease_name' &&
              (currType === 'Human' ||
                currType === 'Animalia' ||
                currType === 'Cell line')
            "
            v-model="disease"
            class="w-100"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="Please input search disease"
            :remote-method="diseaseQuerySearch"
            :loading="diseaseLoading"
          >
            <el-option
              v-for="item in diseaseOptions"
              :key="'diseaseOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.dataSource === 'host_biome' ||
              scope.row.dataSource === 'non_host_biome' ||
              scope.row.dataSource === 'env_biome' ||
              scope.row.dataSource === 'env_biome_water'
            "
            v-model="biome[scope.row.attributesField]"
            class="w-100"
            filterable
            clearable
            :fit-input-width="false"
            remote
            reserve-keyword
            placeholder="Please input search"
            :remote-method="
              query => {
                querySearchBiomeAsync(query, scope.row.dataSource);
              }
            "
            :loading="biomeLoading[scope.row.dataSource]"
          >
            <el-option
              v-for="item in biomeOptions[scope.row.dataSource]"
              :key="
                scope.row.attributesField +
                scope.row.dataSource +
                'Option-' +
                item.value
              "
              :title="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.dataType === 'Select' &&
              scope.row.valueRange.length > 10
            "
          >
            <el-option
              v-for="(item, idx) in scope.row.valueRange"
              :key="idx"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <div
            v-else-if="
              scope.row.dataType === 'Select' &&
              scope.row.valueRange.length <= 10
            "
          >
            <span>{{ scope.row.valueRange.join(' | ') }}</span>
          </div>
          <el-select
            v-else-if="scope.row.dataType === 'Select2'"
            filterable
            clearable
            placeholder="Select"
          >
            <el-option-group
              v-for="group in scope.row.valueRange"
              :key="group.parent_name"
              :label="group.parent_name"
            >
              <el-option
                v-for="option in group.value_array"
                :key="group.parent_name + option"
                :label="option"
                :value="option"
              />
            </el-option-group>
          </el-select>
          <span v-else>{{ scope.row.valueFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
  import FillTip from '@/views/submit/components/FillTip.vue';
  import { computed, getCurrentInstance, ref, toRefs, watch } from 'vue';
  import {
    findBiomeLike,
    findDiseaseLike,
    findPlatformLike,
    findTaxonomyLike,
  } from '@/api/search';
  import { trimStr } from '@/utils';

  const { proxy } = getCurrentInstance();

  let props = defineProps({
    showDialog: {
      type: Boolean,
      default: false,
    },
    baseType: {
      type: String,
      default: '',
    },
    currType: {
      type: String,
      default: '',
    },
    attrList: {
      type: Object,
      default: () => {},
    },
  });

  let { baseType, currType, attrList } = toRefs(props);

  let filterAttrList = ref([]);
  let selectName = ref('');

  function nameChange() {
    if (selectName.value) {
      filterAttrList.value = attrList.value.filter(
        it => it.attributesField.indexOf(selectName.value) !== -1,
      );
    } else {
      filterAttrList.value = attrList.value;
    }
  }

  watch(
    () => props.showDialog,
    () => {
      selectName.value = '';
      nameChange();
    },
  );

  const showDialog = computed({
    get() {
      return props.showDialog;
    },
    set(val) {
      proxy.$emit('update:showDialog', val);
    },
  });

  let taxonomy = ref('');
  let taxonomyOptions = ref([]);
  let taxonomyLoading = ref(false);

  /** 查询ES中的Taxonomy */
  const taxonomyQuerySearch = query => {
    taxonomyLoading.value = true;
    findTaxonomyLike({ keyword: query })
      .then(response => {
        taxonomyOptions.value = response.data;
      })
      .finally(() => {
        taxonomyLoading.value = false;
      });
  };

  let platform = ref('');
  let platformOptions = ref([]);
  let platformLoading = ref(false);

  /** 查询ES中的Platform */
  const platformQuerySearch = query => {
    platformLoading.value = true;
    findPlatformLike({ keyword: query })
      .then(response => {
        platformOptions.value = response.data;
      })
      .finally(() => {
        platformLoading.value = false;
      });
  };

  let disease = ref('');
  const diseaseOptions = ref([]);
  const diseaseLoading = ref(false);

  /** 查询ES中的Disease */
  const diseaseQuerySearch = query => {
    diseaseLoading.value = true;
    findDiseaseLike({ keyword: query })
      .then(response => {
        diseaseOptions.value = response.data;
      })
      .finally(() => {
        diseaseLoading.value = false;
      });
  };

  let biome = ref({});
  const biomeOptions = ref({});
  const biomeLoading = ref({
    host_biome: false,
    non_host_biome: false,
    env_biome: false,
    env_biome_water: false,
  });
  /** 查询ES中的Biome */
  const querySearchBiomeAsync = (query, type) => {
    biomeLoading.value[type] = true;
    findBiomeLike({ type: type, keyword: query })
      .then(response => {
        biomeOptions.value[type] = response.data;
      })
      .finally(() => {
        biomeLoading.value[type] = false;
      });
  };
</script>

<style scoped lang="scss">
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
