package org.biosino.system.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.HttpStatus;
import org.biosino.common.core.enums.FtpFileLogStatus;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.FtpFileLogQueryDTO;
import org.biosino.system.mapper.FtpFileLogMapper;
import org.biosino.system.service.FtpFileLogService;
import org.biosino.system.service.meta.BaseService;
import org.biosino.system.vo.metadata.FtpFileLogVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FtpFileLogServiceImpl extends ServiceImpl<FtpFileLogMapper, FtpFileLog> implements FtpFileLogService {

    private final BaseService baseService;
    private final RedisService redisService;

    @Override
    public long countFtpFileLogNum(String creator, String path) {
        Wrapper<FtpFileLog> queryWrapper = Wrappers.<FtpFileLog>lambdaQuery()
                .eq(FtpFileLog::getCreator, creator)
                .likeRight(FtpFileLog::getPath, path)
                .in(FtpFileLog::getStatus, FtpFileLogStatus.includeExsistedStatus());
        long count = this.count(queryWrapper);
        return count;
    }

    @Override
    public void deleteByCreatorAndPath(String creator, String path) {
        Wrapper<FtpFileLog> queryWrapper = Wrappers.<FtpFileLog>lambdaQuery()
                .eq(FtpFileLog::getCreator, creator)
                .likeRight(FtpFileLog::getPath, path)
                .in(FtpFileLog::getStatus, FtpFileLogStatus.includeExsistedStatus());
        this.remove(queryWrapper);
    }

    @Override
    public long checkFtpFileLog(String email, String path) {

        long dbCount = getFtpFileLogNum(email, path);
        long storageCount = getFtpFileCount(email, path);

        if (dbCount != storageCount) {
            throw new ServiceException("inconsistent! dbCount = " + dbCount + ", storageCount = " + storageCount);
        }
        return dbCount;
    }

    @Override
    public long getFtpFileLogNum(String email, String path) {
        // 获取member顺便校验email
        MemberDTO member = baseService.getMemberInfoByEmail(email);

        String targetPath = MyFileUtils.changeToLinuxSeparator(SecurityUtils.getMemberFtpPath(member)
                + File.separator + path);

        return this.countFtpFileLogNum(member.getId(), targetPath);
    }

    @Override
    public long getFtpFileCount(String email, String path) {
        // 获取member顺便校验email
        MemberDTO member = baseService.getMemberInfoByEmail(email);

        String targetPath = MyFileUtils.changeToLinuxSeparator(SecurityUtils.getMemberFtpPath(member)
                + File.separator + path);
        File file = FileUtil.file(targetPath);
        if (!file.exists()) {
            throw new ServiceException("File or directory does not exists! path = " + path);
        }
        int size = FileUtil.loopFiles(file).size();
        if (size == 0) {
            throw new ServiceException("File or directory is empty! path = " + path);
        }
        return size;
    }

    @Override
    public synchronized List<FtpFileLogVO> startSync(String email, String path, boolean readMd5) {
        String LOCK_KEY = "ftp_preload_file_sync_";

        String lockKey = LOCK_KEY + email;
        if (redisService.hasKey(lockKey)) {
            throw new ServiceException("The current email has a synchronization task request being processed, please try again later.");
        }

        try {
            long timeout = 12L;
            redisService.setCacheObject(lockKey, true, timeout, TimeUnit.HOURS);

            // 校验path
            getFtpFileCount(email, path);

            // 获取member顺便校验email
            MemberDTO member = baseService.getMemberInfoByEmail(email);

            String targetPath = MyFileUtils.changeToLinuxSeparator(SecurityUtils.getMemberFtpPath(member)
                    + File.separator + path);
            File file = FileUtil.file(targetPath);

            if (!file.exists()) {
                throw new ServiceException("File or directory does not exists! path = " + path);
            }

            // 删除数据
            this.deleteByCreatorAndPath(member.getId(), targetPath);

            // 重新同步数据
            List<FtpFileLog> saveList = new ArrayList<>();
            AtomicInteger count = new AtomicInteger();
            FileUtil.loopFiles(file).forEach(x -> {
                if (x.isFile()) {

                    FtpFileLog ftpFileLog = new FtpFileLog();
                    ftpFileLog.setId(IdUtils.getShortUUID());
                    ftpFileLog.setName(x.getName());
                    String fileAbsolutePath = FilenameUtils.normalizeNoEndSeparator(x.getAbsolutePath(), true);

                    ftpFileLog.setPath(fileAbsolutePath);
                    ftpFileLog.setSize(x.length());
                    ftpFileLog.setStatus(FtpFileLogStatus.unchecked.getStatus());

                    // 是否将MD5文件对应的内容读取到文件内
                    if (readMd5) {
                        File md5File = FileUtil.file(fileAbsolutePath + ".md5");
                        try {
                            if (FileUtil.exist(md5File)) {
                                String md5 = NodeUtils.readMd5(md5File);
                                ftpFileLog.setMd5(md5);
                                ftpFileLog.setMd5FileContent(md5);
                            }
                        } catch (Exception e) {
                            log.error("{} : {}", e.getMessage(), md5File.getAbsolutePath());
                        }
                    }

                    ftpFileLog.setCreateTime(new Date(x.lastModified()));
                    ftpFileLog.setCreator(member.getId());

                    saveList.add(ftpFileLog);
                    count.getAndIncrement();
                    if (saveList.size() > 2000) {
                        this.saveBatch(saveList);
                        // !important 清空saveList
                        saveList.clear();
                    }
                }
            });
            // !important 保存剩余的
            if (CollUtil.isNotEmpty(saveList)) {
                this.saveBatch(saveList);
            }
            if (count.get() > 2000) {
                return new ArrayList<>();
            }

            List<FtpFileLogVO> result = saveList.stream().map(x -> {
                FtpFileLogVO vo = new FtpFileLogVO();
                BeanUtil.copyProperties(x, vo);
                vo.setMd5FileStatus(NodeUtils.getFileMd5Status(x.getPath()));
                vo.setReadableFileSize(FileUtil.readableFileSize(x.getSize()));
                return vo;
            }).collect(Collectors.toList());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            redisService.deleteObject(lockKey);
        }

    }

    @Override
    public TableDataInfo selectFtpFileLogList(FtpFileLogQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getCreatorEmail())) {
            MemberDTO member = baseService.getMemberInfoByEmail(queryDTO.getCreatorEmail());
            queryDTO.setCreator(member.getId());
        }

        Wrapper<FtpFileLog> qw = Wrappers.<FtpFileLog>lambdaQuery().eq(StrUtil.isNotBlank(queryDTO.getCreator()), FtpFileLog::getCreator, queryDTO.getCreator())
                .like(StrUtil.isNotBlank(queryDTO.getPath()), FtpFileLog::getPath, queryDTO.getPath())
                .eq(StrUtil.isNotBlank(queryDTO.getStatus()), FtpFileLog::getStatus, queryDTO.getStatus())
                .in(FtpFileLog::getStatus, FtpFileLogStatus.allStatusExcludeAfterUploaded())
                .ge(queryDTO.getBeginTime() != null, FtpFileLog::getCreateTime, queryDTO.getBeginTime() != null ? DateUtil.beginOfDay(queryDTO.getBeginTime()) : null)
                .le(queryDTO.getEndTime() != null, FtpFileLog::getCreateTime, queryDTO.getEndTime() != null ? DateUtil.endOfDay(queryDTO.getEndTime()) : null);

        List<FtpFileLog> list = this.list(qw);

        List<String> creators = list.stream().map(FtpFileLog::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = baseService.getMemberIdToEmailMap(creators);

        List<FtpFileLogVO> result = list.stream().map(x -> {
            FtpFileLogVO vo = new FtpFileLogVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getSize()));
            vo.setMd5FileStatus(NodeUtils.getFileMd5Status(x.getPath()));
            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        }).collect(Collectors.toList());

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(result);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFtpFileByIds(List<String> ids) {
        List<FtpFileLog> ftpFileLogs = this.listByIds(ids);

        for (FtpFileLog ftpFileLog : ftpFileLogs) {
            try {
                // 删除文件
                FileUtil.del(ftpFileLog.getPath());
                ftpFileLog.setStatus(FtpFileLogStatus.deleted.getStatus());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 批量修改数据，将状态修改为删除
        this.saveOrUpdateBatch(ftpFileLogs);
    }
}
