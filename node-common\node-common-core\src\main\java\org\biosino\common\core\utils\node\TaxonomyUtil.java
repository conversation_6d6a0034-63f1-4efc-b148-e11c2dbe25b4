package org.biosino.common.core.utils.node;

import cn.hutool.core.util.StrUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */
public class TaxonomyUtil {
    public static final String TAX_ID_PRE = "Taxonomy ID:";
    private static final Pattern TAX_ID_PATTERN = Pattern.compile("\\[(\\d+)\\]");

    /**
     * 解析Taxonomy名称和id
     * 正确格式:
     * root [Taxonomy ID:1]
     * root [1]
     */
    public static TaxonomyEsDTO parseTaxInfo(final String organism) {
        String input = StrUtil.trimToNull(organism);
        if (input == null) {
            return null;
        }
        String taxId = null;
        String taxName;
        final String idStartFlag = "[";
        final int idStartIndex = input.lastIndexOf(idStartFlag);
        if (input.endsWith("]") && idStartIndex > 0) {
            taxName = StrUtil.trimToNull(input.substring(0, idStartIndex));

            input = input.substring(idStartIndex).replace("：", ":").toLowerCase().replaceAll("\\[\\s*taxonomy id:\\s*", idStartFlag);
            // 定义正则表达式，匹配中括号内的数字
            final Matcher matcher = TAX_ID_PATTERN.matcher(input);
            while (matcher.find()) {
                taxId = matcher.group(1);
            }
        } else {
            taxName = StrUtil.trimToNull(organism);
        }

        if (taxName == null) {
            return null;
        }

        final TaxonomyEsDTO taxonomyNode = new TaxonomyEsDTO();
        taxonomyNode.setScientificName(taxName);
        if (taxId != null) {
            taxonomyNode.setTaxId(taxId);
        }

        return taxonomyNode;
    }
}
