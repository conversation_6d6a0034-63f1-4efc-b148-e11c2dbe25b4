package org.biosino.system.service.meta;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONWriter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.msg.VerificationCreateMsg;
import org.biosino.system.dto.dto.DataQueryDTO;
import org.biosino.system.dto.dto.ModifyIntegrityDTO;
import org.biosino.system.dto.dto.export.DataExportDTO;
import org.biosino.system.repository.*;
import org.biosino.system.service.FtpFileLogService;
import org.biosino.system.vo.metadata.DataListVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/6
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataService extends BaseService {

    private final MessageSender messageSender;

    private final DataRepository dataRepository;

    private final RunRepository runRepository;

    private final ExperimentRepository experimentRepository;

    private final AnalysisRepository analysisRepository;

    private final FtpFileLogService ftpFileLogService;

    private final Md5OpLogRepository md5OpLogRepository;

    private final RedisService redisService;

    public Page<DataListVO> listUnarchived(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);

        queryDTO.setArchived(ArchiveEnum.no.name());

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        List<String> creators = page.getContent().stream().map(Data::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
            // 设置creatorEmail
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
            return vo;
        });
        return result;
    }

    public Page<DataListVO> listRawData(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);

        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistRun(true);

        if (CollUtil.isNotEmpty(queryDTO.getExpNos()) || CollUtil.isNotEmpty(queryDTO.getSapNos())) {
            List<String> runNos = runRepository.findAllRunNosByExpNosAndSapNos(queryDTO.getExpNos(), queryDTO.getSapNos());
            queryDTO.setRunNos(runNos);
        }

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        List<String> runNos = page.getContent().stream()
                .map(Data::getRunNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取运行信息
        Map<String, Run> runNoToRunMap = runRepository.findAllByRunNoIn(runNos)
                .stream()
                .collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> expNos = runNoToRunMap.values().stream()
                .map(Run::getExpNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取实验信息
        Map<String, Experiment> expNoToExperimentMap = experimentRepository.findAllByExpNoIn(expNos)
                .stream()
                .collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> creators = page.getContent().stream()
                .map(Data::getCreator)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));

            Run run = runNoToRunMap.get(x.getRunNo());
            if (run != null) {
                vo.setRunNo(run.getRunNo());
                vo.setExpNo(run.getExpNo());
                vo.setSapNo(run.getSapNo());

                Experiment experiment = expNoToExperimentMap.get(run.getExpNo());
                if (experiment != null) {
                    vo.setProjNo(experiment.getProjectNo());
                    vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));
                }
            }

            return vo;
        });

        return result;
    }

    public Page<DataListVO> listAnalysisData(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);

        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistAnalysis(true);

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        // 提取所有分析编号
        List<String> analysisNos = page.getContent().stream()
                .map(Data::getAnalNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取分析信息
        Map<String, Analysis> analysisNoToAnalysisMap = analysisRepository.findAllByAnalNoIn(analysisNos)
                .stream()
                .collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> creators = page.getContent().stream()
                .map(Data::getCreator)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));

            Analysis analysis = analysisNoToAnalysisMap.get(x.getAnalNo());
            if (analysis != null) {
                vo.setAnalNo(analysis.getAnalysisNo());
                vo.setAnalName(analysis.getName());
                vo.setAnalType(analysis.getAnalysisType());
                vo.setCustomAnalType(analysis.getCustomAnalysisType());
            }

            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));

            return vo;
        });

        return result;
    }

    public Page<DataListVO> listPrivateData(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);
        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setSecurity(CollUtil.newArrayList(SecurityEnum._private.getDesc()));

        Page<Data> page = dataRepository.findDataPage(queryDTO);

        List<String> runNos = page.getContent().stream()
                .map(Data::getRunNo).filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取运行信息
        Map<String, Run> runNoToRunMap = runRepository.findAllByRunNoIn(runNos)
                .stream()
                .collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> expNos = runNoToRunMap.values().stream()
                .map(Run::getExpNo)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取实验信息
        Map<String, Experiment> expNoToExperimentMap = experimentRepository.findAllByExpNoIn(expNos)
                .stream()
                .collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> creators = page.getContent().stream()
                .map(Data::getCreator)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 提取所有分析编号
        List<String> analysisNos = page.getContent().stream()
                .map(Data::getAnalNo).filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取分析信息
        Map<String, Analysis> analysisNoToAnalysisMap = analysisRepository.findAllByAnalNoIn(analysisNos)
                .stream()
                .collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        Page<DataListVO> result = page.map(x -> {
            DataListVO vo = new DataListVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
            vo.setCreatorEmail(memberIdToEmailMap.get(x.getCreator()));

            if (StrUtil.isNotBlank(x.getRunNo())) {
                Run run = runNoToRunMap.get(x.getRunNo());
                if (run != null) {
                    vo.setRunNo(run.getRunNo());
                    vo.setExpNo(run.getExpNo());
                    vo.setSapNo(run.getSapNo());

                    Experiment experiment = expNoToExperimentMap.get(run.getExpNo());
                    if (experiment != null) {
                        vo.setProjNo(experiment.getProjectNo());
                    }
                }
            }
            if (StrUtil.isNotBlank(x.getAnalNo())) {
                Analysis analysis = analysisNoToAnalysisMap.get(x.getAnalNo());
                if (analysis != null) {
                    vo.setAnalNo(analysis.getAnalysisNo());
                    vo.setAnalName(analysis.getName());
                    vo.setAnalType(analysis.getAnalysisType());
                    vo.setCustomAnalType(analysis.getCustomAnalysisType());
                }
            }

            return vo;
        });


        return result;
    }

    public void deleteByDataNos(List<String> dataNos) {
        dataRepository.updateToDeleteAllByDatNoIn(dataNos);
        for (String dataNo : dataNos) {
            updateEsData(AuthorizeType.data.name(), dataNo);
        }
    }

    public String checkPath4AllocDataNoPath(String path) {
        /*记录当前路径下有多少个文件需要分配Data ID*/
        long fileNum = 0L;

        path = MyFileUtils.changeToLinuxSeparator(path);

        File file = FileUtil.file(DirConstants.DATA_HOME, path);

        if (!file.exists()) {
            // 不存在文件或者目录
            return StrUtil.format("File or directory does not exists! path = {}", path);
        }
        if (!file.canRead()) {
            return StrUtil.format("File cannot read! path = {}", path);
        }
        if (file.isHidden()) {
            return StrUtil.format("Hidden file. path = {}", path);
        }

        String fullPath = file.getAbsolutePath();
        if (file.isFile()) {
            String md5FilePath = fullPath + ".md5";
            File md5File = FileUtil.file(md5FilePath);
            String md5FileRelativePath = MyFileUtils.absoluteToRelativePath(md5FilePath, DirConstants.DATA_HOME);
            if (!md5File.exists()) {
                return StrUtil.format("md5 file not exists! path = {}", md5FileRelativePath);
            } else if (md5File.isHidden()) {
                return StrUtil.format("md5 file is hidden! md5FilePath = {}", md5FileRelativePath);
            } else if (!md5File.canRead()) {
                return StrUtil.format("md5 file cannot read! path = {}", md5FileRelativePath);
            } else if (md5File.length() == 0) {
                return StrUtil.format("md5 file is empty! md5FilePath = {}", md5FileRelativePath);
            } else {
                try {
                    // 判断文件内容是否正确
                    NodeUtils.readMd5(md5File);
                } catch (ServiceException e) {
                    return StrUtil.format("md5 file is content error! md5FilePath = {}", md5FileRelativePath);
                }
            }
            fileNum++;
        } else if (file.isDirectory()) {
            File[] fileList = file.listFiles();
            List<String> errorMsg = new ArrayList<>();
            if (fileList == null || fileList.length == 0) {
                return StrUtil.format("There is no file in this path! path = {}", path);
            }
            for (File fileItem : fileList) {
                String fileItemPath = fileItem.getAbsolutePath();
                String fileRelativePath = MyFileUtils.absoluteToRelativePath(fileItemPath, DirConstants.DATA_HOME);
                if (!fileItem.canRead()) {
                    errorMsg.add(StrUtil.format("File cannot read! path = {}", fileRelativePath));
                    continue;
                }
                if (fileItem.isHidden() || fileItem.isDirectory()) {
                    continue;
                }
                if (!fileItemPath.endsWith(".md5")) {
                    /*确认MD5文件是否存在*/
                    String md5FilePath = fileItemPath + ".md5";
                    File md5File = FileUtil.file(md5FilePath);
                    String md5FileRelativePath = MyFileUtils.absoluteToRelativePath(md5FilePath, DirConstants.DATA_HOME);
                    if (!md5File.exists()) {
                        errorMsg.add(StrUtil.format("md5 file not exists! md5FilePath = {}", md5FileRelativePath));
                    } else if (md5File.isHidden()) {
                        errorMsg.add(StrUtil.format("md5 file is hidden! md5FilePath = {}", md5FileRelativePath));
                    } else if (!md5File.canRead()) {
                        errorMsg.add(StrUtil.format("md5 file can not read! md5FilePath = {}", md5FileRelativePath));
                    } else if (md5File.length() == 0) {
                        errorMsg.add(StrUtil.format("md5 file is empty! md5FilePath = {}", md5FileRelativePath));
                    } else {
                        try {
                            // 判断文件内容是否正确
                            NodeUtils.readMd5(md5File);
                        } catch (ServiceException e) {
                            errorMsg.add(StrUtil.format("md5 file is content error! md5FilePath = {}", md5FileRelativePath));
                        }
                    }
                    fileNum++;
                }
            }
            if (CollUtil.isNotEmpty(errorMsg)) {
                return CollUtil.join(errorMsg, "\n");
            }

        }

        return String.valueOf(fileNum);
    }

    public synchronized List<DataListVO> startAlloc(String path, String email) {
        // 校验path
        String msg = checkPath4AllocDataNoPath(path);
        if (!NumberUtil.isNumber(msg)) {
            throw new ServiceException("Check Path Error!");
        }
        // 校验email
        MemberDTO memberDTO = getMemberInfoByEmail(email);
        String memberId = memberDTO.getId();

        File file = FileUtil.file(DirConstants.DATA_HOME, path);

        List<Data> dataList = new ArrayList<>();

        if (file.isFile()) {
            Data data = obtainData(file, memberId);
            dataList.add(data);
        } else if (file.isDirectory()) {

            File[] files = file.listFiles();

            for (File fileItem : files) {
                String fileItemPath = fileItem.getAbsolutePath();

                // 如果数据不可读就跳过
                if (!fileItem.canRead() || fileItem.isHidden() || fileItem.isDirectory()) {
                    continue;
                }
                if (!fileItemPath.endsWith(".md5")) {
                    Data data = obtainData(fileItem, memberId);
                    dataList.add(data);
                }
            }
        }

        if (CollUtil.isNotEmpty(dataList)) {
            // 需要新增的dataList
            List<Data> insertDataList = dataList.stream().filter(x -> StrUtil.isBlank(x.getDatNo())).collect(Collectors.toList());
            List<Data> insertedDataList = dataRepository.saveAll(insertDataList);
            for (Data data : insertedDataList) {
                Data tempData = BeanUtil.copyProperties(data, Data.class, "tempData");
                data.setTempData(tempData);
            }
            dataRepository.saveAll(insertedDataList);

            // 如果是已经存在dataNo，根据情况更新tempData
            List<Data> updateDataList = dataList.stream().filter(x -> StrUtil.isNotBlank(x.getDatNo())).collect(Collectors.toList());
            for (Data data : updateDataList) {
                if (data.getTempData() != null) {
                    Data tempData = BeanUtil.copyProperties(data, Data.class, "tempData");
                    data.setTempData(tempData);
                }
            }
            List<Data> updatedDataList = dataRepository.saveAll(updateDataList);

            List<Data> allDataList = new ArrayList<>();
            allDataList.addAll(insertedDataList);
            allDataList.addAll(updatedDataList);

            List<DataListVO> result = allDataList.stream().map(x -> {
                DataListVO vo = new DataListVO();
                BeanUtil.copyProperties(x, vo);
                vo.setReadableFileSize(FileUtil.readableFileSize(x.getFileSize()));
                vo.setCreatorEmail(memberDTO.getEmail());
                return vo;
            }).collect(Collectors.toList());
            return result;
        }
        return null;
    }

    private Data obtainData(File file, String memberId) {
        String filePath = file.getAbsolutePath();

        String md5FilePath = filePath + ".md5";
        String md5Content = NodeUtils.readMd5(md5FilePath);
        String relativepath = MyFileUtils.absoluteToRelativePath(filePath, DirConstants.DATA_HOME);

        // 查询数据库里是否已经有这条记录，如果有，只更新一下md5
        Optional<Data> optional = dataRepository.findFirstByFilePath(relativepath);

        if (optional.isPresent()) {
            Data data = optional.get();
            data.setMd5(md5Content);
            data.setFileSize(FileUtil.size(file));
            return data;
        }

        Data data = new Data();
        data.setId(IdUtil.objectId());
        data.setName(file.getName());
        data.setFileName(file.getName());
        data.setFileSize(FileUtil.size(file));
        data.setFilePath(relativepath);
        data.setUploadType(UploadType.transfer.name());
        data.setDataType(FileTypeUtils.getDataTypeByName(file.getName()));

        data.setSecurity(SecurityEnum._private.getDesc());
        data.setCreator(memberId);

        Date now = new Date();
        data.setCreateDate(now);
        data.setUpdateDate(now);
        data.setArchived(ArchiveEnum.no.name());
        data.setOwnership(OwnershipEnum.self_support.getDesc());
        data.setSourcePath(null);

        data.setMd5(md5Content);

        return data;
    }


    public List<String> verifyFtpFile(List<String> ids) {
        String LOCK_KEY = "system_verification_task_key";
        if (redisService.hasKey(LOCK_KEY)) {
            throw new ServiceException("You already have a verification request being processed, please try again later!");
        }

        try {
            long timeout = 12L;
            redisService.setCacheObject(LOCK_KEY, true, timeout, TimeUnit.HOURS);

            List<String> errMsgs = new ArrayList<>();
            for (String id : ids) {
                // 校验选择的文件和对应的md5是否存在
                String msg = verifyFtpFileExist(id);
                if (StrUtil.isNotBlank(msg)) {
                    errMsgs.add(msg);
                } else {
                    FtpFileLog ftpFileLog = ftpFileLogService.getById(id);
                    String filePath = ftpFileLog.getPath();
                    // 发送md5校验的消息给task服务
                    VerificationCreateMsg checkMd5Msg = new VerificationCreateMsg(ftpFileLog.getId(), ftpFileLog.getCreator(), filePath);
                    messageSender.sendDelayMsg("verification_create_key", checkMd5Msg);
                    ftpFileLog.setStatus(FtpFileLogStatus.queuing.getStatus());
                    ftpFileLogService.saveOrUpdate(ftpFileLog);
                    // 保存操作日志
                    Optional<Md5OpLog> optional = md5OpLogRepository.findFirstByFilePath(filePath);
                    Md5OpLog md5OpLog;
                    if (!optional.isPresent()) {
                        md5OpLog = new Md5OpLog();
                        md5OpLog.setCreateDate(new Date());
                    } else {
                        md5OpLog = optional.get();
                    }
                    md5OpLog.setFilePath(filePath);
                    md5OpLog.setReplyDate(new Date());
                    md5OpLog.setStatus("init");
                    md5OpLogRepository.save(md5OpLog);
                }
            }
            return errMsgs;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            redisService.deleteObject(LOCK_KEY);
        }
    }

    public List<String> checkFtpFile(List<String> ids) {
        List<String> errMsgs = new ArrayList<>();
        for (String id : ids) {
            String msg = verifyFtpFileExist(id);
            if (StrUtil.isNotBlank(msg)) {
                errMsgs.add(msg);
            }
        }
        return errMsgs;
    }

    private String verifyFtpFileExist(String id) {
        FtpFileLog ftpFileLog = ftpFileLogService.getById(id);
        // 查询数据库中是否存在对应的记录
        if (ftpFileLog == null) {
            return StrUtil.format("{}  verify failed. Cause:  file does not exists!");
        }
        String filePath = ftpFileLog.getPath();
        File file = new File(filePath);
        // 如果文件不存在
        if (!file.exists()) {
            log.info("Err in verifyUploadedFtpHomeFile : ftpFile does not exists! path = {}", filePath);
            return StrUtil.format("{}  verify failed. Cause:  file does not exists!", filePath);
        }
        File md5File = new File(filePath + ".md5");
        // 如果对应的md5文件不存在
        if (!md5File.exists()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File does not exists! path = {}", filePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File does not exists!", filePath);
        }
        // md5文件内容校验已放在node-sftp中处理
        // 如果对应的md5不是文件
        if (!md5File.isFile()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File is not a file! path = {}", filePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File is not a file!", filePath);
        }
        // 如果对应的md5文件不可读
        if (!md5File.canRead()) {
            log.info("Err in verifyUploadedFtpHomeFile : md5File can not read! path = {}", filePath);
            return StrUtil.format("{}  verify failed. Cause:  md5File can not read!", filePath);
        }

        try {
            // 读取md5
            ftpFileLog.setMd5FileContent(NodeUtils.readMd5(md5File));
        } catch (Exception e) {
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return StrUtil.format(md5File + " content error! ");
        }

        ftpFileLogService.updateById(ftpFileLog);


        return null;
    }

    public void batchModifyIntegrity(ModifyIntegrityDTO modifyIntegrityDTO) {
        dataRepository.updateCompleteByDatNoIn(modifyIntegrityDTO.getDataNos(), modifyIntegrityDTO.getComplete());
    }

    public String getMemberFtpHomePath(String creatorEmail) {
        if (StrUtil.isBlank(creatorEmail)) {
            throw new ServiceException("email can not be blank!");
        }
        MemberDTO member = getMemberInfoByEmail(creatorEmail);
        String memberFtpPath = SecurityUtils.getMemberFtpPath(member);
        // 如果目录不存在则创建目录
        if (!FileUtil.exist(memberFtpPath)) {
            FileUtil.mkdir(memberFtpPath);
        }
        return memberFtpPath;
    }

    public File exportRawData(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);

        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistRun(true);

        if (CollUtil.isNotEmpty(queryDTO.getExpNos()) || CollUtil.isNotEmpty(queryDTO.getSapNos())) {
            List<String> runNos = runRepository.findAllRunNosByExpNosAndSapNos(queryDTO.getExpNos(), queryDTO.getSapNos());
            queryDTO.setRunNos(runNos);
        }
        MongoPagingIterator<Data> iterator = dataRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "data.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Data> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Data data : next) {
                    DataExportDTO item = new DataExportDTO();
                    BeanUtils.copyProperties(data, item);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }

    public File exportAnalysisData(DataQueryDTO queryDTO) {
        setDataQueryCreator(queryDTO);

        queryDTO.setArchived(ArchiveEnum.yes.name());
        queryDTO.setExistAnalysis(true);
        MongoPagingIterator<Data> iterator = dataRepository.getPagingIterator(queryDTO);
        File tempDir = MyFileUtils.getTempDir();
        File resultFile = FileUtil.file(tempDir, "data.json");
        FileUtil.touch(resultFile);
        try (FileWriter fileWriter = new FileWriter(resultFile);
             JSONWriter jsonWriter = new JSONWriter(fileWriter)) {
            jsonWriter.config(SerializerFeature.PrettyFormat, true);
            jsonWriter.config(SerializerFeature.WriteDateUseDateFormat, true);
            jsonWriter.startArray();
            while (iterator.hasNext()) {
                List<Data> next = iterator.next();
                if (CollUtil.isEmpty(next)) {
                    break;
                }
                for (Data data : next) {
                    DataExportDTO item = BeanUtil.copyProperties(data, DataExportDTO.class);
                    jsonWriter.writeObject(item);
                }
            }
            jsonWriter.endArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultFile;
    }
}
