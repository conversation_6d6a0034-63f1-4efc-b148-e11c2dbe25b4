# Tomcat
server:
  port: 9215

# Spring
spring:
  application:
    # 应用名称
    name: node-api
  profiles:
    # 环境配置
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
      config:
        # 配置中心地址
        server-addr: @nacos.server@
        namespace: @nacos.namespace@
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-circular-references: true
