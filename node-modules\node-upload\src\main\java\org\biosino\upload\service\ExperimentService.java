package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.enums.ExperimentTypeEnum;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDictService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.ExperimentBatchDTO;
import org.biosino.upload.dto.ExperimentDTO;
import org.biosino.upload.dto.SelectOption;
import org.biosino.upload.dto.mapper.ExperimentDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ErrorMsgVO;
import org.biosino.upload.vo.ExperimentVO;
import org.biosino.upload.vo.PublishVO;
import org.biosino.upload.vo.exp.ExpTypeVO;
import org.biosino.upload.vo.exp.SubExpSampleVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.entity.ExpSampleType.*;
import static org.biosino.upload.tool.excelparseutil.imp.ExcelParser.RELATED_LINKS_SEPARATOR;

@Service
@Slf4j
@RequiredArgsConstructor
public class ExperimentService extends BaseService {

    private final RunRepository runRepository;
    private final ExperimentRepository experimentRepository;
    private final ProjectRepository projectRepository;
    private final SampleRepository sampleRepository;
    private final DataRepository dataRepository;
    private final PublishRepository publishRepository;
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final RemoteDictService remoteDictService;
    private final DictService dictService;

    public ExperimentVO getExpInfoByNo(String expNo) {
        if (expNo == null) {
            throw new ServiceException("Experiment ID cannot be empty");
        }
        Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("Not found data"));

        if (!StrUtil.equals(experiment.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }
        ExperimentVO result = new ExperimentVO();
        if (experiment.getAudited().equals(AuditEnum.audited.name()) && experiment.getTempData() == null) {
            ExperimentDTOMapper.INSTANCE.copyToVo(experiment, result);
            List<PublishVO> publishVO = getPublishVO(AuthorizeType.experiment, expNo);
            result.setPublish(publishVO);
        } else {
            ExperimentDTOMapper.INSTANCE.copyToVo(experiment.getTempData(), result);
            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.experiment, expNo);
            result.setPublish(publishVo);
        }

        return result;
    }

    private void checkNameExist(String memberId, String expNo, String prjNo, String name) {
        // 校验实验名称
        Experiment existData = experimentRepository.validateExpName(memberId, expNo, prjNo, name);
        if (existData != null && existData.getTempData() != null) {
            throw new ServiceException("The experimental name already exists in submission: " + existData.getTempData().getSubNo());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ExperimentVO save(ExperimentDTO dto) {
        String subNo = dto.getSubNo();

        Submission submission = getEditSubmissionByNo(subNo);
        String expNo = submission.getExpSingleNo();

        // 校验实验名称
        checkNameExist(SecurityUtils.getMemberId(), expNo, dto.getProjectNo(), dto.getName());

        final Experiment experiment = dtoToDb(dto, expNo, submission, SecurityUtils.getMemberId());

        // 保存文献
        savePublish(dto.getPublish(), AuthorizeType.experiment, experiment.getExpNo());

        if (expNo == null) {
            // 新增实验
            submission.setExpSingleNo(experiment.getExpNo());
            saveEditSubmission(submission);
        }

        experimentRepository.save(experiment);
        // 将生成的id更新到tempData字段
        experimentRepository.updateTempId(CollUtil.toList(experiment.getExpNo()));

        return getExpInfoByNo(experiment.getExpNo());
    }

    private Experiment dtoToDb(ExperimentDTO dto, String expNo, Submission submission, String memberId) {
        return dtoToDb(dto, expNo, submission, memberId, null);
    }

    private Experiment dtoToDb(ExperimentDTO dto, String expNo, Submission submission, String memberId, Experiment savedExp) {
        final Date now = new Date();
        Experiment dbExp;
        // 新增实验
        if (expNo == null) {
            dbExp = initExp(dto, now, memberId, submission.getSubmitter());
            // 保存暂存数据
            dbExp.setTempData(ExperimentDTOMapper.INSTANCE.copy(dbExp));
        } else {
            // 编辑实验
            if (savedExp == null) {
                dbExp = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("Not found data"));
            } else {
                dbExp = savedExp;
            }
            dto.setExpNo(dbExp.getExpNo());
            // 保存暂存数据
            Experiment tempData = dbExp.getTempData();
            if (tempData == null) {
                tempData = ExperimentDTOMapper.INSTANCE.copy(dbExp);
            }
            ExperimentDTOMapper.INSTANCE.copyToDb(dto, tempData);
            if (AuditEnum.audited.name().equals(tempData.getAudited())) {
                tempData.setAudited(AuditEnum.unaudited.name());
            }

            tempData.setUpdateDate(now);
            tempData.setRelatedLinks(NodeUtils.cleanRelatedLinks(tempData.getRelatedLinks()));

            // single保存：更新外层的Experiment数据
            if (AuditEnum.init.name().equals(tempData.getAudited())) {
                dbExp = ExperimentDTOMapper.INSTANCE.copy(tempData);
            }
            dbExp.setTempData(tempData);
        }
        deleteEmptyAttr(dbExp);
        deleteEmptyAttr(dbExp.getTempData());
        return dbExp;
    }

    /**
     * 删除空属性
     *
     * @param item
     */
    private void deleteEmptyAttr(final Experiment item) {
        if (item != null) {
            item.setAttributes(deleteEmptyMap(item.getAttributes()));
//            item.setBackup(deleteEmptyMap(item.getBackup()));
        }
    }

    private Experiment initExp(ExperimentDTO dto, Date now, String memberId, final Submitter submitter) {
        final Experiment exp = new Experiment();
        ExperimentDTOMapper.INSTANCE.copyToDb(dto, exp);
        // 手动指定id效率太低
//        exp.setId(IdUtil.objectId());
        // 临时编号
        exp.setExpNo(IdUtil.fastSimpleUUID());
        exp.setProjectNo(dto.getProjectNo());
        exp.setCreateDate(now);
        exp.setUpdateDate(now);
        exp.setCreator(memberId);
        exp.setOwnership(OwnershipEnum.self_support.getDesc());
        exp.setSubmitter(submitter);
        exp.setHitNum(0L);
        exp.setAudited(AuditEnum.init.name());
        exp.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
        exp.setRelatedLinks(NodeUtils.cleanRelatedLinks(exp.getRelatedLinks()));
        return exp;
    }

    public String validateExpName(String expNo, String projectNo, String name) {
        if (StrUtil.isBlank(name)) {
            return "The experiment name cannot be blank";
        }
        if (StrUtil.isBlank(projectNo)) {
            return "Please select Project ID";
        }

        // 校验实验名称
        checkNameExist(SecurityUtils.getMemberId(), expNo, projectNo, name);

        return null;
    }

    /**
     * 批量保存实验数据
     *
     * @param dto
     */
    public List<ErrorMsgVO> batchSave(final ExperimentBatchDTO dto) {
        if (dto == null) {
            throw new ServiceException("Parameter error.");
        }
        final String memberId = SecurityUtils.getMemberId();
        // 所有正式项目编号
        final List<String> proNoList = projectRepository.findAllProjectNosByCreator(memberId);
        // 所有临时项目名称和对应编号
        final Map<String, String> tempPrjNameAndNoMap = projectRepository.findInitNameAndNoByCreator(memberId);
        if (CollUtil.isEmpty(proNoList) && CollUtil.isEmpty(tempPrjNameAndNoMap)) {
            throw new ServiceException("Current user has not yet created a project");
        }

        // 提交信息
        final String subNo = dto.getSubNo();
        final Submission submission = getEditSubmissionByNo(subNo);

        final AuthorizeType stageEnum = AuthorizeType.findByName(dto.getStage()).orElseThrow(() -> new ServiceException("The stage type is wrong. Please Check."));
        if (!stageEnum.equals(AuthorizeType.experiment)) {
            throw new ServiceException("The stage type is wrong. Please Check.");
        }
        // 删除空行
        dto.delBlank();
        // 标题信息，表格数据
        final List<String> titles = dto.getTitles();
        final List<Object[]> datas = dto.getDatas();
        final int totalCol = titles.size();
        final int totalRow = datas.size();
        if (totalCol == 0 || totalRow == 0) {
            throw new ServiceException("The submitted data cannot be empty");
        }

        // 名称和id
        final String nameColKey = EXPERIMENT_NAME;
        final String idColKey = EXPERIMENT_ID;

        // 名称列索引
        final int nameColIndex = requiredCol(titles, nameColKey);
        // 新增的名称集合
        final Set<String> names = new HashSet<>();
        // 编号列索引
        final int idColIndex = requiredCol(titles, idColKey);
        final Set<String> ids = new HashSet<>();
        // 项目id列索引
        final int prjColIndex = requiredCol(titles, PROJECT_ID);
        // 项目名称列索引
        final int prjNameColIndex = requiredCol(titles, PROJECT_NAME);

        // 获取属性字典表配置
        final String expType = dto.getExpType();
        ExpSampleType expSampleType = expSampleTypeRepository.findByName(expType);

        if (expSampleType == null) {
            throw new ServiceException("Experiment dictionary data not found exp data");
        }

        // 其它必需列校验
        int descColIndex = 0;
        if (!BaseAttrType.none.name().equals(expSampleType.getDesc())) {
            descColIndex = requiredCol(titles, DESCRIPTION);
        }
        int protocolColIndex = 0;
        if (!BaseAttrType.none.name().equals(expSampleType.getProtocol())) {
            protocolColIndex = requiredCol(titles, PROTOCOL);
        }

        final int reLinkColIndex = requiredCol(titles, RELATED_LINKS);

        // security
        final int secColIndex = titles.indexOf(UploadService.SECURITY);
        final boolean secColExist = secColIndex > -1;
        final List<String> allSecurity = SecurityEnum.includeAllSecurity();

//        dictService.checkExpSapPermission(expType, expSampleType);
        final Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
        final HashSet<String> attrNotExist = new HashSet<>(rangeMap.keySet());
        titles.forEach(attrNotExist::remove);
        if (CollUtil.isNotEmpty(attrNotExist)) {
            throw new ServiceException(StrUtil.format("Attributes not found: {}", CollUtil.join(attrNotExist, ", ")));
        }

        // 字典中配置的推荐属性必填个数
        Integer recommendNum = expSampleType.getRecommendNum();
        if (recommendNum == null) {
            recommendNum = 0;
        }

        // 新增的临时数据名称集合
        final Set<String> insertNameSet = new LinkedHashSet<>();

        // 校验表格数据
        final List<ErrorMsgVO> errors = new LinkedList<>();
        for (int i = 0; i < totalRow; i++) {
            final Object[] row = datas.get(i);
            if (row.length != totalCol) {
                throw new ServiceException("There are values without attributes!");
            }
            // 实验(样本)名称校验
            final String name = strVal(row[nameColIndex]);
            if (name == null) {
                // 必填
                emptyErr(i, errors, nameColKey);
            }

            // id校验
            final String id = strVal(row[idColIndex]);
            if (id != null) {
                // id唯一
                duplicateErr(i, errors, ids, idColKey, id);
            } else {
                // 新的数据 要求name唯一
                if (name != null) {
                    duplicateErr(i, errors, names, nameColKey, name);
                }
            }

            if (StrUtil.isBlank(id) && StrUtil.isNotBlank(name)) {
                insertNameSet.add(name);
            }

            // 项目编号校验
            final String prjNo = strVal(row[prjColIndex]);
            if (prjNo != null) {
                if (!proNoList.contains(prjNo)) {
                    errors.add(errMsg(i, PROJECT_ID, prjNo, "Project id not found in your projects"));
                }
            } else {
                // 项目编号为空时，项目名称必填
                final String prjName = strVal(row[prjNameColIndex]);
                if (prjName == null) {
                    errors.add(errMsg(i, PROJECT_NAME, null, "Project id or name is required"));
                } else {
                    final String tempPrjNo = tempPrjNameAndNoMap.get(prjName);
                    if (tempPrjNo == null) {
                        errors.add(errMsg(i, PROJECT_NAME, prjName, "Project Name not found in your projects"));
                    } else {
                        // 设置临时项目编号
                        row[prjColIndex] = tempPrjNo;
                    }
                }
            }

            // security字段校验
            if (secColExist) {
                final String val = strVal(row[secColIndex]);
                if (val == null) {
                    // 必填
                    emptyErr(i, errors, UploadService.SECURITY);
                } else {
                    // 候选项校验
                    selectionErr(i, errors, allSecurity, UploadService.SECURITY, val);
                }
            }

            // 属性校验
            int recommendCount = 0;

            // description字段校验
            if (descColIndex != 0) {
                final String desc = strVal(row[descColIndex]);
                if (desc == null) {
                    if (BaseAttrType.required.name().equals(expSampleType.getDesc())) {
                        // 必填
                        emptyErr(i, errors, DESCRIPTION);
                    }
                } else if (BaseAttrType.recommend.name().equals(expSampleType.getDesc())) {
                    recommendCount++;
                }
            }

            // protocol字段校验
            if (protocolColIndex != 0) {
                final String protocol = strVal(row[protocolColIndex]);
                if (protocol == null) {
                    if (BaseAttrType.required.name().equals(expSampleType.getProtocol())) {
                        // 必填
                        emptyErr(i, errors, PROTOCOL);
                    }
                } else if (BaseAttrType.recommend.name().equals(expSampleType.getProtocol())) {
                    recommendCount++;
                }
            }

            boolean libraryLayoutPaired = false;
            for (Map.Entry<String, ExpSampleType.Attributes> entry : rangeMap.entrySet()) {
                final String attrName = entry.getKey(); // 属性名
                final int attrIndex = titles.indexOf(attrName);
                final String attrVal = strVal(row[attrIndex]); // 属性值
                ExpSampleType.Attributes attributes = rangeMap.get(attrName);
                if (attributes != null) {
                    final String required = attributes.getRequired();
                    final boolean isRequired = BaseAttrType.required.name().equals(required);
                    if (attrVal == null) {
                        if (isRequired) {
                            // 必填项
                            emptyErr(i, errors, attrName);
                        }
                    } else if (BaseAttrType.recommend.name().equals(required)) {
                        // 统计值不为空的推荐项数量
                        recommendCount++;
                    }

                    if (READ_LENGTH_FOR_MATE2.equals(attrName) && libraryLayoutPaired && StrUtil.isBlank(attrVal)) {
                        errors.add(errMsg(i, ExpSampleType.READ_LENGTH_FOR_MATE2, attrVal, "This field is required when library_layout is Paired."));
                    }

                    if (StrUtil.isNotBlank(attrVal)) {
                        if ("library_layout".equals(attrName) && "Paired".equals(attrVal)) {
                            libraryLayoutPaired = true;
                        }

                        // Microarray类型的platform校验
                        if (ExpSampleType.PLATFORM.equals(attrName) && ExperimentTypeEnum.Microarray.getDesc().equals(expType)) {
                            if (Boolean.FALSE.equals(remoteDictService.existPlatform(attrVal, SecurityConstants.INNER).getData())) {
                                errors.add(errMsg(i, ExpSampleType.PLATFORM, attrVal, "Not within the scope of selection! Please see the experiment attributes page."));
                            }
                        } else {
                            // 下拉框校验
                            final List<String> rangeStrList = attributes.getRangeStrList();
                            if (!attributes.isAllowCreate() && CollUtil.isNotEmpty(rangeStrList)) {
                                selectionErr(i, errors, rangeStrList, attrName, attrVal);
                            }
                            // 日期校验
                            if (ExpSampleDataType.Date.name().equals(attributes.getDataType())) {
                                final String date = dateErr(i, errors, attrName, attrVal);
                                if (date != null) {
                                    row[attrIndex] = date;
                                }
                            }
                            // 正则校验
                            if (StrUtil.isNotBlank(attributes.getValueRegex())) {
                                if (!ReUtil.isMatch(attributes.getValueRegex(), attrVal)) {
                                    errors.add(errMsg(i, attrName, attrVal, "Data format error, please refer to: " + attributes.getValueFormat()));
                                }
                            }
                        }
                    }
                }
            }
            if (recommendCount < recommendNum) {
                // 推荐字段没有满足最小必填个数
                errors.add(errMsg(i, "Too few recommended fields", String.valueOf(recommendCount),
                        StrUtil.format("The number of recommended fields filled in is less than {}", recommendNum)));
            }
        }

        if (CollUtil.isNotEmpty(errors)) {
            // 表格校验错误
            return errors;
        } else {
            // 查库校验
            final List<String> expMultipleNos = expMultipleNos(submission);
            // 所有已入库的名称
            final Map<String, String> dbNames = experimentRepository.findTempNameByNamesInAndExpNoNotIn(names, memberId, expMultipleNos);
            final List<Experiment> savedExp = experimentRepository.findAllByNosAndCreator(ids, memberId);
            // 所有已入库的数据
            final Map<String, Experiment> dbExpMap = new HashMap<>();
            for (Experiment experiment : savedExp) {
                dbExpMap.put(experiment.getExpNo(), experiment);
                final List<String> usedIds = experiment.getUsedIds();
                if (CollUtil.isNotEmpty(usedIds)) {
                    for (String usedId : usedIds) {
                        if (usedId != null) {
                            dbExpMap.put(usedId, experiment);
                        }
                    }
                }
            }
            // 用于判断编辑的实验是否已被其他submission使用
            final Set<String> allExpNosOfSubmission = experimentRepository.findAllTempNoByNos(expMultipleNos, memberId);
            for (int i = 0; i < totalRow; i++) {
                final Object[] row = datas.get(i);
                final String id = strVal(row[idColIndex]);
                final String name = strVal(row[nameColIndex]);
                if (id != null) {
                    // 编辑
                    if (!dbExpMap.containsKey(id)) {
                        errors.add(errMsg(i, idColKey, id, "id does not exist or not allow edit"));
                    } else {
                        // 要求只能编辑审核通过的实验
                        final Experiment experiment = dbExpMap.get(id);
                        if (!AuditEnum.audited.name().equals(experiment.getAudited())
                                && !allExpNosOfSubmission.contains(id)) {
                            // 存在正式编号的实验已被编辑，且待审核
                            errors.add(errMsg(i, idColKey, id, "The data ID has already been used in submission: " + experiment.getSubNo()));
                        }
                    }
                } else if (dbNames.containsKey(name)) {
                    // 新增, 名称重复
                    errors.add(errMsg(i, nameColKey, name, "name exist in submission: " + dbNames.get(name)));
                }
            }

            // 找出哪些新增状态的组学将要被删除
            List<String> multipleNos = submission.getExpMultipleNos();
            if (CollUtil.isNotEmpty(multipleNos)) {
                List<String> insertNameList = new ArrayList<>();
                for (String multipleNo : multipleNos) {
                    // 如果是非正式ID
                    if (!multipleNo.startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                        insertNameList.add(multipleNo);
                    }
                }
                if (CollUtil.isNotEmpty(insertNameList)) {
                    Map<String, String> nameMap = experimentRepository.findNameByIds(insertNameList);

                    if (CollUtil.isNotEmpty(nameMap)) {

                        Set<String> keySet = nameMap.keySet();

                        for (String name : keySet) {
                            // 删除新增的数据情况
                            if (!insertNameSet.contains(name)) {

                                String expNo = nameMap.get(name);

                                // 校验当前组学在哪些Run中已被使用
                                List<Run> runs = runRepository.findAllNameByFieldAndNo("exp_no", expNo);

                                if (CollUtil.isNotEmpty(runs)) {
                                    for (Run run : runs) {
                                        Run tempData = run.getTempData();
                                        String subNo2 = tempData.getSubNo().equals(dto.getSubNo()) ? tempData.getSubNo() + " (Current Submission)" : tempData.getSubNo();
                                        String runNo = AuditEnum.init.name().equals(tempData.getAudited()) ? "Unassigned formal ID" : tempData.getRunNo();

                                        String msg = "Delete failed, The current data is already in use in Submission NO: " + subNo2 + ", Run No: " + runNo + ", Run Name: " + tempData.getName();
                                        errors.add(errMsg(null, "experiment_name", name, msg));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (CollUtil.isNotEmpty(errors)) {
                return errors;
            }

            // 全部校验通过
            dbNames.clear();
            allExpNosOfSubmission.clear();

            // 所有已入库临时数据的名称和对象map
            final Map<String, Experiment> nameItemOfTempData = experimentRepository.findTempNameByNamesInAndExpNoIn(names, memberId, expMultipleNos);

            final List<Experiment> toSaveData = new ArrayList<>();
            final List<Experiment> toSaveDataWithId = new ArrayList<>();
            for (int i = 0; i < totalRow; i++) {
                final Object[] row = datas.get(i);

                final String name = strVal(row[nameColIndex]);
                final String prjNo = strVal(row[prjColIndex]);

                final ExperimentDTO sourceDto = new ExperimentDTO();
                sourceDto.setSubNo(subNo);
                sourceDto.setProjectNo(projectRepository.findTopByProjectNo(prjNo)
                        .orElseThrow(() -> new ServiceException("project未找到：" + prjNo)).getProjectNo());
                sourceDto.setName(name);
                sourceDto.setDescription(strVal(row[descColIndex]));
                sourceDto.setProtocol(strVal(row[protocolColIndex]));
                final String reLink = strVal(row[reLinkColIndex]);
                if (reLink != null) {
                    sourceDto.setRelatedLinks(CollUtil.toList(reLink.split(RELATED_LINKS_SEPARATOR)));
                }
                final Map<String, Object> attributes = new LinkedHashMap<>();
                // 读取属性数据
                for (Map.Entry<String, ExpSampleType.Attributes> entry : rangeMap.entrySet()) {
                    final String key = entry.getKey();
                    final String attrVal = strVal(row[titles.indexOf(key)]);
                    attributes.put(key, attrVal);
                }
                sourceDto.setAttributes(CollUtil.isEmpty(attributes) ? null : attributes);
                sourceDto.setExpType(expType);

                Experiment experiment = null;
                // 编号
                String id = strVal(row[idColIndex]);
                if (id == null) {
                    // 新增的临时数据，根据名称获取已入库的id
                    experiment = nameItemOfTempData.get(name);
                    if (experiment != null) {
                        id = StrUtil.trimToNull(experiment.getExpNo());
                    }
                }
                if (id == null) {
                    experiment = dtoToDb(sourceDto, null, submission, memberId);
                    toSaveData.add(experiment);
                } else {
                    experiment = dtoToDb(sourceDto, id, submission, memberId, dbExpMap.getOrDefault(id, experiment));
                    toSaveDataWithId.add(experiment);
                }
            }

            dbExpMap.clear();
            // 编辑和新增分开保存，提高保存效率
            experimentRepository.saveAll(toSaveDataWithId);
            // 保存实验信息
            experimentRepository.saveAll(toSaveData);
            toSaveData.addAll(toSaveDataWithId);
            // 保存后生成的实验编号集合
            final List<String> dbNos = new ArrayList<>();
            for (Experiment experiment : toSaveData) {
                dbNos.add(experiment.getExpNo());
            }
            // 更新submission, 删除临时实验数据
            experimentRepository.deleteTempByNosAndCreator(expMultipleNos, memberId, dbNos);
            // 将生成的id更新到tempData字段
            experimentRepository.updateTempId(dbNos);

            submission.setExpMultipleNos(dbNos);
            saveEditSubmission(submission);
            return null;
        }
    }

    private List<String> expMultipleNos(final Submission submission) {
        List<String> expMultipleNos = submission.getExpMultipleNos();
        if (expMultipleNos == null) {
            expMultipleNos = new ArrayList<>();
        }
        return expMultipleNos;
    }

    public List<SelectOption> getExperimentOptions() {
        List<Experiment> experiments = experimentRepository.findAllByCreator(SecurityUtils.getMemberId());
        if (CollUtil.isEmpty(experiments)) {
            return null;
        }
        return experiments.stream().map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getExpNo());
            if (x.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                option.setLabel(x.getExpNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }
            option.setParams(MapUtil.of("projectNo", x.getProjectNo()));
            return option;
        }).collect(Collectors.toList());
    }

    public Page<SelectOption> getExperimentOptionsByPage(ArchivedSelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        Page<Experiment> page = experimentRepository.findAllByPage(queryDTO);

        // 获取projectNos
        Set<String> projectNos = page.getContent().stream().map(Experiment::getProjectNo).collect(Collectors.toSet());
        List<Project> projectList = projectRepository.findAllByProjectNoIn(projectNos);
        Map<String, Project> projectMap = new HashMap<>();
        projectList.forEach(x -> projectMap.put(x.getProjectNo(), x));

        return page.map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getExpNo());
            if (x.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                option.setLabel(x.getExpNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }

            Project project = projectMap.get(x.getProjectNo());
            SelectOption projectOption = new SelectOption();
            projectOption.setValue(project.getProjectNo());
            if (project.getProjectNo().startsWith(SequenceType.PROJECT.getPrefix())) {
                projectOption.setLabel(project.getProjectNo() + " (" + project.getName() + ")");
            } else {
                projectOption.setLabel(project.getName());
            }

            option.setParams(MapUtil.of("project", projectOption));
            return option;
        });
    }

    /**
     * 初始化Experiment Type，和表格数据
     */
    public ExpTypeVO getExpTypeData(final String expType, final String subNo) {
        ExpSampleType expTypeData = dictService.getExpSapDictData(expType);

        // 属性数据
        final List<ExpSampleType.Attributes> attributes = new ArrayList<>();
        attributes.add(initAttr(BaseAttrType.optional, EXPERIMENT_ID, EXPERIMENT_ID, ExpSampleDataType.Input, "For newly created experiments, the experiment ID can be left blank. When modifying an experiment that has already been assigned an experiment ID, the experiment ID must be filled in. Once the experiment ID is assigned, it cannot be changed."));
        attributes.add(initAttr(BaseAttrType.required, EXPERIMENT_NAME, EXPERIMENT_NAME, ExpSampleDataType.Input, "Fill in the unique name of the experiment you plan to create or the name of an experiment that has been successfully created and assigned an experiment ID. If the experiment ID and experiment name do not match, it is considered an update to the experiment name."));
        attributes.add(initAttr(BaseAttrType.optional, PROJECT_ID, PROJECT_ID, ExpSampleDataType.Input, "For newly created projects, the project ID can be left blank. To add experiments for a successfully created project which has project ID already, fill in the project ID here."));
        attributes.add(initAttr(BaseAttrType.optional, PROJECT_NAME, PROJECT_NAME, ExpSampleDataType.Input, "Fill in the unique name of the project you plan to create or the name of a project that has been successfully created and assigned a project ID. If the project ID and project name do not match, the project ID shall prevail and the project name shall be ignored."));

        if (!BaseAttrType.none.name().equals(expTypeData.getDesc())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getDesc()), DESCRIPTION, DESCRIPTION, ExpSampleDataType.Input, "Description of the experiment."));
        }
        if (!BaseAttrType.none.name().equals(expTypeData.getProtocol())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getProtocol()), PROTOCOL, PROTOCOL, ExpSampleDataType.Input, "Description of experiment designing."));
        }
        attributes.add(initAttr(BaseAttrType.optional, RELATED_LINKS, RELATED_LINKS, ExpSampleDataType.Input));

        attributes.addAll(expTypeData.getAttributes());

        for (ExpSampleType.Attributes attribute : attributes) {
            if (attribute.getDescription() == null && attribute.getDataType().startsWith("Number")) {
                attribute.setDescription("Number");
            }
        }
        // 获取表格数据
        List<Map<String, Object>> rows = null;
        if (StrUtil.isNotBlank(subNo)) {
            // 查询回显信息
            final Submission submission = getEditSubmissionByNo(subNo);
            final List<String> expMultipleNos = submission.getExpMultipleNos();
            if (CollUtil.isNotEmpty(expMultipleNos)) {
                final String memberId = SecurityUtils.getMemberId();
                final List<Experiment> experimentList = experimentRepository.findAllTempByNosAndCreatorAndExpType(expMultipleNos, memberId, expType);
                if (CollUtil.isNotEmpty(experimentList)) {
                    final String initAudit = AuditEnum.init.name();
                    final List<String> prjNos = experimentList.stream().filter(x -> initAudit.equals(x.getAudited())).map(Experiment::getProjectNo).collect(Collectors.toList());
                    final Map<String, String> noNameMap = projectRepository.findNoAndNameByProjectNosAndCreator(prjNos, memberId);
                    final List<String> notInit = AuditEnum.allNotInit();
                    rows = new ArrayList<>();
                    for (Experiment experiment : experimentList) {
                        final Map<String, Object> row = new HashMap<>();
                        if (!initAudit.equals(experiment.getAudited())) {
                            // 不回显临时id
                            row.put(EXPERIMENT_ID, experiment.getExpNo());
                        }
                        row.put(EXPERIMENT_NAME, experiment.getName());
                        final String projectNo = experiment.getProjectNo();
                        String prjName = noNameMap.get(initAudit + ProjectCustomRepository.PROJECT_KEY_CONNECTOR + projectNo);
                        if (prjName == null) {
                            row.put(PROJECT_ID, projectNo);
                            for (String audit : notInit) {
                                prjName = noNameMap.get(audit + ProjectCustomRepository.PROJECT_KEY_CONNECTOR + projectNo);
                                if (prjName != null) {
                                    row.put(PROJECT_NAME, prjName);
                                    break;
                                }
                            }
                        } else {
                            // init状态不回显项目id
                            row.put(PROJECT_NAME, prjName);
                        }
                        row.put(DESCRIPTION, experiment.getDescription());
                        row.put(RELATED_LINKS, relatedLinksStr(experiment.getRelatedLinks()));
                        row.put(PROTOCOL, experiment.getProtocol());
                        Map<String, Object> attr = experiment.getAttributes();
                        if (CollUtil.isNotEmpty(attr)) {
                            row.putAll(attr);
                        }
                        rows.add(row);
                    }
                }
            }
        }
        ExpTypeVO vo = new ExpTypeVO();
        vo.setRows(rows);
        vo.setAttributes(attributes);
        return vo;
    }

    public List<SubExpSampleVO> getExpTypeDataList(List<String> nos, String creator) {
        if (CollUtil.isEmpty(nos)) {
            return null;
        }

        List<Experiment> experimentList = experimentRepository.findAllTempByNosAndCreatorAndExpType(nos, creator, null);

        if (CollUtil.isEmpty(experimentList)) {
            return null;
        }

        // 有可能single和multiple用户填写的类型不同，因此需要分组
        Map<String, List<Experiment>> experimentMap = experimentList.stream()
                .sorted(Comparator.comparing(Experiment::getCreateDate).reversed())
                .collect(Collectors.groupingBy(Experiment::getExpType));

        List<SubExpSampleVO> result = new ArrayList<>();

        experimentMap.forEach((expType, value) -> {
            SubExpSampleVO vo = new SubExpSampleVO();
            vo.setType(expType);

            List<String> titles = new ArrayList<>();

            // 基础公共字段标题
            titles.add(EXPERIMENT_ID);
            titles.add(EXPERIMENT_NAME);
            titles.add(PROJECT_ID);
            titles.add(PROJECT_NAME);
            titles.add(DESCRIPTION);
            titles.add(RELATED_LINKS);
            titles.add(PROTOCOL);

            ExpSampleType expTypeData = dictService.getExpSapDictData(expType);
            List<String> attrFieldList = expTypeData.getAttributes().stream()
                    .sorted(Comparator.comparing(ExpSampleType.Attributes::getSort))
                    .map(ExpSampleType.Attributes::getAttributesField).collect(Collectors.toList());

            titles.addAll(attrFieldList);
            vo.setTitle(titles);

            final String initAudit = AuditEnum.init.name();
            final List<String> prjNos = experimentList.stream().filter(x -> initAudit.equals(x.getAudited())).map(Experiment::getProjectNo).collect(Collectors.toList());
            final Map<String, String> noNameMap = projectRepository.findNoAndNameByProjectNosAndCreator(prjNos, creator);

            List<Map<String, Object>> rows = new ArrayList<>();

            for (Experiment experiment : experimentList) {
                if (!experiment.getExpType().equals(expType)) {
                    continue;
                }
                final Map<String, Object> row = new HashMap<>();
                // 需要提供exp id，但是前端不显示
                row.put(EXPERIMENT_ID, experiment.getExpNo());
                row.put(EXPERIMENT_NAME, experiment.getName());
                final String projectNo = experiment.getProjectNo();
                String prjName = noNameMap.get(initAudit + ProjectCustomRepository.PROJECT_KEY_CONNECTOR + projectNo);
                row.put(PROJECT_ID, projectNo);
                row.put(PROJECT_NAME, prjName);
                row.put(DESCRIPTION, experiment.getDescription());
                row.put(RELATED_LINKS, relatedLinksStr(experiment.getRelatedLinks()));
                row.put(PROTOCOL, experiment.getProtocol());
                Map<String, Object> attr = experiment.getAttributes();
                if (CollUtil.isNotEmpty(attr)) {
                    row.putAll(attr);
                }
                rows.add(row);
            }

            vo.setRows(rows);

            result.add(vo);
        });

        return result;
    }

    public static ExpSampleType.Attributes initAttr(BaseAttrType required, String attributesName, String attributesField,
                                                    ExpSampleDataType dataType) {
        return initAttr(required, attributesName, attributesField, dataType, null);
    }

    public static ExpSampleType.Attributes initAttr(BaseAttrType required, String attributesName, String attributesField,
                                                    ExpSampleDataType dataType, String description) {
        return initAttr(required, attributesName, attributesField, dataType, description, false);
    }

    public static ExpSampleType.Attributes initAttr(BaseAttrType required, String attributesName, String attributesField,
                                                    ExpSampleDataType dataType, String description, final boolean customAttrFlag) {
        ExpSampleType.Attributes attr = new ExpSampleType.Attributes();
        attr.setRequired(required.name());
        attr.setAttributesName(attributesName);
        attr.setAttributesField(attributesField);
        attr.setDataType(dataType.name());
        attr.setDescription(description);
        attr.setCustomAttrFlag(customAttrFlag);
        return attr;
    }


    public List<DeleteErrorMsgVO> delete(String subNo, Boolean single) {
        if (StrUtil.isBlank(subNo) || single == null) {
            throw new ServiceException("The request parameter is illegal");
        }
        Submission submission = getEditSubmissionByNo(subNo);

        List<String> expNos;
        if (single) {
            expNos = CollUtil.newArrayList(submission.getExpSingleNo());
            submission.setExpSingleNo(null);
        } else {
            expNos = submission.getExpMultipleNos();
            submission.setExpMultipleNos(null);
        }

        if (CollUtil.isEmpty(expNos)) {
            throw new ServiceException("There is no data to delete");
        }

        List<DeleteErrorMsgVO> vos = new ArrayList<>();

        for (String expNo : expNos) {
            // 校验当前组学在哪些Run中已被使用
            List<Run> runs = runRepository.findAllNameByFieldAndNo("exp_no", expNo);

            if (CollUtil.isNotEmpty(runs)) {
                for (Run run : runs) {
                    Run tempData = run.getTempData();

                    DeleteErrorMsgVO vo = new DeleteErrorMsgVO();

                    Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("not found experiment: " + expNo));
                    vo.setTarget(experiment.getName());

                    vo.setName(tempData.getName());
                    vo.setType(AuthorizeType.run.name());

                    String tempSubNo = tempData.getSubNo();
                    vo.setSubNo(tempSubNo.equals(subNo) ? tempSubNo + " (Current Submission)" : tempSubNo);

                    vo.setNo(AuditEnum.init.name().equals(tempData.getAudited()) ? "Unassigned formal ID" : tempData.getRunNo());

                    vos.add(vo);
                }
            }
        }

        if (CollUtil.isNotEmpty(vos)) {
            return vos;
        }

        // 删除数据
        experimentRepository.deleteTempByNosAndCreator(expNos, SecurityUtils.getMemberId(), null);

        saveEditSubmission(submission);
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public ExperimentVO saveEdit(ExperimentDTO dto) {
        String subNo = dto.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);
        String expNo = dto.getExpNo();

        Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("Not found Experiment: " + dto.getProjectNo()));

        if (!StrUtil.equals(experiment.getVisibleStatus(), VisibleStatusEnum.Unaccessible.name())) {
            throw new ServiceException("No editing allowed!");
        }

        Experiment tempData;
        if (experiment.getTempData() == null) {
            tempData = ExperimentDTOMapper.INSTANCE.copy(experiment);
        } else {
            tempData = experiment.getTempData();
        }

        ExperimentDTOMapper.INSTANCE.copyToDb(dto, tempData);

        tempData.setSubmitter(submission.getSubmitter());
        tempData.setRelatedLinks(NodeUtils.cleanRelatedLinks(tempData.getRelatedLinks()));
        tempData.setUpdateDate(new Date());
        tempData.setAudited(AuditEnum.unaudited.name());

        // 删除空的attr
        deleteEmptyAttr(tempData);
        experiment.setTempData(tempData);

        saveEditPublish(dto.getPublish(), AuthorizeType.experiment, experiment.getExpNo());

        // 保存数据
        experimentRepository.save(experiment);

        // 更新submission
        submission.setExpSingleNo(experiment.getExpNo());
        saveEditSubmission(submission);

        return getExpInfoByNo(experiment.getExpNo());
    }

    public DeleteCheckResultVO deleteCheck(String expNo, String memberId, boolean validateShare) {
        // 找到这个experiment
        Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("Not found Experiment: " + expNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(experiment.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }
        // 找到这个Experiment下的Run
        List<Run> runList = runRepository.findAllByExpNoIn(CollUtil.newArrayList(expNo));

        // 过滤出sap_no查询相关的Sample 简要信息
        List<String> sapNos = runList.stream().map(Run::getSapNo).distinct().collect(Collectors.toList());

        List<Sample> sampleList = sampleRepository.findAllBySapNoIn(sapNos);
        List<String> runNos = runList.stream().map(Run::getRunNo).distinct().collect(Collectors.toList());

        List<Data> dataList = dataRepository.findAllByRunNoIn(runNos);
        List<String> dataNos = dataList.stream().map(Data::getDatNo).distinct().collect(Collectors.toList());

        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (experiment.getTempData() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(expNo);
            vo.setNo(experiment.getTempData().getExpNo());
            vo.setType(AuthorizeType.experiment.name());
            vo.setName(experiment.getTempData().getName());
            vo.setSubNo(experiment.getTempData().getSubNo());
            errors.add(vo);
        }

        for (Run run : runList) {
            if (run.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(expNo);
                vo.setNo(run.getTempData().getRunNo());
                vo.setType(AuthorizeType.run.name());
                vo.setName(run.getTempData().getName());
                vo.setSubNo(run.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        // 在tempData中查询和ExpNos相关联的
        List<Run> tempRunList = runRepository.findTempByExpNoIn(CollUtil.newArrayList(expNo));
        for (Run run : tempRunList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(expNo);
            vo.setNo(run.getTempData().getRunNo());
            vo.setType(AuthorizeType.run.name());
            vo.setName(run.getTempData().getName());
            vo.setSubNo(run.getTempData().getSubNo());
            errors.add(vo);
        }

        // 遍历sampleList
        for (Sample sample : sampleList) {
            // 如果有tempData，证明数据被编辑且未被审核通过，不允许被删除
            if (sample.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(sample.getSapNo());
                vo.setType(AuthorizeType.sample.name());
                vo.setName(sample.getTempData().getName());
                vo.setSubNo(sample.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        List<Sample> tempSampleList = sampleRepository.findTempBySapNoIn(sapNos);
        for (Sample sample : tempSampleList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(expNo);
            vo.setNo(sample.getTempData().getSapNo());
            vo.setType(AuthorizeType.sample.name());
            vo.setName(sample.getTempData().getName());
            vo.setSubNo(sample.getTempData().getSubNo());
            errors.add(vo);
        }
        for (Data data : dataList) {
            if (data.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(expNo);
                vo.setNo(data.getTempData().getDatNo());
                vo.setType(AuthorizeType.data.name());
                vo.setName(data.getTempData().getName());
                vo.setSubNo(data.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        Map<String, String> runNoSapNoMap = runRepository.findDetailBySapNoIn(sapNos).stream().collect(Collectors.toMap(Run::getRunNo, Run::getSapNo, (existingValue, newValue) -> existingValue));
        runNoSapNoMap.forEach((k, v) -> {
            // 如果sapNo被其他的run用了，删除这个sapNo
            if (!CollUtil.contains(runNos, k)) {
                sapNos.remove(v);
            }
        });

        List<Data> tempDataList = dataRepository.findTempByRunNoIn(runNos);

        for (Data data : tempDataList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(expNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }
        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setExpNos(CollUtil.newArrayList(expNo));
        resultVO.setRunNos(runNos);
        resultVO.setSapNos(sapNos);
        resultVO.setDataNos(dataNos);
        resultVO.setErrors(new ArrayList<>(errors));

        // 是否需要校验被删除的数据在share review request里面使用
        if (validateShare) {
            validateShareAndReviewAndRequest(resultVO, memberId);
        }

        return resultVO;
    }

    public void deleteExperimentAll(String expNo, String memberId) {
        Experiment experiment = experimentRepository.findTopByExpNo(expNo).orElseThrow(() -> new ServiceException("No Experiment found"));
        if (!StrUtil.equals(experiment.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }

        DeleteCheckResultVO checkResultVO = deleteCheck(expNo, memberId, false);
        // 删除数据检查
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The Experiment cannot be deleted because it is associated with other data");
        }
        // 添加删除的日志
        addUserCenterDeleteLog(expNo, AuthorizeType.experiment.name(), checkResultVO);
        // 将visible_status更新为delete
        experimentRepository.updateToDeleteAllByExpNoIn(checkResultVO.getExpNos());
        runRepository.updateToDeleteAllByRunNoIn(checkResultVO.getRunNos());
        sampleRepository.updateToDeleteAllBySapNoIn(checkResultVO.getSapNos());
        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());

        // 删除相关联的publish
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.experiment.name(), checkResultVO.getExpNos());
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos());

        // 通知删除es索引
        updateEsData(AuthorizeType.experiment.name(), expNo);

    }
}
