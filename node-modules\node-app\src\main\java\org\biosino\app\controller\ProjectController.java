package org.biosino.app.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.app.dto.GsaExportQueryDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.service.GsaService;
import org.biosino.app.service.ProjectService;
import org.biosino.app.vo.PrjExpSapListSearchVO;
import org.biosino.app.vo.PrjExpSapListVO;
import org.biosino.app.vo.PrjExpSapTypeVO;
import org.biosino.app.vo.list.ProjectListVO;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Project 详情
 *
 * <AUTHOR> Li
 */
@RestController
@RequestMapping("/project")
@RequiredArgsConstructor
public class ProjectController extends BaseController {

    private final ProjectService projectService;

    private final GsaService gsaService;

    /**
     * 获取项目的基本信息
     */
    @GetMapping("/getGeneralInfo/{projNo}")
    public AjaxResult getGeneralInfo(@PathVariable("projNo") String projNo) {
        return success(projectService.getGeneralInfo(projNo));
    }

    /**
     * 获取项目的基本统计信息
     */
    @GetMapping("/getStatInfo/{projNo}")
    public AjaxResult getStatInfo(@PathVariable("projNo") String projNo) {
        return success(projectService.getStatInfo(projNo));
    }

    /**
     * 获取详细统计信息
     */
    @GetMapping("/getStatDetail/{projNo}")
    public AjaxResult getStatDetail(@PathVariable("projNo") String projNo) {
        return success(projectService.getStatDetail(projNo));
    }

    /**
     * 获取Project相关联的Analysis以及Data信息
     */
    @GetMapping("/getRelatedAnalysis/{projNo}")
    public AjaxResult getRelatedAnalysis(@PathVariable("projNo") String projNo) {
        return success(projectService.getRelatedAnalysis(projNo));
    }

    /**
     * 获取submitter信息
     */
    @GetMapping("/getAuthorInfo/{projNo}")
    public AjaxResult getAuthorInfo(@PathVariable("projNo") String projNo) {
        return success(projectService.getProjectAuthorInfo(projNo));
    }

    /**
     * 列出用户的Project列表
     */
    @GetMapping("/listProject")
    public TableDataInfo listProject(UserCenterListSearchDTO queryDTO) {
        Page<ProjectListVO> page = projectService.listAuditedProject(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 校验当前用户是否有访问的权限
     */
    @RequestMapping("/checkProjectPermission/{projectNo}")
    public AjaxResult checkProjectPermission(@PathVariable("projectNo") String projectNo) {
        String result = projectService.checkProjectPermission(projectNo);
        return success(result);
    }

    /**
     * 获取Project下expType和sapType的表格标题相关数据
     */
    @RequestMapping("/getExpAndSampleTable/{projectNo}")
    public AjaxResult getExpAndSampleTable(@PathVariable("projectNo") String projectNo) {
        PrjExpSapTypeVO vo = projectService.getExpAndSampleTable(projectNo);
        return success(vo);
    }

    /**
     * 获取Project下expType和sapType的表格数据
     */
    @GetMapping("/getExpAndSampleList")
    public AjaxResult getExpAndSampleList(@Validated PrjExpSapListSearchVO searchVO) {
        PrjExpSapListVO vo = projectService.getExpAndSampleList(searchVO);
        return success(vo);
    }

    /**
     * 导出Project下exp和sap表格
     */
    @PostMapping("/exportExpAndSample")
    public void exportExpAndSample(@Validated PrjExpSapListSearchVO searchVO, HttpServletRequest request, HttpServletResponse response) {
        projectService.exportExpAndSample(searchVO, request, response);
    }

    /**
     * 导出GSA数据
     */
    @RequestMapping("/exportGsaData")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Browse", module2 = "Project", module3 = "GSA", desc = "导出GSA数据")
    public void exportGsaData(@Validated GsaExportQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {
        gsaService.exportGsaData(queryDTO, request, response);
    }

    /**
     * 导出SRA数据
     */
    @RequestMapping("/exportSraData")
    @Log(system = SytemEnum.APP, businessType = BusinessType.EXPORT, module1 = "Browse", module2 = "Project", module3 = "SRA", desc = "导出SRA数据")
    public void exportSraData(@Validated GsaExportQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {
        gsaService.exportSraData(queryDTO, request, response);
    }

    /**
     * 查询当前project下expType的种类
     */
    @RequestMapping("/getProjectExpTypes/{projectNo}")
    public AjaxResult getExpTypeList(@PathVariable("projectNo") String projectNo) {
        return success(gsaService.getProjectExpTypes(projectNo));
    }

    /**
     * 查询当前project下subjectType的种类
     */
    @RequestMapping("/getProjectSubjectTypes/{projectNo}")
    public AjaxResult getSubjectTypeList(@PathVariable("projectNo") String projectNo) {
        return success(gsaService.getProjectSubjectTypes(projectNo));
    }

    /**
     * 查询当前project下security的种类
     */
    @RequestMapping("/getProjectDataSecurity/{projectNo}")
    public AjaxResult getSecurityList(@PathVariable("projectNo") String projectNo) {
        return success(gsaService.getProjectDataSecurity(projectNo));
    }

    /**
     * 查询当前project下experiment的信息
     */
    @RequestMapping("/getProjectExpInfo")
    public AjaxResult getProjectExpInfo(@RequestBody GsaExportQueryDTO queryDTO) {
        return success(gsaService.getProjectExpInfo(queryDTO));
    }


}
