package org.biosino.system.service.impl.standmg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.enums.es.YesOrNo;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.core.utils.bean.BeanValidators;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.system.domain.dto.StandMgDataDTO;
import org.biosino.system.domain.vo.SelectVo;
import org.biosino.system.dto.dto.standmg.Select2Item;
import org.biosino.system.dto.dto.standmg.StandMgAttrDTO;
import org.biosino.system.dto.dto.standmg.StandMgQueryDTO;
import org.biosino.system.dto.dto.standmg.StandSetExportDTO;
import org.biosino.system.dto.mapper.AttributesMapper;
import org.biosino.system.repository.ExpSampleTypeRepository;
import org.biosino.system.service.standmg.IStandMgExpService;
import org.biosino.system.vo.standmg.StandMgAttrVO;
import org.biosino.system.vo.standmg.StandMgExpVO;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组学类型字典配置 服务层
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StandMgExpServiceImpl implements IStandMgExpService {
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final Validator validator;

    @Override
    public StandMgExpVO list(final StandMgQueryDTO search) {
        final ExpSampleTypeEnum typeEnum = getTypeEnum(search.getType());

        final List<ExpSampleType> data = expSampleTypeRepository.list(search);
        final StandMgExpVO vo = new StandMgExpVO();
        vo.setTbData(data);

        vo.setAttrTypeList(initSelect(CollUtil.toList(BaseAttrType.values())));
        vo.setStatusList(initSelect(CollUtil.toList(DataStatusEnum.values())));

        final List<String> lvl1Names = expSampleTypeRepository.findLvl1NameByType(typeEnum);
        vo.setParentList(lvl1Names.stream().map(item -> {
            final SelectVo selectVo = new SelectVo();
            selectVo.setLabel(item);
            selectVo.setValue(item);
            return selectVo;
        }).collect(Collectors.toList()));
        return vo;
    }

    private ExpSampleTypeEnum getTypeEnum(final String type) {
        return ExpSampleTypeEnum.findByName(type).orElseThrow(() -> new ServiceException("Invalid type"));
    }

    private List<SelectVo> initSelect(List<? extends Enum<?>> enums) {
        if (CollUtil.isEmpty(enums)) {
            return new ArrayList<>();
        }
        return enums.stream().map(item -> {
            final SelectVo selectVo = new SelectVo();
            selectVo.setLabel(item.name());
            selectVo.setValue(item.name());
            return selectVo;
        }).collect(Collectors.toList());
    }

    /**
     * 修改数据状态
     */
    @Override
    public boolean updateStatus(final StandMgDataDTO standMgDataDTO) {
        final String id = standMgDataDTO.getId();
        if (StrUtil.isBlank(id)) {
            throw new ServiceException("ID cannot be empty");
        }
        final ExpSampleType expSampleType = expSampleTypeRepository.findById(id).orElseThrow(() -> new ServiceException("Data not found"));

        final boolean disable = standMgDataDTO.isDisable();
        final Date now = new Date();
        expSampleType.setStatus(disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name());
        if (DataStatusEnum.enable.name().equals(expSampleType.getStatus())) {
            // 启用某一项时，禁用其它相同的名称数据
            final StandMgQueryDTO search = new StandMgQueryDTO();
            search.setEqName(expSampleType.getName());
            search.setType(expSampleType.getType());
            //search.setNotId(expSampleType.getId());

            // 启用某一项时，禁用其它相同的名称数据
            final List<ExpSampleType> list = expSampleTypeRepository.list(search);
            enableHandler(list, expSampleType.getId(), expSampleType);
        }

        expSampleType.setUpdateTime(now);
        expSampleTypeRepository.save(expSampleType);
        return true;
    }

    @Override
    public String uploadTemplate(final MultipartFile file, final String name, final String version, final String type,
                                 final String parentName, final Boolean isExample, final String id) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("File cannot be empty");
        }
        if (StrUtil.isBlank(name) || StrUtil.isBlank(version) || StrUtil.isBlank(type)) {
            throw new ServiceException("Parameter error");
        }
        final String originalFilename = file.getOriginalFilename();
        if (!MyFileUtils.isExcel(file.getContentType(), originalFilename)) {
            throw new ServiceException("Only supports xls and xlsx files");
        }

        // 检查名称、版本号是否存在
        checkNameAndVersion(name, type, version, id);

        final String temPre = "NODE_";
        String fileName = temPre + (ExpSampleTypeEnum.experiment.name().equals(type) ? "EXP_" : "SAP_");
        if (Boolean.TRUE.equals(isExample)) {
            fileName = "Example_" + fileName;
        }
        if (StrUtil.isNotBlank(parentName)) {
            fileName += parentName + "(" + name + ")";
        } else {
            fileName += name;
        }
        fileName += "_v" + version + "." + FileUtil.extName(originalFilename);
        try {
            final File saveFile = MyFileUtils.expSampleTemplate(DirConstants.DATA_HOME, fileName);
            if (saveFile.exists()) {
                saveFile.delete();
            }
            saveFile.createNewFile();
            file.transferTo(saveFile);
            return saveFile.getName();
        } catch (IOException e) {
            log.info("Upload error:", e);
            throw new ServiceException("Upload error:" + e.getMessage());
        }
    }

    /**
     * 检查名称、版本号是否存在
     */
    private List<ExpSampleType> checkNameAndVersion(final String name, final String type, final String version, final String id) {
        final List<ExpSampleType> list = expSampleTypeRepository.findByNameAndType(name, type);
        if (CollUtil.isNotEmpty(list)) {
            for (final ExpSampleType sampleType : list) {
                if (sampleType.getVersion().equalsIgnoreCase(version)) {
                    if (id == null || !id.equals(sampleType.getId())) {
                        throw new ServiceException(StrUtil.format("Name and version already exist"));
                    }
                }
            }
        }
        return list;
    }

    @Override
    public boolean saveExpSampleType(ExpSampleType expSampleType, final String username) {
        // 删除空白字符
        expSampleType = BeanUtils.trimStrToNullFields(expSampleType);

        // 校验参数
        final String name = expSampleType.getName();
        final String type = expSampleType.getType();
        final String version = expSampleType.getVersion();
        final String id = expSampleType.getId();
        // 校验名称、版本号是否已存在
        final List<ExpSampleType> list = checkNameAndVersion(name, type, version, id);

        final String desc = expSampleType.getDesc();
        final String protocol = expSampleType.getProtocol();
        if (StrUtil.isBlank(desc) || StrUtil.isBlank(protocol)) {
            throw new ServiceException("Parameter error");
        }

        final File templateFile = MyFileUtils.expSampleTemplate(DirConstants.DATA_HOME, expSampleType.getTemplateName());
        if (!templateFile.exists() || !templateFile.isFile()) {
            throw new ServiceException("Template file not exist");
        }
        final File exampleFile = MyFileUtils.expSampleTemplate(DirConstants.DATA_HOME, expSampleType.getExampleName());
        if (!exampleFile.exists() || !exampleFile.isFile()) {
            throw new ServiceException("Example file not exist");
        }

        final Date now = new Date();
        expSampleType.setUpdateTime(now);
        if (StrUtil.isBlank(id)) {
            // 新增
            expSampleType.setCreator(username);
            expSampleType.setCreateTime(now);
        } else {
            // 编辑
            final ExpSampleType dbData = expSampleTypeRepository.findById(id).orElseThrow(() -> new ServiceException("Data not found"));
            expSampleType.setCreator(dbData.getCreator());
            expSampleType.setCreateTime(dbData.getCreateTime());
            expSampleType.setAttributes(dbData.getAttributes());
        }
        // 启用某一项时，禁用其它相同的名称数据
        enableHandler(list, expSampleType.getId(), expSampleType);

        expSampleTypeRepository.save(expSampleType);
        return true;
    }

    /**
     * 组学(样本)类型字典数据导出
     */
    @Override
    public void exportStand(HttpServletRequest request, HttpServletResponse response, final String standType) {
        final ExpSampleTypeEnum typeEnum = getTypeEnum(standType);
        final List<ExpSampleType> all = expSampleTypeRepository.findByType(typeEnum);
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("No data");
        }
        InputStream inputStream = null;
        try {
            // 读取模版文件中基础信息
            final String filesPath = "/files/NODE_Dataset_standards.xlsx";
            inputStream = this.getClass().getResourceAsStream(filesPath);
            final ExcelUtil<StandSetExportDTO> util = new ExcelUtil<>(StandSetExportDTO.class);
            final List<StandSetExportDTO> standSetExportDTOS = util.importExcel(inputStream);
            final List<StandSetExportDTO> baseData = new ArrayList<>();
            final Map<String, StandSetExportDTO> templateMap = new LinkedHashMap<>();
            for (StandSetExportDTO item : standSetExportDTOS) {
                final String dataSet = item.getDataSet();
                final String submodule = item.getSubmodule();
                final String fieldName = item.getFieldName();
                if (standType.equalsIgnoreCase(dataSet) && "general".equalsIgnoreCase(submodule)) {
                    baseData.add(item);
                }
                templateMap.put(genKey(dataSet, submodule, fieldName), item);
            }

            final List<StandSetExportDTO> data = new ArrayList<>(baseData);
            for (ExpSampleType item : all) {
                List<ExpSampleType.Attributes> attributes = item.getAttributes();
                if (CollUtil.isNotEmpty(attributes)) {
                    attributes = attributes.stream().filter(x -> DataStatusEnum.enable.name().equals(x.getStatus()))
                            .sorted(new ExpSampleType.MyAttributesComparator()).collect(Collectors.toList());

                    final String name = item.getName();
                    for (ExpSampleType.Attributes attribute : attributes) {
                        final StandSetExportDTO dto = new StandSetExportDTO();
                        dto.setDataSet(StrUtil.upperFirst(standType));
                        dto.setSubmodule(name);
                        final String attributesField = attribute.getAttributesField();
                        dto.setFieldName(attributesField);

                        dto.setFieldMeaning(attribute.getDescription());

                        final String dataType = attribute.getDataType();
                        dto.setDataType(ExpSampleDataType.initExportType(dataType));

                        dto.setUnique(YesOrNo.No.getTitle());
                        dto.setRequired(BaseAttrType.findYesOrNoByType(attribute.getRequired()).getTitle());

                        dto.setVersion(item.getVersion());
                        dto.setIntroductionDate(DateUtils.formatDateToStr("yyyy/MM/dd", attribute.getCreateTime()));

                        final List<Object> valueRange = attribute.getValueRange();
                        if (CollUtil.isNotEmpty(valueRange)) {
                            if (ExpSampleDataType.Select.name().equals(dataType)) {
                                dto.setRemark(genCellValue(StrUtil.join(",\n", valueRange)));
                            } else if (ExpSampleDataType.Select2.name().equals(dataType)) {
                                dto.setRemark(genCellValue(JSON.toJSONString(valueRange)));
                            }
                        }

                        dto.setParentRecord(item.getParentName());

                        final StandSetExportDTO temDto = templateMap.get(genKey(dto.getDataSet(), dto.getSubmodule(), dto.getFieldName()));
                        if (temDto != null) {
//                            dto.setEmpty(temDto.getEmpty());
                            if (dto.getRemark() == null) {
                                dto.setRemark(temDto.getRemark());
                            }
                        }
                        data.add(dto);
                    }
                }
            }
            util.exportExcel(response, data, "Standard Data Set");
        } catch (Exception e) {
            log.error("组学(样本)类型字典数据导出出错", e);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    private String genKey(String dataSet, String submodule, String fieldName) {
        return String.join("_", dataSet, submodule, fieldName).toLowerCase();
    }

    private String genCellValue(String value) {
        if (value == null) {
            return null;
        }
        // poi中，excel单元格字符最大长度为32767
        final int maxLength = 32000;
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }


    /**
     * 属性详情列表
     */
    @Override
    public StandMgAttrVO attrDetail(final String standType, final String standId) {
        if (StrUtil.isBlank(standId)) {
            throw new ServiceException("id cannot be empty");
        }
        final ExpSampleType expSampleType = expSampleTypeRepository.findById(standId).orElseThrow(() -> new ServiceException("Data not found"));
        if (!expSampleType.getType().equals(standType)) {
            throw new ServiceException("Standard type error");
        }
        final StandMgAttrVO vo = new StandMgAttrVO();
        vo.setExpSampleType(expSampleType);

        vo.setAttrTypeList(Arrays.stream(ExpSampleDataType.values()).map(item -> {
            final SelectVo selectVo = new SelectVo();
            selectVo.setLabel(item.getTitle());
            selectVo.setValue(item.name());
            selectVo.setRemark(item.getRemark());
            return selectVo;
        }).collect(Collectors.toList()));
        vo.setStatusList(initSelect(CollUtil.toList(DataStatusEnum.values())));
        vo.setRequiredList(initSelect(CollUtil.toList(BaseAttrType.values())));
        return vo;
    }

    /**
     * 保存属性数据
     */
    @Override
    public boolean saveAttr(StandMgAttrDTO dto, final String username) {
        final ExpSampleType expSampleType = checkExpSampleType(dto);
        expSampleTypeRepository.save(expSampleType);
        return true;
    }

    /**
     * 删除属性
     */
    @Override
    public boolean delAttr(final String standId, final String id) {
        final ExpSampleType expSampleType = expSampleTypeRepository.findById(standId).orElseThrow(() -> new ServiceException("Standard data not found"));
        final List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
        if (CollUtil.isNotEmpty(attributes)) {
            final Iterator<ExpSampleType.Attributes> iterator = attributes.iterator();
            while (iterator.hasNext()) {
                if (iterator.next().getId().equals(id)) {
                    iterator.remove();
                    break;
                }
            }
            if (CollUtil.isEmpty(attributes)) {
                expSampleType.setAttributes(null);
            } else {
                expSampleType.setAttributes(attributes);
            }
            expSampleTypeRepository.save(expSampleType);
        }
        return true;
    }

    /**
     * 修改属性数据状态
     */
    @Override
    public boolean updateAttrStatus(StandMgDataDTO standMgDataDTO) {
        final String id = standMgDataDTO.getId();
        final String standId = standMgDataDTO.getStandId();
        if (StrUtil.isBlank(id) || StrUtil.isBlank(standId)) {
            throw new ServiceException("Parameter error!");
        }
        final ExpSampleType expSampleType = expSampleTypeRepository.findById(standId).orElseThrow(() -> new ServiceException("Data not found"));
        final List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
        if (CollUtil.isEmpty(attributes)) {
            throw new ServiceException("Attributes is empty");
        }
        final boolean disable = standMgDataDTO.isDisable();
        final String status = disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name();
        final Date now = new Date();
        boolean hasAttr = false;
        for (ExpSampleType.Attributes attr : attributes) {
            if (id.equals(attr.getId())) {
                hasAttr = true;
                attr.setStatus(status);
                attr.setUpdateTime(now);
                break;
            }
        }
        if (!hasAttr) {
            throw new ServiceException("Attribute not found");
        }
        expSampleType.setUpdateTime(now);
        expSampleTypeRepository.save(expSampleType);
        return true;
    }

    /**
     * 属性数据导出
     */
    @Override
    public void exportAttr(HttpServletResponse response, StandMgDataDTO standMgDataDTO) {
        final ExpSampleType expSampleType = expSampleTypeRepository.findById(standMgDataDTO.getStandId()).orElseThrow(() -> new ServiceException("Data not found"));
        final List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
        if (CollUtil.isEmpty(attributes)) {
            throw new ServiceException("Attributes is empty");
        }
        final List<StandMgAttrDTO> standMgAttrDTOS = AttributesMapper.INSTANCE.copyListToDTO(attributes);
        final Map<String, ExpSampleDataType> cellTypeMap = ExpSampleDataType.nameMap();
        for (StandMgAttrDTO standMgAttrDTO : standMgAttrDTOS) {
            final String dataType = standMgAttrDTO.getDataType();

            final ExpSampleDataType typeEnum = cellTypeMap.get(dataType);
            if (typeEnum == null) {
                throw new ServiceException("Illegal attribute input type");
            }
            standMgAttrDTO.setDataTypeStr(typeEnum.getTitle());
            final List<Object> valueRange = standMgAttrDTO.getValueRange();
            if (ExpSampleDataType.Select == typeEnum) {
                standMgAttrDTO.setSelectStr(CollUtil.join(valueRange, "\n"));
                standMgAttrDTO.setAllowCreateStr(excelBoolStrVal(standMgAttrDTO.isAllowCreate()));
            } else if (ExpSampleDataType.Select2 == typeEnum) {
                standMgAttrDTO.setJsonStr(JSON.toJSONString(valueRange));
                standMgAttrDTO.setAllowCreateStr(excelBoolStrVal(standMgAttrDTO.isAllowCreate()));
            } else {
                standMgAttrDTO.setAllowCreateStr(null);
            }
        }
        final ExcelUtil<StandMgAttrDTO> util = new ExcelUtil<>(StandMgAttrDTO.class);
        util.exportExcel(response, standMgAttrDTOS, "Attributes data");
    }

    /**
     * 属性数据批量导入
     */
    @Override
    public String importAttrData(final MultipartFile file, final String username, final String type,
                                 final Boolean deleteOld, final String standId) {
        if (StrUtil.isBlank(username)) {
            throw new ServiceException("no permission");
        }
        if (StrUtil.isBlank(type) || StrUtil.isBlank(standId)) {
            throw new ServiceException("Parameter error");
        }
        ExcelUtil<StandMgAttrDTO> util = new ExcelUtil<>(StandMgAttrDTO.class);
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            final List<StandMgAttrDTO> dataList = util.importExcel(inputStream);
            if (CollUtil.isEmpty(dataList)) {
                throw new ServiceException("Importing data cannot be empty");
            }
            final Map<String, ExpSampleDataType> titleMap = ExpSampleDataType.titleMap();
            ExpSampleType data = null;
            for (StandMgAttrDTO dto : dataList) {
                if (data == null) {
                    data = expSampleTypeRepository.findById(standId).orElseThrow(() -> new ServiceException("Standard data not found"));
                    if (Boolean.TRUE.equals(deleteOld)) {
                        data.setAttributes(null);
                    }
                }
                dto.setType(type);
                dto.setStandId(standId);
                final String dataTypeStr = dto.getDataTypeStr();
                final ExpSampleDataType typeEnum = titleMap.get(dataTypeStr);
                final String attributesName = dto.getAttributesName();
                if (typeEnum == null) {
                    throw new ServiceException(StrUtil.format("Illegal attribute input type: {}", attributesName));
                }
                dto.setDataType(typeEnum.name());
                if (ExpSampleDataType.Select == typeEnum) {
                    dto.setAllowCreate(excelBoolVal(dto.getAllowCreateStr()));
                } else if (ExpSampleDataType.Select2 == typeEnum) {
                    dto.setAllowCreate(excelBoolVal(dto.getAllowCreateStr()));
                } else {
                    dto.setAllowCreate(false);
                }
                // 校验参数
                BeanValidators.validateWithException(validator, dto);

                dto.setExpSampleType(data);
                data = checkExpSampleType(dto);
            }
            if (data != null) {
                expSampleTypeRepository.save(data);
            }
            return "success";
        } catch (Exception e) {
            log.warn("导入失败", e);
            return e.getMessage();
        } finally {
            IoUtil.close(inputStream);
        }
    }

    private String excelBoolStrVal(final boolean val) {
        return String.valueOf(val).toUpperCase();
    }

    private boolean excelBoolVal(final String val) {
        return val != null && Boolean.parseBoolean(val.toLowerCase());
    }

    /**
     * 校验提交的属性数据
     */
    private ExpSampleType checkExpSampleType(StandMgAttrDTO dto) {
        // 删除空白字符
        dto = BeanUtils.trimStrToNullFields(dto);
        final ExpSampleTypeEnum standardType = getTypeEnum(dto.getType());
        // group校验
        if (standardType == ExpSampleTypeEnum.sample) {
            if (dto.getGroup() == null) {
                throw new ServiceException("The group cannot be empty");
            }
        } else {
            dto.setGroup(null);
        }

        // 判断是否为系统保留字段
        final String attributesField = dto.getAttributesField();
        if (isReservedField(attributesField)) {
            throw new ServiceException(StrUtil.format("'{}' is a system reserved field!", attributesField));
        }

        final ExpSampleDataType inputType = ExpSampleDataType.findByName(dto.getDataType()).orElseThrow(() -> new ServiceException("Illegal attribute input type"));
        switch (inputType) {
            case Select:
                final String selectStr = dto.getSelectStr();
                if (StrUtil.isBlank(selectStr)) {
                    throw new ServiceException("Select value range cannot be empty");
                }
                final String[] split = selectStr.split("\n");
                if (ArrayUtil.isEmpty(split)) {
                    throw new ServiceException("Select value range cannot be empty");
                }
                final Set<String> valCol = new LinkedHashSet<>();
                for (String s : split) {
                    s = StrUtil.trimToNull(s);
                    if (s == null) {
                        continue;
                    }
                    valCol.add(s);
                }
                if (CollUtil.isEmpty(valCol)) {
                    throw new ServiceException("Select value range cannot be empty");
                }
                dto.setValueRange(new ArrayList<>(valCol));
                break;
            case Select2:
                final String jsonStr = dto.getJsonStr();
                if (StrUtil.isBlank(jsonStr)) {
                    throw new ServiceException(inputType.getTitle() + " JSON data cannot be empty");
                }
                List<Select2Item> select2Items;
                try {
                    select2Items = JSON.parseArray(jsonStr, Select2Item.class);
                    if (CollUtil.isEmpty(select2Items)) {
                        throw new ServiceException(inputType.getTitle() + " JSON data cannot be empty");
                    }
                    // parent_name和value_array都不能重复
                    final Set<String> parentNames = new HashSet<>();
                    final Set<String> vals = new HashSet<>();
                    for (Select2Item select2Item : select2Items) {
                        final String parentName = StrUtil.trimToNull(select2Item.getParent_name());
                        if (parentName == null) {
                            throw new ServiceException(StrUtil.format("'parent_name' cannot be empty in “{}”", inputType.getTitle()));
                        }
                        if (parentNames.contains(parentName)) {
                            throw new ServiceException(StrUtil.format("'parent_name' duplication in “{}”: {}", inputType.getTitle(), parentName));
                        } else {
                            parentNames.add(parentName);
                        }

                        final List<String> value_array = select2Item.getValue_array();
                        if (CollUtil.isEmpty(value_array)) {
                            throw new ServiceException(StrUtil.format("'value_array' cannot be empty in “{}”", inputType.getTitle()));
                        }
                        final List<String> value_array_trim = new ArrayList<>();
                        for (String val : value_array) {
                            val = StrUtil.trimToNull(val);
                            if (val == null) {
                                throw new ServiceException(StrUtil.format("'value_array' value cannot be empty of “{}” in “{}”", parentName, inputType.getTitle()));
                            }
                            if (vals.contains(val)) {
                                throw new ServiceException(StrUtil.format("'value_array' duplication in “{}”: {}", inputType.getTitle(), val));
                            } else {
                                vals.add(val);
                            }
                            value_array_trim.add(val);
                        }
                        // 将trim后的字段覆盖原字段
                        select2Item.setParent_name(parentName);
                        select2Item.setValue_array(value_array_trim);
                    }
                } catch (ServiceException e) {
                    throw e;
                } catch (JSONException e) {
                    throw new ServiceException(inputType.getTitle() + " JSON data format error");
                }
                dto.setValueRange(new ArrayList<>(select2Items));
                break;
            case Custom:
                break;
            default:
                dto.setAllowCreate(false);
                break;
        }
        if (inputType != ExpSampleDataType.Custom) {
            dto.setDataSource(null);
        }
        final String standId = dto.getStandId();
        final ExpSampleType expSampleType = dto.getExpSampleType() == null
                ? expSampleTypeRepository.findById(standId).orElseThrow(() -> new ServiceException("Standard data not found"))
                : dto.getExpSampleType();

        final String id = dto.getId();

        // 检查属性名称和字段名不能重复
        List<ExpSampleType.Attributes> attributesSaved = expSampleType.getAttributes();
        if (CollUtil.isNotEmpty(attributesSaved)) {
            for (final ExpSampleType.Attributes attr : attributesSaved) {
                if (attr.getAttributesName().equals(dto.getAttributesName())) {
                    if (id == null || !id.equals(attr.getId())) {
                        throw new ServiceException(StrUtil.format("Attribute name already exist"));
                    }
                }
                if (attr.getAttributesField().equals(attributesField)) {
                    if (id == null || !id.equals(attr.getId())) {
                        throw new ServiceException(StrUtil.format("Attribute field already exist"));
                    }
                }
            }
        }


        final Date now = new Date();
        if (id == null) {
            // 新增
            if (attributesSaved == null) {
                attributesSaved = new ArrayList<>();
            }
            final ExpSampleType.Attributes attributes = new ExpSampleType.Attributes();
            AttributesMapper.INSTANCE.copyToDb(dto, attributes);
            attributes.setId(IdUtil.fastSimpleUUID());
            attributes.setCreateTime(now);
            attributes.setUpdateTime(now);
            attributesSaved.add(attributes);
        } else {
            // 编辑
            if (CollUtil.isEmpty(attributesSaved)) {
                throw new ServiceException(StrUtil.format("Attribute data not found"));
            }
            boolean hasData = false;
            for (ExpSampleType.Attributes attr : attributesSaved) {
                if (id.equals(attr.getId())) {
                    hasData = true;
                    final Date createTime = attr.getCreateTime();
                    AttributesMapper.INSTANCE.copyToDb(dto, attr);
                    attr.setCreateTime(createTime);
                    attr.setUpdateTime(now);
                    break;
                }
            }
            if (!hasData) {
                throw new ServiceException(StrUtil.format("Attribute data not found:" + id));
            }
        }
        expSampleType.setAttributes(attributesSaved);
        return expSampleType;
    }

    /**
     * 判断属性名称是否为保留字段
     */
    private boolean isReservedField(final String attributesField) {
        final List<String> list = ExpSampleType.allBaseColumn();
        for (String s : list) {
            if (s.equalsIgnoreCase(attributesField)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断属性、推荐属性配置是否合法
     * 启用某一项时，禁用其它相同的名称数据
     */
    private void enableHandler(final List<ExpSampleType> list, final String currId, final ExpSampleType expSampleType) {
        // 数据启用
        if (DataStatusEnum.enable.name().equals(expSampleType.getStatus())) {
            // 校验属性不能为空
            final List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            final int attrSize = attributes == null ? 0 :
                    CollUtil.size(attributes.stream().filter(x -> DataStatusEnum.enable.name().equals(x.getStatus())).collect(Collectors.toList()));
            if (attrSize == 0) {
                throw new ServiceException("The enabled attributes cannot be empty");
            }

            // 校验启用数据的推荐字段数不能超过属性总数
            int recAttrCount = 0;
            for (ExpSampleType.Attributes attribute : attributes) {
                if (BaseAttrType.recommend.name().equals(attribute.getRequired()) && DataStatusEnum.enable.name().equals(attribute.getStatus())) {
                    recAttrCount++;
                }
            }
            if (intVal(expSampleType.getRecommendNum()) > recAttrCount) {
                throw new ServiceException("The Recommend Num cannot be greater than the total number of recommended attributes");
            }

            // 启用某一项时，禁用其它相同的名称数据
            if (CollUtil.isNotEmpty(list)) {
                final List<ExpSampleType> disableList = new ArrayList<>();
                for (ExpSampleType sampleType : list) {
                    if (currId == null || !currId.equals(sampleType.getId())) {
                        sampleType.setStatus(DataStatusEnum.disable.name());
                        sampleType.setUpdateTime(new Date());
                        disableList.add(sampleType);
                    }
                }
                if (CollUtil.isNotEmpty(disableList)) {
                    expSampleTypeRepository.saveAll(disableList);
                }
            }
        }
    }

    private int intVal(Integer v) {
        return v == null ? 0 : v;
    }

    @Override
    public List<String> getAllNames() {
        List<ExpSampleType> expTypes = expSampleTypeRepository.listExpType();
        List<ExpSampleType> sapTypes = expSampleTypeRepository.listSampleType();
        List<String> names = new ArrayList<>();
        names.addAll(expTypes.stream().map(ExpSampleType::getName).collect(Collectors.toList()));
        names.addAll(sapTypes.stream().map(ExpSampleType::getName).collect(Collectors.toList()));
        return names;
    }
}
