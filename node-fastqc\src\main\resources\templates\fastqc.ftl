#!/bin/sh

set -e
export LC_ALL=en_US.utf-8
echo `date` '${dataNo} started fastqc ...'
${initCommand}
# 如果软链接不纯在 建立软链接
if [ ! -L ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType} ]; then
ln -s ${nodeDataPath}/${dataFilePath} ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType}
fi

# 执行seqkit
singularity exec -B ${nodeDataPath}:${nodeDataPath} -B ${fastqcPath}:${fastqcPath} -B ${imagePath}:${imagePath} -B ${scriptPath}:${scriptPath} ${imagePath}/seqkit-2.8.1.sif \
${scriptPath}/shell/seqkit.sh ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType} ${fastqcPath}/${resultBaseDir}/${dataNo} ${dataFileName} \
${thread} > ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.fastqc.log 2>> ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.fastqc.err.txt

#执行fastqc
singularity exec -B ${nodeDataPath}:${nodeDataPath} -B ${fastqcPath}:${fastqcPath} -B ${imagePath}:${imagePath} -B ${scriptPath}:${scriptPath} ${imagePath}/trim_galore-0.6.10.sif \
${scriptPath}/shell/fastqc.sh ${fastqcPath}/${resultBaseDir}/${dataNo}/${dataFileName}${fileType} ${fastqcPath}/${resultBaseDir}/${dataNo} ${dataFileName} \
${thread}  > ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.fastqc.log 2>> ${fastqcPath}/${resultBaseDir}/${dataNo}/qsub/qsub.fastqc.err.txt


echo `date` '${dataNo} finished fastqc ...'

sleep 1 && echo 'RUN COMPLETED!' && sync
