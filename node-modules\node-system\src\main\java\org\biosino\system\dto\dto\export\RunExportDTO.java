package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> @date 2025/1/10
 */
@Data
public class RunExportDTO {
    @JSONField(name = "id", ordinal = 1)
    private String id;

    @JSONField(name = "run_no", ordinal = 2)
    private String runNo;

    @JSONField(name = "sub_no", ordinal = 3)
    private String subNo;

    @JSONField(name = "exp_no", ordinal = 4)
    private String expNo;

    @JSONField(name = "sap_no", ordinal = 5)
    private String sapNo;

    @JSONField(name = "name", ordinal = 6)
    private String name;

    @JSONField(name = "description", ordinal = 7)
    private String description;

    @JSONField(name = "related_links", ordinal = 8)
    private List<String> relatedLinks;

    @JSONField(name = "creator", ordinal = 9)
    private String creator;

    @JSONField(name = "submission_date", ordinal = 10)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 11)
    private Date updateDate;

    @JSONField(name = "public_date", ordinal = 12)
    private Date publicDate;

    @JSONField(name = "temp_data", ordinal = 13)
    private Run tempData;

    @JSONField(name = "hit_num", ordinal = 14)
    private Long hitNum;

    @JSONField(name = "export_num", ordinal = 15)
    private Long exportNum;

    @JSONField(name = "submitter", ordinal = 16)
    private Submitter submitter;

    @JSONField(name = "other_ids", ordinal = 17)
    private List<OtherIds> otherIds;

    @JSONField(name = "operator", ordinal = 18)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 19)
    private Date operationDate;

    @JSONField(name = "used_ids", ordinal = 20)
    private List<String> usedIds;

    @JSONField(name = "ownership", ordinal = 21)
    private String ownership;

    @JSONField(name = "source_project", ordinal = 22)
    private List<String> sourceProject;

    @JSONField(name = "visible_status", ordinal = 23)
    private String visibleStatus;

    @JSONField(name = "audited", ordinal = 24)
    private String audited;
}
