package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.MD5Encode;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.common.mongo.entity.Review;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.dto.PublishDTO;
import org.biosino.upload.mq.IndexUpdateEvent;
import org.biosino.upload.repository.ResourceAuthorizeRepository;
import org.biosino.upload.repository.ReviewRepository;
import org.biosino.upload.repository.ShareRepository;
import org.biosino.upload.repository.SubmissionRepository;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;
import org.biosino.upload.vo.ErrorMsgVO;
import org.biosino.upload.vo.PublishVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.upload.tool.excelparseutil.imp.ExcelParser.RELATED_LINKS_SEPARATOR;

@Service
public class BaseService {
    @Autowired
    private PublishService publishService;
    @Autowired
    private SubmissionRepository submissionRepository;
    @Autowired
    private UserCenterDeleteLogService userCenterDeleteLogService;
    @Autowired
    private RemoteMemberService remoteMemberService;
    @Autowired
    private ShareRepository shareRepository;
    @Autowired
    private ReviewRepository reviewRepository;
    @Autowired
    private ResourceAuthorizeRepository resourceAuthorizeRepository;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RemoteDataService remoteDataService;


    /**
     * 获取必须存在的列索引号
     */
    public static int requiredCol(final List<String> titles, String title) {
        final int i = titles.indexOf(title);
        if (i < 0) {
            throw new ServiceException("The format of file is wrong. Column not found: " + title);
        }
        return i;
    }

    /**
     * 获取表格数据，转为字符串
     */
    public static String strVal(Object obj) {
        return obj == null ? null : StrUtil.trimToNull(obj.toString());
    }

    public Submission getEditSubmissionByNo(String subNo) {
        Submission submission = submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("Submission data not found"));
        checkSubmission(submission);
        return submission;
    }

    public Submission getSubmissionByNo(String subNo) {
        return submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("Submission data not found"));
    }

    private static void checkSubmission(Submission submission) {
        if (!Objects.equals(SecurityUtils.getMemberId(), submission.getCreator())) {
            throw new ServiceException("No editing permission! You are not the owner of the current submission data");
        }
        if (!SubmissionStatusEnum.editing.name().equals(submission.getStatus())) {
            throw new ServiceException("The current submission does not allow editing");
        }
    }

    public void saveEditSubmission(Submission submission) {
        saveSubmission(submission, true);
    }

    public void saveSubmission(Submission submission) {
        saveSubmission(submission, false);
    }

    public void saveSubmission(Submission submission, boolean check) {
        submission.setUpdateTime(new Date());
        if (check) {
            checkSubmission(submission);
        }
        submissionRepository.save(submission);
    }

    public void savePublish(List<PublishDTO> publishDTO, AuthorizeType authorizeType, String typeId) {
        publishService.save(publishDTO, authorizeType.name(), typeId, false);
    }

    public void saveEditPublish(List<PublishDTO> publishDTO, AuthorizeType authorizeType, String typeId) {
        publishService.save(publishDTO, authorizeType.name(), typeId, true);
    }

    public void updatePublish(AuthorizeType authorizeType, String oldId, String newId) {
        publishService.updatePublish(authorizeType.name(), oldId, newId);
    }

    public void submitPublish(AuthorizeType authorizeType, String typeId) {
        publishService.submitPublish(authorizeType.name(), typeId);
    }

    public List<PublishVO> getTempPublishVO(AuthorizeType authorizeType, String typeId) {
        return publishService.getTempPublishVO(authorizeType.name(), typeId);
    }

    public List<PublishVO> getPublishVO(AuthorizeType authorizeType, String typeId) {
        return publishService.getPublishVo(authorizeType.name(), typeId);
    }

    /**
     * 将excel行数据，转换为列数据
     */
    public Map<String, List<Object>> getExcelMap(List<String> titles, List<Object[]> datas) {
        Map<String, List<Object>> excelMap = new LinkedHashMap<>();
        for (int i = 0; i < titles.size(); i++) {
            List<Object> colList = new ArrayList<>();
            for (Object[] data : datas) {
                colList.add(data[i]);
            }
            excelMap.put(titles.get(i), colList);
        }
        return excelMap;
    }

    public void mustFilledCheck(Map<String, List<Object>> excelMap, String item, List<ErrorMsgVO> errors) {
        List<Object> itemList = excelMap.get(item);
        if (CollUtil.isNotEmpty(itemList)) {
            for (int i = 0; i < itemList.size(); i++) {
                if (itemList.get(i) == null || StringUtil.isBlank(itemList.get(i).toString())) {
                    errors.add(errMsg(i, item, "", "  can't be empty"));
                }
            }
        }
    }

    public static ErrorMsgVO errMsg(Integer rowNum, String columnName, String value, String message) {
        if (rowNum == null) {
            return new ErrorMsgVO(null, columnName, value, message);
        }
        return new ErrorMsgVO(rowNum + 1, columnName, value, message);
    }

    /**
     * 字段为空错误信息
     */
    public static void emptyErr(int rowNum, List<ErrorMsgVO> errors, String title) {
        errors.add(errMsg(rowNum, title, null, "can't be empty"));
    }

    /**
     * 字段重复错误
     */
    public static void duplicateErr(int rowNum, List<ErrorMsgVO> errors, Collection<String> coll, String title, String val) {
        if (coll.contains(val)) {
            errors.add(errMsg(rowNum, title, val, "is duplicated"));
        } else {
            coll.add(val);
        }
    }

    /**
     * 下拉框数据不匹配
     */
    public static void selectionErr(int rowNum, List<ErrorMsgVO> errors, List<String> range, String title, String val) {
        if (!range.contains(val)) {
            errors.add(errMsg(rowNum, title, val, "Not within the scope of selection"));
        }
    }

    public static String dateErr(int rowNum, List<ErrorMsgVO> errors, String title, String val) {
        try {
            return DateFormatUtils.format(ExcelParser.parseDateStr(val), DatePattern.NORM_DATETIME_PATTERN);
        } catch (ParseException e) {
            errors.add(errMsg(rowNum, title, val, "Incorrect date format"));
            return null;
        }
    }


    /**
     * Related Link拼接字符串
     *
     * @param relatedLinks
     * @return
     */
    public static String relatedLinksStr(List<String> relatedLinks) {
        return CollUtil.isEmpty(relatedLinks) ? null : CollUtil.join(relatedLinks, RELATED_LINKS_SEPARATOR);
    }

    /**
     * 删除map中值为空的项，若全部值为空则返回null
     *
     * @param map
     * @param <T>
     * @return
     */
    public static <T> Map<String, T> deleteEmptyMap(Map<String, T> map) {
        if (CollUtil.isEmpty(map)) {
            return null;
        }
        map.entrySet().removeIf(entry -> strVal(entry.getValue()) == null);
        if (CollUtil.isEmpty(map)) {
            return null;
        } else {
            return map;
        }
    }

    /**
     * 添加用户中心删除数据的日志
     */
    public void addUserCenterDeleteLog(String no, String type, DeleteCheckResultVO checkResultVO) {
        userCenterDeleteLogService.addDeleteLog(no, type, checkResultVO);
    }

    /**
     * 校验密码是否正确
     */
    public void checkPassword(String password) {
        R<MemberDTO> r = remoteMemberService.getOneMemberByMemberId(SecurityUtils.getMemberId(), "FtpUser", SecurityConstants.INNER);
        if (r.getData() == null || R.isError(r)) {
            throw new ServiceException("Member Service Error");
        }
        MemberDTO data = r.getData();
        String password2 = data.getPassword();

        if (!SecurityUtils.matchesPassword(MD5Encode.encode(password), password2)) {
            throw new ServiceException("Err: Password error");
        }
    }

    /**
     * 删除校验时检查数据有没有在share review request
     */
    public void validateShareAndReviewAndRequest(DeleteCheckResultVO checkResultVO, String memberId) {
        // 校验被删除的数据在哪些share里面使用
        validateShare(checkResultVO, memberId);

        // 校验被删除的数据在哪些review里面使用
        // validateReview(checkResultVO, memberId);
        // 校验被删除的数据在哪些request里面使用
        // validateResourceAuthorize(checkResultVO, memberId);
    }

    public void validateShare(DeleteCheckResultVO checkResultVO, String memberId) {
        HashMap<String, LinkedHashSet<String>> shareMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(checkResultVO.getProjNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.project.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getProjNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> projectNos = share.getProjects().stream().map(ShareProject::getProjectNo).collect(Collectors.toList());
                    for (String projNo : checkResultVO.getProjNos()) {
                        if (projectNos.contains(projNo)) {
                            shareMap.computeIfAbsent(projNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getExpNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.experiment.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getExpNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> expNos = share.getExperiments().stream().map(ShareExperiment::getExpNo).collect(Collectors.toList());
                    for (String expNo : checkResultVO.getExpNos()) {
                        if (expNos.contains(expNo)) {
                            shareMap.computeIfAbsent(expNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getRunNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.run.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getRunNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> runNos = share.getRuns().stream().map(ShareRun::getRunNo).collect(Collectors.toList());
                    for (String runNo : checkResultVO.getRunNos()) {
                        if (runNos.contains(runNo)) {
                            shareMap.computeIfAbsent(runNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getSapNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.sample.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getSapNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> sapNos = share.getSamples().stream().map(ShareSample::getSapNo).collect(Collectors.toList());
                    for (String sapNo : checkResultVO.getSapNos()) {
                        if (sapNos.contains(sapNo)) {
                            shareMap.computeIfAbsent(sapNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getAnalNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.analysis.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getAnalNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> analNos = share.getAnalysis().stream().map(ShareAnalysis::getAnalNo).collect(Collectors.toList());
                    for (String analNo : checkResultVO.getAnalNos()) {
                        if (analNos.contains(analNo)) {
                            shareMap.computeIfAbsent(analNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.data.name());
            List<Share> shareList = shareRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getDataNos(), memberId);
            if (CollUtil.isNotEmpty(shareList)) {
                for (Share share : shareList) {
                    List<String> dataNos = share.getDatas().stream().map(ShareData::getDatNo).collect(Collectors.toList());
                    for (String dataNo : checkResultVO.getDataNos()) {
                        if (dataNos.contains(dataNo)) {
                            shareMap.computeIfAbsent(dataNo, k -> new LinkedHashSet<>()).add(share.getShareId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(shareMap)) {
            checkResultVO.setShareMap(shareMap);
        }
    }

    public void validateReview(DeleteCheckResultVO checkResultVO, String memberId) {
        HashMap<String, LinkedHashSet<String>> reviewMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(checkResultVO.getProjNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.project.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getProjNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> projectNos = review.getProjects().stream().map(ShareProject::getProjectNo).collect(Collectors.toList());
                    for (String projNo : checkResultVO.getProjNos()) {
                        if (projectNos.contains(projNo)) {
                            reviewMap.computeIfAbsent(projNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getExpNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.experiment.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getExpNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> expNos = review.getExperiments().stream().map(ShareExperiment::getExpNo).collect(Collectors.toList());
                    for (String expNo : checkResultVO.getExpNos()) {
                        if (expNos.contains(expNo)) {
                            reviewMap.computeIfAbsent(expNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getRunNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.run.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getRunNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> runNos = review.getRuns().stream().map(ShareRun::getRunNo).collect(Collectors.toList());
                    for (String runNo : checkResultVO.getRunNos()) {
                        if (runNos.contains(runNo)) {
                            reviewMap.computeIfAbsent(runNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getSapNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.sample.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getSapNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> sapNos = review.getSamples().stream().map(ShareSample::getSapNo).collect(Collectors.toList());
                    for (String sapNo : checkResultVO.getSapNos()) {
                        if (sapNos.contains(sapNo)) {
                            reviewMap.computeIfAbsent(sapNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getAnalNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.analysis.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getAnalNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> analNos = review.getAnalysis().stream().map(ShareAnalysis::getAnalNo).collect(Collectors.toList());
                    for (String analNo : checkResultVO.getAnalNos()) {
                        if (analNos.contains(analNo)) {
                            reviewMap.computeIfAbsent(analNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            TypeInformation typeInfo = TypeInformation.typeInfoMap.get(AuthorizeType.data.name());
            List<Review> reviewList = reviewRepository.findByTypeNoInAndCreator(typeInfo, checkResultVO.getDataNos(), memberId);
            if (CollUtil.isNotEmpty(reviewList)) {
                for (Review review : reviewList) {
                    List<String> dataNos = review.getDatas().stream().map(ReviewData::getDataNo).collect(Collectors.toList());
                    for (String dataNo : checkResultVO.getDataNos()) {
                        if (dataNos.contains(dataNo)) {
                            reviewMap.computeIfAbsent(dataNo, k -> new LinkedHashSet<>()).add(review.getReviewId());
                        }
                    }
                }
            }

        }
        if (CollUtil.isNotEmpty(reviewMap)) {
            checkResultVO.setReviewMap(reviewMap);
        }
    }

    public void validateResourceAuthorize(DeleteCheckResultVO checkResultVO, String memberId) {
        HashMap<String, LinkedHashSet<String>> resourceAuthorizeMap = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(checkResultVO.getDataNos())) {
            List<ResourceAuthorize> resourceAuthorizeList = resourceAuthorizeRepository.findByDataNoInAndCreator(checkResultVO.getDataNos(), memberId);
            if (CollUtil.isNotEmpty(resourceAuthorizeList)) {
                for (ResourceAuthorize resourceAuthorize : resourceAuthorizeList) {
                    for (String dataNo : checkResultVO.getDataNos()) {
                        if (resourceAuthorize.getData().contains(dataNo)) {
                            // 将resourceAuthorize.getAuthorizeTo() 变为 邮箱
                            resourceAuthorizeMap.computeIfAbsent(dataNo, k -> new LinkedHashSet<>()).add(resourceAuthorize.getAuthorizeTo());
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(resourceAuthorizeMap)) {
            checkResultVO.setResourceAuthorizeMap(resourceAuthorizeMap);
        }
    }

    public void updateEsData(String type, String typeNo) {
        final DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(type);
        searchVO.setTypeNo(typeNo);
        searchVO.setMustHasData(false);
        R<List<RelatedDataDTO>> r = remoteDataService.findAllByTypeAndNo(searchVO, SecurityConstants.INNER);
        if (R.isError(r)) {
            throw new ServiceException("Data Service Error");
        }
        if (CollUtil.isEmpty(r.getData())) {
            return;
        }
        List<RelatedDataDTO> relatedDataDTOS = r.getData();

        List<String> projNos = relatedDataDTOS.stream().map(RelatedDataDTO::getProjNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> analNos = relatedDataDTOS.stream().map(RelatedDataDTO::getAnalNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> dataNos = relatedDataDTOS.stream().map(RelatedDataDTO::getDatNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        if (CollUtil.isNotEmpty(projNos)) {
            // 删除node_related_es索引中索引数据（防止无data的索引数据不更新的问题）
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.project, projNos, true));
        }
        if (CollUtil.isNotEmpty(analNos)) {
            // 删除node_related_es索引中索引数据（防止无data的索引数据不更新的问题）
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.analysis, analNos, true));
        }
        if (CollUtil.isNotEmpty(dataNos)) {
            applicationContext.publishEvent(new IndexUpdateEvent(this, AuthorizeType.data, dataNos));
        }
    }

}
