import request from '@/utils/request';

const baseURL = '/system/metadata/project';

// 查询用户拥有的项目列表
export function listProject(data) {
  return request({
    url: `${baseURL}/listProject`,
    method: 'post',
    data: data,
  });
}

// 根据projNo查询用户的project信息
export function getProjInfo(projNo) {
  return request({
    url: `${baseURL}/getByNo/${projNo}`,
    method: 'get',
  });
}

// 获取用户的ProjectList
export function getProjectList(creator) {
  return request({
    url: `${baseURL}/getProjectList?creator=${creator}`,
    method: 'get',
  });
}

// 保存编辑
export function editProject(data) {
  return request({
    url: `${baseURL}/edit`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// project删除预检查
export function projDeleteCheck(projNo) {
  return request({
    url: `${baseURL}/deleteCheck/${projNo}`,
    method: 'get',
  });
}

// 删除项目所有以及相关联的
export function deleteProjectAll(params) {
  return request({
    url: `${baseURL}/deleteProjectAll`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 修改项目及关联的数据的creator
export function updateProjCreator(params) {
  return request({
    url: `${baseURL}/updateCreator`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// Batch Modify Source
export function batchModifySource(data) {
  return request({
    url: `${baseURL}/batchModifySource`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// 获取sourceTarget信息
export function getSourceProjectMetadataList(params) {
  return request({
    url: `${baseURL}/getSourceProjectMetadataList`,
    method: 'get',
    params: params,
  });
}

// 刷新ES索引
export function refreshIndex(projNo) {
  return request({
    url: `${baseURL}/refreshIndex/${projNo}`,
    method: 'get',
  });
}
