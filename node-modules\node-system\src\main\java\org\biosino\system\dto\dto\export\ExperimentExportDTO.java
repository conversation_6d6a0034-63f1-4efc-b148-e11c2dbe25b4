package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.system.dto.dto.ExperimentDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2025/1/10
 */
@Data
public class ExperimentExportDTO {

    @JSONField(name = "id", ordinal = 1)
    private String id;

    @JSONField(name = "exp_no", ordinal = 2)
    private String expNo;

    @JSONField(name = "sub_no", ordinal = 3)
    private String subNo;

    @JSONField(name = "proj_no", ordinal = 4)
    private String projectNo;

    @JSONField(name = "name", ordinal = 5)
    private String name;

    @JSONField(name = "description", ordinal = 6)
    private String description;

    @JSONField(name = "protocol", ordinal = 7)
    private String protocol;

    @JSONField(name = "related_links", ordinal = 8)
    private List<String> relatedLinks;

    @JSO<PERSON>ield(name = "creator", ordinal = 9)
    private String creator;

    @JSONField(name = "audited", ordinal = 10)
    private String audited;

    @JSONField(name = "submission_date", ordinal = 11)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 12)
    private Date updateDate;

    @JSONField(name = "public_date", ordinal = 13)
    private Date publicDate;

    @JSONField(name = "hit_num", ordinal = 14)
    private Long hitNum;

    @JSONField(name = "export_num", ordinal = 15)
    private Long exportNum;

    @JSONField(name = "submitter", ordinal = 16)
    private Submitter submitter;

    @JSONField(name = "other_ids", ordinal = 17)
    private List<OtherIds> otherIds;

    @JSONField(name = "operator", ordinal = 18)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 19)
    private Date operationDate;

    @JSONField(name = "temp_data", ordinal = 20)
    private ExperimentDTO tempData;

    @JSONField(name = "attributes", ordinal = 20)
    private Map<String, Object> attributes;

    @JSONField(name = "used_ids", ordinal = 21)
    private List<String> usedIds;

    @JSONField(name = "ownership", ordinal = 22)
    private String ownership;

    @JSONField(name = "source_project", ordinal = 23)
    private List<String> sourceProject;

    @JSONField(name = "exp_type", ordinal = 24)
    private String expType;

    @JSONField(name = "visible_status", ordinal = 25)
    private String visibleStatus;
}
