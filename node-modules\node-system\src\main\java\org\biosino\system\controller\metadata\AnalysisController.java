package org.biosino.system.controller.metadata;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.dto.dto.AnalysisDTO;
import org.biosino.system.dto.dto.AnalysisDataExportDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.service.meta.AnalysisService;
import org.biosino.system.vo.metadata.AnalysisListVO;
import org.biosino.system.vo.metadata.AnalysisVO;
import org.biosino.upload.api.dto.RemoteSelectQueryDTO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/4/29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/metadata/analysis")
public class AnalysisController {

    private final AnalysisService analysisService;

    /**
     * 列出所有的Analysis
     */
    @RequestMapping("/listAnalysis")
    public TableDataInfo listAnalysis(@RequestBody MetadataQueryDTO queryDTO) {
        Page<AnalysisListVO> page = analysisService.listAuditedAnalysis(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 后端项目详情导出data列表数据
     */
    @PostMapping("/downloadData")
    public void downloadData(DataListSearchVO searchVO, HttpServletRequest request, HttpServletResponse response) {
        List<AnalysisDataExportDTO> result = analysisService.listDownloadData(searchVO);
        ExcelUtil<AnalysisDataExportDTO> util = new ExcelUtil<AnalysisDataExportDTO>(AnalysisDataExportDTO.class);
        util.exportExcel(response, result, "sheet1");
    }


    /**
     * 查询Analysis的详细信息
     */
    @GetMapping("/getByNo/{analNo}")
    public AjaxResult getByNo(@PathVariable("analNo") String analNo) {
        AnalysisVO result = analysisService.getAnalInfoByNo(analNo);
        return AjaxResult.success(result);
    }

    /**
     * 获取所有编辑AnalysisType
     */
    @GetMapping("/getAuditedAnalType")
    public AjaxResult getAuditedAnalType() {
        List<String> result = analysisService.getAuditedAnalType();
        return AjaxResult.success(result);
    }

    /**
     * 获取Target选项
     */
    @RequestMapping("/getTargetOptions")
    public TableDataInfo getTargetOptions(@RequestBody RemoteSelectQueryDTO queryDTO) {
        return analysisService.getTargetOptions(queryDTO);
    }

    /**
     * 获取pipeline选项
     */
    @RequestMapping("/getPipelineOptions")
    public TableDataInfo getPipelineOptions(@RequestBody RemoteSelectQueryDTO queryDTO) {
        return analysisService.getPipelineOptions(queryDTO);
    }

    /**
     * 编辑analysis
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Analysis", businessType = BusinessType.UPDATE)
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody AnalysisDTO analysisDTO) {
        AnalysisVO vo = analysisService.updateAnalysis(analysisDTO);
        return AjaxResult.success(vo);
    }

    /**
     * 修改用户、删除 预检查
     */
    @GetMapping("/deleteCheck/{analNo}")
    public AjaxResult deleteCheck(@PathVariable("analNo") String analNo) {
        DeleteCheckResultVO result = analysisService.deleteCheck(analNo);
        return AjaxResult.success(result);
    }

    /**
     * 删除Analysis 及其下面相关的内容
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Analysis", businessType = BusinessType.DELETE)
    @RequestMapping("/deleteAll")
    public AjaxResult deleteAll(String analNo) {
        // 删除数据
        analysisService.deleteAll(analNo);
        return AjaxResult.success();
    }

    /**
     * 修改creator
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Analysis", module3 = "Change Creator", businessType = BusinessType.UPDATE)
    @RequestMapping("/updateCreator")
    public AjaxResult changeCreator(String analNo, String newCreator) {
        analysisService.updateCreator(analNo, newCreator);
        return AjaxResult.success();
    }

    /**
     * 刷新Analysis下面的 浏览页的索引 和 Data关联大表
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Analysis", desc = "刷新ES索引", businessType = BusinessType.OTHER)
    @RequestMapping("/refreshIndex/{analNo}")
    public AjaxResult refreshIndex(@PathVariable("analNo") String analNo) {
        analysisService.refreshIndex(analNo);
        return AjaxResult.success();
    }

    /**
     * 导出analysis数据
     */
    @Log(module1 = "Metadata Mgmt", module2 = "Analysis", module3 = "Export Data", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("metadata:analysis:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        MetadataQueryDTO queryDTO = JSON.parseObject(query, MetadataQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = analysisService.exportAnalysis(queryDTO);
        DownloadUtils.download(request, response, file, "analysis.json");
    }

}
