import request from '@/utils/request';

const baseURL = '/app/security';

// 获取security的data数据
export function getSecurityDataList(params) {
  return request({
    url: `${baseURL}/getDataList`,
    method: 'get',
    params: params,
  });
}

// 更新安全等级
export function updateDataSecurity(data) {
  return request({
    url: `${baseURL}/updateDataSecurity`,
    method: 'post',
    timeout: 0,
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// 校验数据的人类组学
export function verifyDataHumanType(data) {
  return request({
    url: `${baseURL}/verifyDataHumanType`,
    method: 'post',
    data: data,
  });
}

// 检索自动补全查询
export function searchSelectWord(data) {
  return request({
    url: `${baseURL}/searchSelectData`,
    method: 'post',
    data: data,
  });
}

// 验证Security中的metadata有无修改
export function verifyMetadataEdit(params) {
  return request({
    url: `${baseURL}/verifyMetadataEdit`,
    method: 'get',
    params: params,
  });
}
