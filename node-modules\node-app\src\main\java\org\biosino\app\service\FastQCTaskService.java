package org.biosino.app.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.dto.FastQCTaskQueryDTO;
import org.biosino.app.repository.DataRepository;
import org.biosino.app.repository.FastQCTaskRepository;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.FastQCTask;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/5/22
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FastQCTaskService {

    private final FastQCTaskRepository fastQCTaskRepository;
    private final DataRepository dataRepository;

    public Page<FastQCTask> list(FastQCTaskQueryDTO queryDTO) {
        Page<FastQCTask> page = fastQCTaskRepository.findPage(queryDTO);
        return page;
    }

    public FastQCTask findFirstByDataNo(String dataNo) {
        return fastQCTaskRepository.findFirstByDataNo(dataNo).orElseThrow(() -> new ServiceException("FastQCTask not found"));
    }

    public void downloadReport(String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        FastQCTask task = findFirstByDataNo(dataNo);
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("data not exist!"));
        if (!StrUtil.equals(FastQCTaskStatusEnum.success.name(), task.getStatus())) {
            throw new ServiceException("FastQCTask not success");
        }
        // String zipFilePath = task.getFastqcResult().getZipFilePath();
        File file = FileUtil.file(DirConstants.FASTQC_HOME,
                DateUtils.dateTimeFormat4Ftp(task.getDataCreateDate()),
                dataNo,
                MyFileUtils.getFileNamePrefix(data.getName()) + "_fastqc.zip");
        log.info("download report: {}", file.getAbsolutePath());
        DownloadUtils.download(request, response, file, StrUtil.format("{}_FastQC_Report.zip", dataNo));
    }

    public List<FastQCTask> findAllByDataNoIn(List<String> dataNos) {
        return fastQCTaskRepository.findAllByDataNoIn(dataNos);
    }
}
