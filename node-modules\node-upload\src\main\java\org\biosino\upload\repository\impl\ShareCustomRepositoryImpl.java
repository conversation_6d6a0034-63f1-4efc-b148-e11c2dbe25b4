package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONPath;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Share;
import org.biosino.upload.repository.ShareCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/1/18
 */
@RequiredArgsConstructor
public class ShareCustomRepositoryImpl implements ShareCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Share> findShareByShareTo(String... email) {
        Criteria criteria = Criteria.where("share_to").in(email)
                .and("status").is(ShareStatusEnum.sharing.name());
        return mongoTemplate.find(Query.query(criteria), Share.class);
    }

    @Override
    public boolean existAccessableNo(String no, TypeInformation typeInfo, String memberEmail) {
        List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("share_to").in(memberEmail));
        conditions.add(Criteria.where(typeInfo.getShareMongoQueryField()).in(no));
        conditions.add(Criteria.where("status").is(ShareStatusEnum.sharing.name()));
        Query query = new Query(new Criteria().andOperator(conditions));
        return mongoTemplate.exists(query, Share.class);
    }

    @Override
    public Set<String> getAccessableNos(Collection<String> nos, TypeInformation typeInfo, String memberEmail) {
        List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("share_to").in(memberEmail));
        conditions.add(Criteria.where(typeInfo.getShareMongoQueryField()).in(nos));
        conditions.add(Criteria.where("status").is(ShareStatusEnum.sharing.name()));
        Query query = new Query(new Criteria().andOperator(conditions));
        List<Share> shares = mongoTemplate.find(query, Share.class);
        String jsonString = JSON.toJSONString(shares);

        String jsonPathExpression = "$.." + typeInfo.getShareListField() + "[*]." + typeInfo.getField();
        Object extract = JSONPath.extract(jsonString, jsonPathExpression);
        List<String> matchNos = extract instanceof JSONArray
                ? ((JSONArray) extract).toJavaList(String.class)
                : new ArrayList<>();

        if (CollUtil.isEmpty(matchNos)) {
            return new HashSet<>();
        }

        Set<String> result = CollUtil.intersectionDistinct(nos, matchNos);
        return result;
    }

    @Override
    public List<Share> findByTypeNoInAndCreator(TypeInformation typeInfo, List<String> typeNos, String creator) {
        if (CollUtil.isEmpty(typeNos) || StrUtil.isBlank(creator)) {
            return new ArrayList<>();
        }
        List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("creator").in(creator));
        conditions.add(Criteria.where(typeInfo.getShareMongoQueryField()).in(typeNos));
        conditions.add(Criteria.where("status").is(ShareStatusEnum.sharing.name()));
        conditions.add(new Criteria().orOperator(
                Criteria.where("expire_date").gt(new Date()),
                Criteria.where("expire_date").exists(false)
        ));
        Query query = new Query(new Criteria().andOperator(conditions));
        return mongoTemplate.find(query, Share.class);
    }
}
