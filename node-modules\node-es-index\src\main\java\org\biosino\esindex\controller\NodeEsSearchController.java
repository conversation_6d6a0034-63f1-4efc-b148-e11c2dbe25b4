package org.biosino.esindex.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.es.api.dto.*;
import org.biosino.es.api.vo.BrowseStatResVO;
import org.biosino.es.api.vo.detail.ExpSapSearchVO;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.esindex.service.NodeEsSearchService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * node 索引控制层
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/nodeES")
public class NodeEsSearchController extends BaseController {

    private final NodeEsSearchService nodeEsSearchService;


    /**
     * 根据项目编号模糊检索所有项目编号
     */
    @GetMapping("/searchPrjId/{keyword}")
    public R<List<String>> searchPrjId(@PathVariable("keyword") String keyword) {
        try {
            return R.ok(nodeEsSearchService.searchPrjId(keyword));
        } catch (Exception e) {
            log.error("根据项目编号模糊检索所有项目编号出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据项目编号统计数据
     */
    @PostMapping("/statByPrj")
    public R<Map<String, StatDTO>> statByPrj(@RequestBody Set<String> prjNos) {
        try {
            return R.ok(nodeEsSearchService.statByPrj(prjNos));
        } catch (Exception e) {
            log.error("根据项目编号统计数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询多组学项目数据
     */
    @PostMapping("/searchMultipleOmics")
    public R<FdMultipleResDTO> searchMultipleOmics(@RequestBody MultipleOmicsQueryVO searchVO) {
        try {
            return R.ok(nodeEsSearchService.searchMultipleOmics(searchVO));
        } catch (Exception e) {
            log.error("查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据项目编号集合，查询多组学项目数据
     */
    @PostMapping("/searchMultOmicByPrjNos")
    public R<List<FeatureDataPrjDTO>> searchMultOmicByPrjNos(@RequestBody List<String> prjNos) {
        try {
            return R.ok(nodeEsSearchService.searchMultOmicByPrjNos(prjNos));
        } catch (Exception e) {
            log.error("按编号查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询多样本项目数据
     */
    @PostMapping("/searchMultipleSample")
    public R<FdMultipleResDTO> searchMultipleSample(@RequestBody MultipleSampleQueryVO searchVO) {
        try {
            return R.ok(nodeEsSearchService.searchMultipleSample(searchVO));
        } catch (Exception e) {
            log.error("查询多样本项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据项目编号集合，查询多样本项目数据
     */
    @PostMapping("/searchMultSampleByPrjNos")
    public R<List<FeatureDataPrjDTO>> searchMultSampleByPrjNos(@RequestBody List<String> prjNos) {
        try {
            return R.ok(nodeEsSearchService.searchMultSampleByPrjNos(prjNos));
        } catch (Exception e) {
            log.error("按编号查询多样本项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询单样本多组学样本数据
     */
    @PostMapping("/searchSingleSap")
    public R<FdMultipleResDTO> searchSingleSap(@RequestBody SingleSapQueryVO searchVO) {
        try {
            return R.ok(nodeEsSearchService.searchSingleSap(searchVO));
        } catch (Exception e) {
            log.error("查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据样本编号集合，查询单样本多组学样本数据
     */
    @PostMapping("/searchSingleSapcByNos")
    public R<List<FeatureDataPrjDTO>> searchSingleSapcByNos(@RequestBody List<String> nos) {
        try {
            return R.ok(nodeEsSearchService.searchSingleSapcByNos(nos));
        } catch (Exception e) {
            log.error("按编号查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 特殊数据集 首页组学统计数据
     */
    @GetMapping("/expStatInfo")
    public R<Map<String, StatDTO>> expStatInfo() {
        try {
            return R.ok(nodeEsSearchService.expStatInfo());
        } catch (Exception e) {
            log.error("按编号查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 特色数据集 首页水圈数据统计
     */
    @RequestMapping("/statByBiomeCurated")
    public R<Map<String, StatDTO>> statByBiomeCurated() {
        try {
            return R.ok(nodeEsSearchService.statByBiomeCurated());
        } catch (Exception e) {
            log.error("按编号查询多组学项目数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据项目编号，查询样本和对应组学类型
     */
    @PostMapping("/findSapNoAndExpTypesByPrjNos")
    public R<FdSampleDTO> findSapNoAndExpTypesByPrjNos(@RequestBody FdQueryDTO search) {
        try {
            return R.ok(nodeEsSearchService.findSapNoAndExpTypesByPrjNos(search));
        } catch (Exception e) {
            log.error("根据项目编号，查询样本编号出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询node_es，根据项目编号、数据类型，查询对应字段统计数据
     */
    @PostMapping("/countByTypeAndField")
    public R<List<FieldCountDTO>> countByTypeAndField(@RequestBody ExpSapSearchVO searchVO) {
        try {
            return R.ok(nodeEsSearchService.countByTypeAndField(searchVO));
        } catch (Exception e) {
            log.error("根据项目编号、数据类型，查询对应字段统计数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据项目编号查询所有样本编号出错
     */
    @GetMapping("/searchAllSapNoByPrjId")
    public R<ProjSapNosDTO> searchAllSapNoByPrjId(final String prjNo) {
        try {
            return R.ok(nodeEsSearchService.searchAllSapNoByPrjId(prjNo));
        } catch (Exception e) {
            log.error("根据项目编号查询所有样本编号出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据编号对应关联类型数据
     */
    @GetMapping("/searchTypeData")
    public R<Map<String, NodeEsTypeDTO>> searchTypeData(@RequestParam(value = "typeIds") List<String> typeIds, @RequestParam(value = "type") String type) {
        try {
            return R.ok(nodeEsSearchService.searchTypeData(typeIds, type));
        } catch (Exception e) {
            log.error("根据编号对应关联类型数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据编号集合和类型查询对应数据总大小
     */
    @PostMapping("/findAllSizeByNosAndType")
    public R<Long> findAllSizeByNosAndType(@RequestBody FileSizeSearchDTO searchDTO) {
        try {
            return R.ok(nodeEsSearchService.findAllSizeByNosAndType(searchDTO));
        } catch (Exception e) {
            log.error("根据编号集合和类型查询对应数据总大小出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取样本-实验组合统计数据
     */
    @GetMapping("/statExpBySap")
    public R<SampleExpStatDTO> statExpBySap(@RequestParam(value = "sapType") String sapType,
                                            @RequestParam(value = "expType") String expType) {
        try {
            return R.ok(nodeEsSearchService.statExpOfSap(sapType, expType));
        } catch (Exception e) {
            log.error("获取样本-实验组合统计数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取多组学项目统计数据
     */
    @GetMapping("/statMultiExp")
    public R<MultiExpStatDTO> statMultiExp(final Boolean searchSample, final String sapType) {
        try {
            return R.ok(nodeEsSearchService.statMultiExp(Boolean.TRUE.equals(searchSample), sapType));
        } catch (Exception e) {
            log.error("获取多组学项目统计数据出错", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询前端浏览页统计数据
     */
    @PostMapping("/findBrowseStatNum")
    public R<Map<String, BrowseStatResVO>> findBrowseStatNum(@RequestBody BrowseStatEsDTO dto) {
        try {
            return R.ok(nodeEsSearchService.findBrowseStatNum(dto));
        } catch (Exception e) {
            log.error("查询前端浏览页统计数据出错", e);
            return R.fail(e.getMessage());
        }
    }

}
