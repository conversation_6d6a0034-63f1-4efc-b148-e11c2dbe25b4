import request from '@/utils/request';

const baseURL = '/system/metadata/experiment';

// 查询用户拥有的Experiment列表
export function listExperiment(params) {
  return request({
    url: `${baseURL}/listExperiment`,
    method: 'post',
    data: params,
  });
}

// 根据expNo查询用户的Experiment信息
export function getExpInfo(projNo) {
  return request({
    url: `${baseURL}/getByNo/${projNo}`,
    method: 'get',
  });
}

// 保存编辑
export function editExp(data) {
  return request({
    url: `${baseURL}/edit`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    data: data,
  });
}

// Experiment删除预检查
export function expDeleteCheck(expNo) {
  return request({
    url: `${baseURL}/deleteCheck/${expNo}`,
    method: 'get',
  });
}

// 删除Experiment所有以及相关联的
export function deleteExpAll(params) {
  return request({
    url: `${baseURL}/deleteAll`,
    method: 'delete',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}

// 获取Experiment的ExpType
export function getAuditedExpType() {
  return request({
    url: `${baseURL}/getAuditedExpType`,
    method: 'get',
  });
}

// 修改Experiment及关联的数据的creator
export function updateExpCreator(params) {
  return request({
    url: `${baseURL}/updateCreator`,
    method: 'post',
    headers: {
      repeatSubmit: true,
    },
    params: params,
  });
}
