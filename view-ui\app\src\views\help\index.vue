<template>
  <div class="analyse-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="Help" />

      <el-row :gutter="20" class="mt-1 helps">
        <el-col :span="6" class="hidden-xs-only">
          <div class="card plr-0" style="position: sticky; top: 30px">
            <!--            <ul id="navbar-example" class="pb-1">-->
            <el-tree :data="toc" :props="defaultProps" default-expand-all>
              <template #default="{ data }">
                <ul>
                  <li
                    :class="[
                      `layer-${data.layer}`,
                      { active: activeId === data.id },
                    ]"
                  >
                    <a
                      class="d-flex"
                      :title="data.title"
                      @click="handleClick(data.id)"
                    >
                      <span class="font-600 mr-03">{{ data.sort }}</span>

                      <span
                        v-if="data.subtitle"
                        class="text-warning font-600 font-14"
                        >( {{ data.subtitle }} )</span
                      >
                      <span
                        style="
                          display: inline-block;
                          white-space: normal;
                          width: 100%;
                          overflow-wrap: break-word;
                        "
                        class="font-600"
                        >{{ data.title }}</span
                      >
                    </a>
                  </li>
                </ul>
              </template>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="18" :xs="24" :sm="24" :md="18">
          <div class="card">
            <h4 id="help-1" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 1 </el-tag>
              Overview
            </h4>
            <p>
              NODE is a biological big data collection platform, including the
              experimental sample information collection, the file upload of the
              sequences, and the analysis, share and download of the results.
              NODE platform consists of six main modules: project, sample,
              experiment, run, data, and analysis. Project and Sample are
              independent of each other, but can be linked through Run. In this
              way, the metadata and sequence information can be integrated.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help1.png" alt="" />
            </div>
            <h5 id="help-1-1" class="text-primary font-16">
              1.1 Supported Experiment Type
            </h5>
            <p class="mb-03 text-main-color">
              The number of NODE experiment types is fourteen, including:
            </p>
            <ul>
              <li>
                <p>(1) <b>Genomic:</b> Use for DNA sequencing.</p>
              </li>
              <li>
                <p>(2) <b>Transcriptomic:</b> Use for RNA sequencing.</p>
              </li>
              <li>
                <p>
                  (3) <b>Metagenomic:</b> Use for DNA sequencing, which is
                  extracted from all microorganisms in environmental samples.
                </p>
              </li>
              <li>
                <p>
                  (4) <b>Metatranscriptomic:</b> Use for RNA sequencing, which
                  is extracted from all microorganisms in environmental samples.
                </p>
              </li>
              <li>
                <p>
                  (5) <b>Genomic single cell:</b> Use for single cell DNA
                  sequencing.
                </p>
              </li>
              <li>
                <p>
                  (6) <b>Transcriptomic single cell:</b> Use for single cell RNA
                  sequencing.
                </p>
              </li>
              <li>
                <p>(7) <b>Proteomic:</b> Use for protein sequencing.</p>
              </li>
              <li>
                <p>
                  (8) <b>Metabolomic:</b> Use for metabolites in living
                  organisms.
                </p>
              </li>
              <li>
                <p>
                  (9) <b>Metabolomic-NMR:</b> Use for metabolites in living
                  organisms by NMR detection.
                </p>
              </li>
              <li>
                <p>
                  (10) <b>Electron microscopy:</b> Use for electron microscopy
                  imaging of biological sample.
                </p>
              </li>
              <li>
                <p>(11) <b>Microarray:</b> Use for microarray data.</p>
              </li>
              <li>
                <p>
                  (12) <b>Synthetic:</b> Use for synthetic biology sequencing
                  data.
                </p>
              </li>
              <li>
                <p>(13) <b>Viral RNA:</b> Use for viral RNA sequencing.</p>
              </li>
              <li>
                <p>
                  (14) <b>Flow cytometry:</b> Use for determination of the
                  biological properties of cells or organelles.
                </p>
              </li>
            </ul>
            <h5 id="help-1-2" class="text-primary font-16">
              1.2 Supported Sample Type
            </h5>
            <p class="mb-03 text-main-color">
              The number of NODE sample types is eight, including:
            </p>
            <ul>
              <li>
                <p>
                  (1) <b>Human:</b> Use for human samples that have no privacy
                  concerns.
                </p>
              </li>
              <li>
                <p>
                  (2) <b>Animalia:</b> Use for multicellular samples derived
                  from common laboratory animals, e.g., mouse, rat, Zebrafish,
                  etc.
                </p>
              </li>
              <li>
                <p>(3) <b>Plant:</b> Use for any plant sample.</p>
              </li>
              <li>
                <p>
                  (4) <b>Cell line:</b> Use for cell lines derived from common
                  laboratory.
                </p>
              </li>
              <li>
                <p>
                  (5) <b>Microbe:</b> Use for any microbe sample or microbial
                  strains, e.g. Yeast.
                </p>
              </li>
              <li>
                <p>
                  (6) <b>Environment host:</b> Use for metagenomic and
                  host-associated samples, for example human gut metagenome,
                  human oral metagenome, fish gut metagenome, etc.
                </p>
              </li>
              <li>
                <p>
                  (7) <b>Environment non-host:</b> Use for metagenomic and
                  non-host-associated samples, for example marine metagenome,
                  fresh water metagenome, soil metagenome, etc.
                </p>
              </li>
              <li>
                <p>
                  (8) <b>Pathogen affecting public health:</b> Clinical or
                  host-associated, environmental, food or the other pathogen.
                </p>
              </li>
            </ul>

            <el-divider> </el-divider>

            <h4 id="help-2" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 2 </el-tag>
              Register and Login
            </h4>
            <h5 id="help-3" class="text-primary font-16">
              2.1 Sign up for NODE
            </h5>
            <p class="mb-03 text-main-color">1.Register for NODE</p>
            <p>
              Enter your email address, password and confirm password, enter the
              user information including first name, last name, organization and
              country/region to register to NODE, while the other information
              only has to be provided according to user’s interests. Select "I
              have read and agreed to the Privacy Policy.". Click "Create
              account".
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-1.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">2.Registration is done</p>
            <p>Your registration is done in NODE.</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-2.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">3.Activate your account</p>
            <p>
              Then you will receive a registration activation link in your
              registered email box from NODE. Click the link within 48 hours to
              activate your account.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-3.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">4.Activation is done</p>
            <p>
              After the activation, you will be reminded that you have done all
              steps of your registration. You can start to login NODE.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-4.png" alt="" />
            </div>
            <p class="mb-03 font-600">Some Tips:</p>
            <p>
              Please use a new and valid email address and enter email address
              properly for registration. Or you will get the following
              reminders:
            </p>
            <p>(1) Email registered</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-5.png" alt="" />
            </div>
            <p>(2) Not a valid email</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-6.png" alt="" />
            </div>

            <h5 id="help-4" class="text-primary font-16">
              2.2 Login to NODE account
            </h5>
            <p>
              After the registration, user can login to NODE by inputting proper
              email address and password in the login page. NODE provides
              "Remember Password" function so that user doesn’t have to input
              email address and password every time using NODE. We suggest that
              you ONLY activate this function in your personal computer.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-2-1.png" alt="" />
            </div>

            <h5 id="help-5" class="text-primary font-16">
              2.3 Forget password/Reset password
            </h5>
            <p>
              If you forget your password, you can simply click "Forget
              Password" and input your email address in the following page and
              click send to send password reset to your email.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-1.png" alt="" />
            </div>
            <p>
              Here is the email example received from NODE for the reset of the
              password.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-2.png" alt="" />
            </div>
            <p>
              Click "Password Reset", user can reach the password reset page in
              NODE. To keep your account safe, we strongly encourage you NOT to
              share your password.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-3.png" alt="" />
            </div>
            <h5 id="help-6" class="text-primary font-16">
              2.4 Modifies registration information
            </h5>
            <p>
              In User Center, registered users can modify registration
              information by Edit.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-4-1.png" alt="" />
            </div>

            <el-divider> </el-divider>

            <h4 id="help-7" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 3 </el-tag>
              User Center
            </h4>
            <h5 id="help7-1" class="text-primary font-16">
              3.1 User center overview
            </h5>
            <p>
              User center is a convenient tool for user to have an overview of
              his/her own NODE data as well as management of data requests.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-1.png" alt="" />
            </div>
            <p>
              In the following part of user center, user can have an overview of
              NODE data, the detail information of the user and highly accessed
              publications related to the research with high access rate in
              NODE, as well as other convenient function for the share and use
              of the data, such as "My shares", "Share From Others" , "My
              Requests", "Requests From Others", and "My Reviews".
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-2.png" alt="" />
            </div>
            <h5 id="help7-2" class="text-primary font-16">3.2 My data list</h5>
            <p>
              "My data list" show all the data of the user, including the lists
              of projects, experiments, samples, runs, analysis, data, publishes
              and submissions. Besides, the data name, status, uploaded date and
              operate are displayed. In experiments, samples, runs and analysis,
              there is the batch modify link in the right icon "operate" button.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-3.png" alt="" />
            </div>
            <div class="text-center">
              <img src="@/assets/images/help3-4.png" alt="" />
            </div>
            <p>
              Furthermore, you can input the filter words and period time to
              search your data, and then the filtered data list is shown in the
              red box on the left.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-5.png" alt="" />
            </div>
            <h5 id="help7-3" class="text-primary font-16">
              3.3 My Data Statistics
            </h5>
            <p>
              "My Data Statistics" show the data statistics of project, sample,
              analysis, experiment, run and data. Users can see the total amount
              of data, the ratio of accessible or unaccessible data, and the
              total amount, accessible amount or unaccessible amount of files in
              project, sample, analysis, experiment, run, and data.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-6.png" alt="" />
            </div>
            <h5 id="help7-4" class="text-primary font-16">
              3.4 My Data Activity
            </h5>
            <p>
              "My Data Activity" present the numbers of projects, experiments,
              samples, analysis and total data of the user according to three
              security status respectively. "Views of Data" display the numbers
              of user's data with graphs in every month. "Download of Data"
              display the download times of user's data with graphs in every
              month.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help3-7.png" alt="" />
            </div>
            <el-divider> </el-divider>
            <h4 id="help-26" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 4 </el-tag>
              Detail Page
            </h4>
            <h5 id="help26-1" class="text-primary font-16">
              4.1 Project Detail
            </h5>
            <p>
              In project details page, project information includes project ID,
              project name, project description, total data amount, total file
              amount, total sample amount, experiment ID, experiment type,
              experiment name, experiment information, sample type, sample ID,
              sample name, sample information, file type, file amount, file
              security status, data list, data quality control information and
              download link. In data quality control information, users can see
              FASTQC report and download FASTQC report in "operate" column.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-1.png" alt="" />
            </div>
            <h5 id="help26-2" class="text-primary font-16">
              4.2 Experiment Detail
            </h5>
            <p>
              In experiment details page, experiment information includes
              experiment ID, experiment type, experiment name, attributes,
              sample type, file type, file amount, file security status, data
              list, data quality control information and download link. In data
              quality control information, users can see FASTQC report and
              download FASTQC report in "operate" column.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-2.png" alt="" />
            </div>
            <h5 id="help26-3" class="text-primary font-16">
              4.3 Sample Detail
            </h5>
            <p>
              In sample details page, sample information includes sample ID,
              sample type, sample name, organism, tissue, attributes, experiment
              type, file type, file amount, file security status, data list,
              data quality control information and download link. In data
              quality control information, users can see FASTQC report and
              download FASTQC report in "operate" column.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-3.png" alt="" />
            </div>
            <h5 id="help26-4" class="text-primary font-16">
              4.4 Analysis Detail
            </h5>
            <p>
              In analysis details page, analysis information includes analysis
              ID, analysis name, analysis type, total data amount, total file
              amount, pipeline information, target information, data list and
              dowload link.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-4.png" alt="" />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-8" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 5 </el-tag>
              Raw Data Upload
            </h4>

            <p>
              NODE (The National Omics Data Encyclopedia) provides an
              integrated, compatible, comparable, and scalable multi-omics
              resource platform that supports flexible data management and
              effective data release. NODE uses a hierarchical data architecture
              to support storage of muti-omics data including sequencing data,
              MS based proteomics data, MS or NMR based metabolomics data, and
              fluorescence imaging data.
            </p>
            <p class="mt-1">
              The data submission process in NODE consists of three steps,
              including "Submit raw data", "Submit metadata" and "Archiving".
              The arrow bar on the following page indicates each step of the
              submission process by highlighting the specific step.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help4-1.png" alt="" />
            </div>
            <p>
              NODE provides three different ways for data uploading, including,
              "Http upload", "FTP upload" and "Express hard drive".
            </p>
            <h5 id="help8-1" class="text-primary font-16">5.1 HTTP Upload</h5>
            <p>
              Uploading data via website for files less than 200MB. This method
              of uploading data is very suitable for someone who has a small
              amount of data to transfer to NODE. Click "Select your file", then
              select your file and see the file in Unarchived Data.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help4-2.png" alt="" />
            </div>
            <h5 id="help8-2" class="text-primary font-16">5.2 SFTP Upload</h5>
            <p>
              If data are too large to upload to NODE using http, users can
              apply for the Sftp upload. The Sftp account and password are the
              user's email and password for logging into NODE.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help4-3.png" alt="" />
            </div>

            <h5 id="help8-2-1" class="text-primary font-16">5.2.1 FTP tools</h5>

            <p>
              We recommend using FTP client software to upload files, e.g.,<a
                href="https://filezilla-project.org/download.php?type=client"
                target="_blank"
                class="text-primary"
                >FileZilla</a
              >
            </p>
            <p>Host: sftp://fms.biosino.org</p>
            <p>Username:NODE user name (email)</p>
            <p>Password:NODE password</p>
            <p>Port:44397</p>
            <div class="text-center">
              <img src="@/assets/images/help4-4.png" alt="" />
            </div>
            <div class="text-center">
              <img src="@/assets/images/help4-5.png" alt="" />
            </div>
            <h5 id="help8-2-2" class="text-primary font-16">
              5.2.2 Command line
            </h5>
            <p class="ml-1">SFTP</p>
            <div class="alert-default">
              <p>
                sftp -oPort=44397
                <a
                  class="text-primary"
                  href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </p>
            </div>
            <p class="pl-20">Password: your-node-password</p>
            <div class="alert-default">
              <p>Navigate to the target folder you need</p>
              <p>put example.fastq.gz</p>
            </div>
            <p class="ml-1">LFTP</p>
            <div class="alert-default">
              <p>lftp</p>
              <p>
                lftp :~> connect
                <a href="sftp://fms.biosino.org:44397" class="text-primary"
                  >sftp://fms.biosino.org:44397</a
                >
              </p>
            </div>
            <p class="pl-20">Password: your-node-password</p>
            <p class="pl-20">You can use mput to upload data</p>
            <div class="alert-default">
              <p>mput *.gz</p>
            </div>
            <p>
              Once the data is uploaded, you can click the "Unchecked" button to
              find the data in your account.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-1.png" alt="" />
            </div>

            <h5 id="help8-2-3" class="text-primary font-16">
              5.2.3 Data Integrity Check
            </h5>
            <p>
              The data uploaded through FTP needs to undergo MD5 verification
              and integrity verification to ensure the integrity of the data
              before it can officially enter the Node.
            </p>
            <p>
              Click on the homepage "Submit" to enter the submission system.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-2.png" alt="" />
            </div>
            <p>
              Once the data is uploaded, you can click the "Unchecked" button to
              find the data in your account. Select the files or folders that
              require integrity verification, and then click the "Data Integrity
              Check" button.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-3.png" alt="" />
            </div>
            <p>
              After data submission integrity verification, the progress of
              integrity verification can be viewed in the "Status" column in the
              "Checking". And the failed reasons showed in the "Failed Cause"
              column.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-4.png" alt="" />
            </div>
            <p>
              The data that has completed integrity verification can be viewed
              in 'Unarchived data'. If you want to delete the data, you can
              delete the data one by one in the "Operate", or can select all the
              data you want to delete, and then click the "Delete" button.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-5.png" alt="" />
            </div>
            <div class="mb-05 mt-05 font-600">Some Tips:</div>
            <div>
              <div class="mt-05">
                1. When uploading data to FTP, create folders based on the
                experiment type and date of the data, place the same experiment
                type in the same folder, and upload the entire folder. During
                integrity verification, the data in the entire folder can be
                checked together.
              </div>
              <div class="mt-05">
                2. The files in each folder should not exceed 4000.
              </div>
            </div>

            <h5 id="help8-3" class="text-primary font-16">
              5.3 Express hard drive
            </h5>
            <p>
              Due to network condition, massive scale of data may not be safely
              uploaded. Users can post data to NODE by using hard drive. Before
              posting, please inform us in advance (+86 021-54920550). After
              posting, please provide us with the details of your post. Our
              contact information is as below:
            </p>
            <p>
              Email address:
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <p>
              Address: Bio-Med Big Data Center, 320 Yue Yang Road, Shanghai,
              200031 P.R.China
            </p>
            <div class="text-center">
              <img src="@/assets/images/help4-6.png" alt="" />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-9" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 6 </el-tag>
              Metadata & Archiving
            </h4>
            <p>
              After successfully uploaded, data would be in unarchived status.
              We STRONGLY encourage users to fill metadata as completed as
              possible after unloading data. This information is crucial if you
              want your data to be searched and visited, and share your data
              with other researchers. In NODE, users could use the "MetaData"
              function to arrange the metadata information, including Submitter,
              Project，Experiment，Sample，Run.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-1.png" alt="" />
            </div>
            <p>
              There are two types of data archiving forms: raw data and analysis
              data archiving. Raw data archiving is mainly used to archive
              metadata, and analysis data archiving is applied to archive data
              analyzed by different kinds of software or tools.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2.png" alt="" />
            </div>
            <h5 id="help-10" class="text-primary font-16">
              6.1 Raw data archiving
            </h5>
            <p id="help-11" class="text-primary mb-05">6.1.1 New submission</p>
            <p>
              When clicking the Raw Data button and the Continue button, the
              user will enter the metadata archiving interface.
            </p>
            <p>
              Here is some guidance for filling detail metadata information of
              each part.
            </p>
            <div class="font-600 mt-05">
              Submitter:
              <div class="mt-05">
                First name: the given name of the submitter
              </div>
              <div class="mt-05">Last name: the submitter’s family name</div>
              <div class="mt-05">
                Organization: the name of the organization where the submitter
                worked, eg Shanghai Institute of Nutrition and Health, CAS
              </div>
              <div class="mt-05">
                Email: the valid email address of the submitter
              </div>
              <div class="mt-05">
                Country/Region: the country or the region where the submitter
                from.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-3.png" alt="" />
              </div>
            </div>

            <div class="font-600">
              Project:
              <div class="mt-05">
                Project name: NODE allows users to add data to existed project
                or create new project name.
              </div>
              <div class="mt-05">
                Description: the description of the project
              </div>
              <div class="mt-05">
                Related links: links to resources related to this project
                (publication, datasets, online databases). User could use the
                plus button to add more links based on the project.
              </div>

              <div class="text-center">
                <img src="@/assets/images/help5-4.png" alt="" />
              </div>
            </div>

            <div class="font-600">
              Experiment:
              <p>
                Experiment name: NODE allows users to add the data to existed
                experiment or create new experiment name.
              </p>
              <p class="mt-05">
                Experiment description: the description of the experiment.
              </p>
              <p class="mt-05">
                Platform: the platform that the user used to do the experiment,
                NODE provides six different options, including Illumina Hiseq
                2000, Illumina Hiseq 2500, Illumina Hiseq 3000, ABI SOLiD 3, ABI
                SOLiD 4 and Illumina Miseq. Please select the correct platform
                according to your experimental design.
              </p>
              <p class="mt-05">
                Planned read length: the user could input the planned read
                length according to his/her experiment
              </p>
              <p class="mt-05">
                Mate pair: the user could choose Y/N base on his/her data.
              </p>
              <p class="mt-05">
                Library name: the user can define a name for his/her library
              </p>
              <p class="mt-05">
                Library_strategy: sequencing technique intended for this
                library. NODE provides five options, including WGS, EST,
                RNA-Seq, FINISHING, Chip-Seq. Please select the correct library
                strategy according to your experimental design.
              </p>
              <p class="mt-05">
                Library_source: specifies the type of source material that is
                being sequenced. NODE provides three options for selection,
                including genomic, transcriptomic, metagenomic. Please select
                the correct library source according to your experimental
                design.
              </p>
              <p class="mt-05">
                Library_selection: method used to enrich the target in the
                sequence library preparation. NOED provides three options
                including PCR, RT-PCR and RANDOM. Please select the correct
                library source according to your experimental design.
              </p>
              <p class="mt-05">
                Library_layout: the way how the sequence is amplified. NODE
                provides three options including single, paired, targeted_loci.
                Please select the correct library source according to your
                experimental design.
              </p>
              <p class="mt-05">
                Library_construction: briefly describe the way how the user
                constructed the library.
              </p>
              <p class="mt-05">
                Related_links: related links for your experiment, user could use
                the plus button to add more links based on your experiment.
              </p>

              <div class="text-center">
                <img src="@/assets/images/help5-5.png" alt="" />
              </div>
            </div>
            <div class="font-600">
              Sample:
              <p>
                Sample_name: NODE allows users to add the data to existed sample
                or create new sample name.
              </p>
              <p class="mt-05">
                Sample Organism: the organism the user used to prepare the
                sample, eg : liver, means this sample is get from liver. or in
                general from human.
              </p>
              <p class="mt-05">
                Sample description: the description of the sample
              </p>
              <p class="mt-05">
                Sample attributes: if there is any other information related to
                the sample that the user can not fill in any part of the sample
                table, please put in the sample attributes part. The user could
                user plus or minus button to add or delete attributes,
                respectively.
              </p>
              <p class="mt-05">
                Sample provider: the information of the people who provided the
                sample for the experimental analysis.
              </p>
              <p class="mt-05">
                Related links: links to resources related to this sample or
                sample set (publication, datasets, online databases). User could
                use the plus button to add more links based on your sample.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help5-6.png" alt="" />
              </div>
            </div>
            <div class="font-600">
              Run:
              <p>
                Run name: NODE allows users to add the data to existed run or
                create new run name.
              </p>
              <div>Description: the description of the run.</div>
              <div class="text-center">
                <img src="@/assets/images/help5-7.png" alt="" />
              </div>
            </div>

            <div class="font-600">
              Archives:
              <div>
                The new archive fills experiment_name, sample_name, run_name,
                and data_id. Click the "Check & Save" button and then click the
                "submit" button.
              </div>
              <div>Description: the description of the run.</div>
              <div class="text-center">
                <img src="@/assets/images/help5-8.png" alt="" />
              </div>
            </div>

            <div class="font-600">
              Submission:
              <div>
                After archive, in my submission, users can see the submission
                id, and users can revoke the submission to re-edit the metadata.
              </div>
              <div>Description: the description of the run.</div>
              <div class="text-center">
                <img src="@/assets/images/help5-9.png" alt="" />
              </div>
            </div>
            <p id="help-12" class="text-primary mb-05">6.1.2 Re-archive</p>
            <p>
              After review pass, users can re-edit the metadata, or batch modify
              the metadata, or add new data to existing projects. If users want
              to re-edit the metadata, users re-edit the project, experiment,
              sample, and analysis one by one in my data list of user center; or
              users batch modify experiment, sample and analysis in Metadata.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-1-2.png" alt="" />
            </div>
            <p>
              If users want to batch modify the experiments, users fill the
              experiment id and their complete information, which need to batch
              modify, in experiment multiple. If the original field value is
              changed to a null value, and the system defaults to deleting and
              changing the field value. Click the "Check & Save" button and then
              submit in Archiving Multiple.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-1-3.png" alt="" />
            </div>
            <p>
              If users want to batch modify the samples, users fill the sample
              id and their complete information, which need to batch modify, in
              sample multiple. If the original field value is changed to a null
              value, and the system defaults to deleting and changing the field
              value. Click the "Check & Save" button and then submit in
              Archiving Multiple.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-1-4.png" alt="" />
            </div>
            <ul class="re-archive">
              <li>
                If users want to batch modify the run name in the existing
                project, users fill run id, new run name and data id, which need
                to batch modify, in archiving multiple.
              </li>
              <li>
                If users want to add new data in the existing project, users
                fill the experiment id, sample id, new run name and new data id
                in archiving multiple.
              </li>
              <li>
                If users want to batch modify the data for new run name in the
                existing project, users fill the experiment id, sample id, new
                run name and data id, which need to batch modify, in archiving
                multiple.
              </li>
              <li>
                If users want to add new experiment, new sample and new data in
                the existing project, users fill the experiment name, project id
                and experiment information in experiment multiple, fill the
                sample name and sample information in sample multiple, and fill
                experiment name, sample name, run name, and data id.
              </li>
            </ul>
            <p>
              Click the "Check & Save" button and then submit in Archiving
              Multiple.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-1-5.png" alt="" />
            </div>
            <h5 id="help-13" class="text-primary font-16 mt-2">
              6.2 Analysis data archiving
            </h5>
            <p id="help-14" class="text-primary">6.2.1. New submission</p>

            <div>
              <div class="mt-05">
                When clicking the Analysis Data button, the user will enter the
                analysis data archiving interface.
              </div>
              <div class="mt-05">
                Here is some guidance for filling analysis data information.
              </div>
              <div class="mt-05">
                Analysis name: NODE allows users to add the data to existed
                analysis or create new analysis name.
              </div>
              <div class="mt-05">
                Analysis description: the description of the analysis.
              </div>
              <div class="mt-05">
                Program: the name of software or tools to analyze the data.
              </div>
              <div class="mt-05">
                Link: links to resources related to the analysis step.
              </div>
              <div>class="mt-05"Version: the version of the program.</div>
              <div class="mt-05">
                Output: select the corresponding data ID of the analysis data
              </div>
              <div class="mt-05">
                Target: associate data with projects, experiments, samples,
                runs, data and analysis by clicking each ID number.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-10.png" alt="" />
              </div>
              <div class="font-600">Archives:</div>
              <div>
                The new archive fills analysis_name and data_id. Click the
                "Check & Save" button and then click the "submit" button.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-11.png" alt="" />
              </div>

              <div class="font-600">Submission:</div>
              <div>
                After archive, in my submission, users can see the submission
                id, and users can revoke the submission to re-edit the metadata.
                And users can receive e-mail of submission after review pass.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-12.png" alt="" />
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-13.png" alt="" />
              </div>
            </div>
            <p id="help-5-2-2" class="text-primary mb-05">6.2.1 Re-archive</p>
            <p>
              Users re-edit the analysis one by one in my data list of user
              center; or users batch modify analysis in Metadata.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-2-1.png" alt="" />
            </div>
            <p>
              If users want to batch modify the analysis, users fill the
              analysis id and their complete information, which need to batch
              modify, in analysis multiple. If the original field value is
              changed to a null value, and the system defaults to deleting and
              changing the field value. Click the "Check & Save" button and then
              submit in Archiving Multiple.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-2-2.png" alt="" />
            </div>
            <p>
              If users want to add new data in the existing analysis, users fill
              the analysis id, and new data id in archiving multiple. Click the
              "Check & Save" button and then submit in Archiving Multiple.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help6-2-3.png" alt="" />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-15" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 7 </el-tag>
              Data Management
            </h4>
            <h5 id="help15-1" class="text-primary font-16">
              7.1 Raw data management
            </h5>
            <div>
              <div>
                Unchecked data files can be deleted in the "Unchecked" table in
                the submit page.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help15-1.png" alt="" />
              </div>
            </div>
            <h5 id="help15-2" class="text-primary font-16">
              7.2 Meta data management
            </h5>
            <p>
              In User Center, Project section of My Data List displays the basic
              information of the user's uploaded project. In Project page, the
              user can view all the uploaded project list pages, and the user
              can click each project id and view the details of metadata
              information of different projects. If project is unaccessible, the
              user can delect the project.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help15-2.png" alt="" />
            </div>

            <el-divider> </el-divider>

            <h4 id="help-16" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 8 </el-tag>
              Data Security
            </h4>
            <h5 id="help16-1" class="text-primary font-16">8.1 Overview</h5>
            <div>
              <p>
                NODE offers three levels of data security, including public
                data, restricted data and private data. Public data can be
                searched, viewed and downloaded by anyone using NODE. Restricted
                data can only be searched using the search function in NODE. If
                you want to visit or download this part of the data, you need to
                get permission from the data owner. Private data can only be
                viewed by the data owner.
              </p>
              <p class="mt-05">
                Once you have uploaded your data, it is automatically considered
                private unless you change its security level. Please note that
                the NODE only allows data to be converted from a high security
                level to a low security level. For example, you can only change
                private data to restricted data or public data, otherwise it is
                not allowed. You can also convert restricted data to public
                data, but not public data to restricted data.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-1.png" alt="" />
              </div>
              <div>Change of data security in NODE</div>
              <p>
                NODE encourages users to share your data so that other
                scientists can find out about your research and help other
                scientists with their research.
              </p>
              <div>
                Note: Only the data owner can make the following changes
              </div>
              <h5 id="help16-2" class="text-primary font-16">
                8.2 Private->Restricted
              </h5>
              <p>
                As the owner of the data, you can go to the Project, Experiment,
                Sample, or Analysis detail page and use the Security function to
                change the security level of the data.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-2.png" alt="" />
              </div>
              <p>
                Once you change the security level of the data from Private to
                Restricted, you have taken the step of sharing the data. The
                data will be available for browsing on the NODE. However, other
                users will still not be able to view or download the data. On
                the other hand, you can use the "until" function to choose how
                long you want to keep the data restricted, one year or two
                years. At the end of this period, the data will automatically be
                released. On the other hand, if you activate the "require
                request" function, other users will have to apply for
                authorisation to request access to the data.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-3.png" alt="" />
              </div>
              <p>
                Once you have decided how you want to keep your data, you can
                click "Confirm" and the NODE will display another confirmation
                of your data status change, as shown below:
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-4.png" alt="" />
              </div>
              <h5 id="help16-3" class="text-primary font-16">
                8.3 Private->Public
              </h5>
              <p>
                Once you change the security level of your data from restricted
                to public, which means anyone logging to NODE can search, visit
                and download your data, and the quality control information of
                the data will also be disclosed synchronously. Also, NODE will
                show up a confirmation for you to reconfirm the security status
                of your data.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-5.png" alt="" />
              </div>
              <h5 id="help16-4" class="text-primary font-16">
                8.4 Restricted->Public
              </h5>
              <p>
                Changing the security status from restricted to public is the
                same as changing the security status from private to public.
              </p>
              <p>
                If the security level of the data is changed from Private to
                Restricted or Public, the visibility of Projects, Experiments,
                Samples and Runs will change from Unaccessible to Accessible,
                i.e. other NODE users will be able to view the relevant
                information.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help7-6.png" alt="" />
              </div>
              <div class="mb-05 mt-05 font-600">Some Tips:</div>
              <div>
                The following three ways of changing the security status of data
                are NOT allowed by NODE. If you have to do the following
                changes, please contact
                <a class="text-primary" href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
                <div class="mt-05">1. Public-------->Private</div>
                <div class="mt-05">2. Public-------->Restricted</div>
                <div class="mt-05">3. Restricted -------->Private</div>
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-17" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 9 </el-tag>
              NODE Search
            </h4>
            <h5 id="help-18" class="text-primary font-16">
              9.1 Full Text Search
            </h5>
            <div>
              <p>
                NODE provides full text search. Although results searched by
                NODE will not be very precise, user can get all information
                related to the keyword inputted for search. User can use the
                information in left panel to further reach the detail
                information which he/she is interested in. For example, if the
                user searches "DNA" in NODE, he/she can get totally 51576
                results. On the right panel of the result page, user can find
                the calculation of results number according to data access, NODE
                Level, and Multiple Omics, etc. for further filter of search
                results.
              </p>
              <div class="text-center">
                <img src="@/assets/images/help8-1.png" alt="" />
              </div>
            </div>
            <h5 id="help-19" class="text-primary font-16">
              9.2 Advanced Search
            </h5>
            <div>
              In advanced search, users can select ID, name, description, etc.
              and combine ID, name, description, etc. by "AND", "OR", "NOT" to
              search the data which you want to search.
              <div class="text-center">
                <img src="@/assets/images/help8-2.png" alt="" />
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-20" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 10 </el-tag>
              Share, Review and Request
            </h4>
            <h5 id="help20-1" class="text-primary font-16">10.1 Data Share</h5>
            <div>
              <div style="text-align: justify">
                Sharing your data with other researchers is crucial to make your
                research known to other scientists and to help other scientists
                with their research. On the Project Details page, you can use
                the Share button, when the data are private or restricted and
                the shared user is NODE user, as shown in the image below. A
                similar Share button can also be found on the Sample, Experiment
                and Analysis detail pages.
                <div class="text-center">
                  <img src="@/assets/images/help9-1.png" alt="" />
                </div>
                After clicking on the Share button, you can select which part of
                the data to share by clicking on the corresponding box in front
                of Project, Experiment, or Sample and enter the email addresses
                of the person(s) with whom you wish to share this part of the
                data. The user can see all the data that has been shared on the
                following "My Share" page. Persons being shared needs to be a
                registered NODE user.
                <div class="text-center">
                  <img src="@/assets/images/help9-2.png" alt="" />
                </div>
                On the My Shares page you can see all your shared data. The
                Cancel button in red can be used to unshare the data.
                <div class="text-center">
                  <img src="@/assets/images/help9-3.png" alt="" />
                </div>
                Users that are shared will receive an email from NODE to inform
                the successful share of data.
                <div class="text-center">
                  <img src="@/assets/images/help9-4.png" alt="" />
                </div>
                They can then review the shared data under "Share From Others"
                and download the data they are interested in by clicking the
                download button in blue.
                <div class="text-center">
                  <img src="@/assets/images/help9-5.png" alt="" />
                </div>
              </div>
              <h5 id="help20-2" class="text-primary font-16">
                10.2 Data Review
              </h5>
              <div style="text-align: justify">
                If data owner wants to publish data, this feature is very useful
                to make data visible to reviewers. On the detail page of the
                project, the user can use the review button surrounded by a red
                ellipse as shown in the following picture, when the data are
                restricted. The similar review button can also be found in the
                sample, experiment and analysis detail pages.
                <div class="text-center">
                  <img src="@/assets/images/help9-6.png" alt="" />
                </div>
                The user can use this feature to select the specific person to
                view the data by entering the person's name, email address and
                the period of time they wish to view the data. The user can also
                select which part of the data he/she wishes to review by
                clicking on the appropriate box in front of Project, Experiment,
                or Sample.
                <div class="text-center">
                  <img src="@/assets/images/help9-7.png" alt="" />
                </div>
                Once you have confirmed the data to be reviewed, the "My Review"
                page is displayed. You can see all the data that has been
                checked.
                <div class="text-center">
                  <img src="@/assets/images/help9-8.png" alt="" />
                </div>
                Users that are reviewed will receive an email from NODE to
                download the successful review of data by clicking Here button.
                <div class="text-center">
                  <img src="@/assets/images/help9-9.png" alt="" />
                </div>
              </div>
              <h5 id="help20-3" class="text-primary font-16">
                10.3 Request for Restricted Data
              </h5>
              <div>
                You have to apply for the authorization from author to use
                restricted data.
                <div>For example:</div>
                <div>
                  User A has restricted data in NODE called OEP00000775, shown
                  as below:
                </div>
                <div>
                  User B can search this restricted data called OEP000775, and
                  visit it, but cannot download it:
                </div>
                <div class="text-center">
                  <img src="@/assets/images/help9-10.png" alt="" />
                </div>
                If user B want to download the data, user B has to apply for
                owner’s authorization by sending a request. User B can select
                which part of the data is required and wait for user A’s
                permission. The detail request text could be beneficial for
                passing.
                <div class="text-center">
                  <img src="@/assets/images/help9-11.png" alt="" />
                </div>
                <div class="text-center">
                  <img src="@/assets/images/help9-12.png" alt="" />
                </div>
                User B can check my requests in user center.
                <div class="text-center">
                  <img src="@/assets/images/help9-13.png" alt="" />
                </div>
                Then user A will receive an email from NODE shown as below:
                <div class="text-center">
                  <img src="@/assets/images/help9-14.png" alt="" />
                </div>
                User A can check the requests from others in user center.
                <div class="text-center">
                  <img src="@/assets/images/help9-15.png" alt="" />
                </div>
                User A can login to NODE and find the data request in user
                center to permit or decline this request.
                <div class="text-center">
                  <img src="@/assets/images/help9-16.png" alt="" />
                </div>
                If user A authorize this request, user B will receive a
                verification code which lasts for one month. User B just simply
                clicks ID to jump to the download page, and clicks Export Data
                Links to get the download address.
                <div class="text-center">
                  <img src="@/assets/images/help9-17.png" alt="" />
                </div>
                <div class="text-center">
                  <img src="@/assets/images/help9-18.png" alt="" />
                </div>
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-21" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 11 </el-tag>
              Data Download
            </h4>
            <h5 id="help-22" class="text-primary font-16">
              11.1 HTTP download
            </h5>
            <div>
              <span class="text-main-color">1.Bulk Download</span>

              <div>
                Download all links related to one project by using the
                "Download" function in NODE. This function is highly recommended
                for the download of large amounts of data.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help10-1.png" alt="" />
              </div>
              Here is the example of all links for data related to the project
              "OEP000035".
              <div class="text-center">
                <img src="@/assets/images/help10-2.png" alt="" />
              </div>
              <span class="text-main-color">2.Download specific data</span>
              <div>
                Download files one by one by clicking the download button. This
                function is more likely to be used to download the specific data
                which is interested by the researcher.
                <div class="text-center">
                  <img src="@/assets/images/help10-3.png" alt="" />
                </div>
              </div>
            </div>
            <h5 id="help-23" class="text-primary font-16">11.2 FTP download</h5>
            <div>
              Tips:
              <div>
                The data download directory is arranged according to the Run id,
                in the form of /Public/byRun/OER/OER/OER.
              </div>
              <div class="mt-05">"byRun" is for rawdata download</div>
              <div class="mt-05">
                "byAnalysis" is for analysis data download
              </div>
              <div class="mt-05">Where can I get the run list?</div>
              <div class="mt-05">
                Using "Export data links" in project detail page.
              </div>
              <div class="text-center">
                <img src="@/assets/images/help10-5.png" alt="" />
              </div>
              <span class="text-main-color">(1)Windows</span>
              <div>
                We recommend using FTP client software to upload files, e.g.,
                FileZilla
              </div>
              <div>Host: sftp://fms.biosino.org</div>
              <div>Username: NODE username (email)</div>
              <div>Password: NODE password</div>
              <div>Port: 44398</div>
              Click on the "Quickconnect" for data download.
              <div class="text-center">
                <img src="@/assets/images/help10-4.png" alt="" />
              </div>
              <span class="text-main-color"> (2)Linux</span>
              <div class="ml-1">SFTP</div>
              <div class="alert-default">
                sftp -oPort=44398
                <a
                  class="text-primary"
                  href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </div>
              <div class="mt-05 ml-1">Password: your-node-password</div>
              <div class="mt-05 ml-1">
                Navigate to the target folder you need
              </div>
              <div class="alert-default mt-05">
                cd /Public/byRun/OER**/OER****/OER****** get example.fastq.gz
              </div>
              <div class="mt-05 ml-1">LFTP</div>
              <div class="mt-05 alert-default">
                lftp :~> connect sftp://fms.biosino.org:44398
              </div>
              <div class="mt-05 alert-default">
                lftp fms.biosino.org:~> your-node-register-email
              </div>
              <div class="mt-05 ml-1">Password: your-node-password</div>
              <div class="mt-05 ml-1">
                You can use glob pget or mget to download data
              </div>
              <div class="mt-05 alert-default">
                glob pget /Public/byRun/OER**/OER****/OER******/*.gz
              </div>
              <div class="mt-05 alert-default">
                mget /Public/byRun/OER**/OER****/OER******/*.gz
              </div>
            </div>
            <el-divider> </el-divider>
            <h4 id="help-24" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 12 </el-tag>
              Human Genetic Resources Data Submission
            </h4>
            <div style="text-align: justify">
              We expect you to comply with applicable laws or regulations if you
              plan to submit the data that contains human genetic information to
              us. Please also make sure your submitted data is set to RESTRICTED
              status by using our data submission tool. In addition, your
              submission should not breach participating individuals’ rights and
              comprise their original signed consents. For specific management
              regulations and rules, please refer to
              <a
                href="http://www.gov.cn/zhengce/content/2019-06/10/content_5398829.html"
                target="_blank"
                class="text-primary"
                >http://www.gov.cn/zhengce/content/2019-06/10/content_5398829.html</a
              >
              and
              <a
                href="https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/fgzc/bmgz/202306/t20230601_186416.html"
                target="_blank"
                class="text-primary"
                >https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/fgzc/bmgz/202306/t20230601_186416.html</a
              >
            </div>

            <el-divider> </el-divider>
            <h4 id="help-25" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 13 </el-tag>
              FAQ
            </h4>
            <h5 id="help25-1" class="text-primary font-16">
              13.1 "Activation failed" during login
            </h5>
            <p>
              The error message "Activation failed" during login process may be
              caused by link timeout or users repeatedly click the link. Users
              can try to login, and if they can not login, users can contact
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
              . Or users copy and paste the incomplete link.
            </p>
            <h5 id="help25-2" class="text-primary font-16">
              13.2 "Username or Password error" during login
            </h5>
            <p>
              If the error message "Username or Password error" is shown during
              login process, users can confirm whether "Username" (email) or
              "Password" is correct. And if users forget the password, the users
              can click the "Forget password" button to find the password.
            </p>
            <h5 id="help25-3" class="text-primary font-16">
              13.3 Set data security level during contribution
            </h5>
            <p>
              The status of data may be restricted during the contribution
              process. How to set the security level sees
              <a href="#help-15" class="text-primary">1.2.7</a> Change of data
              security in NODE.
            </p>
            <h5 id="help25-4" class="text-primary font-16">
              13.4 Cite data in contribution
            </h5>
            <p>
              After successfully submitting your data to NODE, we recommend
              using the following wording to describe the data deposition in
              your manuscript:
            </p>
            <p>
              All data are accessible in NODE (
              <a
                href="https://www.biosino.org/node/"
                target="_blank"
                class="text-primary"
                >https://www.biosino.org/node/</a
              >
              ) with the accession number XXX (e.g., OEP00000073) or through the
              URL:
              <a
                href="https://www.biosino.org/node/project/detail/XXX"
                target="_blank"
                class="text-primary"
                >https://www.biosino.org/node/project/detail/XXX</a
              >
            </p>
            <p class="mt-1">Please cite the following publication:</p>
            <p>
              Advances in multi-omics big data sharing platform research.
              Chinese Bulletin of Life Sciences. 2023, 35(12): 1553-1560. DOI:
              <a
                href="https://lifescience.sinh.ac.cn/article.php?id=3716"
                target="_blank"
                class="text-primary"
                >10.13376/j.cbls/2023169</a
              >
            </p>
            <h5 id="help25-5" class="text-primary font-16">
              13.5 Use template example file
            </h5>
            <p>
              During the metadata submission process, users encounter confusion
              about how to fill in the field content.
            </p>
            <p>
              Users can download the excel example to see the example of the
              field content.
            </p>
            <div class="text-center">
              <img src="@/assets/images/help12-1.png" alt="" />
            </div>
            <h5 id="help25-6" class="text-primary font-16">
              13.6 Integrity verification not completed
            </h5>
            <p>
              After submitting the raw data, the integrity verification is not
              completed for a long time. Users can see the Failed Cause, and
              contact Email address:
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <div class="text-center">
              <img src="@/assets/images/help12-2.png" alt="" />
            </div>
            <h5 id="help25-7" class="text-primary font-16">
              13.7 Submission review not completed
            </h5>
            <p>
              After submitting metadata, the review result has not been received
              for a long time. Users can contact
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <h5 id="help25-8" class="text-primary font-16">
              13.8 How to split md5 files
            </h5>
            <p>
              Users often obtain the following md5 documents from sequencing
              companies, for example "md5_result-1.txt":
            </p>
            <div class="alert-default mt-05">
              <pre>
87409b3f8fe799930e9fda080535c756	A.R1.fq.gz
8d82dd4365b6fdf740327c04bf08b754	A.R2.fq.gz
74242c579e8342da4fbf54f09864fb83	B.R1.fq.gz
1c42cf18fbd58be4a5edacf6419e1c78	B.R2.fq.gz</pre
              >
            </div>
            <p>Prepare the following split script file “md5_split.sh”:</p>
            <div class="alert-default mt-05">
              <pre>
while read line
do
    filename=`echo $line |cut -d' ' -f2`
    echo $line > ${filename}.md5
done < $1</pre
              >
            </div>
            <p>Run the script file with the following command：</p>
            <div class="alert-default mt-05">
              <pre>
sh [split script file name] [md5 documents name from sequencing companies]
sh md5_split.sh md5_result-1.txt </pre
              >
            </div>

            <h5 id="help25-9" class="text-primary font-16">
              13.9 How to do data integrity check
            </h5>
            <p>
              How to do data integrity check sees
              <a
                href="javascript:void(0)"
                class="text-primary"
                @click="handleClick('help8-2-3')"
                >5.2.3 Data Integrity Check.</a
              >
            </p>
            <h5 id="help25-10" class="text-primary font-16">
              13.10 How to transfer my data to another user
            </h5>
            <p>
              In the NODE system, you have the flexibility to transfer the
              ownership of your archived and audited metadata and raw data,
              encompassing Projects, Experiments, Samples, and Analyses, to
              other registered users within the NODE ecosystem. To begin this
              process, please follow these simple steps:
            </p>
            <ul>
              <li>
                <p>
                  (1) Compose an email using the email address that is linked to
                  your NODE account.
                </p>
              </li>
              <li>
                <p>
                  (2) Send this email to
                  <a href="mailto:<EMAIL>" class="text-primary"
                    ><EMAIL></a
                  >, ensuring that you also include the recipient's email
                  address in the CC field.
                </p>
              </li>
              <li>
                <p>
                  (3) In the body of your email, clearly outline your request to
                  transfer the specific data (referred to as XXXX) currently
                  under your NODE account (designated as XXX) to another
                  designated NODE account (also referred to as XXX). Please
                  ensure that both the sender and recipient accounts are
                  registered within the NODE system and that you are the
                  rightful owner of the data.
                </p>
              </li>
              <li>
                <p>
                  (4) Upon receiving your email, the NODE support team will take
                  over, guiding you through the remainder of the data transfer
                  procedure.
                </p>
              </li>
            </ul>
            <p>
              We are here to ensure a smooth transition, so if you have any
              questions or need further assistance, don't hesitate to reach out
              to our support team.
            </p>
            <h5 id="help25-11" class="text-primary font-16">
              13.11 Data submissions for the same research group by multiple
              individuals
            </h5>
            <p>
              Users often inquire about how to facilitate data submissions for
              the same research group by multiple individuals. We recommend the
              following strategies:
            </p>
            <p>
              First of all, each research group should have a dedicated account,
              typically the principal investigator's (PI) commonly used email
              address, which serves as the owner of the group's data.
            </p>
            <ul class="spot-list">
              <li>
                Strategy 1: Different members within the group, such as students
                and staff, register and submit data using their individual
                accounts (email addresses). After the data submission and
                archiving are complete, they could actively email
                <EMAIL> from the submission email to request
                transferring the data to the group's fixed account(refer to
                section
                <a
                  href="javascript:void(0)"
                  class="text-primary"
                  @click="handleClick('help25-10')"
                  >13.10</a
                >
                for details).
              </li>
              <li>
                Strategy 2: Different members within the group, such as students
                and staff, register and submit data using their individual
                accounts (email addresses). During data submission, they should
                fully provide the PI's email information (refer to section
                <a
                  href="javascript:void(0)"
                  class="text-primary"
                  @click="handleClick('help-11')"
                  >6.1.1</a
                >
                Submitter for details). Subsequently, the PI can request, via
                this email, to transfer the data to their own email account
                through node-support.
              </li>
              <li>
                Strategy 3: The account information within the research group is
                maintained internally. When there is a need for data submission,
                the account information is shared with the person responsible
                for the data submission.
              </li>
            </ul>
            <p>
              These strategies ensure a structured and efficient approach to
              data submission, maintaining clarity and accountability within the
              research group.
            </p>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { defaultProps } from 'element-plus/es/components/select-v2/src/useProps';

  const activeId = ref('help-1');
  const toc = reactive([
    {
      id: 'help-1',
      title: 'Overview',
      layer: 'first',
      sort: '1.',
      children: [
        {
          id: 'help-1-1',
          title: 'Supported Experiment Type',
          layer: 'second',
          sort: '1.1',
        },
        {
          id: 'help-1-2',
          title: 'Supported Sample Type',
          layer: 'second',
          sort: '1.2',
        },
      ],
    },
    {
      id: 'help-2',
      title: 'Register and Login',
      layer: 'first',
      sort: '2.',
      children: [
        {
          id: 'help-3',
          title: 'Sign up for NODE',
          layer: 'second',
          sort: '2.1',
        },
        {
          id: 'help-4',
          title: 'Login to NODE account',
          layer: 'second',
          sort: '2.2',
        },
        {
          id: 'help-5',
          title: 'Forget password/Reset password',
          layer: 'second',
          sort: '2.3',
        },
        {
          id: 'help-6',
          title: 'Modifies registration information',
          layer: 'second',
          sort: '2.4',
        },
      ],
    },

    {
      id: 'help-7',
      title: 'User Center',
      layer: 'first',
      subtitle: 'Updated',
      sort: '3.',
      children: [
        {
          id: 'help7-1',
          title: 'User center overview',
          layer: 'second',
          sort: '3.1',
        },
        { id: 'help7-2', title: 'My data list', layer: 'second', sort: '3.2' },
        {
          id: 'help7-3',
          title: 'My Data Statistics',
          layer: 'second',
          sort: '3.3',
        },
        {
          id: 'help7-4',
          title: 'My Data Activity',
          layer: 'second',
          sort: '3.4',
        },
      ],
    },
    {
      id: 'help-26',
      title: 'Detail Page',
      layer: 'first',
      subtitle: 'Updated',
      sort: '4.',
      children: [
        {
          id: 'help26-1',
          title: 'Project Detail',
          layer: 'second',
          sort: '4.1',
        },
        {
          id: 'help26-2',
          title: 'Experiment Detail',
          layer: 'second',
          sort: '4.2',
        },
        {
          id: 'help26-3',
          title: 'Sample Detail',
          layer: 'second',
          sort: '4.3',
        },
        {
          id: 'help26-4',
          title: 'Analysis Detail',
          layer: 'second',
          sort: '4.4',
        },
      ],
    },
    {
      id: 'help-8',
      title: 'Raw Data Upload',
      layer: 'first',
      subtitle: 'Updated',
      sort: '5.',
      children: [
        { id: 'help8-1', title: 'HTTP Upload', layer: 'second', sort: '5.1' },
        {
          id: 'help8-2',
          title: 'SFTP Upload',
          layer: 'second',
          sort: '5.2',
          children: [
            {
              id: 'help8-2-1',
              title: 'FTP tools',
              layer: 'third',
              sort: '5.2.1',
            },
            {
              id: 'help8-2-2',
              title: 'Command line',
              layer: 'third',
              sort: '5.2.2',
            },
            {
              id: 'help8-2-3',
              title: 'Data Integrity Check',
              layer: 'third',
              sort: '5.2.3',
            },
          ],
        },
        {
          id: 'help8-3',
          title: 'Express hard drive',
          layer: 'second',
          sort: '5.3',
        },
      ],
    },
    {
      id: 'help-9',
      title: 'Metadata & Archiving',
      layer: 'first',
      subtitle: 'Updated',
      sort: '6.',
      children: [
        {
          id: 'help-10',
          title: 'Raw data archiving',
          layer: 'second',
          sort: '6.1',
          children: [
            {
              id: 'help-11',
              title: 'New submission',
              layer: 'third',
              sort: '6.1.1',
            },
            {
              id: 'help-12',
              title: 'Re-archive',
              layer: 'third',
              sort: '6.1.2',
            },
          ],
        },
        {
          id: 'help-13',
          title: 'Analysis data archiving',
          layer: 'second',
          sort: '6.2',
          children: [
            {
              id: 'help-14',
              title: 'New submission',
              layer: 'third',
              sort: '6.2.1',
            },
            {
              id: 'help-5-2-2',
              title: 'Re-archive',
              layer: 'third',
              sort: '6.2.2',
            },
          ],
        },
      ],
    },

    {
      id: 'help-15',
      title: 'Data Management',
      layer: 'first',
      sort: '7.',
      children: [
        {
          id: 'help15-1',
          title: 'Raw data management',
          layer: 'second',
          sort: '7.1',
        },
        {
          id: 'help15-2',
          title: 'Meta data management',
          layer: 'second',
          sort: '7.2',
        },
      ],
    },
    {
      id: 'help-16',
      title: 'Data Security',
      layer: 'first',
      sort: '8.',
      children: [
        { id: 'help16-1', title: 'Overview', layer: 'second', sort: '8.1' },
        {
          id: 'help16-2',
          title: 'Private->Restricted',
          layer: 'second',
          sort: '8.2',
        },
        {
          id: 'help16-3',
          title: 'Private->Public',
          layer: 'second',
          sort: '8.3',
        },
        {
          id: 'help16-4',
          title: 'Restricted->Public',
          layer: 'second',
          sort: '8.4',
        },
      ],
    },
    {
      id: 'help-17',
      title: 'NODE Search',
      layer: 'first',
      subtitle: 'Updated',
      sort: '9.',
      children: [
        {
          id: 'help-18',
          title: 'Full Text Search',
          layer: 'second',
          sort: '9.1',
        },
        {
          id: 'help-19',
          title: 'Advanced Search',
          layer: 'second',
          sort: '9.2',
        },
      ],
    },

    {
      id: 'help-20',
      title: 'Share, Review and Request',
      layer: 'first',
      sort: '10.',
      children: [
        { id: 'help20-1', title: 'Data Share', layer: 'second', sort: '10.1' },
        { id: 'help20-2', title: 'Data Review', layer: 'second', sort: '10.2' },
        {
          id: 'help20-3',
          title: 'Request for Restricted Data',
          layer: 'second',
          sort: '10.3',
        },
      ],
    },
    {
      id: 'help-21',
      title: 'Data download',
      layer: 'first',
      sort: '11.',
      children: [
        {
          id: 'help-22',
          title: 'HTTP download',
          layer: 'second',
          sort: '11.1',
        },
        {
          id: 'help-23',
          title: 'FTP download',
          layer: 'second',
          sort: '11.2',
        },
      ],
    },

    {
      id: 'help-24',
      title: 'Human Genetic Resources Data Submission',
      layer: 'first',
      sort: '12.',
    },
    {
      id: 'help-25',
      title: 'FAQ',
      layer: 'first',
      subtitle: 'Updated',
      sort: '13.',
      children: [
        {
          id: 'help25-1',
          title: '"Activation failed" during login',
          layer: 'second',
          sort: '13.1',
        },
        {
          id: 'help25-2',
          title: '"Username or Password error" during login',
          layer: 'second',
          sort: '13.2',
        },
        {
          id: 'help25-3',
          title: 'Set data security level during contribution ',
          layer: 'second',
          sort: '13.3',
        },
        {
          id: 'help25-4',
          title: 'Cite data in contribution  ',
          layer: 'second',
          sort: '13.4',
        },
        {
          id: 'help25-5',
          title: 'Use template example file  ',
          layer: 'second',
          sort: '13.5',
        },
        {
          id: 'help25-6',
          title: 'Integrity verification not completed ',
          layer: 'second',
          sort: '13.6',
        },
        {
          id: 'help25-7',
          title: 'Submission review not completed',
          layer: 'second',
          sort: '13.7',
        },
        {
          id: 'help25-8',
          title: 'How to split md5 files',
          layer: 'second',
          sort: '13.8',
        },
        {
          id: 'help25-9',
          title: 'How to do data integrity check',
          layer: 'second',
          sort: '13.9',
        },
        {
          id: 'help25-10',
          title: 'How to transfer my data to another user',
          layer: 'second',
          sort: '13.10',
        },
        {
          id: 'help25-11',
          title:
            'Data submissions for the same research group by multiple individuals',
          layer: 'second',
          sort: '13.11',
        },
      ],
    },
  ]);

  const handleClick = id => {
    const section = document.getElementById(id);
    // section.scrollIntoView({ behavior: 'smooth' });
    section.scrollIntoView({
      top: 100,
      behavior: 'smooth',
      block: 'start',
      inline: 'start',
    });
    // Highlight the corresponding directory title
    activeId.value = id;
  };
</script>

<style lang="scss" scoped>
  p {
    text-align: justify;
  }
  .re-archive {
    padding-left: 20px;
  }
  .re-archive li {
    list-style-type: disc; /* 实心圆点 */
    &::marker {
      color: #3a78e8;
    }
  }

  .Updated {
    color: #3a78e8 !important;
  }
  .active {
    a {
      color: #3a78e8 !important;
    }
  }

  .analyse-page {
    padding: 20px 0 25px 0;
    font-size: 16px;
    .container {
      margin: 0 auto;
    }
    .detail-box {
      background: #fff;
      border-radius: 4px;
      position: relative;
      box-shadow: 0 1px 20px 0 rgba(118, 109, 224, 0.1);
      padding: 16px;
    }
    .el-tag {
      border-radius: 8px;
      margin-right: 6px;
    }
    .el-divider {
      margin: 0.5rem 0 1rem !important;
    }
    h5 {
      margin-bottom: 0.5rem;
    }
    .mb-03 {
      margin-bottom: 0.3rem;
    }
    h5 {
      font-weight: 600;
    }
    img {
      text-align: center;
      max-width: 80%;
      margin: 10px 0;
    }
  }
  .layer-first {
    a {
      color: #333333 !important;
    }
    &.active a {
      color: #3a78e8 !important;
    }
  }
  .layer-second {
    color: #777777 !important;
  }
  .layer-third {
    color: #888888 !important;
  }
  li {
    a {
      font-weight: 600;
      font-size: 16px;
    }
    padding: 6px 0;
    line-height: 18px;
    &:hover a {
      color: #3a78e8 !important;
    }
  }

  .el-tree {
    max-height: 700px;
    overflow-y: scroll;
  }
  :deep(.el-tree-node__content) {
    margin-bottom: 6px;
  }

  .alert-default {
    padding: 5px 15px;
    margin: 0 15px;
    color: #666;
    background-color: #f0f0f0;
    border-color: #e5e5e5;
  }

  :deep(.el-tree-node__content) {
    height: auto !important;
  }

  .spot-list {
    list-style-type: none; /* 去掉默认的列表样式 */
    padding-left: 20px; /* 给圆点留出空间 */
  }

  .spot-list li {
    line-height: 1.7;
    position: relative; /* 允许伪元素定位 */
    margin-bottom: 8px; /* 可选：调整列表项间距 */
    padding-left: 0; /* 确保内容与圆点保持对齐 */
    text-indent: 0; /* 避免缩进 */
  }

  .spot-list li::before {
    content: ''; /* 添加伪元素 */
    position: absolute;
    left: -15px; /* 定位到最左侧 */
    top: 17px; /* 确保与第一行对齐 */
    width: 6px; /* 圆点宽度 */
    height: 6px; /* 圆点高度 */
    background-color: #1456f0; /* 设置圆点颜色 */
    border-radius: 50%; /* 圆点形状 */
  }
</style>
