<template>
  <div class="app-container">
    <div class="card list mb-1">
      <h3 class="mb-0 mt-0">Please Fill in Email and Path</h3>
      <el-divider></el-divider>
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-form-item
          label="Synchronization Email"
          label-width="180"
          prop="email"
        >
          <el-input
            v-model="form.email"
            style="width: 400px"
            clearable
            placeholder="填写前台用户的email地址"
          />
        </el-form-item>
        <el-form-item
          label="Synchronization Path"
          label-width="180"
          prop="path"
        >
          <el-input
            v-model="form.path"
            clearable
            placeholder="填写需要同步的ftp目录地址,直接从用户的ftp根目录开始即可"
            style="width: 400px"
          />
        </el-form-item>
        <el-form-item
          label="Synchronization MD5"
          prop="readMd5"
          label-width="180"
        >
          <el-radio-group v-model="form.readMd5">
            <el-radio :label="1">Yes, MD5由Node提供</el-radio>
            <el-radio :label="0">No, MD5由用户提供</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleGetRecordNum"
            >Get FtpFileLog Record Num
          </el-button>
          <el-button
            type="primary"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleCheckFtpFileLog"
            >Check ftp_file_log Records
          </el-button>
          <el-button
            type="warning"
            :disabled="isStrBlank(form.email) || isStrBlank(form.path)"
            @click="handleStartSync"
            >Start Synchronization
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="showResultTable" class="card list">
      <h3 class="mb-0 mt-0">Data Allocation Result</h3>
      <el-divider></el-divider>
      <el-form
        v-show="showSearch"
        ref="searchForm"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Path">
          <el-input v-model="queryParams.path" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
      >
        <el-table-column prop="name" label="Name" min-width="120" />
        <el-table-column
          prop="path"
          label="Path"
          show-overflow-tooltip
          min-width="180"
        />
        <el-table-column prop="readableFileSize" label="Size" width="90" />
        <el-table-column
          prop="md5"
          label="MD5"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column prop="md5File" label="MD5 File" width="140" sortable>
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.md5FileStatus === 'Provided'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <CircleCheckFilled></CircleCheckFilled>
              </el-icon>
              <el-icon
                v-if="scope.row.md5FileStatus === 'Not Provided'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <RemoveFilled />
              </el-icon>
              <el-icon
                v-if="scope.row.md5FileStatus === 'Invalid Format'"
                :color="iconColor(scope.row.md5FileStatus)"
              >
                <CircleCloseFilled />
              </el-icon>
              <span
                :style="{
                  color: iconColor(scope.row.md5FileStatus),
                }"
                >{{ scope.row.md5FileStatus }}</span
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="Submission Date" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        :auto-scroll="false"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import {
    checkFtpFileLog,
    getFtpFileRecordCount,
    startSync,
  } from '@/api/metadata/data';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    total: 0,
    form: {
      email: '',
      path: '',
      readMd5: 0,
    },
    queryParams: {
      path: '',
      pageNum: 1,
      pageSize: 20,
    },
  });
  let rules = reactive({
    path: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    email: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
    readMd5: [
      {
        required: true,
        trigger: 'blur',
      },
    ],
  });

  const { queryParams, form, total } = toRefs(data);

  const showResultTable = ref(false);
  const showSearch = ref(true);
  let dataList = ref([]);
  let tableData = ref([]);

  function resetQuery() {
    queryParams.value.path = '';
    getDataList();
  }

  function handleGetRecordNum() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('Getting FtpFileLog Record Num, please wait...');
        getFtpFileRecordCount(form.value)
          .then(response => {
            proxy.$modal.alertSuccess(
              `FtpFileLog Record Num: ${response.data}`,
            );
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function handleCheckFtpFileLog() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('Checking ftp_file_log Records, please wait...');
        checkFtpFileLog(form.value)
          .then(response => {
            proxy.$modal.alertSuccess(`consistent! count = ${response.data}`);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function handleStartSync() {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        proxy.$modal.loading('Syncing..., please wait...');
        startSync(form.value)
          .then(response => {
            dataList.value = response.data;
            showResultTable.value = true;
            getDataList();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function getDataList() {
    let list = dataList.value;
    if (!isStrBlank(queryParams.value.path)) {
      list = list.filter(
        item => item.path.indexOf(queryParams.value.path) !== -1,
      );
    }
    // 对list进行分页
    total.value = list.length;
    if (total.value > 0) {
      let start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
      let end =
        start + queryParams.value.pageNum > total.value
          ? total.value
          : start + queryParams.value.pageSize;
      tableData.value = list.slice(start, end);
    } else {
      tableData.value = [];
    }
  }

  const iconColor = status => {
    if (status === 'Provided') {
      return '#3A78E8';
    } else if (status === 'Invalid Format') {
      return '#FF8989';
    } else if (status === 'Not Provided') {
      return '#999999';
    } else return '#999999';
  };
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
