package org.biosino.api.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.AESUtil;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TokenVerifyInterceptor implements HandlerInterceptor {

    private final RemoteMemberService remoteMemberService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 提交 token 校验
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        String token = request.getParameter("token");

        if (StringUtils.isBlank(token)) {
            token = request.getHeader("token");
        }
        if (StringUtils.isBlank(token)) {
            throw new ServiceException("Err, Parameter token is null");
        }

        String decrypt = AESUtil.decrypt(token, StandardCharsets.UTF_8, "******TEST@NODE*");

        // 验证token逻辑 TODO
        log.info("TOKEN : " + decrypt);

        R<MemberDTO> r = remoteMemberService.getMemberInfoByEmail(decrypt, decrypt, SecurityConstants.INNER);

        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException("token错误");
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }
}
