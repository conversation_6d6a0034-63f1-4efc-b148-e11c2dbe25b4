package org.biosino.esindex.index.pojo2doc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.es.entity.NodeEs;
import org.biosino.common.es.enums.NodeEsTypeEnum;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.esindex.index.wrapper.WrapperRunData;

import java.util.LinkedHashSet;
import java.util.Map;

public class ConvertPojo2SampleData extends ConvertPojoAbstract<WrapperRunData, NodeEs> {

//    private final NodeEsMapper nodeEsMapper;

    public ConvertPojo2SampleData() {
    }

    // @Override
    public NodeEs toDocument(Sample docData, NodeEs expNodeEs, NodeEs prjNodeEs, final NodeEs initPrj, final NodeEs initExp) {
        String ownership = docData.getOwnership();
        if (StringUtils.isBlank(ownership) || OwnershipEnum.self_support.getDesc().equalsIgnoreCase(ownership)) { // NODE 数据
            return toDoc(docData, expNodeEs, prjNodeEs, initPrj, initExp);
        }
        return null;
    }

    private NodeEs toDoc(final Sample docData, NodeEs expNodeEs, NodeEs prjNodeEs, final NodeEs initPrj, final NodeEs initExp) {
        final String id = docData.getSapNo();
        final NodeEs doc = initNodeEs(id, NodeEsTypeEnum.sample, docData.getCreateDate(), docData.getCreator(), docData.getName(), docData.getDescription()
                , docData.getUpdateDate(), docData.getVisibleStatus(), docData.getUsedIds());

        doc.setSecurity(addCollToSet(docData.getSecurities()));
        LinkedHashSet<String> coll = addCollToSet(docData.getDataTypes());
        doc.setFileType(getFirst(coll));
        doc.setRelaFileType(coll);

        final String subjectType = StrUtil.trimToNull(docData.getSubjectType());
        coll = addObjToSet(subjectType);
        doc.setSampleType(getFirst(coll));
        doc.setRelaSampleType(coll);
//        if (subjectType != null && subjectType.contains("Environment host")) {
//            coll.add("Environment host All");
//        }

        coll = addObjToSet(docData.getOrganism());
        doc.setOrganism(getFirst(coll));
        doc.setRelaOrganism(coll);

        coll = addObjToSet(docData.getTissue());
        doc.setTissue(getFirst(coll));
        doc.setRelaTissue(coll);

        final Map<String, String> attributes = docData.getAttributes();
        if (CollUtil.isNotEmpty(attributes)) {
            coll = addObjToSet(attributes.get("subject_id"));
            doc.setSubjectId(getFirst(coll));
            doc.setRelaSubjectId(coll);

            coll = addObjToSet(attributes.get("biomaterial_provider"));
            doc.setBiomaterialProvider(getFirst(coll));
            doc.setRelaBiomaterialProvider(coll);

            coll = addObjToSet(attributes.get("disease"));
            doc.setDisease(getFirst(coll));
            doc.setRelaDisease(coll);

            coll = addObjToSet(attributes.get("dis_phenotype"));
            doc.setDisPhenotype(getFirst(coll));
            doc.setRelaDisPhenotype(coll);

            coll = addObjToSet(attributes.get("mutation_type"));
            doc.setMutationType(getFirst(coll));
            doc.setRelaMutationType(coll);

            coll = addObjToSet(attributes.get("sample_loc"));
            doc.setSampleLoc(getFirst(coll));
            doc.setRelaSampleLoc(coll);

            coll = addObjToSet(attributes.get("gender"));
            doc.setGender(getFirst(coll));
            doc.setRelaGender(coll);

            coll = addObjToSet(attributes.get("extracted_mol_type"));
            doc.setExtractedMolType(getFirst(coll));
            doc.setRelaExtractedMolType(coll);

            coll = addObjToSet(attributes.get("dev_stage"));
            doc.setDevStage(getFirst(coll));
            doc.setRelaDevStage(coll);

            coll = addObjToSet(attributes.get("biome"));
            doc.setBiome(getFirst(coll));
            doc.setRelaBiome(coll);

            // env_biome
            coll = addObjToSet(attributes.get("env_biome"));
            doc.setEnvBiome(getFirst(coll));
            doc.setRelaEnvBiome(coll);

            // env_material
            coll = addObjToSet(attributes.get("env_material"));
            doc.setEnvMaterial(getFirst(coll));
            doc.setRelaEnvMaterial(coll);

            // env_feature
            coll = addObjToSet(attributes.get("env_feature"));
            doc.setEnvFeature(getFirst(coll));
            doc.setRelaEnvFeature(coll);
        }

        wrapSubmitter(doc, docData.getSubmitter());
        wrapPublishInfo(doc, docData.getPublishInfos());
        // 合并关联字段
        mergePrjInfo(initPrj, doc);
//        mergeExpInfo(initExp, doc);

        mergeSapInfo(doc, expNodeEs);
        mergeSapInfo(doc, prjNodeEs);
        ConvertPojoAbstract.addFastQc(doc, docData.getSeqkitResultSet());
        return doc;
    }

}
