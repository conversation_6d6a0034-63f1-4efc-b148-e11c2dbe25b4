<template>
  <div class="bg-gray radius-8 d-flex align-items-center log-card p-10">
    <svg-icon :icon-class="icon"> </svg-icon>
    <div class="ml-05">
      <div class="text-main-color font-600 font-18 name">{{ name }}</div>
      <div>
        <el-tooltip effect="light" content="success" placement="top">
          <span class="text-primary cursor-pointer">{{
            formatNumber(count)
          }}</span>
        </el-tooltip>

        <span class="text-secondary-color" style="margin: 0 0.3rem">/</span>

        <el-tooltip effect="light" content="total" placement="top">
          <span class="text-secondary-color text-primary cursor-pointer">{{
            formatNumber(total)
          }}</span>
        </el-tooltip>
      </div>

      <div v-if="name === 'Run' || name === 'Analysis'" class="font-16">
        <el-tooltip
          effect="light"
          content="Number of data passed"
          placement="top"
        >
          <span class="text-warning">{{ data }}</span>
        </el-tooltip>

        <span class="text-secondary-color" style="margin: 0 0.3rem"></span>
        <el-tooltip
          v-if="size && size !== ''"
          effect="light"
          content="Number of data size passed"
          placement="top"
        >
          <span class="text-secondary-color text-warning">({{ size }})</span>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps } from 'vue';
  import { formatNumber } from '@/utils';
  defineProps({
    icon: {
      type: String,
    },
    name: {
      type: String,
    },
    data: {
      type: Number,
      required: false,
      default: 0,
    },
    size: {
      type: String,
      required: false,
    },
    count: {
      type: Number,
      default: 0,
    },
    total: {
      type: Number,
      default: 0,
      required: false,
    },
  });
</script>

<style scoped lang="scss">
  .svg-icon {
    width: 55px;
    height: 55px;
  }
  .log-card {
    height: 98px;
  }
</style>
