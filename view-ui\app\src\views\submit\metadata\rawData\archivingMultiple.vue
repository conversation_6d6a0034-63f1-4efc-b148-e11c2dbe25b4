<template>
  <div class="w-100">
    <div v-loading="isRequesting" class="card card-container">
      <DownloadTemplate
        :key="'downloadTemp' + currStage"
        v-model="isRequesting"
        :curr-stage="currStage"
        :curr-data-type="dataType"
        @change-ht-table-data="changeHtTableData"
      >
      </DownloadTemplate>
      <ht-table
        :key="'rawData-archive-ht-table-' + htTableKey"
        ref="htTableRef"
        :hot-table-data="hotTableData"
        :hot-columns="hotColumns"
        :stretch-h="'all'"
      ></ht-table>

      <div class="text-align-right pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s"
          round
          :disabled="isRequesting"
          @click="saveData"
          >Check & Save
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >Reset
        </el-button>
        <el-button
          :disabled="!subNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >Delete
        </el-button>
      </div>
      <result-log
        v-if="resultDialogOpen"
        curr-exp-type="Archiving-RawData"
        :log-data="resultDialogData"
      ></result-log>
    </div>
    <div class="card mt-1 p-20">
      <el-tabs v-model="activeName" type="card" class="demo-tabs">
        <el-tab-pane label="Unarchived Data" name="Unarchived Data">
          <pre-archived-table
            ref="unarchivedTableRef"
            table-type="rawData"
            archived="no"
            :show-archived-column="false"
            :show-export-btn="true"
            :show-cancel-archive-btn="false"
          ></pre-archived-table>
        </el-tab-pane>
        <el-tab-pane label="Archiving Data" name="Archiving Data">
          <pre-archived-table
            ref="archivingTableRef"
            table-type="rawData"
            archived="yes"
            :show-archived-column="true"
            :show-export-btn="true"
            :show-cancel-archive-btn="false"
          ></pre-archived-table>
        </el-tab-pane>
        <el-tab-pane label="Archived Raw Data" class="ml5">
          <archived-raw-data-table
            ref="archivedRawDataTable"
            :show-export-btn="true"
          ></archived-raw-data-table>
        </el-tab-pane>
        <el-tab-pane
          label="Archived Analysis Data"
          name="Archived Analysis Data"
        >
          <archived-analysis-data-table
            ref="archivedAnalysisDataTable"
            :show-export-btn="true"
          ></archived-analysis-data-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <ArchivingDialog ref="archivingDialogRef"></ArchivingDialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import DownloadTemplate from '@/views/submit/metadata/rawData/common/DownloadTemplate.vue';
  import PreArchivedTable from '@/views/submit/rawdata/common/PreArchivedTable.vue';
  import ArchivedAnalysisDataTable from '@/views/submit/rawdata/common/ArchivedAnalysisDataTable.vue';
  import ArchivedRawDataTable from '@/views/submit/rawdata/common/ArchivedRawDataTable.vue';
  import HtTable from '@/components/HtTable/index.vue';
  import { requiredValidator } from '@/utils/ht/validator';
  import useSubmissionStore from '@/store/modules/metadata';
  import {
    batchSaveRawDataArchive,
    deleteArchiving,
    getMultiRawDataArchiveBySubNo,
  } from '@/api/metadata/archive';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import ResultLog from '@/views/submit/metadata/rawData/common/ResultLog.vue';
  import ArchivingDialog from '@/views/submit/components/ArchivingDialog.vue';

  const isRequesting = ref(false);
  let { proxy } = getCurrentInstance();
  // 提交者信息
  const submissionStore = useSubmissionStore();
  // 提交编号
  const { subNo } = storeToRefs(submissionStore);
  // 当前阶段
  const currStage = ref('archiving');
  let dataType = ref('rawData');
  /*********************************************************/
  let htTableKey = ref(0);
  let hotTableData = reactive([]);
  let hotColumns = reactive([]);

  onMounted(() => {
    // 初始化列信息
    initHtColumns();

    if (subNo.value) {
      // 查询初始化数据
      getMultiRawDataArchiveBySubNo(subNo.value).then(response => {
        hotTableData = response.data || reactive([]);
        // 重新渲染组件
        htTableKey.value++;
      });
    }

    htTableKey.value++;
  });

  function initHtColumns() {
    hotColumns = [
      {
        title: 'experiment_id',
        type: 'text',
        isRequired: 'optional',
        data: 'experiment_id',
        des: 'For newly created experiments , the experiment ID can be left blank. To archive data for a successfully created experiment which has experiment ID already, fill in the experiment ID here. ',
        show: true,
      },
      {
        title: 'experiment_name',
        type: 'text',
        isRequired: 'optional',
        data: 'experiment_name',
        des: 'Fill in the unique name of the experiment you plan to create or the name of a experiment that has been successfully created and assigned a experiment ID. If the experiment ID and experiment name do not match, the experiment ID shall prevail and the experiment name shall be ignored.',
        show: true,
      },
      {
        title: 'sample_id',
        type: 'text',
        isRequired: 'optional',
        data: 'sample_id',
        des: 'For newly created samples, the sample ID can be left blank. To archive data for a successfully created sample which has sample ID already, fill in the sample ID here. ',
        show: true,
      },
      {
        title: 'sample_name',
        type: 'text',
        isRequired: 'optional',
        data: 'sample_name',
        des: 'Fill in the unique name of the sample you plan to create or the name of a sample that has been successfully created and assigned a sample ID. If the sample ID and sample name do not match, the sample ID shall prevail and the sample name shall be ignored.',
        show: true,
      },
      {
        title: 'run_id',
        type: 'text',
        isRequired: 'optional',
        data: 'run_id',
        des: 'For newly created runs, the run ID can be left blank. To archive raw data for a successfully created run which has an run ID already, fill in the run ID here. If the experiment ID and experiment name do not match, the experiment ID shall prevail and the experiment name shall be ignored.',
        show: true,
      },
      {
        title: 'run_name',
        type: 'text',
        isRequired: 'optional',
        data: 'run_name',
        des: 'Fill in the unique name of the run you plan to create or the name of a run that has been successfully created and assigned an run ID. If the run ID and run name do not match, it is considered an update to the run name.',
        show: true,
      },
      {
        title: 'run_description',
        type: 'text',
        isRequired: 'optional',
        data: 'run_description',
        des: 'Description of the run.',
        show: true,
      },
      {
        title: 'data_id',
        type: 'text',
        isRequired: 'required',
        data: 'data_id',
        validator: requiredValidator,
        des: 'Fill in the data ID to be archived for raw data. For pair end sequencing data, two files (forward and reverse) are a run. For single end sequencing data, a file is a run. The data ID filled in the data_id column cannot be duplicated.',
        show: true,
      },
      {
        title: 'file_name',
        type: 'text',
        isRequired: 'optional',
        data: 'file_name',
        des: 'If fill the file name, it will update the data`s file name.',
        show: true,
      },
      {
        title: 'data_remark',
        type: 'text',
        isRequired: 'optional',
        data: 'data_remark',
        des: 'Remark of the data.',
        show: true,
      },
    ];
  }

  /** 上传excel成功后初始化ht表格 */
  function changeHtTableData(excelData) {
    proxy.$refs['htTableRef'].changeTbData(excelData);
  }

  function changeLoadingFlag(flag) {
    isRequesting.value = flag;
  }

  /*********************************************************/

  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);

  function checkAndSubmit() {
    proxy.$modal.loading('Saving data, please wait...');
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  }

  /** 提交数据 */
  const saveData = () => {
    changeLoadingFlag(true);
    // 使用setTimeout，保证loading动画立即弹出
    setTimeout(checkAndSubmit, 100);
  };

  function saveForm() {
    resultDialogOpen.value = false;
    let { validatePromise, hotInstance } = proxy.$refs['htTableRef'];
    if (!validatePromise) {
      proxy.$modal.closeLoading();
      return;
    }
    validatePromise().then(valid => {
      if (!valid) {
        changeLoadingFlag(false);
        proxy.$modal.closeLoading();
        // 校验失败
        proxy.$modal.msgWarning('Please correct all invalid cells.');
      } else {
        doSaveForm(hotInstance);
      }
    });
  }

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm('Are you sure to delete all Multiple Archived Data?')
      .then(() => {
        proxy.$modal.loading('Deleting, please wait');
        const params = {
          subNo: subNo.value,
          type: 'rawData',
          single: false,
        };
        deleteArchiving(params)
          .then(() => {
            proxy.$modal.msgSuccess('Cancel archived successful');
            refreshTable();
            resetForm();
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {});
  };

  function resetForm() {
    hotTableData.length = 0;
    proxy.$refs['htTableRef'].initHandsontable();
  }

  function refreshTable() {
    proxy.$refs['unarchivedTableRef'].getDataList();
    proxy.$refs['archivingTableRef'].getDataList();
    proxy.$refs['archivedAnalysisDataTable'].getDataList();
    proxy.$refs['archivedRawDataTable'].getDataList();
  }

  function doSaveForm(hotInstance) {
    resultDialogOpen.value = false;
    let data = hotInstance.getData();
    let titles = hotInstance.getColHeader();
    let paramData = {
      stage: 'archiving',
      subNo: subNo.value,
      dataType: dataType.value,
      datas: data,
      titles: titles,
    };
    batchSaveRawDataArchive(paramData)
      .then(response => {
        let data = response.data;
        if (data) {
          // 提交失败，展示错误信息
          resultDialogData.value = data;
          resultDialogOpen.value = true;
        } else {
          refreshTable();
          proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
        }
        changeLoadingFlag(false);
      })
      .catch(() => {
        changeLoadingFlag(false);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const activeName = ref('Unarchived Data');
</script>

<style lang="scss" scoped>
  :deep(.el-tabs--card > .el-tabs__header) {
    border-bottom: none;

    .el-tabs__nav {
      border: none;
    }

    .el-tabs__item {
      margin-left: 3px;
      border-radius: 4px;
      background-color: #edf4fe;
    }

    .el-tabs__item.is-active {
      background-color: #3a78e8;
      color: #fff;
      border-radius: 4px;
    }
  }

  .btn-round-primary {
    padding: 12px !important;
  }

  :deep(.el-radio__label) {
    font-size: 16px;
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }

  :deep(.dialog .el-dialog__body) {
    text-align: center;
    padding: 10px 15px 0 15px;

    a:hover {
      color: #3a78e8;
    }
  }
</style>
