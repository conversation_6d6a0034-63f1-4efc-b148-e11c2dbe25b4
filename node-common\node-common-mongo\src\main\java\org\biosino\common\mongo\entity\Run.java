package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Document
public class Run {
    @Id
    private String id;

    @NotBlank(message = "run_no不能为空")
    @GenerateValue(prefix = SequenceType.RUN)
    @Field("run_no")
    private String runNo;

    @Field("sub_no")
    private String subNo;

    @NotBlank(message = "exp_no不能为空")
    @Field("exp_no")
    private String expNo;

    @NotBlank(message = "sap_no不能为空")
    @Field("sap_no")
    private String sapNo;

    @NotBlank(message = "name不能为空")
    private String name;

    private String description;

    @Field("related_links")
    private List<String> relatedLinks;

    @NotBlank(message = "creator不能为空")
    private String creator;

    @NotNull(message = "submission_date不能为空")
    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    @Field("public_date")
    private Date publicDate;

    @Field("temp_data")
    private Run tempData;

    @Field("hit_num")
    private Long hitNum;

    @Field("export_num")
    private Long exportNum;

    private Submitter submitter;

    @Field("other_ids")
    private List<OtherIds> otherIds;

    private String operator;

    @Field("operation_date")
    private Date operationDate;

    @Field("used_ids")
    private List<String> usedIds;

    private String ownership;

    @Field("source_project")
    private List<String> sourceProject;

    @ValidEnum(enumClass = VisibleStatusEnum.class, allowNull = false, message = "visible_status不在合法范围内")
    // VisibleStatusEnum
    @Field("visible_status")
    private String visibleStatus;

    @ValidEnum(enumClass = AuditEnum.class, allowNull = false, message = "audited不在合法范围内")
    private String audited;

    // 非数据库字段
    @Transient
    private Long dataNum;

    @Transient
    private String projectNo;

    @Transient
    private String flag;

}
