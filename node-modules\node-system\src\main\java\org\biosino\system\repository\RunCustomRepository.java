package org.biosino.system.repository;

import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface RunCustomRepository {


    List<Run> findDetailByExpNoIn(Collection<String> expNos);

    List<Run> findAllByExpNoIn(List<String> expNos);

    List<Run> findTempByExpNoIn(Collection<String> expNos);

    List<Run> findDetailBySapNoIn(Collection<String> sapNos);

    void updateToDeleteAllByRunNoIn(Collection<String> runNos);

    void updateCreatorByRunNoIn(Collection<String> runNos, String creator);

    Optional<Run> findFirstByRunNo(String runNo);

    List<Run> findAllByRunNoIn(Collection<String> runNos);

    List<String> findAllRunNosByExpNosAndSapNos(List<String> expNos, List<String> sapNos);

    Page<Run> findRunPage(MetadataQueryDTO queryDTO);

    MongoPagingIterator<Run> getPagingIterator(MetadataQueryDTO queryDTO);
}
