package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
public class DataExportDTO {
    @JSONField(name = "id", ordinal = 1)
    private String id;

    @JSONField(name = "dat_no", ordinal = 2)
    private String datNo;

    @JSONField(name = "sub_no", ordinal = 3)
    private String subNo;

    @JSONField(name = "run_no", ordinal = 4)
    private String runNo;

    @JSONField(name = "anal_no", ordinal = 5)
    private String analNo;

    @JSONField(name = "name", ordinal = 6)
    private String name;

    @JSONField(name = "data_type", ordinal = 7)
    private String dataType;

    @JSONField(name = "upload_type", ordinal = 8)
    private String uploadType;

    @JSONField(name = "remark", ordinal = 9)
    private String remark;

    @JSONField(name = "archived", ordinal = 10)
    private String archived;

    @JSONField(name = "text", ordinal = 11)
    private String text;

    @JSONField(name = "file_name", ordinal = 12)
    private String fileName;

    @JSONField(name = "file_path", ordinal = 13)
    private String filePath;

    @JSONField(name = "ftp_path", ordinal = 14)
    private String ftpPath;

    @JSONField(name = "source_path", ordinal = 15)
    private String sourcePath;

    @JSONField(name = "file_size", ordinal = 16)
    private Long fileSize;

    @JSONField(name = "bases", ordinal = 17)
    private Long bases;

    @JSONField(name = "ip", ordinal = 18)
    private String ip;

    @JSONField(name = "express", ordinal = 19)
    private String express;

    @JSONField(name = "tracking_num", ordinal = 20)
    private String trackingNum;

    @JSONField(name = "creator", ordinal = 21)
    private String creator;

    @JSONField(name = "submission_date", ordinal = 22)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 23)
    private Date updateDate;

    @JSONField(name = "security", ordinal = 24)
    private String security;

    @JSONField(name = "public_date", ordinal = 25)
    private Date publicDate;

    @JSONField(name = "operator", ordinal = 26)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 27)
    private Date operationDate;

    @JSONField(name = "human_record_option", ordinal = 28)
    private String humanRecordOption;

    @JSONField(name = "human_record_no", ordinal = 29)
    private String humanRecordNo;

    @JSONField(name = "status", ordinal = 30)
    private String status;

    @JSONField(name = "upload_time", ordinal = 31)
    private Date uploadTime;

    @JSONField(name = "start_time", ordinal = 32)
    private Date startTime;

    @JSONField(name = "used_ids", ordinal = 33)
    private List<String> usedIds;

    @JSONField(name = "ownership", ordinal = 34)
    private String ownership;

    @JSONField(name = "md5", ordinal = 35)
    private String md5;

    @JSONField(name = "cal_date", ordinal = 36)
    private Date calcDate;

    @JSONField(name = "temp_data", ordinal = 37)
    private Data tempData;

    @JSONField(name = "complete", ordinal = 38)
    private Boolean complete;
}
