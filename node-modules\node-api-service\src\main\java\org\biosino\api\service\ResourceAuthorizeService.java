package org.biosino.api.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.biosino.api.dto.ResourceAuthorizeCreateDTO;
import org.biosino.api.repository.DataRepository;
import org.biosino.api.repository.ResourceAuthorizeRepository;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.MailTemplate;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/4/15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceAuthorizeService {

    private final RemoteMemberService remoteMemberService;

    private final RemoteNotificationService remoteNotificationService;

    private final RemoteDataService remoteDataService;

    private final DataRepository dataRepository;

    private final ResourceAuthorizeRepository resourceAuthorizeRepository;

    public void saveBatch(ResourceAuthorizeCreateDTO dto) {
        String username = dto.getUsername();
        String password = dto.getPassword();
        String description = dto.getDescription();
        MemberDTO requestUser = validUsernameAndPassword(username, password);

        String type = dto.getType();
        List<String> typeNos = dto.getTypeNos();
        Optional<AuthorizeType> nameOption = AuthorizeType.findByName(type);
        if (!nameOption.isPresent()) {
            throw new ServiceException("The stage type is wrong. Please Check.");
        }
        // api接口暂时不支持以data类型的批量申请
        if (StrUtil.equals(type, AuthorizeType.data.name())) {
            throw new ServiceException("API接口暂时不支持以data类型的批量申请");
        }

        List<ResourceAuthorize> saveList = new ArrayList<>();
        for (String typeNo : typeNos) {
            ResourceAuthorize authorize = createResourceAuthorize(type, typeNo, description, requestUser);
            if (ObjectUtil.isNull(authorize)) {
                log.info("该请求已存在，已跳过处理, user:{}, type:{}, typeNo:{}", requestUser.getId(), type, typeNo);
                continue;
            }
            saveList.add(authorize);
        }

        resourceAuthorizeRepository.saveAll(saveList);

        for (ResourceAuthorize resourceAuthorize : saveList) {
            R<MemberDTO> nodeUser = remoteMemberService.getOneMemberByMemberId(resourceAuthorize.getOwner(), "FtpUser", SecurityConstants.INNER);
            if (ObjectUtil.isNull(nodeUser) || ObjectUtil.isNull(nodeUser.getData()) || R.isError(nodeUser)) {
                log.warn("saveRequestData出错,用户：{} 数据未查询到", resourceAuthorize.getOwner());
                throw new ServiceException("用户服务异常，请联系管理员");
            }
            // 收件人
            MemberDTO ownerUser = nodeUser.getData();
            if (ownerUser != null && StrUtil.isNotBlank(ownerUser.getEmail())) {
                Map<String, Object> params = new HashMap<>();
                params.put("lastName", ownerUser.getLastName());
                params.put("requestUserName", requestUser.getName());
                params.put("data", StringUtils.join(resourceAuthorize.getData(), ","));

                SendEmailDTO sendEmailDTO = new SendEmailDTO();
                // todo 改成真实的邮箱
                sendEmailDTO.setToEmail(ownerUser.getEmail());
                sendEmailDTO.setMailTemplate(MailTemplate.Request_Msg);
                sendEmailDTO.setParams(params);

                remoteNotificationService.sendEmail(sendEmailDTO, SecurityConstants.INNER);
            }
        }
    }

    private ResourceAuthorize createResourceAuthorize(String type, String typeNo, String description, MemberDTO requestUser) {
        DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(type);
        searchVO.setTypeNo(typeNo);
        // 这个分页查询接口要加上PageSize,否则默认是10
        searchVO.setPageSize(100000);
        R<List<RelatedDataDTO>> r = remoteDataService.findAllByTypeAndNo(searchVO, SecurityConstants.INNER);

        if (ObjectUtil.isNull(r) || ObjectUtil.isNull(r.getData())) {
            throw new ServiceException("索引服务异常，请联系管理员");
        }
        if (R.FAIL == r.getCode()) {
            throw new ServiceException(r.getMsg());
        }

        List<String> dataNos = r.getData().stream().map(RelatedDataDTO::getDatNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        Data data = dataRepository.findByDatNo(dataNos.get(0)).orElseThrow(() -> new ServiceException("No found the Data:" + dataNos.get(0)));
        R<MemberDTO> nodeUser = remoteMemberService.getOneMemberByMemberId(data.getCreator(), "FtpUser", SecurityConstants.INNER);
        if (ObjectUtil.isNull(nodeUser) || ObjectUtil.isNull(nodeUser.getData()) || R.isError(nodeUser)) {
            log.warn("saveRequestData出错,用户：{}数据未查询到", data.getCreator());
            throw new ServiceException("用户服务异常，请联系管理员");
        }
        boolean existed = resourceAuthorizeRepository.existsRequestRecord(requestUser.getId(), type, typeNo, RequestStatusEnum.request);

        if (existed) {
            log.info("The request has been sent, user:{}, type:{}, typeNo:{}", requestUser.getId(), type, typeNo);
            return null;
        }

        ResourceAuthorize resourceAuthorize = new ResourceAuthorize();
        resourceAuthorize.setOwner(data.getCreator());
        resourceAuthorize.setType(type);
        resourceAuthorize.setTypeId(typeNo);
        resourceAuthorize.setData(dataNos);
        resourceAuthorize.setStatus(RequestStatusEnum.request.getDesc());
        if (StrUtil.isNotBlank(description)) {
            resourceAuthorize.setDescription(description);
        }

        resourceAuthorize.setAuthorizeTo(requestUser.getId());
        resourceAuthorize.setApplyDate(new Date());

        return resourceAuthorize;

    }

    private MemberDTO validUsernameAndPassword(String username, String password) {
        // 校验密码
        R<MemberDTO> ftpUser = remoteMemberService.getMemberInfoByEmail(username, "FtpUser", SecurityConstants.INNER);
        MemberDTO requestUser = ftpUser.getData();

        if (requestUser == null) {
            throw new ServiceException(StrUtil.format("username or password error: {}", username));
        }
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        String pwd = SecureUtil.md5(password);
        if (!bCryptPasswordEncoder.matches(pwd, requestUser.getPassword())) {
            throw new ServiceException(StrUtil.format("username or password error: {}", username));
        }
        return requestUser;
    }
}
