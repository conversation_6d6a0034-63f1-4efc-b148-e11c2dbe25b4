package org.biosino.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.SubmissionDataTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.SubmissionDTO;
import org.biosino.system.repository.DataRepository;
import org.biosino.system.repository.SubmissionRepository;
import org.biosino.system.vo.SubmissionVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionService {

    private final RedisService redisService;
    private final DataRepository dataRepository;
    private final SubmissionRepository submissionRepository;
    private final RemoteMemberService remoteMemberService;

    private final static String PASS_SUBMISSION_TASK_KEY = "pass_submission_task_key_";

    public Page<SubmissionVO> list(SubmissionDTO dto) {

        if (StrUtil.isNotBlank(dto.getCreatorEmail())) {
            String memberId = getMemberInfoByEmail(dto.getCreatorEmail().trim());
            dto.setCreator(memberId);
        }

        PageImpl<Submission> page = submissionRepository.findAllPage(dto);

        List<String> creators = page.getContent().stream().map(Submission::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        return page.map(submission -> {
            SubmissionVO submissionVO = new SubmissionVO();
            BeanUtil.copyProperties(submission, submissionVO);
            submissionVO.setProjNum(submission.getProjNo() != null ? 1 : 0);
            submissionVO.setExpNum(getExpNum(submission));
            submissionVO.setSapNum(getSapNum(submission));
            submissionVO.setRunNum(getRunNum(submission));
            submissionVO.setAnalNum(getAnalNum(submission));
            submissionVO.setDataNum(getDataSet(submission).size());

            Submitter submitter = submission.getSubmitter();
            String submitterName = StrUtil.join(" ", submitter.getFirstName(), submitter.getLastName());
            submissionVO.setSubmitter(submitterName);

            submissionVO.setCreatorEmail(memberIdToEmailMap.get(submission.getCreator()));

            String lockKey = PASS_SUBMISSION_TASK_KEY + submission.getSubNo();
            Boolean inStorage = redisService.hasKey(lockKey);
            submissionVO.setProcessing(inStorage);

            return submissionVO;
        });
    }

    private Integer getRunNum(Submission submission) {
        Set<String> dataSet = getDataSet(submission);

        List<Data> dataList = dataRepository.findAllByDataNoIn(dataSet, SecurityUtils.getMemberId());
        long count = dataList.stream().filter(x -> x.getTempData() != null).map(x -> x.getTempData().getRunNo())
                .filter(StrUtil::isNotBlank).distinct().count();

        return Math.toIntExact(count);
    }

    private Set<String> getDataSet(Submission submission) {
        Set<String> dataSet = new HashSet<>();
        if (SubmissionDataTypeEnum.rawData.name().equals(submission.getDataType())) {
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                dataSet.addAll(submission.getRawDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                dataSet.addAll(submission.getRawDataMultipleNos());
            }
        }
        if (SubmissionDataTypeEnum.analysisData.name().equals(submission.getDataType())) {
            if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
                dataSet.addAll(submission.getAnalysisDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
                dataSet.addAll(submission.getAnalDataMultipleNos());
            }
        }
        return dataSet;
    }

    private Integer getExpNum(Submission submission) {
        int dataNum = submission.getExpSingleNo() != null ? 1 : 0;
        if (CollUtil.isNotEmpty(submission.getExpMultipleNos())) {
            dataNum += submission.getExpMultipleNos().size();
        }
        return dataNum;
    }

    private Integer getSapNum(Submission submission) {
        int sapNum = submission.getSapSingleNo() != null ? 1 : 0;
        List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
        if (CollUtil.isEmpty(sapMultipleData)) {
            return sapNum;
        }
        for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
            List<String> nos = sapMultipleDatum.getNos();
            if (CollUtil.isNotEmpty(nos)) {
                sapNum += nos.size();
            }
        }
        return sapNum;
    }

    private Integer getAnalNum(Submission submission) {
        int dataNum = submission.getAnalSingleNo() != null ? 1 : 0;
        if (CollUtil.isNotEmpty(submission.getAnalMultipleNos())) {
            dataNum += submission.getAnalMultipleNos().size();
        }
        return dataNum;
    }

    public String getMemberInfoByEmail(String email) {
        R<MemberDTO> rMember = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember) || rMember.getData() == null) {
            throw new ServiceException(StrUtil.format("User {} not found", email));
        }
        return rMember.getData().getId();
    }

    public Map<String, String> getMemberIdToEmailMap(List<String> memberIds) {
        Map<String, String> memberIdToEmailMap = new HashMap<>();
        for (String memberId : memberIds) {
            MemberDTO member = getOneMemberByMemberId(memberId);
            if (member != null) {
                memberIdToEmailMap.put(memberId, member.getEmail());
            }
        }
        return memberIdToEmailMap;
    }

    public MemberDTO getOneMemberByMemberId(String memberId) {
        R<MemberDTO> rMember = remoteMemberService.getOneMemberByMemberId(memberId, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember)) {
            throw new ServiceException(rMember.getMsg());
        }
        return rMember.getData();
    }

}
