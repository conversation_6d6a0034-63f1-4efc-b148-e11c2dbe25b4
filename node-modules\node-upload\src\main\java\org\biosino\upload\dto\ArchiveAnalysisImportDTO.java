package org.biosino.upload.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/1
 */
@Data
public class ArchiveAnalysisImportDTO {

    private Integer rowIndex;

    @JsonProperty("analysis_id")
    @JSONField(name = "analysis_id")
    private String analNo;

    @JsonProperty("analysis_name")
    @JSONField(name = "analysis_name")
    private String analName;

    @JsonProperty("data_id")
    @JSONField(name = "data_id")
    private String dataNo;

    @JsonProperty("file_name")
    @JSONField(name = "file_name")
    private String fileName;

    @JsonProperty("data_remark")
    @JSONField(name = "data_remark")
    private String dataRemark;

    @JsonProperty("file_type")
    @JSONField(name = "file_type")
    private String fileType;

    @JsonProperty("file_size")
    @JSONField(name = "file_size")
    private String fileSize;

    @JsonProperty("upload_date")
    @JSONField(name = "upload_date")
    private Date uploadDate;

    @JsonProperty("file_path")
    @JSONField(name = "file_path")
    private String filePath;
}
