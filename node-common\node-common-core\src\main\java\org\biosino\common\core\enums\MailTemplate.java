package org.biosino.common.core.enums;

public enum MailTemplate {

    Request_Msg("National Omics Data Encyclopedia-Request Information", "request.ftl"),
    Request_Result("National Omics Data Encyclopedia-Request Result", "request-result.ftl"),
    Share_To("National Omics Data Encyclopedia-Share Information", "share.ftl"),
    Review_To("National Omics Data Encyclopedia-Review Information", "review.ftl"),
    Data_Expire("National Omics Data Encyclopedia-Restrict Expiring Notice", "data-expire.ftl"),
    Security_Change("National Omics Data Encyclopedia-Restrict Security Changed Notice", "security-change.ftl"),
    Express_Data_Info("National Omics Data Encyclopedia-Express Data Send", "express-data-send.ftl"),
    UploadSubmission_Check("National Omics Data Encyclopedia-UploadSubmission Check", "uploadsubmission_check.ftl"),
    Audit_Pass("National Omics Data Submission Pass", "audit-pass.ftl"),
    Audit_Reject("National Omics Data Submission Rejected", "audit-reject.ftl"),
    Sync_Success("National Omics Data Sync Data From Other System Success", "sync-success.ftl"),
    Sync_Fail("National Omics Data Sync Data From Other System Fail", "sync-fail.ftl"),
    System_Error("National Omics Data System Error", "system-error.ftl");

    private String operatorName;
    private String templateName;

    MailTemplate(String operatorName, String templateName) {
        this.operatorName = operatorName;
        this.templateName = templateName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public String getTemplateName() {
        return templateName;
    }

}
