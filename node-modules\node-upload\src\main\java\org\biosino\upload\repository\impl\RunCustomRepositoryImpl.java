package org.biosino.upload.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.repository.RunCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.tempKey;

/**
 * <AUTHOR> Li
 * @date 2024/1/15
 */
@RequiredArgsConstructor
public class RunCustomRepositoryImpl implements RunCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public Run findByNo(String runNo) {
        Criteria criteria = Criteria.where("run_no").is(runNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.findOne(Query.query(criteria), Run.class);
    }


    @Override
    public Run findTempByNo(String runNo) {
        Criteria criteria = Criteria.where("temp_data.run_no").is(runNo);
        return mongoTemplate.findOne(Query.query(criteria), Run.class);
    }

    @Override
    public List<Run> findAllByCreator(Set<String> collection, String creator) {
        if (CollUtil.isEmpty(collection) || StrUtil.isBlank(creator)) {
            return Collections.emptyList();
        }
        Criteria criteria = Criteria.where("run_no").in(collection)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("creator").is(creator)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(Query.query(criteria), Run.class);
    }

    @Override
    public List<Run> findAllInitByNames(Set<String> collection, String creator) {
        if (CollUtil.isEmpty(collection) || StrUtil.isBlank(creator)) {
            return Collections.emptyList();
        }
        Criteria criteria = Criteria.where("name").in(collection)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("creator").is(creator)
                .and("audited").is(AuditEnum.init.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(Query.query(criteria), Run.class);
    }

    @Override
    public List<Run> findAllByCreator(String creator) {
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        if (StrUtil.isNotBlank(creator)) {
            criteria.andOperator(Criteria.where("creator").is(creator));
        }
        query.addCriteria(criteria);
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public Page<Run> findAllByPage(ArchivedSelectQueryDTO queryDTO) {
        Criteria criteria = Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("creator").is(queryDTO.getCreator());
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("run_no").regex(pattern),
                    Criteria.where("name").regex(pattern));
        }
        if (StrUtil.isNotBlank(queryDTO.getExpNo())) {
            criteria.and("exp_no").is(queryDTO.getExpNo());
        }
        if (StrUtil.isNotBlank(queryDTO.getSapNo())) {
            criteria.and("sap_no").is(queryDTO.getSapNo());
        }

        Query query = new Query();
        query.addCriteria(criteria);
        query.fields().include("name").include("run_no").include("exp_no").include("sap_no");
        query.with(Sort.by(Sort.Direction.DESC, "submission_date"));

        // 统计
        long count = mongoTemplate.count(query, Run.class);
        // 分页
        query.with(queryDTO.getPageable());
        // 查询
        List<Run> content = mongoTemplate.find(query, Run.class);
        // 精准查询并放到首位
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Criteria subCri = new Criteria();
            subCri.orOperator(Criteria.where("run_no").is(queryDTO.getName()), Criteria.where("name").is(queryDTO.getName()));
            Run run = mongoTemplate.findOne(Query.query(subCri), Run.class);
            if (run != null) {
                // 将list的首位替换成exp
                content.set(0, run);
            }
        }
        return new PageImpl<>(content, queryDTO.getPageable(), count);
    }

    @Override
    public List<Run> findAllNameByFieldAndNo(String field, String no) {
        Query query = Query.query(Criteria.where(tempKey(field)).is(no)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public List<Run> findAllNameByFieldAndNoIn(String field, Collection<String> nos) {
        Query query = Query.query(Criteria.where(tempKey(field)).in(nos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public boolean validateRunName(String creator, String name) {
        Query query = new Query();
        Criteria criteria = Criteria.where("creator").is(creator)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("audited").is(AuditEnum.init.name())
                .and("name").is(name)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return !mongoTemplate.exists(query, Run.class);
    }

    /**
     * 清除掉没有被任何Data关联使用的Run数据
     */
    @Override
    public void deletedUselessRun(String creator) {
        Set<String> tempRunIds = aggregateRunId(creator, "temp_data.run_no");
        Set<String> runIds = aggregateRunId(creator, "run_no");

        // 只删除没被使用的非正式的Run
        for (String tempRunId : tempRunIds) {
            if (!runIds.contains(tempRunId)) {
                Query deleteQuery = Query.query(Criteria.where("_id").is(tempRunId));
                mongoTemplate.remove(deleteQuery, Run.class);
            }
        }
    }

    private Set<String> aggregateRunId(String creator, String foreignField) {
        // 创建聚合管道操作
        AggregationOperation lookup = Aggregation.lookup("data", "run_no", foreignField, "data");
        AggregationOperation match;
        if ("run_no".equals(foreignField)) {
            match = Aggregation.match(Criteria.where("creator").is(creator).and("data").ne(CollUtil.newArrayList()).and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        } else {
            match = Aggregation.match(Criteria.where("creator").is(creator).and("data").is(CollUtil.newArrayList()).and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        }
        AggregationOperation project = Aggregation.project("_id");

        Aggregation aggregation = Aggregation.newAggregation(lookup, match, project);

        // 执行聚合查询
        AggregationResults<Run> results = mongoTemplate.aggregate(aggregation, Run.class, Run.class);

        // 获取未关联记录的列表
        List<Run> unlinkedRuns = results.getMappedResults();
        if (CollUtil.isNotEmpty(unlinkedRuns)) {
            return unlinkedRuns.stream().map(Run::getId).collect(Collectors.toSet());
        }
        return CollUtil.newHashSet();
    }

    @Override
    public List<Run> findTempByExpNoIn(Collection<String> expNos) {
        Criteria criteria = Criteria.where(tempKey("exp_no")).in(expNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(new Query(criteria), Run.class);
    }

    @Override
    public List<Run> findDetailBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where("sap_no").in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("run_no").include("sap_no");
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public void updateToDeleteAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return;
        }
        Query query = new Query(Criteria.where("run_no").in(runNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Update update = new Update().set("visible_status", VisibleStatusEnum.Deleted.name());
        // user_id不等于0代表是后台管理员来删除
        if (SecurityUtils.getUserId() != 0) {
            update.set("operator", SecurityUtils.getUserId().toString());
            update.set("operation_date", new Date());
        } else {
            update.set("update_date", new Date());
        }
        mongoTemplate.updateMulti(query, update, Run.class);
    }

    @Override
    public List<Run> findAllBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where("sap_no").in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(new Query(criteria), Run.class);
    }

    @Override
    public List<Run> findTempBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where(tempKey("sap_no")).in(sapNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        return mongoTemplate.find(new Query(criteria), Run.class);
    }

    @Override
    public Optional<Run> findFirstByRunNo(String runNo) {
        if (StrUtil.isBlank(runNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("run_no").is(runNo),
                Criteria.where("used_ids").in(runNo)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        Run run = mongoTemplate.findOne(query, Run.class);
        return Optional.ofNullable(run);
    }

    @Override
    public List<Run> findAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(Criteria.where("exp_no").in(expNos));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public List<Run> findAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("run_no").in(runNos),
                Criteria.where("used_ids").in(runNos)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Run.class);
    }

    @Override
    public void deleteByRunNoInAndAuditedInit(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return;
        }
        Query query = new Query(Criteria.where("run_no").in(runNos)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.init.name()));
        mongoTemplate.remove(query, Run.class);
    }
}
