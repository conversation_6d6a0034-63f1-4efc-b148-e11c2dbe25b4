package org.biosino.system.controller.fd;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.es.api.dto.FdQueryDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.service.fd.IFeatureDataWebService;
import org.biosino.system.vo.fd.FdDetailVO;
import org.biosino.system.vo.fd.FdHomeStatVO;
import org.biosino.system.vo.fd.FdOmicsListVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 特殊数据集 node前端查询与统计接口
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/fdWeb")
public class FeatureDataWebController extends BaseController {
    private final IFeatureDataWebService featureDataWebService;

    @GetMapping("/homeStat")
    public AjaxResult homeStat() {
        FdHomeStatVO vo = featureDataWebService.homeStat();
        return success(vo);
    }

    @GetMapping("/fdResDetail")
    public AjaxResult fdResDetail(FdQueryDTO search) {
        FdDetailVO vo = featureDataWebService.fdResDetail(search);
        return success(vo);
    }

    @GetMapping("/fdOmicsDetail")
    public AjaxResult fdOmicsDetail(FdQueryDTO search) {
        FdOmicsListVO vo = featureDataWebService.fdOmicsDetail(search);
        return success(vo);
    }

    @GetMapping("/hmdsStatDetail")
    public AjaxResult hmdsStatDetail() {
        List<FdHomeStatVO.FdStatItem> result = featureDataWebService.getBiomeCuratedStatData();
        return success(result);
    }

    @GetMapping("/hmdsSapList")
    public TableDataInfo hmdsSapList(MetadataQueryDTO queryDTO) {
        Page<FdDetailVO.SapInfo> result = featureDataWebService.hmdsSapList(queryDTO);
        return new TableDataInfo(result.getContent(), (int) result.getTotalElements());
    }

    /**
     * 特殊数据集刷新缓存
     */
    @GetMapping("/refreshFdCache")
    public R<Boolean> refreshFdCache() {
        try {
            featureDataWebService.refreshFdCache();
            return R.ok(true);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
