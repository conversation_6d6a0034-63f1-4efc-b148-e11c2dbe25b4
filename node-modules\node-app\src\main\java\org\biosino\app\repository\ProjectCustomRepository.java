package org.biosino.app.repository;

import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.common.mongo.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Repository
public interface ProjectCustomRepository {

    Project findByProjectNoWithPermission(String projectNo);

    Map<String, Object> findStatInfo(String projNo, boolean owner);

    /**
     * 浏览列表项目统计信息
     */
    BrowseStatDTO findBrowseStatInfo(String projNo);

    List<Project> findDetailByProjNoIn(Collection<String> projNos);

    Project findByNo(String projectNo);

    boolean existVisibleByNo(String projectNo);

    Page<Project> findProjectPage(UserCenterListSearchDTO queryDTO);

    void incHitNum(String projId);

    Optional<Project> findTopByProjectNo(String projectNo);


    List<Project> findAllByProjNoIn(Collection<String> projNosSet);

    List<Project> findHasTempDataByProjNoIn(Collection<String> projNos);
}
