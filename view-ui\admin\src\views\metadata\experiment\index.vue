<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="ID" prop="nos">
          <el-input
            v-model="queryParams.noStr"
            placeholder="Search for ID"
            style="width: 240px"
            type="textarea"
            :rows="1"
            clearable
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="Search for Name"
            style="width: 240px"
            @keyup.enter="getDataList"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Creator" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="Submitter Email" prop="submitterEmail">
          <el-input
            v-model="queryParams.submitterEmail"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="Submitter Organization" prop="submitterOrgName">
          <el-input
            v-model="queryParams.submitterOrgName"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item prop="sourceProject" label="Tag">
          <el-select
            v-model="queryParams.tags"
            clearable
            style="width: 220px"
            :teleported="false"
            multiple
          >
            <el-option
              v-for="dict in tag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Submission Date" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
          <el-button type="warning" icon="download" @click="exportId"
            >Export ID
          </el-button>
          <el-button
            v-hasPermi="['metadata:experiment:export']"
            type="info"
            icon="download"
            @click="exportData"
            >Export
          </el-button>
        </el-form-item>
      </el-form>
      <div class="radius-12 p-20 builder pos-relative mb-1 mt-1 bg-gray">
        <h3 class="text-secondary-color mb-1">Advanced Search Builder</h3>
        <div
          v-for="(it, index) in queryParams.advQueryList"
          :key="'bld_' + index"
          :class="[index === 0 ? '' : 'mt-1', 'd-flex', 'align-items-center']"
        >
          <div v-if="index === 0" class="w-30 mr-1"></div>
          <el-select
            v-else
            v-model="it.relation"
            :teleported="false"
            class="w-30 radius-12 m-2 mr-1"
          >
            <el-option :key="'rela' + index + 'AND'" label="AND" value="AND" />
            <el-option :key="'rela' + index + 'OR'" label="OR" value="OR" />
            <el-option
              :key="'rela' + index + 'AND NOT'"
              label="AND NOT"
              value="AND NOT"
            />
            <el-option
              :key="'rela' + index + 'OR NOT'"
              label="OR NOT"
              value="OR NOT"
            />
            <el-option
              :key="'rela' + index + 'AND LIKE'"
              label="AND LIKE"
              value="AND LIKE"
            />
            <el-option
              :key="'rela' + index + 'OR LIKE'"
              label="OR LIKE"
              value="OR LIKE"
            />
          </el-select>

          <!-- 字段下拉框 -->
          <el-input
            v-model="it.queryField"
            class="ml-1 w-40"
            placeholder="Please input field"
            clearable
          ></el-input>

          <!-- 值域框 start -->

          <el-input
            v-model="it.inputValue"
            class="ml-1"
            clearable
            placeholder="Please input value"
          ></el-input>
          <!-- 值域框 end -->

          <div style="width: 220px">
            <el-button
              :class="index === 0 ? 'icon-minus ml-1' : 'ml-1'"
              circle
              type="warning"
              plain
              @click="removeParam(index)"
            >
              <el-icon>
                <Minus />
              </el-icon>
            </el-button>

            <el-button
              v-if="index === queryParams.advQueryList.length - 1"
              type="primary"
              class="ml-2"
              circle
              plain
              @click="addParam"
            >
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
          </div>

          <!--<el-button class="btn-round-primary" round>
            <span class="integrity">Show Index List</span>
          </el-button>-->
        </div>
      </div>

      <div class="select-exptype bg-gray p-10-15 radius-8 mb-1">
        <el-form-item label="Experiment Type:" style="margin-bottom: 0">
          <el-checkbox-group
            v-model="queryParams.expTypes"
            @change="getDataList"
          >
            <el-checkbox
              v-for="item in expTypeOpts"
              :key="item"
              :value="item"
              :label="item"
              style="width: 150px"
              >{{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="expNo" label="ID" sortable min-width="115">
          <template #default="scope">
            <a
              class="text-primary"
              href="javascript:void(0)"
              @click="showDetail(scope.row)"
            >
              {{ scope.row.expNo }}
            </a>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="Name"
          min-width="120"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="expType"
          label="Experiment Type"
          min-width="160"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="sapTypes"
          label="Sample Type"
          min-width="160"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.sapTypes.join('; ') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="Description"
          min-width="160"
          show-overflow-tooltip
        />
        <el-table-column
          prop="sapNum"
          label="Number of samples"
          width="160"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag type="success">{{ scope.row.sapNum }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="submitter"
          label="Submitter"
          width="150"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="creatorEmail"
          label="Creator"
          width="165"
          show-overflow-tooltip
        />
        <el-table-column
          prop="visibleStatus"
          label="Status"
          min-width="130"
          sortable
        >
          <template #default="scope">
            <div class="d-flex align-items-center">
              <el-icon
                v-if="scope.row.visibleStatus === 'Accessible'"
                color="#67C23A"
              >
                <View />
              </el-icon>
              <el-icon v-else color="#F56C6C">
                <Hide />
              </el-icon>
              <div
                class="ml-05"
                :style="{
                  color:
                    scope.row.visibleStatus === 'Accessible'
                      ? '#67C23A'
                      : '#F56C6C',
                }"
              >
                {{ scope.row.visibleStatus }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Security" width="75">
          <template #default="scope">
            <div class="lock" :style="computedStyle(scope.row.dataCount)">
              <el-tooltip placement="right">
                <template #content>
                  <div>
                    <span>Public : </span>
                    <span>{{ scope.row.dataCount.Public }}</span>
                  </div>
                  <div>
                    <span>Private: </span>
                    <span>{{ scope.row.dataCount.Private }}</span>
                  </div>
                  <div>
                    <span>Restricted: </span>
                    <span>{{ scope.row.dataCount.Restricted }}</span>
                  </div>
                </template>
                <el-icon class="cursor-pointer">
                  <Lock v-if="scope.row.visibleStatus === 'Accessible'" />
                  <Unlock v-else />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="Submission Date"
          min-width="160"
          sortable
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          min-width="390"
          fixed="right"
          width="140"
        >
          <template #default="scope">
            <el-tooltip content="Change Creator">
              <svg-icon
                icon-class="creator"
                class-name="meta-svg"
                @click="preChangeCheck(scope.row.expNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Edit">
              <svg-icon
                icon-class="edits"
                class-name="meta-svg"
                @click="toEdit(scope.row.expNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Change Security">
              <svg-icon
                icon-class="security"
                class-name="meta-svg"
                @click="openSecurityDialog(scope.row.expNo)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Delete">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="preDeleteCheck(scope.row.expNo)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
    <Security ref="securityRef"></Security>

    <delete-log ref="deleteLog" curr-type="Experiment"></delete-log>
    <!-- change creator -->
    <change-creator-confirm
      v-model:new-creator="newCreator"
      v-model:show-dialog="showChangeDialog"
      :delete-check-result="deleteCheckResult"
      @change-creator-method="confirmChange"
    >
    </change-creator-confirm>
    <delete-confirm
      v-model:show-dialog="showDeleteDialog"
      :delete-check-result="deleteCheckResult"
      @delete-method="confirmDelete"
    ></delete-confirm>
  </div>
</template>
<script setup>
  import Security from '@/components/Security/index.vue';
  import DeleteLog from '@/views/metadata/common/DeleteLog.vue';
  import DeleteConfirm from '@/views/metadata/common/DeleteConfirm.vue';

  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import ChangeCreatorConfirm from '@/views/metadata/common/ChangeCreatorConfirm.vue';
  import { createAccessToken } from '@/api/login';
  import {
    deleteExpAll,
    expDeleteCheck,
    getAuditedExpType,
    listExperiment,
    updateExpCreator,
  } from '@/api/metadata/experiment';

  const router = useRouter();

  const { proxy } = getCurrentInstance();
  const { tag } = proxy.useDict('tag');

  let expTypeOpts = ref([]);

  onMounted(() => {
    getAuditedExpType().then(response => {
      expTypeOpts.value = response.data;
    });
    getDataList();
  });

  const openSecurityDialog = expNo => {
    proxy.$refs['securityRef'].init('experiment', expNo);
  };

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      noStr: '',
      nos: [],
      submitterEmail: '',
      submitterOrgName: '',
      tags: [],
      pageNum: 1,
      pageSize: 20,
      creatorEmail: '',
      expTypes: [],
      orderByColumn: 'createDate',
      isAsc: 'descending',
      advQueryList: [
        {
          relation: 'AND',
          queryField: '',
          inputValue: '',
        },
        {
          relation: 'AND',
          queryField: '',
          inputValue: '',
        },
      ],
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  // 监听 noStr 的变化，并同步更新 no
  watch(
    () => data.queryParams.noStr,
    newVal => {
      data.queryParams.nos = newVal ? newVal.split('\n') : [];
    },
  );

  const queryItem = {
    relation: 'AND',
    queryField: '',
    inputValue: '',
  };

  // 删除一行查询条件
  function removeParam(index) {
    if (queryParams.value.advQueryList.length <= 2) {
      return false;
    }
    queryParams.value.advQueryList.splice(index, 1);
  }

  // 添加一行查询条件
  function addParam() {
    if (queryParams.value.advQueryList.length >= 30) {
      return false;
    }
    queryParams.value.advQueryList.push({
      ...queryItem,
    });
  }

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listExperiment(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    dateRange.value = [];
    queryParams.value.noStr = '';
    proxy.resetForm('queryRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    // console.log(column);
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  const toEdit = no => {
    router.push({
      path: `/metadata/edit/experiment/${no}`,
    });
  };

  let showDeleteDialog = ref(false);
  let expNo = ref('');
  let newCreator = ref('');
  let deleteCheckResult = ref({});

  function preDeleteCheck(no) {
    proxy.$modal.loading('Opening Delete Dialog, please wait');
    expNo.value = no;
    expDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showDeleteDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  let showChangeDialog = ref(false);

  function preChangeCheck(no) {
    proxy.$modal.loading('Opening Change Creator Dialog, please wait');
    expNo.value = no;
    expDeleteCheck(no)
      .then(response => {
        if (response.data.errors && response.data.errors.length !== 0) {
          proxy.$refs['deleteLog'].openLog(response.data.errors);
          return;
        }
        deleteCheckResult.value = response.data;
        showChangeDialog.value = true;
        // 验证密码
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmDelete() {
    proxy.$modal.loading('deleting, please wait');
    deleteExpAll({
      expNo: expNo.value,
    })
      .then(() => {
        showDeleteDialog.value = false;
        proxy.$modal.alertSuccess('Delete successful');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function confirmChange() {
    proxy.$modal.loading('changing, please wait');
    updateExpCreator({
      expNo: expNo.value,
      newCreator: newCreator.value,
    })
      .then(() => {
        showChangeDialog.value = false;
        newCreator.value = '';
        proxy.$modal.alertSuccess('Change Creator successful');
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function showDetail(row) {
    proxy.$modal.loading('opening, please wait');
    // 预先生成access_token
    createAccessToken({ memberId: row.creator })
      .then(response => {
        const token = response.data;
        let href = `${import.meta.env.VITE_APP_WEB_URL}/experiment/detail/${
          row.expNo
        }?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const computedStyle = security => {
    return {
      '--my-bg-public': security.Public ? '#07bcb4' : '#DDDDDD',
      '--my-bg-private': security.Private ? '#3a78e8' : '#CCCCCC',
      '--my-bg-restricted': security.Restricted ? '#fe7f2b' : '#bebebe',
    };
  };

  function exportId() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/experiment/exportId',
      {
        query,
      },
      `Experiment_ID_${new Date().getTime()}.txt`,
    );
  }

  function exportData() {
    let query = JSON.stringify(
      proxy.addDateRange(queryParams.value, dateRange.value),
    );
    proxy.download(
      'system/metadata/experiment/exportData',
      {
        query,
      },
      `Experiment_${new Date().getTime()}.json`,
    );
  }
</script>

<style scoped lang="scss">
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }

  .lock {
    --bg-public: var(--my-bg-public, #07bcb4);
    --bg-private: var(--my-bg-private, #3a78e8);
    --bg-restricted: var(--my-bg-restricted, #fe7f2b);

    display: inline-flex;
    flex-grow: 1;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: 5px;
    padding: 0.7em 0.7em;
    border-radius: 50%;
    background: radial-gradient(white calc(52% - 1px), transparent 30%),
      conic-gradient(
        from 18deg,
        var(--bg-public) 33.3%,
        var(--bg-private) 0% 66.6%,
        var(--bg-restricted) 0%
      );
  }
</style>
<style lang="scss" scoped>
  .el-popper {
    max-width: 350px !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px !important;
  }
</style>
