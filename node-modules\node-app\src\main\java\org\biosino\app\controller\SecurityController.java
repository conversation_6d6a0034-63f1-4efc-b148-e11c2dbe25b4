package org.biosino.app.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.app.api.dto.UpdateSecurityDTO;
import org.biosino.app.service.SecurityService;
import org.biosino.app.vo.SecurityVO;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 更新数据安全等级
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/security")
@RequiredArgsConstructor
public class SecurityController extends BaseController {

    @Autowired
    private SecurityService securityService;

    /**
     * 获取当前权限的Data数据列表
     */
    @GetMapping("/getDataList")
    public AjaxResult getDataList(@Validated DataShareSearchVO searchDTO) {
        SecurityVO securityVO = securityService.getDataList(searchDTO);
        return AjaxResult.success(securityVO);
    }

    /**
     * 自动补全提示
     */
    @PostMapping("/searchSelectData")
    public AjaxResult searchSelectData(@RequestBody DataShareSearchVO searchDTO) {
        Set<String> set = securityService.searchSelectData(searchDTO);
        return AjaxResult.success(set);
    }

    /**
     * 更新安全等级
     */
    @PostMapping("/updateDataSecurity")
    @Log(system = SytemEnum.APP, businessType = BusinessType.UPDATE, module1 = "Data Security")
    public AjaxResult updateDataSecurity(@RequestBody @Validated UpdateSecurityDTO dto) {
        List<String> errMsg = securityService.updateDataSecurity(dto);
        return AjaxResult.success(errMsg);
    }

    /**
     * 校验数据的人类组学
     */
    @PostMapping("/verifyDataHumanType")
    public AjaxResult verifyDataHumanType(@RequestBody @Validated UpdateSecurityDTO dto) {
        Set<String> humans = securityService.verifyDataHumanType(dto);
        return AjaxResult.success(humans);
    }

    /**
     * 校验Security中metadata有无正在修改的数据
     */
    @GetMapping("/verifyMetadataEdit")
    public AjaxResult verifyMetadataEdit(@Validated DataShareSearchVO searchDTO) {
        return AjaxResult.success(securityService.verifyMetadataEdit(searchDTO));
    }

}
