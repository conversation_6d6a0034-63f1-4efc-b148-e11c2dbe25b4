package org.biosino.node.process.controller;

import cn.hutool.core.util.StrUtil;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.mongo.entity.DownloadLog;
import org.biosino.node.process.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/6/17
 */
@RestController
public class DataProcessController {

    private final static Map<String, Object> TASK_MAP = new ConcurrentHashMap<>();
    private static final Logger log = LoggerFactory.getLogger(DataProcessController.class);

    @Autowired
    private MetaDataTransService metaDataTransService;

    @Autowired
    private PublishService publishService;

    @Autowired
    private RequestAuthorizeService requestAuthorizeService;

    @Autowired
    private ShareService shareService;

    @Autowired
    private VisitorService visitorService;

    @Autowired
    private DownloadLogService downloadLogService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MemberLoginInfoService memberLoginInfoService;

    @Autowired
    private FastQCTaskService fastQCTaskService;

    @Autowired
    private SamToolTaskService samToolTaskService;

    @Autowired
    private SpecialResourceService specialResourceService;

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private TaxonomyService taxonomyService;

    /**
     * 老库->新库
     * 数据迁移脚本
     */
    @RequestMapping("/transfer/{type}")
    public AjaxResult transfer(@PathVariable String type) {
        String taskKey = "transfer_" + type;
        if (TASK_MAP.containsKey(taskKey)) {
            return AjaxResult.error("任务正在进行中");
        }
        try {
            TASK_MAP.put(taskKey, true);
            log.info("开始transfer:{}", type);
            if (StrUtil.equals(type, "project")) {
                metaDataTransService.transProject();
            } else if (StrUtil.equals(type, "experiment")) {
                metaDataTransService.transExperiment();
            } else if (StrUtil.equals(type, "sample")) {
                metaDataTransService.transSample();
            } else if (StrUtil.equals(type, "run")) {
                metaDataTransService.transRun();
            } else if (StrUtil.equals(type, "data")) {
                metaDataTransService.transData();
            } else if (StrUtil.equals(type, "analysis")) {
                metaDataTransService.transAnalysis();
            } else if (StrUtil.equals(type, "publish")) {
                publishService.process();
            } else if (StrUtil.equals(type, "requestAuthorize")) {
                requestAuthorizeService.process();
            } else if (StrUtil.equals(type, "share")) {
                shareService.process();
            } else if (StrUtil.equals(type, "downloadLog")) {
                mongoTemplate.dropCollection(DownloadLog.class);
                downloadLogService.downloadByExport(10000);
                downloadLogService.downloadByData(10000);
                downloadLogService.downloadByFtpData();
            } else if (StrUtil.equals(type, "visitor")) {
                visitorService.process(10000);
            } else if (StrUtil.equals(type, "review")) {
                reviewService.process();
            } else if (StrUtil.equals(type, "taxonomy")) {
                taxonomyService.process();
            } else {
                throw new ServiceException("不存在此操作");
            }
            log.info("结束transfer:{}", type);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        } finally {
            TASK_MAP.remove(taskKey);
        }
        return AjaxResult.success();
    }

    /**
     * 迁移完成后，再对数据库进行处理
     */
    @RequestMapping("/process/{type}")
    public AjaxResult process(@PathVariable String type) {
        String taskKey = "process_" + type;

        if (TASK_MAP.containsKey(taskKey)) {
            return AjaxResult.error("任务正在进行中");
        }
        try {
            TASK_MAP.put(taskKey, true);
            log.info("开始process: {}", type);
            if (StrUtil.equals(type, "memeberLoginInfo")) {
                memberLoginInfoService.generateIpCountry();
            } else if (StrUtil.equals(type, "fastQCTask")) {
                fastQCTaskService.process();
            } else if (StrUtil.equals(type, "samToolTask")) {
                samToolTaskService.process();
            } else {
                throw new ServiceException("不存在此操作");
            }
            log.info("结束process: {}", type);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        } finally {
            TASK_MAP.remove(taskKey);
        }
        return AjaxResult.success();
    }
}
