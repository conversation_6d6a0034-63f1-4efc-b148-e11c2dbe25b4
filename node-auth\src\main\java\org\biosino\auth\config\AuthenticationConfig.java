package org.biosino.auth.config;

import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.cas.CasProperties;
import org.biosino.auth.oauth2.OAuth2AuthenticationProvider;
import org.biosino.auth.service.CustomUserDetailsService;
import org.jasig.cas.client.validation.Cas20ServiceTicketValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.cas.ServiceProperties;
import org.springframework.security.cas.authentication.CasAssertionAuthenticationToken;
import org.springframework.security.cas.authentication.CasAuthenticationProvider;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;

import java.util.Arrays;

/**
 * 认证管理器配置
 * 单独配置 AuthenticationManager 以避免循环依赖
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class AuthenticationConfig {

    @Autowired
    private OAuth2AuthenticationProvider oAuth2AuthenticationProvider;

    @Autowired
    private CasProperties casProperties;

    /**
     * CAS Service Properties
     */
    @Bean
    public ServiceProperties serviceProperties() {
        ServiceProperties serviceProperties = new ServiceProperties();
        serviceProperties.setService(casProperties.getAppServerUrl() + casProperties.getAppLoginUrl());
        serviceProperties.setAuthenticateAllArtifacts(true);
        return serviceProperties;
    }

    /**
     * CAS Ticket Validator
     */
    @Bean
    public Cas20ServiceTicketValidator cas20ServiceTicketValidator() {
        return new Cas20ServiceTicketValidator(casProperties.getCasServerUrl());
    }

    /**
     * CAS User Details Service
     */
    @Bean
    public AuthenticationUserDetailsService<CasAssertionAuthenticationToken> customUserDetailsService() {
        return new CustomUserDetailsService();
    }

    /**
     * CAS Authentication Provider
     */
    @Bean
    public CasAuthenticationProvider casAuthenticationProvider() {
        CasAuthenticationProvider casAuthenticationProvider = new CasAuthenticationProvider();
        casAuthenticationProvider.setAuthenticationUserDetailsService(customUserDetailsService());
        casAuthenticationProvider.setServiceProperties(serviceProperties());
        casAuthenticationProvider.setTicketValidator(cas20ServiceTicketValidator());
        casAuthenticationProvider.setKey("casAuthenticationProviderKey");
        return casAuthenticationProvider;
    }

    /**
     * 创建自定义的 AuthenticationManager
     * 包含 OAuth2 和 CAS 两种认证提供者
     */
    @Bean
    @Primary
    public AuthenticationManager customAuthenticationManager() {
        log.info("创建自定义 AuthenticationManager，包含 OAuth2 和 CAS 认证提供者");
        return new ProviderManager(Arrays.asList(
                oAuth2AuthenticationProvider,
                casAuthenticationProvider()
        ));
    }
}
