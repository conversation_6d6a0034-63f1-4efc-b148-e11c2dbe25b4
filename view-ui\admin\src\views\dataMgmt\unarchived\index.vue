<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="searchFormRef" :model="queryParams" :inline="true">
        <el-form-item label="Data ID" prop="dataNos">
          <el-input
            v-model="queryParams.dataNos"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="File Name" prop="name">
          <el-input
            v-model="queryParams.name"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Creator" prop="creatorEmail">
          <el-input
            v-model="queryParams.creatorEmail"
            style="width: 220px"
            clearable
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Create Date" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :row-key="row => row.id"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="50"
          align="center"
        />
        <el-table-column prop="datNo" label="Data ID" width="120" sortable />
        <el-table-column
          prop="name"
          :show-overflow-tooltip="true"
          label="File Name"
          min-width="140"
          sortable
        />
        <el-table-column
          prop="sourcePath"
          label="Source Path"
          :show-overflow-tooltip="true"
          min-width="140"
          sortable
        />
        <el-table-column prop="creatorEmail" label="Creator" width="200" />
        <el-table-column prop="dataType" label="Data Type" width="120" />
        <el-table-column
          prop="uploadType"
          label="Upload Type"
          width="140"
          sortable
        />
        <el-table-column
          prop="createDate"
          label="Create Date"
          width="160"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="File Size" width="110" sortable>
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column label="Operate" width="90">
          <template #default="scope">
            <el-tooltip content="Delete">
              <svg-icon
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-show="total > 0"
        style="float: left"
        type="danger"
        icon="Delete"
        :disabled="dataNos.length === 0"
        @click="handleDelete"
        >Delete
      </el-button>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { deleteByDataNos, listUnarchived } from '@/api/metadata/data';
  import { parseTime } from '@/utils/ruoyi';

  onMounted(() => {
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      dataNos: '',
      name: '',
      creatorEmail: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function getDataList() {
    loading.value = true;
    listUnarchived(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** data删除 */
  let dataNos = ref([]);

  function handleSelectionChange(selection) {
    dataNos.value = selection.map(item => item.datNo);
  }

  function handleDelete(row) {
    let nos = row.datNo || dataNos.value;
    proxy.$modal
      .confirm('Are you sure to delete Data ID as "' + nos + '" data items?')
      .then(function () {
        return deleteByDataNos(nos);
      })
      .then(() => {
        getDataList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-select,
    .el-input {
      width: 330px;
    }
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }

  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
