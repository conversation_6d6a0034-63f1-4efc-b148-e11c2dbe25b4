package org.biosino.upload.repository;

import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Share;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface ShareCustomRepository {
    List<Share> findShareByShareTo(String... email);

    boolean existAccessableNo(String no, TypeInformation typeInfo, String memberEmail);

    Set<String> getAccessableNos(Collection<String> nos, TypeInformation typeInfo, String memberEmail);

    List<Share> findByTypeNoInAndCreator(TypeInformation typeInfo, List<String> typeNos, String creator);
}
