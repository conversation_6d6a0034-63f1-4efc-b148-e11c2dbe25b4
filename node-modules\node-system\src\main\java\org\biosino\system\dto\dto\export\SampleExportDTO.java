package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.system.dto.dto.SampleDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2025/1/10
 */
@Data
public class SampleExportDTO {

    @JSONField(name = "id", ordinal = 0)
    private String id;
    
    @JSONField(name = "sap_no", ordinal = 1)
    private String sapNo;

    @JSONField(name = "sub_no", ordinal = 2)
    private String subNo;

    @JSONField(name = "name", ordinal = 3)
    private String name;

    @JSONField(name = "description", ordinal = 4)
    private String description;

    @JSONField(name = "protocol", ordinal = 5)
    private String protocol;

    @JSONField(name = "tax_id", ordinal = 6)
    private String taxId;

    @JSONField(name = "organism", ordinal = 7)
    private String organism;

    @JSONField(name = "tissue", ordinal = 8)
    private String tissue;

    @JSONField(name = "audited", ordinal = 9)
    private String audited;

    @JSONField(name = "related_links", ordinal = 10)
    private List<String> relatedLinks;

    @JSONField(name = "creator", ordinal = 11)
    private String creator;

    @JSONField(name = "submission_date", ordinal = 12)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 13)
    private Date updateDate;

    @JSONField(name = "public_date", ordinal = 14)
    private Date publicDate;

    @JSONField(name = "other_ids", ordinal = 15)
    private List<OtherIds> otherIds;

    @JSONField(name = "operator", ordinal = 16)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 17)
    private Date operationDate;

    @JSONField(name = "hit_num", ordinal = 18)
    private Long hitNum;

    @JSONField(name = "export_num", ordinal = 19)
    private Long exportNum;

    @JSONField(name = "submitter", ordinal = 20)
    private Submitter submitter;

    @JSONField(name = "temp_data", ordinal = 21)
    private SampleDTO tempData;

    @JSONField(name = "attributes", ordinal = 21)
    private Map<String, String> attributes;

    @JSONField(name = "custom_attr", ordinal = 22)
    private Map<String, String> customAttr;

    @JSONField(name = "custom_attr_desc", ordinal = 23)
    private Map<String, String> customAttrDesc;

    @JSONField(name = "used_ids", ordinal = 24)
    private List<String> usedIds;

    @JSONField(name = "ownership", ordinal = 25)
    private String ownership;

    @JSONField(name = "source_project", ordinal = 26)
    private List<String> sourceProject;

    @JSONField(name = "subject_type", ordinal = 27)
    private String subjectType;

    @JSONField(name = "visible_status", ordinal = 28)
    private String visibleStatus;
}
