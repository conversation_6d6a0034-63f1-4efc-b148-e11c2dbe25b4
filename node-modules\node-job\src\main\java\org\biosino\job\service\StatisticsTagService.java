package org.biosino.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.statistics.StatisticsTag;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.biosino.job.repository.*;
import org.biosino.job.repository.util.RepositoryUtil;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/7/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsTagService {

    private final StatisticsTagRepository statisticsTagRepository;

    private final ExperimentRepository experimentRepository;

    private final RunRepository runRepository;

    private final FastQCTaskRepository fastQCTaskRepository;

    private final DataRepository dataRepository;

    private final MongoTemplate mongoTemplate;

    private final RemoteDataService remoteDataService;

    /**
     * 查询所有tag
     */
    public List<String> findAllTag(Class<?> clazz) {
        Query query = new Query(Criteria.where("source_project").ne("").not().size(0));

        List<String> tag = mongoTemplate.findDistinct(query, "source_project", clazz, String.class);

        return tag;
    }

    /**
     * 根据状态和tag统计数量
     */
    public Long countByStatusInAndSourceProjectIn(List<String> status, List<String> sourceProjectNos, Class clazz) {
        Criteria criteria = new Criteria().andOperator(Criteria.where("visible_status").in(status),
                Criteria.where("source_project").in(sourceProjectNos),
                RepositoryUtil.auditedBase());
        Query query = new Query(criteria);
        return mongoTemplate.count(query, clazz);
    }


    public void generate(String month) {
        // 查询所有的tag
        List<String> projTags = findAllTag(Project.class);
        List<String> sapTags = findAllTag(Sample.class);
        List<String> expTags = findAllTag(Experiment.class);
        List<String> analTags = findAllTag(Analysis.class);

        Set<String> allTags = new LinkedHashSet<>();
        allTags.addAll(projTags);
        allTags.addAll(sapTags);
        allTags.addAll(expTags);
        allTags.addAll(analTags);

        List<StatisticsTag> saveList = new ArrayList<>();
        // 删除当前月份数据
        RepositoryUtil.deleteByMonth(mongoTemplate, month, StatisticsTag.class);

        for (String tag : allTags) {
            StatisticsTag statisticsTag = new StatisticsTag();
            // 保存基本信息
            statisticsTag.setTag(tag);
            statisticsTag.setMonth(month);

            // 统计project
            statisticsTag.setProj(countByStatusInAndSourceProjectIn(VisibleStatusEnum.includeExistsVisibleStatus(), CollUtil.newArrayList(tag), Project.class));
            statisticsTag.setProjAccessible(countByStatusInAndSourceProjectIn(CollUtil.newArrayList(VisibleStatusEnum.Accessible.name()), CollUtil.newArrayList(tag), Project.class));

            // 统计experiment
            statisticsTag.setExp(countByStatusInAndSourceProjectIn(VisibleStatusEnum.includeExistsVisibleStatus(), CollUtil.newArrayList(tag), Experiment.class));
            statisticsTag.setExpAccessible(countByStatusInAndSourceProjectIn(CollUtil.newArrayList(VisibleStatusEnum.Accessible.name()), CollUtil.newArrayList(tag), Experiment.class));

            // 统计sample
            statisticsTag.setSap(countByStatusInAndSourceProjectIn(VisibleStatusEnum.includeExistsVisibleStatus(), CollUtil.newArrayList(tag), Sample.class));
            statisticsTag.setSapAccessible(countByStatusInAndSourceProjectIn(CollUtil.newArrayList(VisibleStatusEnum.Accessible.name()), CollUtil.newArrayList(tag), Sample.class));

            // 统计run
            statisticsTag.setRun(countByStatusInAndSourceProjectIn(VisibleStatusEnum.includeExistsVisibleStatus(), CollUtil.newArrayList(tag), Run.class));
            statisticsTag.setRunAccessible(countByStatusInAndSourceProjectIn(CollUtil.newArrayList(VisibleStatusEnum.Accessible.name()), CollUtil.newArrayList(tag), Run.class));

            // 统计analysis
            statisticsTag.setAnal(countByStatusInAndSourceProjectIn(VisibleStatusEnum.includeExistsVisibleStatus(), CollUtil.newArrayList(tag), Analysis.class));
            statisticsTag.setAnalAccessible(countByStatusInAndSourceProjectIn(CollUtil.newArrayList(VisibleStatusEnum.Accessible.name()), CollUtil.newArrayList(tag), Analysis.class));

            // 存储记录
            Map<String, Long> dataFileSizeMap = new HashMap<>();
            Map<String, Long> dataPublicFileSizeMap = new HashMap<>();
            Map<String, Double> dataSumLenMap = new HashMap<>();
            Map<String, Double> dataQ20Map = new HashMap<>();
            Map<String, Double> dataQ30Map = new HashMap<>();

            // 分页查询条件
            List<Criteria> conditions = new ArrayList<>();
            conditions.add(Criteria.where("source_project").in(tag));
            conditions.add(RepositoryUtil.base());
            Query query = new Query(new Criteria().andOperator(conditions));
            // 从project往下找data
            MongoPagingIterator<Project> projectIterator = new MongoPagingIterator<>(mongoTemplate, Project.class, query, 500);
            while (projectIterator.hasNext()) {
                List<Project> pageContent = projectIterator.next();
                // 获取所有的projectNo
                List<String> projNos = pageContent.stream().map(Project::getProjectNo).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(projNos)) {
                    break;
                }

                RelatedEsSearchVO searchVO = new RelatedEsSearchVO();
                searchVO.setProjNos(projNos);
                collectDataStatInfoFromEs(searchVO, dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map);

                // todo 以下代码注释，改用走es统计
                //     // 找到Project ID下的expNo
                //     List<String> expNos = experimentRepository.findExpNosByProjNoIn(projNos);
                //
                //     // 找到experiment下的runNo
                //     List<String> runNos = runRepository.findRunNosByExpNoIn(expNos);
                //
                //     // 找到下面所有的data
                //     collectDataStatInfo(dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map, runNos);
            }

            // 从experiment往下找data
            MongoPagingIterator<Experiment> experimentIterator = new MongoPagingIterator<>(mongoTemplate, Experiment.class, query, 1000);
            while (experimentIterator.hasNext()) {
                List<Experiment> pageContent = experimentIterator.next();
                List<String> expNos = pageContent.stream().map(Experiment::getExpNo).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(expNos)) {
                    break;
                }

                RelatedEsSearchVO searchVO = new RelatedEsSearchVO();
                searchVO.setExpNos(expNos);
                collectDataStatInfoFromEs(searchVO, dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map);

                // todo 以下代码注释，改用走es统计
                // // 找到experiment下的runNo
                // List<String> runNos = runRepository.findRunNosByExpNoIn(expNos);
                //
                // // 找到下面所有的data
                // collectDataStatInfo(dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map, runNos);
            }

            // 从sample往下找data
            MongoPagingIterator<Sample> sampleIterator = new MongoPagingIterator<>(mongoTemplate, Sample.class, query, 2000);
            while (sampleIterator.hasNext()) {
                List<Sample> pageContent = sampleIterator.next();
                List<String> sapNos = pageContent.stream().map(Sample::getSapNo).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(sapNos)) {
                    break;
                }

                RelatedEsSearchVO searchVO = new RelatedEsSearchVO();
                searchVO.setSapNos(sapNos);
                collectDataStatInfoFromEs(searchVO, dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map);

                // todo 以下代码注释，改用走es统计
                // // 找到sap下的runNo
                // List<String> runNos = runRepository.findRunNosBySapNoIn(sapNos);
                //
                // // 找到下面所有的data
                // collectDataStatInfo(dataFileSizeMap, dataPublicFileSizeMap, dataSumLenMap, dataQ20Map, dataQ30Map, runNos);
            }

            // 从analysis往下找data
            MongoPagingIterator<Analysis> analysisIterator = new MongoPagingIterator<>(mongoTemplate, Analysis.class, query, 500);
            while (analysisIterator.hasNext()) {
                List<Analysis> pageContent = analysisIterator.next();
                List<String> analNos = pageContent.stream().map(Analysis::getAnalysisNo).distinct().collect(Collectors.toList());

                List<Data> dataList = dataRepository.findAllByAnalNoIn(analNos);

                for (Data data : dataList) {
                    if (data.getFileSize() != null) {
                        dataFileSizeMap.put(data.getDatNo(), data.getFileSize());
                        if (StrUtil.equals(data.getSecurity(), SecurityEnum._public.getDesc())) {
                            dataPublicFileSizeMap.put(data.getDatNo(), data.getFileSize());
                        }
                    }
                }

            }

            statisticsTag.setFastqcDataFileNum((long) dataSumLenMap.size());
            statisticsTag.setFastqcDataSumLenSum(dataSumLenMap.values().stream().mapToDouble(Double::doubleValue).sum());
            statisticsTag.setFastqcDataQ20Avg(dataQ20Map.values().stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
            statisticsTag.setFastqcDataQ30Avg(dataQ30Map.values().stream().mapToDouble(Double::doubleValue).average().orElse(0.0));

            Map<String, Double> publicDataSumLenMap = MapUtil.getAny(dataSumLenMap, ArrayUtil.toArray(dataPublicFileSizeMap.keySet(), String.class));
            Map<String, Double> publicDataQ20Map = MapUtil.getAny(dataQ20Map, ArrayUtil.toArray(dataPublicFileSizeMap.keySet(), String.class));
            Map<String, Double> publicDataQ30Map = MapUtil.getAny(dataQ30Map, ArrayUtil.toArray(dataPublicFileSizeMap.keySet(), String.class));

            statisticsTag.setPublicFastqcDataFileNum((long) publicDataSumLenMap.size());
            statisticsTag.setPublicFastqcDataSumLenSum(publicDataSumLenMap.values().stream().mapToDouble(Double::doubleValue).sum());
            statisticsTag.setPublicFastqcDataQ20Avg(publicDataQ20Map.values().stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
            statisticsTag.setPublicFastqcDataQ30Avg(publicDataQ30Map.values().stream().mapToDouble(Double::doubleValue).average().orElse(0.0));

            statisticsTag.setDataFileNum((long) dataFileSizeMap.size());
            statisticsTag.setDataFileSize(dataFileSizeMap.values().stream().mapToLong(Long::longValue).sum());
            statisticsTag.setDataPublicFileNum((long) dataPublicFileSizeMap.size());
            statisticsTag.setDataPublicFileSize(dataPublicFileSizeMap.values().stream().mapToLong(Long::longValue).sum());
            saveList.add(statisticsTag);

        }
        statisticsTagRepository.saveAll(saveList);
    }

    private void collectDataStatInfoFromEs(RelatedEsSearchVO searchVO, Map<String, Long> dataFileSizeMap, Map<String, Long> dataPublicFileSizeMap, Map<String, Double> dataSumLenMap, Map<String, Double> dataQ20Map, Map<String, Double> dataQ30Map) {
        for (int i = 0; i < 800 * 10000; i++) {
            // pageNum从1开始
            searchVO.setPageNum(i + 1);
            searchVO.setPageSize(1000);
            EsPageInfo<RelatedDataDTO> pageInfo = remoteDataService.findRelatedDataPage(searchVO, SecurityConstants.INNER).getData();
            if (CollUtil.isEmpty(pageInfo.getList())) {
                break;
            }
            List<RelatedDataDTO> list = pageInfo.getList();
            List<String> dataNos = list.stream().map(RelatedDataDTO::getDatNo).collect(Collectors.toList());
            List<FastQCTask> fastqcList = fastQCTaskRepository.findByDataNoIn(dataNos);
            Map<String, FastQCTask> dataNoToFastQcTaskMap = fastqcList.stream().collect(Collectors.toMap(FastQCTask::getDataNo, Function.identity(), (existingValue, newValue) -> existingValue));
            for (RelatedDataDTO data : list) {
                if (data.getFileSize() != null) {
                    dataFileSizeMap.put(data.getDatNo(), data.getFileSize());
                    if (StrUtil.equals(data.getSecurity(), SecurityEnum._public.getDesc())) {
                        dataPublicFileSizeMap.put(data.getDatNo(), data.getFileSize());
                    }
                }
                if (dataNoToFastQcTaskMap.containsKey(data.getDatNo())) {
                    FastQCTask fastQCTask = dataNoToFastQcTaskMap.get(data.getDatNo());
                    if (fastQCTask.getSeqkitResult() != null) {
                        if (fastQCTask.getSeqkitResult().getSumLen() != null) {
                            dataSumLenMap.put(data.getDatNo(), fastQCTask.getSeqkitResult().getSumLen());
                        }
                        if (fastQCTask.getSeqkitResult().getQ20() != null) {
                            dataQ20Map.put(data.getDatNo(), fastQCTask.getSeqkitResult().getQ20());
                        }
                        if (fastQCTask.getSeqkitResult().getQ30() != null) {
                            dataQ30Map.put(data.getDatNo(), fastQCTask.getSeqkitResult().getQ30());
                        }
                    }
                }
            }
        }
    }

    private void collectDataStatInfo(Map<String, Long> dataFileSizeMap,
                                     Map<String, Long> dataPublicFileSizeMap,
                                     Map<String, Double> dataSumLenMap,
                                     Map<String, Double> dataQ20Map,
                                     Map<String, Double> dataQ30Map,
                                     List<String> runNos) {
        // 找到下面所有的data
        List<Data> dataList = dataRepository.findAllByRunNoIn(runNos);
        List<String> dataNos = dataList.stream().map(Data::getDatNo).collect(Collectors.toList());

        List<FastQCTask> fastqcList = fastQCTaskRepository.findByDataNoIn(dataNos);

        Map<String, FastQCTask> dataNoToFastQcTaskMap = fastqcList.stream().collect(Collectors.toMap(FastQCTask::getDataNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Data data : dataList) {
            if (data.getFileSize() != null) {
                dataFileSizeMap.put(data.getDatNo(), data.getFileSize());
                if (StrUtil.equals(data.getSecurity(), SecurityEnum._public.getDesc())) {
                    dataPublicFileSizeMap.put(data.getDatNo(), data.getFileSize());
                }
            }
            if (dataNoToFastQcTaskMap.containsKey(data.getDatNo())) {
                FastQCTask fastQCTask = dataNoToFastQcTaskMap.get(data.getDatNo());
                if (fastQCTask.getSeqkitResult() != null) {
                    if (fastQCTask.getSeqkitResult().getSumLen() != null) {
                        dataSumLenMap.put(data.getDatNo(), fastQCTask.getSeqkitResult().getSumLen());
                    }
                    if (fastQCTask.getSeqkitResult().getQ20() != null) {
                        dataQ20Map.put(data.getDatNo(), fastQCTask.getSeqkitResult().getQ20());
                    }
                    if (fastQCTask.getSeqkitResult().getQ30() != null) {
                        dataQ30Map.put(data.getDatNo(), fastQCTask.getSeqkitResult().getQ30());
                    }
                }
            }

        }
    }

}
