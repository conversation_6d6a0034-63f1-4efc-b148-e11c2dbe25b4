package org.biosino.system.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.DownloadLog;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.repository.DownloadLogCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@RequiredArgsConstructor
public class DownloadLogCustomRepositoryImpl implements DownloadLogCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<DownloadLog> findLogPage(LogQueryDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        if (StrUtil.isNotBlank(queryDTO.getUserId())) {
            criteriaList.add(Criteria.where("member_id").is(queryDTO.getUserId()));
        } else if (StrUtil.isNotBlank(queryDTO.getUserEmail())) {
            criteriaList.add(Criteria.where("member_id").is(queryDTO.getUserEmail()));
        }

        if (StrUtil.isNotBlank(queryDTO.getTypeNo())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getTypeNo() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("type_no").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getDownloadType())) {
            criteriaList.add(Criteria.where("download_type").is(queryDTO.getDownloadType()));
        }

        if (StrUtil.isNotBlank(queryDTO.getIp())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getIp() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(Criteria.where("ip").regex(pattern));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        Query query;
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        } else {
            query = new Query();
        }

        // 查询数据量
        long total = mongoTemplate.count(query, DownloadLog.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<DownloadLog> content = mongoTemplate.find(query, DownloadLog.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }
}
