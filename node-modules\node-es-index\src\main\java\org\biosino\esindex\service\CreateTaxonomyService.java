package org.biosino.esindex.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.common.es.entity.TaxonomyEs;
import org.biosino.common.es.mapper.TaxonomyMapper;
import org.biosino.common.mongo.entity.TaxonomyNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import static org.biosino.common.es.entity.TaxonomyEs.TAXONOMY_INDEX_NAME;

@Slf4j
@Service
public class CreateTaxonomyService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private TaxonomyMapper taxonomyMapper;

    /**
     * 删除并重新创建索引库
     */
    private void reCreateIndex() {
        final Boolean exists = taxonomyMapper.existsIndex(TAXONOMY_INDEX_NAME);
        if (Boolean.TRUE.equals(exists)) {
            taxonomyMapper.deleteIndex(TAXONOMY_INDEX_NAME);
            ThreadUtil.safeSleep(5000);
        }
        taxonomyMapper.createIndex();
        ThreadUtil.safeSleep(2000);
    }

    /**
     * 创建Taxonomy索引
     */
    public void createTaxonomy() {

        // 删除原有索引数据库
//        taxonomyMapper.deleteIndex(ESDataType.Taxonomy.esIndex);
        // 创建索引
//        taxonomyMapper.createIndex();
        reCreateIndex();

        final Query query = new Query();
        // query.fields().include("tax_id", "names_scientific_name");
        final Class<TaxonomyNode> taxonomyNodeClass = getCls();
//        long total = taxonomyMongoTemplate.count(query, taxonomyNodeClass);
        // 单次读取数量
        final int size = 10000;
        final Sort sort = Sort.by(Sort.Direction.ASC, "_id");
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            query.with(PageRequest.of(i, size, sort));
            final List<TaxonomyNode> taxonomyList = mongoTemplate.find(query, taxonomyNodeClass);
            if (CollUtil.isEmpty(taxonomyList)) {
                break;
            }

            final List<TaxonomyEs> taxonomyEsList = new ArrayList<>();
            for (TaxonomyNode taxonomy : taxonomyList) {
                final String name = taxonomy.getScientificName();
                final String taxId = taxonomy.getTaxId();
                if (StrUtil.isBlank(taxId) || StrUtil.isBlank(name)) {
                    continue;
                }

                final Set<String> names = new TreeSet<>();
                names.add(name);
                final List<String> alias = taxonomy.getAlias();
                if (CollUtil.isNotEmpty(alias)) {
                    names.addAll(alias);
                }

                final String lineage = taxonomy.getLineage();
                for (String n : names) {
                    final TaxonomyEs taxonomyEs = new TaxonomyEs();
                    taxonomyEs.setTaxId(taxId);
                    taxonomyEs.setScientificName(n);
                    taxonomyEs.setLineage(lineage);
                    taxonomyEs.setSid(IdUtils.snowflakeId());
                    taxonomyEsList.add(taxonomyEs);
                }
            }

            taxonomyMapper.insertBatch(taxonomyEsList);
            log.warn("Taxonomy index synchronized：{}", taxonomyEsList.size());
        }
        log.warn("Taxonomy index synchronization completed");
    }

    private Class<TaxonomyNode> getCls() {
        return TaxonomyNode.class;
    }

}

