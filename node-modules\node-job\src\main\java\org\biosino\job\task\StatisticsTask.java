package org.biosino.job.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.job.service.*;
import org.biosino.job.util.MonthUtil;
import org.springframework.stereotype.Component;

/**
 * 统计定时任务调度
 *
 * <AUTHOR>
 */
@Slf4j
@Component("statisticsTask")
@RequiredArgsConstructor
public class StatisticsTask {
    private final StatisticsDataFlowService statisticsDataFlowService;
    private final StatisticsSubmissionService submissionService;
    private final StatisticsVisitsService statisticsVisitsService;
    private final StatisticsPublishService statisticsPublishService;
    private final StatisticsDownloadService statisticsDownloadService;
    private final StatisticsDataVolumeService statisticsDataVolumeService;
    private final StatisticsTempDataService statisticsTempDataService;
    private final StatisticsShareService statisticsShareService;
    private final StatisticsExpService statisticsExpService;
    private final StatisticsSampleService statisticsSampleService;
    private final StatisticsSampleExpService statisticsSampleExpService;
    private final StatisticsPrjMultiExpService statisticsPrjMultiExpService;
    private final StatisticsSapMultiExpService statisticsSapMultiExpService;
    private final StatisticsTagService statisticsTagService;
    private final StatisticsDataTypeService statisticsDataTypeService;
    private final StatisticsNodeMemberService statisticsNodeMemberService;
    private final StatisticsUserOrgService statisticsUserOrgService;
    private final StatisticsSubMethodService statisticsSubMethodService;
    private final StatisticsExpSapTypeAttrService statisticsExpSapTypeAttrService;
    private final StatisticsAttrCompletenessService statisticsAttrCompletenessService;

    /**
     * 统计 访问量 和 下载量 (每日凌晨3点)
     */
    public void visitorAndDownload() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsVisitsService.calculateByMonth(currentMonth);
        statisticsDownloadService.calculateByMonth(currentMonth);
    }

    /**
     * 统计 Data-Volume (每日凌晨3点10)
     */
    public void dataVolume() {
        // 计算上一个月份
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsDataVolumeService.generateDataVolume(currentMonth);
        statisticsDataFlowService.generate(currentMonth);
    }

    /**
     * 统计文献 (每日凌晨3点20)
     */
    public void publish() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsPublishService.generate(currentMonth);
    }

    /**
     * 统计 数据汇交 (每日凌晨3点30)
     */
    public void submission() {
        // 计算上一个月份
        String currentMonth = MonthUtil.getCurrentMonth();
        submissionService.generate(currentMonth);
    }

    /**
     * 统计 临时数据 (每日凌晨3点50) 更新方式：存量更新
     */
    public void templateData() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsTempDataService.generate(currentMonth);
    }

    /**
     * 统计 数据共享 (每日凌晨4点) 更新方式：增量更新
     */
    public void share() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsShareService.generate(currentMonth);
    }

    /**
     * 组学统计 每日凌晨4点20
     */
    public void expStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsExpService.generate(currentMonth);
    }

    /**
     * 样本基础统计 每日凌晨4点30
     */
    public void sampleStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsSampleService.generate(currentMonth);
    }

    /**
     * 样本-实验组合统计 每日凌晨4点40
     */
    public void sampleExpStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsSampleExpService.generate(currentMonth);
    }

    /**
     * 标签数据统计 每日凌晨4点50
     */
    public void tagStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        log.info("generate statistics tag: {}", currentMonth);
        statisticsTagService.generate(currentMonth);
        log.info("finish statistics tag");
    }

    /**
     * 多组学项目统计, 每日凌晨4点55
     */
    public void prjMultiExpStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsPrjMultiExpService.generate(currentMonth);
    }

    /**
     * 多组学样本统计, 每日凌晨5点
     */
    public void sapMultiExpStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsSapMultiExpService.generate(currentMonth);
    }

    /**
     * 数据类型统计,每日凌晨5点10
     */
    public void dataTypeStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsDataTypeService.generate(currentMonth);
    }

    /**
     * 用户组织统计,每日凌晨5点20
     */
    public void userOrgStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsUserOrgService.generate(currentMonth);
    }

    /**
     * 用户数统计
     */
    public void nodeMemberStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsNodeMemberService.generate(currentMonth);
    }

    /**
     * 统计Experiment和Sample，模板属性字段填写的情况
     */
    public void expSampleTypeStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsExpSapTypeAttrService.generate(currentMonth);
    }

    /**
     * 统计Experiment和Sample，属性填写的完整度
     */
    public void expSampleTypeCompletenessStat() {
        String currentMonth = MonthUtil.getCurrentMonth();
        statisticsAttrCompletenessService.generate(currentMonth);
    }

    /**
     * 全量初始化刷新统计
     */
    public void initStat() {
        statisticsDataVolumeService.calculateAllYears();
        log.warn("Data Volume 统计");

        statisticsDataFlowService.calculateAllYears();
        log.warn("Data Flow统计");

        statisticsPublishService.calculateAllYears();
        log.warn("文献统计（前台）");

        submissionService.calculateAllYears();
        log.warn("数据汇交统计 —— submission统计");

        statisticsSubMethodService.calculateAllYears();
        log.warn("数据提交方式统计");

        statisticsTempDataService.calculateAllYears();
        log.warn("临时数据下载统计");

        statisticsExpService.calculateAllYears();
        log.warn("组学统计(ES)");

        statisticsSampleService.calculateAllYears();
        log.warn("样本基础统计");

        statisticsShareService.calculateAllYears();
        log.warn("数据共享统计");

        statisticsSampleExpService.calculateAllYears();
        log.warn("样本-实验组合统计(ES)");

        statisticsPrjMultiExpService.calculateAllYears();
        log.warn("多组学项目统计(ES)");

        statisticsSapMultiExpService.calculateAllYears();
        log.warn("多组学样本统计(ES)");

        statisticsDataTypeService.calculateAllYears();
        log.warn("数据类型统计");

        statisticsVisitsService.calculateStatisticsForYears();
        log.warn("数据访问量统计");

        statisticsDownloadService.calculateStatisticsForYears();
        log.warn("数据下载和下载量统计");

        statisticsTagService.generate(MonthUtil.getCurrentMonth());
        log.warn("标签数据统计");

        statisticsNodeMemberService.calculateAllYears();
        log.warn("Node Member 用户统计");

        statisticsUserOrgService.calculateAllYears();
        log.warn("用户组织统计");
    }
}
