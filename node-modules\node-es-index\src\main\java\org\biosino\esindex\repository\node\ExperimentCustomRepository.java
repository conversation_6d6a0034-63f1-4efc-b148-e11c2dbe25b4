package org.biosino.esindex.repository.node;

import org.biosino.common.mongo.entity.Experiment;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


@Repository
public interface ExperimentCustomRepository {
    List<String> findNosByTypes(Collection<String> types, Pageable pageable);

    List<Experiment> findAllByExpNoIn(Collection<String> expNos);
}
