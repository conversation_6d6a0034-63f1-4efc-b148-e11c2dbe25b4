package org.biosino.task.repository;

import org.biosino.common.mongo.entity.SamToolTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SamToolTaskRepository extends MongoRepository<SamToolTask, String>, SamToolTaskCustomRepository {
    Optional<SamToolTask> findFirstByDataNo(String dataNo);

    List<SamToolTask> findByDataNoIn(List<String> dataNos);
} 