package org.biosino.upload.repository;

import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface ResourceAuthorizeCustomRepository {

    List<String> searchAuthorizeDataList(String memberId);

    boolean existAccessableDataNo(String dataNo, String memberId);

    Set<String> getAccessableDataNos(Collection<String> dataNo, String memberId);

    List<ResourceAuthorize> findByDataNoInAndCreator(Collection<String> dataNos, String memberId);
}
