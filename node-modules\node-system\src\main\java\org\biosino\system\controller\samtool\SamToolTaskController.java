package org.biosino.system.controller.samtool;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.security.annotation.RequiresPermissions;
import org.biosino.system.dto.dto.SamToolTaskQueryDTO;
import org.biosino.system.service.samtool.SamToolTaskService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/5/22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/samToolTask")
public class SamToolTaskController {

    private final SamToolTaskService samToolTaskService;

    /**
     * 任务列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody SamToolTaskQueryDTO queryDTO) {
        Page<SamToolTask> page = samToolTaskService.list(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 获取任务详细信息
     */
    @RequestMapping("/getByNo/{no}")
    public AjaxResult getByNo(@PathVariable String no) {
        SamToolTask samToolTask = samToolTaskService.findByTaskNo(no);
        return AjaxResult.success(samToolTask);
    }

    /**
     * 重试任务
     */
    @Log(module1 = "SamTool Mgmt", module2 = "Retry", businessType = BusinessType.UPDATE)
    @RequestMapping("/retry")
    public AjaxResult retry(String[] dataNos) {
        samToolTaskService.retry(Arrays.asList(dataNos));
        return AjaxResult.success();
    }

    /**
     * 修改任务优先级
     */
    @Log(module1 = "SamTool Mgmt", module2 = "Change Priority", businessType = BusinessType.UPDATE)
    @RequestMapping("/changePriority")
    public AjaxResult changePriority(String[] dataNos, Integer priority) {
        samToolTaskService.changePriority(Arrays.asList(dataNos), priority);
        return AjaxResult.success();
    }

    /**
     * 导出SamTool数据
     */
    @Log(module1 = "SamTool Mgmt", module2 = "Export", businessType = BusinessType.EXPORT)
    @RequestMapping("/exportData")
    @RequiresPermissions("samtooltask:export")
    public void exportData(String query, HttpServletRequest request, HttpServletResponse response) throws IOException {
        SamToolTaskQueryDTO queryDTO = JSON.parseObject(query, SamToolTaskQueryDTO.class);
        queryDTO.checkNonQuery();
        File file = samToolTaskService.exportSamToolTask(queryDTO);
        DownloadUtils.download(request, response, file, "SamToolTask.json");
    }
} 