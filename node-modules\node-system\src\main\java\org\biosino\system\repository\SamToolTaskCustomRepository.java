package org.biosino.system.repository;

import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.mongo.iterator.MongoPagingIterator;
import org.biosino.system.dto.dto.SamToolTaskQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface SamToolTaskCustomRepository {

    Page<SamToolTask> findPage(SamToolTaskQueryDTO queryDTO);

    void updatePriority(Collection<String> dataNos, Integer priority);

    void retryTask(Collection<String> dataNos);

    MongoPagingIterator<SamToolTask> getPagingIterator(SamToolTaskQueryDTO queryDTO);
} 